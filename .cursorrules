# 项目开发规则

## 功能开发原则
1. 最小功能原则
   - 只实现必要的功能
   - 不添加未要求的功能
   - 不添加复杂交互（如拖拽、动画等）

2. 代码修改原则
   - 遵循最小改动原则
   - 不重构或重写整个组件
   - 只修改必要的部分

3. 需求理解原则
   - 遇到不明确的需求时，先询问具体想要实现什么效果
   - 不要立即开始修改代码
   - 确保理解用户真实意图

4. 界面设计原则
   - 保持界面简单直观
   - 不添加复杂的样式和动画
   - 专注于基本功能和可用性

5. 错误处理原则
   - 遇到错误时，先理解错误原因
   - 不随意修改可能影响其他功能的代码
   - 保持代码的稳定性

## 沟通规则
1. 在修改代码前，先确认理解用户需求
2. 遇到不明确的地方，主动询问
3. 不要过度解释或重复已知信息
4. 专注于解决当前问题，不讨论无关内容

## 项目特定规则
1. 配置管理功能
   - 只保留本地存储
   - 只保留新建和编辑功能
   - 不添加导入导出功能
   - 不添加模板管理功能

2. 界面布局
   - 保持简单直观
   - 不添加复杂交互
   - 确保基本功能可用

3. 代码组织
   - 保持代码结构清晰
   - 不引入不必要的依赖
   - 遵循项目现有的代码风格 