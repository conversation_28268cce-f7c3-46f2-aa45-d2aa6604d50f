# Augment 自动生成代码规则（AUGMENT_RULES.md）

本项目为新开发项目，代码自动生成和修改需遵守以下原则和规范。

---

## 🧠 总体原则
禁止启动服务器，
代码中严禁兼容逻辑，兜底逻辑

- **无需考虑历史兼容性问题**，可直接使用现代 Web 标准与最佳实践。
- 所有实现应**聚焦核心功能本身**，禁止发散功能、封装无请求的扩展。
- 尽可能追求简洁、清晰、可维护的代码结构。
- 尽可能使用稳定的语法，而不是追求最新版本特性。
- 尽可能把通用的逻辑抽取出来，例如工具类，配置类信息。
- 不要过渡设计，如果你认为有必要，可以先和我确定，没问题时在开始
- **使用全局状态管理架构**，不依赖上下游节点的直接数据流动。
- **严禁使用媒体查询（@media）**，必须使用设备检测和条件类名实现响应式布局。

---

## 🧱 技术规范

### Vue 开发约定

- 使用 Vue 3 + `<script setup>` 风格
- 所有组件必须使用 Composition API，不使用 Options API
- props 与 emits 必须使用 `defineProps` / `defineEmits` 定义，带有明确类型
- 文件命名使用 PascalCase（如 `TranslationNode.vue`）
- 变量、函数名使用 camelCase 命名规范
- 避免在模板或逻辑中重复定义相同结构的逻辑
- **严禁使用媒体查询（@media）**，必须使用设备检测和条件类名实现响应式布局

### 响应式设计规范

- **设备检测优先** - 使用 `isMobileDevice()` 函数检测设备类型
- **条件类名** - 使用 `.mobile-layout`, `.desktop-layout` 等类名控制样式
- **统一布局架构** - 所有组件都使用相同的响应式方案，不允许混用
- **禁止屏幕尺寸判断** - 不使用 `window.innerWidth` 等尺寸判断，只使用设备类型判断

### 类型与工具使用

- 禁止使用 `any` 类型，鼓励使用明确类型声明（如 `string[]`, `Record<string, number>` 等）
- 所有可复用逻辑应封装为工具类函数（放入 `src/utils/`）
- 所有配置、常量、规则等应集中管理（放入 `src/config/`）

---

## 🚫 禁止项

### 严禁使用的技术和模式

- **严禁使用媒体查询（@media）** - 任何 CSS 文件中都不允许出现 @media 查询
- **严禁屏幕尺寸判断** - 不使用 `window.innerWidth`、`screen.width` 等尺寸相关判断
- **严禁混合响应式方案** - 不允许在同一项目中混用媒体查询和设备检测

### 代码质量禁止项

- 禁止代码中出现重复实现逻辑，必须封装为工具函数
- 禁止未请求的封装、优化、错误处理逻辑
- 禁止添加注释解释“你为什么这么写”或“这样更好”之类主观性描述
- 禁止使用旧语法或兼容性 hack（如 var、polyfill、`document.querySelector` 等）

---

## 📂 文档与开发规范

### 文档规范

- 所有文档必须统一放在 `docs/` 目录下，按功能分类存放
- 节点相关文档放在 `docs/nodes/` 目录下
- API相关文档放在 `docs/api/` 目录下
- 文档必须使用 Markdown 格式，文件名使用 PascalCase
- 文档必须与代码保持同步，代码更新时必须同步更新文档

### 开发流程

- 前端和后端服务都支持热更新，修改代码后无需手动重启
- 使用全局状态管理代替上下游节点依赖，所有节点通过 nodeStore 交互
- 新增功能时必须同步更新相关文档

### 响应式设计最佳实践

**正确的响应式实现方式：**
```vue
<template>
  <div class="component" :class="{ 'mobile-layout': isMobile }">
    <div class="content">内容</div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { isMobileDevice } from '@/utils/deviceDetector';

const isMobile = computed(() => isMobileDevice());
</script>

<style scoped>
/* 桌面端样式（默认） */
.component {
  padding: 2rem;
  font-size: 1rem;
}

/* 手机端样式 */
.mobile-layout.component {
  padding: 1rem;
  font-size: 0.875rem;
}
</style>
```

**严禁的错误实现方式：**
```css
/* ❌ 严禁使用媒体查询 */
@media (max-width: 768px) {
  .component {
    padding: 1rem;
  }
}

/* ❌ 严禁使用屏幕尺寸判断 */
if (window.innerWidth < 768) {
  // 错误的判断方式
}
```

---
