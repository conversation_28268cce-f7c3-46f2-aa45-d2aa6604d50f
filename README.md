# Echo Lab

Echo Lab是一个用于创建多语言视频内容的工具，支持文本处理、翻译、标注和音频生成等功能。

## 功能特点

- **节点式工作流**：通过连接不同类型的节点创建自定义工作流
- **多语言支持**：支持日语、中文、英语等多种语言
- **文本处理**：分句、序列化、标注等文本处理功能
- **翻译功能**：支持多语言翻译，可批量处理
- **音频生成**：支持多种语言的TTS（文本转语音）功能
- **视频播放模拟**：基于JSON配置模拟视频播放，支持富文本内容、交互功能和智能导航
- **可视化编辑**：直观的拖拽界面，方便编辑和管理
- **JSON导入/导出**：支持将节点配置和位置导出为JSON文件，方便保存和共享
- **内容管理**：支持创建、编辑、预览和管理素材内容，包括草稿和已发布状态

## 节点类型

- **文本节点**：输入和编辑原始文本
- **分句节点**：将文本分割为句子
- **标注节点**：为日语/中文文本添加假名/拼音标注
- **翻译节点**：将文本翻译为多种目标语言
- **富文本节点**：创建带假名标注和交互功能的富文本内容
- **文本序列节点**：管理文本序列，支持设置语速
- **音频节点**：生成文本的语音音频

## 开发环境

### 本地开发

详细的本地开发设置和运行指南请参考：[本地开发指南](docs/LocalDevelopment.md)

### 前端

前端使用Vue 3和Element Plus构建，位于`echo-lab`目录。

```bash
cd echo-lab
npm install
npm run dev
```

### 后端

后端使用Node.js和Express构建，位于`backend`目录。

```bash
cd backend
npm install
npm run dev
```

## 配置

- 前端API配置：`echo-lab/src/config/api.js`
- 语言配置：`echo-lab/src/config/languages.js`

## API规范

项目使用统一的API响应格式，详见[API响应格式规范](docs/api-response-format.md)。所有API开发和调用都应遵循此规范，确保一致性和可预测性。

## 数据库设计

项目使用MySQL数据库存储数据，主要包含以下表：

- **contents表**：存储素材内容，包括名称、描述、配置JSON、缩略图URL、标签和状态等
- **audios表**：存储音频文件元数据，包括文本、语言、说话者、语速、URL、时长和MD5等

详细的数据库设计说明请参考：[数据库设计说明](docs/database-design.md)

初始化数据库：

```bash
cd backend
# 创建数据库和表结构
npm run db:init
# 初始化Sequelize模型
npm run db:create
```

## 视频播放模拟

视频播放模拟功能允许用户：

1. 基于节点网络生成视频播放配置
2. 编辑播放参数（如重复次数、语速等）
3. 预览模拟播放效果，支持富文本内容和交互功能
4. 使用键盘快捷键（左右箭头、空格键）进行智能导航
5. 录制模拟播放内容生成视频
6. 导出/导入配置文件，支持与其他用户共享

富文本功能支持：
- 日语假名标注（显示在汉字上方）
- 鼠标悬停/点击交互（显示详细信息）
- 文本格式化（加粗、斜体、颜色等）

访问路径：`/video-player`

详细文档：[视频播放模拟功能](docs/video-factory/VideoPlaybackSimulation.md)

## 部署指南

项目支持在阿里云ECS服务器上直接部署，详细说明请参考：

- [阿里云ECS部署指南](README.deployment.md)

部署方案特点：
- 一键部署：使用deploy.sh脚本一键完成部署
- PM2进程管理：使用PM2管理Node.js后端进程
- Nginx反向代理：使用Nginx提供前端静态文件和API代理
- 阿里云OSS集成：音频文件存储在阿里云OSS，提高性能和可靠性
- SSL安全：支持HTTPS安全访问

## 项目结构

```
.
├── backend/             # 后端服务
│   ├── models/          # 数据库模型
│   ├── routes/          # API路由
│   ├── services/        # 服务层
│   ├── config/          # 配置文件
│   ├── migrations/      # 数据库迁移
│   ├── scripts/         # 脚本文件
│   ├── .env             # 后端环境变量
│   ├── app.js           # 应用入口
│   └── package.json     # 依赖配置
├── echo-lab/            # 前端应用
│   ├── public/          # 静态资源
│   ├── src/             # 源代码
│   │   ├── components/  # 组件
│   │   │   ├── nodes/   # 节点组件
│   │   │   └── ...
│   │   ├── config/      # 配置文件
│   │   ├── stores/      # 状态管理
│   │   ├── views/       # 页面视图
│   │   └── ...
│   ├── package.json     # 依赖配置
│   └── ...
├── docs/                # 文档
│   ├── deployment/      # 部署文档
│   └── ...
├── nginx.conf           # Nginx配置模板
├── ecosystem.config.js  # PM2配置文件
├── deploy.sh            # 部署脚本
├── start-dev.sh         # 开发环境启动脚本
└── .nvmrc               # Node.js版本配置
```

**注意**：所有后端相关的代码和配置都位于`backend`目录中，所有前端相关的代码和配置都位于`echo-lab`目录中。在开发时，请确保在正确的目录中运行命令。

下载服务器文件
scp -i echo-lab.pem -r ecs-user@47.242.116.72:/home/<USER>/JapRepeater/backend/jobs/5fbb02c3-d902-4c5f-a163-143d95e84324/output ./test

连接服务器
ssh ecs-user@47.242.116.72 -i echo-lab.pem

