/**
 * 安全配置
 * 集中管理应用的安全相关配置
 */
const path = require('path');
const dotenv = require('dotenv');

// 根据环境加载不同的配置文件
const env = process.env.NODE_ENV || 'development';
const envFile = env === 'development' ? '.env.development' : '.env';
const envPath = path.resolve(__dirname, '..', envFile);

// 加载环境变量
dotenv.config({ path: envPath });

// 安全配置
const securityConfig = {
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'echo-lab-secret-key-2025',
    expiresIn: process.env.JWT_EXPIRES_IN || '30d',
  },
  
  // 验证码配置
  verificationCode: {
    expiresIn: parseInt(process.env.CODE_EXPIRES_IN || '600', 10), // 默认10分钟
    length: parseInt(process.env.CODE_LENGTH || '6', 10), // 默认6位
    maxAttempts: parseInt(process.env.MAX_ATTEMPTS || '5', 10), // 默认5次
    ipLockTime: parseInt(process.env.IP_LOCK_TIME || '3600', 10), // 默认1小时
  },
  
  // 请求频率限制配置
  rateLimit: {
    // 全局API限制
    global: {
      windowMs: 60 * 1000, // 1分钟
      max: 100, // 每分钟最多100个请求
      message: '请求过于频繁，请稍后再试',
    },
    
    // 认证API限制
    auth: {
      windowMs: 5 * 60 * 1000, // 5分钟
      max: 20, // 5分钟内最多20次请求
      message: '认证请求过于频繁，请5分钟后再试',
    },
    
    // 验证码API限制
    verificationCode: {
      windowMs: 60 * 60 * 1000, // 1小时
      max: 5, // 1小时内最多5次请求
      message: '验证码请求过于频繁，请1小时后再试',
    },
    
    // TTS API限制
    tts: {
      windowMs: 5 * 60 * 1000, // 5分钟
      max: 30, // 5分钟内最多30次请求
      message: 'TTS请求过于频繁，请稍后再试',
    },
    
    // 视频导出API限制
    videoExport: {
      windowMs: 60 * 60 * 1000, // 1小时
      max: 5, // 1小时内最多5次请求
      message: '视频导出请求过于频繁，请稍后再试',
    },
  },
  
  // CORS配置
  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'X-CSRF-Token',
      'X-Request-ID',
    ],
    credentials: true,
    maxAge: 86400, // 预检请求缓存24小时
  },
  
  // 内容安全策略
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https:"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  
  // 失败登录配置
  failedLogin: {
    // 记录保留天数
    retentionDays: 7,
    // 短时间内允许的最大失败次数
    maxFailures: 5,
    // 检查时间窗口（毫秒）
    checkWindow: 30 * 60 * 1000, // 30分钟
    // 黑名单持续时间（毫秒）
    blacklistDuration: 60 * 60 * 1000, // 1小时
  },
};

module.exports = securityConfig;
