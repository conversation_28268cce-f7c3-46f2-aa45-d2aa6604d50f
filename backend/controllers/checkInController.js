/**
 * 签到控制器
 */
const { CheckIn } = require('../models');
const { nanoid } = require('nanoid');
const { Op } = require('sequelize');

/**
 * 执行签到
 */
const checkIn = async (req, res) => {
  try {
    const userId = req.user.id;
    const today = new Date().toISOString().split('T')[0];

    // 检查今日是否已签到
    const existingCheckIn = await CheckIn.findOne({
      where: {
        userId,
        checkInDate: today
      }
    });

    if (existingCheckIn) {
      return res.json({
        success: false,
        message: '今日已签到'
      });
    }

    // 创建签到记录
    const checkInRecord = await CheckIn.create({
      id: nanoid(),
      userId,
      checkInDate: today,
      rewards: [] // 预留奖励字段
    });

    // 计算新的连续签到天数
    const consecutiveDays = await calculateConsecutiveDays(userId);

    res.json({
      success: true,
      message: `签到成功！连续签到${consecutiveDays}天`,
      data: {
        consecutiveDays,
        checkInDate: today
      }
    });
  } catch (error) {
    console.error('签到失败:', error);
    res.status(500).json({
      success: false,
      message: '签到失败'
    });
  }
};

/**
 * 获取签到统计
 */
const getCheckInStats = async (req, res) => {
  try {
    const userId = req.user.id;
    const today = new Date().toISOString().split('T')[0];

    // 获取总签到天数
    const totalDays = await CheckIn.count({
      where: { userId }
    });

    // 获取本月签到天数
    const now = new Date();
    const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
    const monthlyCount = await CheckIn.count({
      where: {
        userId,
        checkInDate: {
          [Op.startsWith]: currentMonth
        }
      }
    });

    // 获取连续签到天数
    const consecutiveDays = await calculateConsecutiveDays(userId);

    // 获取最长连续签到天数
    const maxConsecutiveDays = await getMaxConsecutiveDays(userId);

    // 检查今日是否已签到
    const todayChecked = await CheckIn.findOne({
      where: {
        userId,
        checkInDate: today
      }
    });

    res.json({
      success: true,
      data: {
        totalDays,
        monthlyCount,
        consecutiveDays,
        maxConsecutiveDays,
        todayChecked: !!todayChecked
      }
    });
  } catch (error) {
    console.error('获取签到统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取签到统计失败'
    });
  }
};

/**
 * 获取签到日历数据
 */
const getCheckInCalendar = async (req, res) => {
  try {
    const userId = req.user.id;
    const { year, month } = req.query;
    
    const targetMonth = `${year}-${String(month).padStart(2, '0')}`;
    
    const checkIns = await CheckIn.findAll({
      where: {
        userId,
        checkInDate: {
          [Op.startsWith]: targetMonth
        }
      },
      order: [['checkInDate', 'ASC']]
    });

    res.json({
      success: true,
      data: checkIns.map(record => ({
        date: record.checkInDate
      }))
    });
  } catch (error) {
    console.error('获取签到日历失败:', error);
    res.status(500).json({
      success: false,
      message: '获取签到日历失败'
    });
  }
};

/**
 * 计算连续签到天数
 */
async function calculateConsecutiveDays(userId) {
  try {
    const records = await CheckIn.findAll({
      where: { userId },
      order: [['checkInDate', 'DESC']]
    });

    if (records.length === 0) return 0;

    let consecutive = 0;
    const today = new Date().toISOString().split('T')[0];
    
    // 从最新的记录开始检查
    for (let i = 0; i < records.length; i++) {
      const currentRecord = records[i];
      
      if (i === 0) {
        // 第一条记录，检查是否是今天或昨天
        const recordDate = new Date(currentRecord.checkInDate);
        const todayDate = new Date(today);
        const dayDiff = Math.floor((todayDate - recordDate) / (1000 * 60 * 60 * 24));
        
        if (dayDiff > 1) {
          // 距离今天超过一天，连续中断
          break;
        }
        consecutive = 1;
      } else {
        // 检查是否与上一条记录相连
        const prevRecord = records[i - 1];
        const currentDate = new Date(currentRecord.checkInDate);
        const prevDate = new Date(prevRecord.checkInDate);
        const dayDiff = Math.floor((prevDate - currentDate) / (1000 * 60 * 60 * 24));
        
        if (dayDiff === 1) {
          consecutive++;
        } else {
          break;
        }
      }
    }

    return consecutive;
  } catch (error) {
    console.error('计算连续签到天数失败:', error);
    return 0;
  }
}

/**
 * 获取最长连续签到天数
 */
async function getMaxConsecutiveDays(userId) {
  try {
    const records = await CheckIn.findAll({
      where: { userId },
      order: [['checkInDate', 'ASC']]
    });

    if (records.length === 0) return 0;

    let maxConsecutive = 0;
    let currentConsecutive = 0;
    let lastDate = null;

    for (const record of records) {
      const currentDate = new Date(record.checkInDate);
      
      if (lastDate) {
        const dayDiff = (currentDate - lastDate) / (1000 * 60 * 60 * 24);
        if (dayDiff === 1) {
          currentConsecutive++;
        } else {
          maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
          currentConsecutive = 1;
        }
      } else {
        currentConsecutive = 1;
      }
      
      lastDate = currentDate;
    }
    
    maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
    return maxConsecutive;
  } catch (error) {
    console.error('获取最长连续签到天数失败:', error);
    return 0;
  }
}

module.exports = {
  checkIn,
  getCheckInStats,
  getCheckInCalendar
};