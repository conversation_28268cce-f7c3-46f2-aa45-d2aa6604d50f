/**
 * 用户反馈控制器
 * 处理用户反馈相关的请求
 */
const feedbackService = require("../services/feedbackService");

/**
 * 提交反馈
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
async function submitFeedback(req, res) {
  try {
    const { type, content, contact, pageUrl } = req.body;

    // 验证必填字段
    if (!content) {
      return res.status(400).json({
        success: false,
        error: "反馈内容不能为空"
      });
    }

    // 验证反馈类型
    if (type && !["suggestion", "bug", "feature", "other"].includes(type)) {
      return res.status(400).json({
        success: false,
        error: "无效的反馈类型"
      });
    }

    // 获取用户ID（如果已登录）
    const userId = req.user ? req.user.id : null;

    // 创建反馈
    const result = await feedbackService.createFeedback(
      {
        userId,
        type: type || "suggestion",
        content,
        contact,
        pageUrl
      },
      req
    );

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.status(201).json(result);
  } catch (error) {
    console.error("提交反馈失败:", error);
    res.status(500).json({
      success: false,
      error: `提交反馈失败: ${error.message}`
    });
  }
}

/**
 * 获取当前用户的反馈列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
async function getUserFeedbacks(req, res) {
  try {
    const { page, pageSize, status, type } = req.query;

    // 获取用户ID
    const userId = req.user.id;

    // 获取反馈列表
    const result = await feedbackService.getFeedbacks({
      userId,
      page: parseInt(page) || 1,
      pageSize: parseInt(pageSize) || 10,
      status,
      type
    });

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error("获取用户反馈列表失败:", error);
    res.status(500).json({
      success: false,
      error: `获取用户反馈列表失败: ${error.message}`
    });
  }
}

/**
 * 获取所有反馈（管理员）
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
async function getAllFeedbacks(req, res) {
  try {
    const { page, pageSize, status, type, userId } = req.query;

    // 获取反馈列表
    const result = await feedbackService.getFeedbacks({
      page: parseInt(page) || 1,
      pageSize: parseInt(pageSize) || 10,
      status,
      type,
      userId
    });

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error("获取所有反馈失败:", error);
    res.status(500).json({
      success: false,
      error: `获取所有反馈失败: ${error.message}`
    });
  }
}

/**
 * 更新反馈状态（管理员）
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
async function updateFeedbackStatus(req, res) {
  try {
    const { id } = req.params;
    const { status, adminReply } = req.body;

    // 验证状态
    if (!["pending", "processing", "resolved", "rejected"].includes(status)) {
      return res.status(400).json({
        success: false,
        error: "无效的状态"
      });
    }

    // 获取管理员ID
    const adminId = req.user.id;

    // 更新反馈状态
    const result = await feedbackService.updateFeedbackStatus(
      id,
      { status, adminReply },
      adminId
    );

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error("更新反馈状态失败:", error);
    res.status(500).json({
      success: false,
      error: `更新反馈状态失败: ${error.message}`
    });
  }
}

module.exports = {
  submitFeedback,
  getUserFeedbacks,
  getAllFeedbacks,
  updateFeedbackStatus
};
