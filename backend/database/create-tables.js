/**
 * 数据库表创建脚本
 * 用于初始化数据库表结构
 */
const db = require('../models');

async function createTables() {
  try {
    console.log('开始创建数据库表...');

    // 同步所有模型到数据库，仅在开发环境强制重新创建表
    await db.sequelize.sync({
      force: process.env.NODE_ENV === 'development', // 仅开发环境强制重新创建
      alter: process.env.NODE_ENV !== 'development', // 生产环境使用alter模式
      hooks: false // 禁用钩子
    });

    console.log('数据库表创建成功！');
    process.exit(0);
  } catch (error) {
    console.error('创建数据库表失败:', error);
    process.exit(1);
  }
}

// 执行创建表操作
createTables();
