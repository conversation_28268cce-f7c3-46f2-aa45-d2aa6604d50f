# 鉴权中间件使用指南

## 📋 概述

本文档规范了项目中鉴权中间件的使用方式，确保权限检查的一致性和安全性。

## 🔧 可用的中间件

### 1. 基础认证中间件

#### `authenticate`
- **用途**: 验证用户是否已登录
- **位置**: `backend/middleware/authMiddleware.js`
- **使用**: 所有需要登录的接口都必须使用

```javascript
const { authenticate } = require('../middleware/authMiddleware');
router.get('/protected', authenticate, (req, res) => {
  // req.user 包含用户信息
});
```

#### `optionalAuthenticate`
- **用途**: 可选的用户认证（登录或未登录都可以访问）
- **使用**: 公开接口但需要区分用户状态时使用

```javascript
const { optionalAuthenticate } = require('../middleware/authMiddleware');
router.get('/public', optionalAuthenticate, (req, res) => {
  // req.user 可能为 null
});
```

### 2. 管理员权限中间件

#### `adminOnly` ⭐ **推荐使用**
- **用途**: 检查管理员权限（需要先通过 authenticate）
- **位置**: `backend/middleware/adminMiddleware.js`
- **特点**: 从数据库实时获取用户角色，权限变更立即生效

```javascript
const { authenticate } = require('../middleware/authMiddleware');
const { adminOnly } = require('../middleware/adminMiddleware');

router.get('/admin-only', authenticate, adminOnly, (req, res) => {
  // 只有管理员可以访问
});
```

#### `adminAuth` ⚠️ **不推荐使用**
- **用途**: 完整的管理员认证（包含认证+权限检查）
- **问题**: 重复了 authenticate 的逻辑，不够灵活

### 3. 功能权限中间件

#### `checkFeaturePermission(featureKey)`
- **用途**: 检查用户是否有特定功能权限
- **位置**: `backend/middleware/featurePermissionMiddleware.js`

```javascript
const { authenticate } = require('../middleware/authMiddleware');
const { checkFeaturePermission } = require('../middleware/featurePermissionMiddleware');

router.get('/feature', authenticate, checkFeaturePermission('content_creation'), (req, res) => {
  // 需要内容创建权限
});
```

## 📝 使用规范

### ✅ 推荐的组合方式

1. **普通用户接口**:
```javascript
router.get('/user-data', authenticate, (req, res) => {});
```

2. **管理员接口**:
```javascript
router.get('/admin-data', authenticate, adminOnly, (req, res) => {});
```

3. **功能权限接口**:
```javascript
router.get('/feature', authenticate, checkFeaturePermission('feature_key'), (req, res) => {});
```

4. **公开接口（可选登录）**:
```javascript
router.get('/public', optionalAuthenticate, (req, res) => {});
```

### ❌ 避免的做法

1. **不要在路由中直接检查角色**:
```javascript
// ❌ 错误做法
router.get('/admin', authenticate, (req, res) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: '权限不足' });
  }
});

// ✅ 正确做法
router.get('/admin', authenticate, adminOnly, (req, res) => {});
```

2. **不要使用已废弃的中间件**:
```javascript
// ❌ 已废弃
const { isAdmin } = require('../middleware/authMiddleware');

// ✅ 使用新的
const { adminOnly } = require('../middleware/adminMiddleware');
```

3. **不要重复认证**:
```javascript
// ❌ 错误：adminAuth 已经包含了认证
router.get('/admin', authenticate, adminAuth, (req, res) => {});

// ✅ 正确
router.get('/admin', authenticate, adminOnly, (req, res) => {});
```

## 🔄 权限实时性

所有权限检查中间件都已更新为从数据库实时获取用户角色，确保：

- ✅ 管理员修改用户权限后立即生效
- ✅ 用户无需重新登录
- ✅ 安全性更高（防止token中角色信息被篡改）

## 🚀 迁移指南

如果你的代码中使用了旧的方式，请按以下方式迁移：

### 1. 替换 `isAdmin`
```javascript
// 旧代码
const { authenticate, isAdmin } = require('../middleware/authMiddleware');
router.get('/admin', isAdmin, (req, res) => {});

// 新代码
const { authenticate } = require('../middleware/authMiddleware');
const { adminOnly } = require('../middleware/adminMiddleware');
router.get('/admin', authenticate, adminOnly, (req, res) => {});
```

### 2. 替换 `adminAuth`
```javascript
// 旧代码
const { adminAuth } = require('../middleware/adminMiddleware');
router.get('/admin', adminAuth, (req, res) => {});

// 新代码
const { authenticate } = require('../middleware/authMiddleware');
const { adminOnly } = require('../middleware/adminMiddleware');
router.get('/admin', authenticate, adminOnly, (req, res) => {});
```

### 3. 移除路由中的角色检查
```javascript
// 旧代码
router.get('/admin', authenticate, (req, res) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: '权限不足' });
  }
  // 业务逻辑
});

// 新代码
router.get('/admin', authenticate, adminOnly, (req, res) => {
  // 业务逻辑
});
```

## 📚 总结

- 使用 `authenticate` 进行基础认证
- 使用 `adminOnly` 进行管理员权限检查
- 使用 `checkFeaturePermission` 进行功能权限检查
- 避免在路由中直接检查用户角色
- 所有权限检查都是实时的，无需重新登录
