/**
 * 功能权限中间件
 * 检查用户是否有权限访问特定功能
 */
const db = require("../models");

/**
 * 检查用户是否有权限访问特定功能
 * @param {string} featureKey - 功能标识符
 * @returns {Function} Express中间件
 */
function checkFeaturePermission(featureKey) {
  return async (req, res, next) => {
    try {
      // 视频播放功能始终允许访问
      if (featureKey === "player_access") {
        return next();
      }

      // 确保用户已认证
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          success: false,
          error: "未授权访问，请先登录",
        });
      }

      // 从数据库实时检查管理员权限
      const user = await db.User.findByPk(req.user.id, {
        attributes: ['id', 'role']
      });

      if (user && user.role === "admin") {
        return next();
      }

      // 检查功能是否全局启用
      const featureFlag = await db.FeatureFlag.findOne({
        where: { featureKey },
      });

      if (featureFlag && featureFlag.isEnabled) {
        // 功能全局启用，所有用户都可以访问
        return next();
      }

      // 检查用户是否有特定权限
      const permission = await db.FeaturePermission.findOne({
        where: {
          featureKey,
          userId: req.user.id,
        },
      });

      if (permission) {
        // 用户有权限访问
        return next();
      }

      // 用户没有权限
      return res.status(403).json({
        success: false,
        error: "您没有权限访问此功能",
      });
    } catch (error) {
      console.error("检查功能权限时出错:", error);
      return res.status(500).json({
        success: false,
        error: "服务器内部错误",
      });
    }
  };
}

module.exports = {
  checkFeaturePermission,
};
