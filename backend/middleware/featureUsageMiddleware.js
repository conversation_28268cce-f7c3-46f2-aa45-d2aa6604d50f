/**
 * 功能使用中间件
 * 检查并记录功能使用情况
 */
const db = require('../models');

/**
 * 检查并记录功能使用情况
 * @param {string} featureKey - 功能标识符
 * @returns {Function} Express中间件
 */
function checkFeatureUsage(featureKey) {
  return async (req, res, next) => {
    try {
      // 确保用户已认证
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          success: false,
          error: '未授权访问，请先登录',
          code: 'UNAUTHORIZED'
        });
      }
      
      // 从数据库实时检查用户信息
      const user = await db.User.findByPk(req.user.id, {
        attributes: ['id', 'role', 'level']
      });

      if (!user) {
        return res.status(403).json({
          success: false,
          error: '用户不存在',
          code: 'USER_NOT_FOUND'
        });
      }

      if (user.role === 'admin') {
        // 记录使用但不检查限制
        await db.FeatureUsageRecord.create({
          userId: req.user.id,
          featureKey,
          usedAt: new Date()
        });
        return next();
      }
      
      if (!user) {
        return res.status(403).json({
          success: false,
          error: '用户不存在',
          code: 'USER_NOT_FOUND'
        });
      }
      
      // 获取功能使用限制
      const usageLimit = await db.FeatureUsageLimit.findOne({
        where: {
          level: user.level,
          featureKey
        }
      });
      
      // 如果没有限制，允许使用
      if (!usageLimit || (!usageLimit.dailyLimit && !usageLimit.monthlyLimit)) {
        // 记录使用
        await db.FeatureUsageRecord.create({
          userId: user.id,
          featureKey,
          usedAt: new Date()
        });
        return next();
      }
      
      // 检查日使用限制
      if (usageLimit.dailyLimit) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        const dailyUsage = await db.FeatureUsageRecord.count({
          where: {
            userId: user.id,
            featureKey,
            usedAt: {
              [db.Sequelize.Op.gte]: today
            }
          }
        });
        
        if (dailyUsage >= usageLimit.dailyLimit) {
          return res.status(429).json({
            success: false,
            error: '您今日的使用次数已达上限',
            code: 'DAILY_LIMIT_EXCEEDED',
            limit: usageLimit.dailyLimit,
            usage: dailyUsage
          });
        }
      }
      
      // 检查月使用限制
      if (usageLimit.monthlyLimit) {
        const firstDayOfMonth = new Date();
        firstDayOfMonth.setDate(1);
        firstDayOfMonth.setHours(0, 0, 0, 0);
        
        const monthlyUsage = await db.FeatureUsageRecord.count({
          where: {
            userId: user.id,
            featureKey,
            usedAt: {
              [db.Sequelize.Op.gte]: firstDayOfMonth
            }
          }
        });
        
        if (monthlyUsage >= usageLimit.monthlyLimit) {
          return res.status(429).json({
            success: false,
            error: '您本月的使用次数已达上限',
            code: 'MONTHLY_LIMIT_EXCEEDED',
            limit: usageLimit.monthlyLimit,
            usage: monthlyUsage
          });
        }
      }
      
      // 记录使用
      await db.FeatureUsageRecord.create({
        userId: user.id,
        featureKey,
        usedAt: new Date()
      });
      
      next();
    } catch (error) {
      console.error('检查功能使用限制时出错:', error);
      return res.status(500).json({
        success: false,
        error: '服务器内部错误',
        code: 'SERVER_ERROR'
      });
    }
  };
}

module.exports = {
  checkFeatureUsage
};
