/**
 * IP过滤中间件
 * 实现IP黑名单和白名单功能，阻止恶意IP访问
 * 从数据库加载IP名单
 */
const ipRangeCheck = require("ip-range-check");
const db = require("../models");

// 内存中的黑白名单缓存
let blacklistCache = [];
let whitelistCache = [];
let cacheExpiration = 0; // 缓存过期时间
const CACHE_DURATION = 5 * 60 * 1000; // 缓存持续时间：5分钟

// 动态黑名单（仅存在内存中）
let dynamicBlacklist = new Map(); // IP => { expires, reason }

// 从数据库加载黑白名单
async function loadListsFromDB() {
  try {
    // 如果缓存未过期，直接返回
    if (Date.now() < cacheExpiration) {
      return;
    }

    console.log("[IPFilter] 从数据库加载IP黑白名单...");

    // 从数据库加载黑名单和白名单
    const blacklistRows = await db.IpList.findAll({
      where: {
        type: "blacklist",
        [db.Sequelize.Op.or]: [
          { expires_at: null }, // 永久黑名单
          { expires_at: { [db.Sequelize.Op.gt]: new Date() } }, // 未过期的黑名单
        ],
      },
      attributes: ["ip"],
    });

    const whitelistRows = await db.IpList.findAll({
      where: {
        type: "whitelist",
      },
      attributes: ["ip"],
    });

    // 更新缓存
    blacklistCache = blacklistRows.map((row) => row.ip);
    whitelistCache = whitelistRows.map((row) => row.ip);

    // 更新缓存过期时间
    cacheExpiration = Date.now() + CACHE_DURATION;

    console.log(
      `[IPFilter] 已从数据库加载${blacklistCache.length}个IP黑名单规则和${whitelistCache.length}个IP白名单规则`
    );
  } catch (error) {
    console.error("[IPFilter] 从数据库加载IP名单失败:", error);

    // 如果加载失败，使用空数组
    blacklistCache = [];
    whitelistCache = [];

    // 设置较短的缓存时间，以便稍后重试
    cacheExpiration = Date.now() + 60 * 1000; // 1分钟后重试
  }
}

// 初始加载
loadListsFromDB().catch((err) => {
  console.error("[IPFilter] 初始加载IP名单失败:", err);
});

// 定期清理过期的动态黑名单
setInterval(() => {
  const now = Date.now();
  let count = 0;

  for (const [ip, data] of dynamicBlacklist.entries()) {
    if (data.expires <= now) {
      dynamicBlacklist.delete(ip);
      count++;
    }
  }

  if (count > 0) {
    console.log(`[IPFilter] 已清理${count}个过期的动态黑名单IP`);
  }
}, 60 * 1000); // 每分钟清理一次

/**
 * 添加IP到动态黑名单
 * @param {string} ip IP地址
 * @param {number} duration 黑名单持续时间（毫秒）
 * @param {string} reason 原因
 */
function addToDynamicBlacklist(ip, duration = 3600000, reason = "可疑行为") {
  dynamicBlacklist.set(ip, {
    expires: Date.now() + duration,
    reason,
  });
  console.warn(
    `[IPFilter] IP ${ip} 已添加到动态黑名单，原因: ${reason}，持续时间: ${
      duration / 1000
    }秒`
  );
}

/**
 * 检查IP是否在黑名单中
 * @param {string} ip IP地址
 * @returns {Promise<boolean>} 是否在黑名单中
 */
async function isBlacklisted(ip) {
  // 确保缓存是最新的
  await loadListsFromDB();

  // 检查动态黑名单
  if (dynamicBlacklist.has(ip)) {
    const data = dynamicBlacklist.get(ip);
    if (data.expires > Date.now()) {
      return true;
    }
    // 过期了，删除
    dynamicBlacklist.delete(ip);
  }

  // 检查静态黑名单
  return blacklistCache.some((item) => {
    if (typeof item === "string") {
      return item === ip || ipRangeCheck(ip, item);
    }
    return false;
  });
}

/**
 * 检查IP是否在白名单中
 * @param {string} ip IP地址
 * @returns {Promise<boolean>} 是否在白名单中
 */
async function isWhitelisted(ip) {
  // 确保缓存是最新的
  await loadListsFromDB();

  // 如果白名单为空，视为所有IP都在白名单中
  if (whitelistCache.length === 0) {
    return true;
  }

  return whitelistCache.some((item) => {
    if (typeof item === "string") {
      return item === ip || ipRangeCheck(ip, item);
    }
    return false;
  });
}

/**
 * 创建IP过滤中间件
 * @param {Object} options 配置选项
 * @returns {Function} Express中间件
 */
function createIpFilter(options = {}) {
  const {
    mode = "blacklist", // blacklist或whitelist
    message = "您的IP地址已被封禁",
    statusCode = 403,
    trustProxy = true, // 是否信任代理
    allowPrivateIPs = true, // 是否允许私有IP
    log = true, // 是否记录日志
  } = options;

  return async (req, res, next) => {
    try {
      // 获取客户端IP
      const ip = trustProxy
        ? (req.headers["x-forwarded-for"] || "").split(",")[0].trim() ||
          req.connection.remoteAddress
        : req.connection.remoteAddress;

      // 检查是否为私有IP
      const isPrivateIP =
        /^(10\.|172\.(1[6-9]|2[0-9]|3[0-1])\.|192\.168\.)/.test(ip);

      // 如果是私有IP且允许私有IP，则放行
      if (isPrivateIP && allowPrivateIPs) {
        return next();
      }

      // 根据模式进行检查
      if (mode === "blacklist") {
        // 黑名单模式：如果在黑名单中，则拒绝访问
        if (await isBlacklisted(ip)) {
          if (log) {
            console.warn(
              `[IPFilter] 拒绝黑名单IP访问: ${ip}, 路径: ${req.method} ${req.originalUrl}`
            );
          }
          return res.status(statusCode).json({
            success: false,
            error: message,
            code: "IP_BLOCKED",
          });
        }
      } else if (mode === "whitelist") {
        // 白名单模式：如果不在白名单中，则拒绝访问
        if (!(await isWhitelisted(ip))) {
          if (log) {
            console.warn(
              `[IPFilter] 拒绝非白名单IP访问: ${ip}, 路径: ${req.method} ${req.originalUrl}`
            );
          }
          return res.status(statusCode).json({
            success: false,
            error: "您的IP地址未被授权访问",
            code: "IP_NOT_ALLOWED",
          });
        }
      }

      // 通过检查，继续处理请求
      next();
    } catch (error) {
      // 如果检查过程中出错，记录错误并放行请求
      // 这是为了确保即使IP检查失败，应用仍然可以正常工作
      console.error(`[IPFilter] IP检查出错: ${error.message}`);
      next();
    }
  };
}

// 预定义的IP过滤中间件
const ipFilter = createIpFilter();

/**
 * 获取动态黑名单
 * @returns {Map} 动态黑名单
 */
function getDynamicBlacklist() {
  return dynamicBlacklist;
}

/**
 * 从动态黑名单中移除IP
 * @param {string} ip IP地址
 * @returns {boolean} 是否成功移除
 */
function removeFromDynamicBlacklist(ip) {
  if (dynamicBlacklist.has(ip)) {
    dynamicBlacklist.delete(ip);
    console.log(`[IPFilter] IP ${ip} 已从动态黑名单中移除`);
    return true;
  }
  return false;
}

/**
 * 强制刷新缓存
 */
function resetCache() {
  cacheExpiration = 0;
  console.log("[IPFilter] 缓存已重置，下次请求将重新加载IP名单");
}

/**
 * 批量添加IP到名单
 * @param {Array} ips IP地址数组
 * @param {string} type 类型：'blacklist'或'whitelist'
 * @param {string} reason 原因或备注
 * @param {string} creator 创建者
 * @returns {Promise<number>} 添加的IP数量
 */
async function batchAddToList(
  ips,
  type = "blacklist",
  reason = "批量添加",
  creator = "system"
) {
  try {
    let count = 0;

    // 插入到数据库
    for (const ip of ips) {
      const [_, created] = await db.IpList.findOrCreate({
        where: { ip, type },
        defaults: {
          reason,
          expires_at: type === "blacklist" ? null : undefined, // 只有黑名单需要过期时间
          created_by: creator,
        },
      });

      if (created) count++;
    }

    // 重置缓存
    resetCache();

    console.log(
      `[IPFilter] 已批量添加${count}个IP到${
        type === "blacklist" ? "黑" : "白"
      }名单`
    );
    return count;
  } catch (error) {
    console.error(
      `[IPFilter] 批量添加IP到${type === "blacklist" ? "黑" : "白"}名单失败:`,
      error
    );
    throw error;
  }
}

/**
 * 批量添加IP到黑名单
 * @param {Array} ips IP地址数组
 * @param {string} reason 原因
 * @param {string} creator 创建者
 * @returns {Promise<number>} 添加的IP数量
 */
async function batchAddToBlacklist(
  ips,
  reason = "批量添加",
  creator = "system"
) {
  return batchAddToList(ips, "blacklist", reason, creator);
}

/**
 * 批量添加IP到白名单
 * @param {Array} ips IP地址数组
 * @param {string} comment 备注
 * @param {string} creator 创建者
 * @returns {Promise<number>} 添加的IP数量
 */
async function batchAddToWhitelist(
  ips,
  comment = "批量添加",
  creator = "system"
) {
  return batchAddToList(ips, "whitelist", comment, creator);
}

module.exports = {
  ipFilter,
  addToDynamicBlacklist,
  removeFromDynamicBlacklist,
  isBlacklisted,
  isWhitelisted,
  loadListsFromDB,
  getDynamicBlacklist,
  resetCache,
  batchAddToList,
  batchAddToBlacklist,
  batchAddToWhitelist,
};
