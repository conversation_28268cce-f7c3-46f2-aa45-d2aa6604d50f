/**
 * 请求频率限制中间件
 * 用于限制API请求频率，防止接口刷新攻击
 */
const rateLimit = require("express-rate-limit");
const { nanoid } = require("nanoid");

/**
 * 创建请求频率限制中间件
 * @param {Object} options 配置选项
 * @returns {Function} Express中间件
 */
function createRateLimiter(options = {}) {
  const {
    windowMs = 60 * 1000, // 默认窗口期：1分钟
    max = 60, // 默认最大请求数：60次/分钟
    message = "请求过于频繁，请稍后再试",
    statusCode = 429,
    keyGenerator = (req) => {
      // 默认使用IP + 路径作为键
      const ip =
        req.ip ||
        req.headers["x-forwarded-for"] ||
        req.connection.remoteAddress;
      return `${ip}:${req.method}:${req.originalUrl}`;
    },
    skip = (req) => false, // 默认不跳过任何请求
    requestIdHeader = "X-Request-ID",
  } = options;

  const limiterOptions = {
    windowMs,
    max,
    message: {
      success: false,
      error: message,
      code: "RATE_LIMIT_EXCEEDED",
    },
    statusCode,
    keyGenerator,
    skip,
    standardHeaders: true, // 返回 RateLimit-* 头部
    legacyHeaders: false, // 禁用 X-RateLimit-* 头部
    handler: (req, res, next, options) => {
      // 记录被限制的请求
      const requestId =
        req.headers[requestIdHeader.toLowerCase()] || nanoid(10);
      console.warn(
        `[RateLimit] 请求被限制: ${req.method} ${req.originalUrl}, IP: ${req.ip}, 请求ID: ${requestId}`
      );

      // 同时记录达到限制的情况（替代已弃用的onLimitReached）
      console.error(
        `[RateLimit] 达到限制: ${req.method} ${req.originalUrl}, IP: ${req.ip}, 请求ID: ${requestId}`
      );

      res.status(options.statusCode).json(options.message);
    },
    // 注意：在 express-rate-limit v7 中，onLimitReached 已被弃用
    // 我们在 handler 中已经处理了记录逻辑
  };

  // 使用内存存储（默认）

  return rateLimit(limiterOptions);
}

// 预定义的限制器
const limiters = {
  // 全局API限制器 - 每个IP每分钟最多100个请求
  global: createRateLimiter({
    windowMs: 60 * 1000,
    max: 100,
    message: "请求过于频繁，请稍后再试",
    skip: (req) => {
      // 跳过静态资源请求
      return req.path.startsWith("/public/");
    },
  }),

  // 认证API限制器 - 更严格的限制
  auth: createRateLimiter({
    windowMs: 5 * 60 * 1000, // 5分钟
    max: 20, // 5分钟内最多20次请求
    message: "认证请求过于频繁，请5分钟后再试",
  }),

  // 验证码API限制器 - 防止暴力破解
  verificationCode: createRateLimiter({
    windowMs: 60 * 60 * 1000, // 1小时
    max: 5, // 1小时内最多5次请求
    message: "验证码请求过于频繁，请1小时后再试",
  }),

  // TTS API限制器 - 防止资源滥用
  tts: createRateLimiter({
    windowMs: 5 * 60 * 1000, // 5分钟
    max: 30, // 5分钟内最多30次请求
    message: "TTS请求过于频繁，请稍后再试",
  }),

  // 视频导出API限制器 - 防止资源滥用
  videoExport: createRateLimiter({
    windowMs: 60 * 60 * 1000, // 1小时
    max: 5, // 1小时内最多5次请求
    message:
      "为了保证服务质量，每小时最多可导出5个视频。您已达到本小时的导出上限，请稍后再试。感谢您的理解与支持！",
  }),

  // 视频下载API限制器 - 相对宽松的限制
  videoDownload: createRateLimiter({
    windowMs: 60 * 60 * 1000, // 1小时
    max: 10, // 1小时内最多10次下载请求
    message: "下载请求过于频繁，请稍后再试。如需重新下载，请等待片刻。",
  }),

  // 视频进度查询API限制器 - 较宽松的限制
  videoProgress: createRateLimiter({
    windowMs: 60 * 1000, // 1分钟
    max: 20, // 1分钟内最多20次请求，配合5秒轮询间隔
    message:
      "视频处理正在进行中，系统正在努力为您生成视频。请稍等片刻再查询进度，页面会自动更新。",
  }),

  // 管理员API限制器 - 较宽松的限制
  admin: createRateLimiter({
    windowMs: 60 * 1000, // 1分钟
    max: 60, // 1分钟内最多60次请求
    message: "管理员请求过于频繁，请稍后再试",
  }),
};

module.exports = limiters;
