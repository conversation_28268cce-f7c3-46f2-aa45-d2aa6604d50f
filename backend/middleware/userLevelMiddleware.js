/**
 * 用户等级中间件
 * 检查用户是否达到所需等级
 */
const db = require('../models');

/**
 * 检查用户是否达到所需等级
 * @param {number} requiredLevel - 所需等级数值
 * @returns {Function} Express中间件
 */
function checkUserLevel(requiredLevel) {
  return async (req, res, next) => {
    try {
      // 确保用户已认证
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          success: false,
          error: '未授权访问，请先登录',
          code: 'UNAUTHORIZED'
        });
      }
      
      // 获取用户信息（包括角色和等级）
      const user = await db.User.findByPk(req.user.id, {
        attributes: ['id', 'role', 'level']
      });

      if (!user) {
        return res.status(403).json({
          success: false,
          error: '用户不存在',
          code: 'USER_NOT_FOUND'
        });
      }

      // 管理员始终有权限
      if (user.role === 'admin') {
        return next();
      }

      // 检查用户等级是否达到要求
      if (user.level >= requiredLevel) {
        return next();
      }
      
      // 用户等级不足
      return res.status(403).json({
        success: false,
        error: '您的会员等级不足，无法访问此功能',
        code: 'INSUFFICIENT_LEVEL',
        currentLevel: user.level,
        requiredLevel: requiredLevel
      });
    } catch (error) {
      console.error('检查用户等级时出错:', error);
      return res.status(500).json({
        success: false,
        error: '服务器内部错误',
        code: 'SERVER_ERROR'
      });
    }
  };
}

module.exports = {
  checkUserLevel
};
