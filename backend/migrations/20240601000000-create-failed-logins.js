'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('failed_logins', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      ip: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: "IP地址"
      },
      email: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: "尝试登录的邮箱"
      },
      user_agent: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: "用户代理"
      },
      reason: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: "失败原因"
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "创建时间"
      }
    });

    // 添加索引
    try {
      await queryInterface.addIndex('failed_logins', ['ip'], {
      name: 'idx_failed_logins_ip'
    });
    try {
      await queryInterface.addIndex('failed_logins', ['email'], {
      name: 'idx_failed_logins_email'
    });
    try {
      await queryInterface.addIndex('failed_logins', ['created_at'], {
      name: 'idx_failed_logins_created_at'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('failed_logins');
  }
};
