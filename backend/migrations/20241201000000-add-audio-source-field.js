"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      console.log("开始添加音频来源字段...");

      // 检查表是否存在
      const tables = await queryInterface.showAllTables();

      if (tables.includes("audios")) {
        // 检查字段是否已存在
        const tableDescription = await queryInterface.describeTable("audios");

        if (!tableDescription.audio_source) {
          // 添加 audio_source 字段
          await queryInterface.addColumn("audios", "audio_source", {
            type: Sequelize.ENUM("tts", "manual", "upload"),
            allowNull: false,
            defaultValue: "tts",
            comment: "音频来源：tts-TTS生成，manual-手动切割，upload-直接上传",
          });

          console.log("✅ 已添加 audio_source 字段到 audios 表");
        } else {
          console.log("⏭️  audio_source 字段已存在，跳过添加");
        }

        // 添加 audio_source 字段的索引
        try {
          await queryInterface.addIndex("audios", ["audio_source"], {
            name: "idx_audio_source",
          });
          console.log("✅ 已添加 audio_source 索引");
        } catch (error) {
          if (error.original && error.original.code === "ER_DUP_KEYNAME") {
            console.log("⏭️  audio_source 索引已存在，跳过添加");
          } else {
            throw error;
          }
        }

        console.log("🎉 音频来源字段迁移完成！");
      } else {
        console.log("⚠️  audios 表不存在，跳过字段添加");
      }
    } catch (error) {
      console.error("❌ 添加字段失败:", error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      console.log("开始回滚音频来源字段...");

      // 检查表是否存在
      const tables = await queryInterface.showAllTables();

      if (tables.includes("audios")) {
        // 删除索引
        try {
          await queryInterface.removeIndex("audios", "idx_audio_source");
          console.log("✅ 已删除 audio_source 索引");
        } catch (error) {
          console.log("⏭️  audio_source 索引不存在或已删除");
        }

        // 删除字段
        try {
          await queryInterface.removeColumn("audios", "audio_source");
          console.log("✅ 已删除 audio_source 字段");
        } catch (error) {
          console.log("⏭️  audio_source 字段不存在或已删除");
        }

        console.log("🎉 音频来源字段回滚完成！");
      }
    } catch (error) {
      console.error("❌ 回滚失败:", error);
      throw error;
    }
  },
};
