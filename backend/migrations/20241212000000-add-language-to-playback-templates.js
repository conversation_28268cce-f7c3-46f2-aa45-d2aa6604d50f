'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // 添加 language 字段，老数据默认设置为日语
    await queryInterface.addColumn('playback_templates', 'language', {
      type: Sequelize.STRING(10),
      allowNull: false,
      defaultValue: 'ja', // 老数据默认为日语
      comment: '模板适用的语言代码，如 ja, en, zh-CN'
    });

    // 确保现有数据都设置为日语
    await queryInterface.sequelize.query(`
      UPDATE playback_templates
      SET language = 'ja'
      WHERE language IS NULL OR language = ''
    `);

    // 添加语言字段的索引
    await queryInterface.addIndex('playback_templates', ['language'], {
      name: 'idx_playback_templates_language'
    });

    console.log('✅ 已为 playback_templates 表添加 language 字段，老数据默认设置为日语');
  },

  async down(queryInterface, Sequelize) {
    // 删除索引
    await queryInterface.removeIndex('playback_templates', 'idx_playback_templates_language');
    
    // 删除字段
    await queryInterface.removeColumn('playback_templates', 'language');
    
    console.log('✅ 已从 playback_templates 表移除 language 字段和索引');
  }
};
