'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('check_ins', {
      id: {
        type: Sequelize.STRING(21),
        primaryKey: true,
        allowNull: false,
      },
      user_id: {
        type: Sequelize.STRING(21),
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      check_in_date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
        comment: '签到日期'
      },
      rewards: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '获得的奖励（预留字段）'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 创建索引（检查是否已存在）
    try {
      try {
      await queryInterface.addIndex('check_ins', ['user_id']);
      console.log('✅ 已添加索引');
    } catch (error) {
      if (error.message.includes('Duplicate key name')) {
        console.log('ℹ️ 索引已存在，跳过添加');
      } else {
        throw error;
      }
    }
    } catch (error) {
      if (!error.message.includes('Duplicate key name')) {
        throw error;
      }
    }

    try {
      try {
      await queryInterface.addIndex('check_ins', ['check_in_date']);
      console.log('✅ 已添加索引');
    } catch (error) {
      if (error.message.includes('Duplicate key name')) {
        console.log('ℹ️ 索引已存在，跳过添加');
      } else {
        throw error;
      }
    }
    } catch (error) {
      if (!error.message.includes('Duplicate key name')) {
        throw error;
      }
    }

    try {
      try {
      await queryInterface.addIndex('check_ins', ['user_id', 'check_in_date'], {
        unique: true,
        name: 'unique_user_date'
      });
    } catch (error) {
      if (!error.message.includes('Duplicate key name')) {
        throw error;
      }
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('check_ins');
  }
};