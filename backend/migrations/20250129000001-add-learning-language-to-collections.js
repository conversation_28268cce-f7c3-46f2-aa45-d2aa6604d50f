"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      console.log("开始添加 learning_language 字段到 collections 表...");

      // 检查表是否存在
      const tables = await queryInterface.showAllTables();

      if (tables.includes("collections")) {
        // 检查字段是否已存在
        const tableDescription = await queryInterface.describeTable("collections");

        if (!tableDescription.learning_language) {
          // 添加 learning_language 字段
          await queryInterface.addColumn("collections", "learning_language", {
            type: Sequelize.STRING(10),
            allowNull: false,
            defaultValue: "ja",
            comment: "合集的目标学习语言，如ja、en、zh-CN等",
          });

          console.log("✅ 已添加 learning_language 字段到 collections 表");

          // 为现有数据设置默认语言（日语）
          await queryInterface.sequelize.query(
            "UPDATE collections SET learning_language = 'ja' WHERE learning_language IS NULL OR learning_language = ''"
          );
          console.log("✅ 已为现有合集数据设置默认语言");

          // 添加索引以提高查询性能
          try {
            await queryInterface.addIndex("collections", ["learning_language"], {
              name: "idx_collections_learning_language",
            });
            console.log("✅ 已添加 learning_language 索引");
          } catch (error) {
            if (error.message.includes("Duplicate key name")) {
              console.log("ℹ️ learning_language 索引已存在，跳过添加");
            } else {
              throw error;
            }
          }

          // 添加复合索引（语言+状态+公开状态）
          try {
            await queryInterface.addIndex("collections", ["learning_language", "status", "is_public"], {
              name: "idx_collections_lang_status_public",
            });
            console.log("✅ 已添加复合索引 (learning_language, status, is_public)");
          } catch (error) {
            if (error.message.includes("Duplicate key name")) {
              console.log("ℹ️ 复合索引已存在，跳过添加");
            } else {
              throw error;
            }
          }
        } else {
          console.log("⏭️  learning_language 字段已存在，跳过添加");
        }
      } else {
        console.log("⚠️  collections 表不存在，跳过迁移");
      }
    } catch (error) {
      console.error("❌ 添加 learning_language 字段失败:", error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      console.log("开始回滚 learning_language 字段...");

      // 检查表是否存在
      const tables = await queryInterface.showAllTables();

      if (tables.includes("collections")) {
        // 检查字段是否存在
        const tableDescription = await queryInterface.describeTable("collections");

        if (tableDescription.learning_language) {
          // 删除复合索引
          try {
            await queryInterface.removeIndex("collections", "idx_collections_lang_status_public");
            console.log("✅ 已删除复合索引");
          } catch (error) {
            console.log("ℹ️ 复合索引不存在或已删除");
          }

          // 删除单字段索引
          try {
            await queryInterface.removeIndex("collections", "idx_collections_learning_language");
            console.log("✅ 已删除 learning_language 索引");
          } catch (error) {
            console.log("ℹ️ learning_language 索引不存在或已删除");
          }

          // 删除字段
          await queryInterface.removeColumn("collections", "learning_language");
          console.log("✅ 已从 collections 表删除 learning_language 字段");
        } else {
          console.log("ℹ️ learning_language 字段不存在，跳过删除");
        }
      } else {
        console.log("⚠️  collections 表不存在，跳过回滚");
      }
    } catch (error) {
      console.error("❌ 回滚 learning_language 字段失败:", error);
      throw error;
    }
  },
};
