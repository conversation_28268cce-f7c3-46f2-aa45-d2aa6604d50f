"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      console.log("开始为 contents 表的 updated_at 字段添加索引...");

      // 检查表是否存在
      const tables = await queryInterface.showAllTables();

      if (tables.includes("contents")) {
        // 检查索引是否已存在
        try {
          await queryInterface.addIndex("contents", ["updated_at"], {
            name: "idx_updated_at",
          });
          console.log("✅ 已为 contents 表的 updated_at 字段添加索引");
        } catch (error) {
          if (error.message.includes("Duplicate key name") || error.message.includes("already exists")) {
            console.log("ℹ️ updated_at 索引已存在，跳过添加");
          } else {
            throw error;
          }
        }
      } else {
        console.log("⚠️  contents 表不存在，跳过索引添加");
      }
    } catch (error) {
      console.error("❌ 添加 updated_at 索引失败:", error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      console.log("开始删除 contents 表的 updated_at 索引...");

      // 检查表是否存在
      const tables = await queryInterface.showAllTables();

      if (tables.includes("contents")) {
        try {
          await queryInterface.removeIndex("contents", "idx_updated_at");
          console.log("✅ 已删除 contents 表的 updated_at 索引");
        } catch (error) {
          if (error.message.includes("doesn't exist") || error.message.includes("not found")) {
            console.log("ℹ️ updated_at 索引不存在，跳过删除");
          } else {
            throw error;
          }
        }
      } else {
        console.log("⚠️  contents 表不存在，跳过索引删除");
      }
    } catch (error) {
      console.error("❌ 删除 updated_at 索引失败:", error);
      throw error;
    }
  },
};
