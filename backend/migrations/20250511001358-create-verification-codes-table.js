"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();

    if (!tables.includes('verification_codes')) {
      await queryInterface.createTable("verification_codes", {
      id: {
        type: Sequelize.STRING(21),
        primaryKey: true,
        allowNull: false,
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      code: {
        type: Sequelize.STRING(6),
        allowNull: false,
      },
      type: {
        type: Sequelize.ENUM("login", "register", "reset_password"),
        defaultValue: "login",
      },
      expires_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      used: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      });
      console.log('✅ 已创建 verification_codes 表');
    } else {
      console.log('ℹ️ verification_codes 表已存在，跳过创建');
    }

    // 添加索引（安全方式）
    try {
      await queryInterface.addIndex("verification_codes", ["email"]);
      console.log('✅ 已添加 email 索引');
    } catch (error) {
      if (error.message.includes('Duplicate key name')) {
        console.log('ℹ️ email 索引已存在，跳过添加');
      } else {
        throw error;
      }
    }

    try {
      await queryInterface.addIndex("verification_codes", ["expires_at"]);
      console.log('✅ 已添加 expires_at 索引');
    } catch (error) {
      if (error.message.includes('Duplicate key name')) {
        console.log('ℹ️ expires_at 索引已存在，跳过添加');
      } else {
        throw error;
      }
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("verification_codes");
  },
};
