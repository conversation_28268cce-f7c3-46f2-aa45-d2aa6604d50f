"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();

    // 如果表已存在，则跳过创建
    if (!tables.includes("feature_permissions")) {
      // 创建功能权限表
      await queryInterface.createTable("feature_permissions", {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        feature_key: {
          type: Sequelize.STRING(50),
          allowNull: false,
          comment: "功能标识符，如 content_management, editor_access",
        },
        user_id: {
          type: Sequelize.STRING(50),
          allowNull: false,
          comment: "用户ID",
        },
        created_at: {
          allowNull: false,
          type: Sequelize.DATE,
        },
        updated_at: {
          allowNull: false,
          type: Sequelize.DATE,
        },
      });

      // 添加唯一索引，确保一个用户对一个功能只有一条记录（安全方式）
      try {
        await queryInterface.addIndex(
          "feature_permissions",
          ["feature_key", "user_id"],
          {
            unique: true,
            name: "feature_permissions_feature_user_unique",
          }
        );
        console.log('✅ 已添加 feature_permissions 唯一索引');
      } catch (error) {
        if (error.message.includes('Duplicate key name')) {
          console.log('ℹ️ feature_permissions 唯一索引已存在，跳过添加');
        } else {
          throw error;
        }
      }
    }

    // 如果表已存在，则跳过创建
    if (!tables.includes("feature_flags")) {
      // 创建全局功能开关表
      await queryInterface.createTable("feature_flags", {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        feature_key: {
          type: Sequelize.STRING(50),
          allowNull: false,
          unique: true,
          comment: "功能标识符",
        },
        is_enabled: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
          comment: "是否全局启用",
        },
        description: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: "功能描述",
        },
        created_at: {
          allowNull: false,
          type: Sequelize.DATE,
        },
        updated_at: {
          allowNull: false,
          type: Sequelize.DATE,
        },
      });

      // 插入默认功能标志
      await queryInterface.bulkInsert("feature_flags", [
        {
          feature_key: "content_management",
          is_enabled: false,
          description: "内容管理功能",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          feature_key: "editor_access",
          is_enabled: false,
          description: "编辑器访问权限",
          created_at: new Date(),
          updated_at: new Date(),
        },
      ]);
    } else {
      // 如果表已存在，检查是否需要插入默认功能标志
      const flags = await queryInterface.sequelize.query(
        "SELECT feature_key FROM feature_flags WHERE feature_key IN ('content_management', 'editor_access')",
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      const existingKeys = flags.map((flag) => flag.feature_key);
      const flagsToInsert = [];

      if (!existingKeys.includes("content_management")) {
        flagsToInsert.push({
          feature_key: "content_management",
          is_enabled: false,
          description: "内容管理功能",
          created_at: new Date(),
          updated_at: new Date(),
        });
      }

      if (!existingKeys.includes("editor_access")) {
        flagsToInsert.push({
          feature_key: "editor_access",
          is_enabled: false,
          description: "编辑器访问权限",
          created_at: new Date(),
          updated_at: new Date(),
        });
      }

      if (flagsToInsert.length > 0) {
        await queryInterface.bulkInsert("feature_flags", flagsToInsert);
      }
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("feature_permissions");
    await queryInterface.dropTable("feature_flags");
  },
};
