"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();

    if (!tables.includes('contents')) {
      await queryInterface.createTable("contents", {
      id: {
        type: Sequelize.STRING(50),
        allowNull: false,
        primaryKey: true,
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      user_id: {
        type: Sequelize.STRING(50),
        allowNull: false, // 设置为不可为空
        references: {
          model: "users",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      status: {
        type: Sequelize.STRING(20),
        allowNull: false,
        defaultValue: "draft",
      },
      config_json: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      thumbnail_url: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      tags: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      is_public: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      });
      console.log('✅ 已创建 contents 表');

      // 添加索引（安全方式）
      const indexes = [
        { columns: ["user_id"], name: "user_id" },
        { columns: ["status"], name: "status" },
        { columns: ["is_public"], name: "is_public" },
        { columns: ["created_at"], name: "created_at" }
      ];

      for (const index of indexes) {
        try {
          await queryInterface.addIndex("contents", index.columns);
          console.log(`✅ 已添加 ${index.name} 索引`);
        } catch (error) {
          if (error.message.includes('Duplicate key name')) {
            console.log(`ℹ️ ${index.name} 索引已存在，跳过添加`);
          } else {
            throw error;
          }
        }
      }
    } else {
      console.log('ℹ️ contents 表已存在，跳过创建');
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("contents");
  },
};
