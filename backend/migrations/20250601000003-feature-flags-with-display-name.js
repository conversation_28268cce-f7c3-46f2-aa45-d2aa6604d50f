'use strict';

/**
 * 添加显示名称字段到功能特性表并初始化数据
 */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // 1. 添加 display_name 字段（检查是否已存在）
      const tableDescription = await queryInterface.describeTable('feature_flags');

      if (!tableDescription.display_name) {
        await queryInterface.addColumn(
          'feature_flags',
          'display_name',
          {
            type: Sequelize.STRING(100),
            allowNull: true,
            comment: '功能显示名称'
          },
          { transaction }
        );
        console.log('✅ 已添加 display_name 字段到 feature_flags 表');
      } else {
        console.log('ℹ️ display_name 字段已存在，跳过添加');
      }
      
      // 2. 清空现有数据
      await queryInterface.bulkDelete('feature_flags', null, { transaction });
      
      // 3. 添加初始功能特性数据
      const now = new Date();
      await queryInterface.bulkInsert('feature_flags', [
        {
          feature_key: 'player_access',
          display_name: '视频播放',
          is_enabled: true,
          description: '访问视频播放页面',
          created_at: now,
          updated_at: now
        },
        {
          feature_key: 'content_management',
          display_name: '内容管理',
          is_enabled: false,
          description: '管理自己的内容',
          created_at: now,
          updated_at: now
        },
        {
          feature_key: 'editor_access',
          display_name: '编辑器访问',
          is_enabled: false,
          description: '访问内容编辑器',
          created_at: now,
          updated_at: now
        },
        {
          feature_key: 'video_export',
          display_name: '视频导出',
          is_enabled: false,
          description: '导出视频文件',
          created_at: now,
          updated_at: now
        }
      ], { transaction });
      
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // 1. 清空数据
      await queryInterface.bulkDelete('feature_flags', null, { transaction });
      
      // 2. 移除 display_name 字段
      await queryInterface.removeColumn('feature_flags', 'display_name', { transaction });
      
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
