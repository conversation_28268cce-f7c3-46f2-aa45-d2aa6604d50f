"use strict";

/**
 * 初始化功能权限数据
 * 添加基本功能标志和等级权限
 */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 1. 清空现有数据
      await queryInterface.bulkDelete("feature_flags", null, { transaction });
      await queryInterface.bulkDelete("level_permissions", null, {
        transaction,
      });

      // 2. 添加基本功能标志
      const now = new Date();
      const features = [
        {
          feature_key: "content_creation",
          display_name: "内容创建与管理",
          description: "创建和管理自己的内容，包括使用编辑器和内容管理页面",
          is_enabled: false,
          created_at: now,
          updated_at: now,
        },
        {
          feature_key: "video_export",
          display_name: "视频导出",
          description: "导出视频文件",
          is_enabled: false,
          created_at: now,
          updated_at: now,
        },
      ];

      await queryInterface.bulkInsert("feature_flags", features, {
        transaction,
      });

      // 3. 添加等级权限
      const levelPermissions = [
        // 等级1 - 基础会员
        {
          level: 1,
          feature_key: "content_creation",
          created_at: now,
          updated_at: now,
        },

        // 等级2 - 高级会员
        {
          level: 2,
          feature_key: "content_creation",
          created_at: now,
          updated_at: now,
        },
        {
          level: 2,
          feature_key: "video_export",
          created_at: now,
          updated_at: now,
        },

        // 等级3 - 专业会员
        {
          level: 3,
          feature_key: "content_creation",
          created_at: now,
          updated_at: now,
        },
        {
          level: 3,
          feature_key: "video_export",
          created_at: now,
          updated_at: now,
        },
      ];

      await queryInterface.bulkInsert("level_permissions", levelPermissions, {
        transaction,
      });

      // 提交事务
      await transaction.commit();
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 清空数据
      await queryInterface.bulkDelete("level_permissions", null, {
        transaction,
      });
      await queryInterface.bulkDelete("feature_flags", null, { transaction });

      // 提交事务
      await transaction.commit();
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  },
};
