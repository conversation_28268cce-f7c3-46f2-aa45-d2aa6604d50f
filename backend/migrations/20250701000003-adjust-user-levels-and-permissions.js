"use strict";

/**
 * 调整用户等级和权限
 * 1. 删除高级会员等级（level 2）
 * 2. 允许基础会员（level 1）使用视频导出功能，但限制为每天1次
 * 3. 重新设计功能使用限制
 */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      const now = new Date();

      // 1. 获取所有level 2的用户，将其升级到level 3
      await queryInterface.sequelize.query(
        `UPDATE users SET level = 3 WHERE level = 2`,
        { transaction }
      );

      // 2. 删除level 2的等级
      await queryInterface.bulkDelete(
        "user_levels",
        { level: 2 },
        { transaction }
      );

      // 3. 删除level 2的权限
      await queryInterface.bulkDelete(
        "level_permissions",
        { level: 2 },
        { transaction }
      );

      // 4. 删除level 2的使用限制
      await queryInterface.bulkDelete(
        "feature_usage_limits",
        { level: 2 },
        { transaction }
      );

      // 5. 为level 1添加视频导出权限
      await queryInterface.bulkInsert(
        "level_permissions",
        [
          {
            level: 1,
            feature_key: "video_export",
            created_at: now,
            updated_at: now,
          },
        ],
        { transaction }
      );

      // 6. 更新level 1的视频导出使用限制
      // 先删除现有的限制（如果有）
      await queryInterface.bulkDelete(
        "feature_usage_limits",
        { level: 1, feature_key: "video_export" },
        { transaction }
      );

      // 添加新的限制
      await queryInterface.bulkInsert(
        "feature_usage_limits",
        [
          {
            level: 1,
            feature_key: "video_export",
            daily_limit: 1,
            monthly_limit: 10,
            created_at: now,
            updated_at: now,
          },
        ],
        { transaction }
      );

      // 7. 更新level 3的视频导出使用限制（专业会员无限制）
      await queryInterface.bulkDelete(
        "feature_usage_limits",
        { level: 3, feature_key: "video_export" },
        { transaction }
      );

      // 专业会员无限制，所以不添加限制记录

      // 提交事务
      await transaction.commit();
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      const now = new Date();

      // 1. 恢复高级会员等级（level 2）
      await queryInterface.bulkInsert(
        "user_levels",
        [
          {
            level: 2,
            name: "高级会员",
            description: "解锁高级功能，更高使用限制",
            is_default: false,
            created_at: now,
            updated_at: now,
          },
        ],
        { transaction }
      );

      // 2. 恢复level 2的权限
      await queryInterface.bulkInsert(
        "level_permissions",
        [
          {
            level: 2,
            feature_key: "player_access",
            created_at: now,
            updated_at: now,
          },
          {
            level: 2,
            feature_key: "content_creation",
            created_at: now,
            updated_at: now,
          },
          {
            level: 2,
            feature_key: "video_export",
            created_at: now,
            updated_at: now,
          },
        ],
        { transaction }
      );

      // 3. 恢复level 2的使用限制
      await queryInterface.bulkInsert(
        "feature_usage_limits",
        [
          {
            level: 2,
            feature_key: "content_creation",
            daily_limit: 30,
            monthly_limit: 300,
            created_at: now,
            updated_at: now,
          },
          {
            level: 2,
            feature_key: "video_export",
            daily_limit: 10,
            monthly_limit: 100,
            created_at: now,
            updated_at: now,
          },
        ],
        { transaction }
      );

      // 4. 删除level 1的视频导出权限
      await queryInterface.bulkDelete(
        "level_permissions",
        { level: 1, feature_key: "video_export" },
        { transaction }
      );

      // 5. 删除level 1的视频导出使用限制
      await queryInterface.bulkDelete(
        "feature_usage_limits",
        { level: 1, feature_key: "video_export" },
        { transaction }
      );

      // 提交事务
      await transaction.commit();
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  },
};
