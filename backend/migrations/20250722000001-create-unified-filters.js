'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 创建统一过滤器表
    await queryInterface.createTable('filters', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '过滤器名称'
      },
      key: {
        type: Sequelize.STRING(100),
        allowNull: false,
        unique: true,
        comment: '过滤器唯一标识符'
      },
      type: {
        type: Sequelize.ENUM('language_level', 'content_type', 'difficulty', 'topic', 'skill'),
        allowNull: false,
        comment: '过滤器类型：语言等级、内容类型、难度、主题、技能'
      },
      language: {
        type: Sequelize.STRING(10),
        allowNull: true,
        comment: '语言代码，null表示通用过滤器'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '过滤器描述'
      },
      color: {
        type: Sequelize.STRING(7),
        allowNull: true,
        comment: '过滤器颜色（十六进制）'
      },
      icon: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '过滤器图标'
      },
      sort_order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '排序顺序'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: '是否激活'
      },
      is_system: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否为系统过滤器（不可删除）'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // 创建内容过滤器关联表
    await queryInterface.createTable('content_filters', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      content_id: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '内容ID'
      },
      filter_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'filters',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: '过滤器ID'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // 添加索引
    await queryInterface.addIndex('filters', ['type']);
    await queryInterface.addIndex('filters', ['language']);
    await queryInterface.addIndex('filters', ['type', 'language']);
    await queryInterface.addIndex('filters', ['is_active']);
    await queryInterface.addIndex('content_filters', ['content_id']);
    await queryInterface.addIndex('content_filters', ['filter_id']);
    await queryInterface.addIndex('content_filters', ['content_id', 'filter_id'], {
      unique: true
    });

    // 插入系统过滤器数据
    const now = new Date();
    
    // 语言等级过滤器
    const languageLevelFilters = [
      // 日语等级
      { name: 'N5', key: 'ja_n5', type: 'language_level', language: 'ja', description: 'N5 - 初级', color: '#4CAF50', icon: '🟢', sort_order: 1 },
      { name: 'N4', key: 'ja_n4', type: 'language_level', language: 'ja', description: 'N4 - 初中级', color: '#8BC34A', icon: '🟡', sort_order: 2 },
      { name: 'N3', key: 'ja_n3', type: 'language_level', language: 'ja', description: 'N3 - 中级', color: '#FFC107', icon: '🟠', sort_order: 3 },
      { name: 'N2', key: 'ja_n2', type: 'language_level', language: 'ja', description: 'N2 - 中高级', color: '#FF9800', icon: '🔴', sort_order: 4 },
      { name: 'N1', key: 'ja_n1', type: 'language_level', language: 'ja', description: 'N1 - 高级', color: '#F44336', icon: '🟣', sort_order: 5 },
      
      // 英语等级
      { name: 'A1', key: 'en_a1', type: 'language_level', language: 'en', description: 'A1 - Beginner', color: '#4CAF50', icon: '🟢', sort_order: 1 },
      { name: 'A2', key: 'en_a2', type: 'language_level', language: 'en', description: 'A2 - Elementary', color: '#8BC34A', icon: '🟡', sort_order: 2 },
      { name: 'B1', key: 'en_b1', type: 'language_level', language: 'en', description: 'B1 - Intermediate', color: '#FFC107', icon: '🟠', sort_order: 3 },
      { name: 'B2', key: 'en_b2', type: 'language_level', language: 'en', description: 'B2 - Upper Intermediate', color: '#FF9800', icon: '🔴', sort_order: 4 },
      { name: 'C1', key: 'en_c1', type: 'language_level', language: 'en', description: 'C1 - Advanced', color: '#F44336', icon: '🟣', sort_order: 5 },
      { name: 'C2', key: 'en_c2', type: 'language_level', language: 'en', description: 'C2 - Proficient', color: '#9C27B0', icon: '⚫', sort_order: 6 },
      
      // 中文等级
      { name: 'HSK1', key: 'zh_hsk1', type: 'language_level', language: 'zh-CN', description: 'HSK1 - 初级', color: '#4CAF50', icon: '🟢', sort_order: 1 },
      { name: 'HSK2', key: 'zh_hsk2', type: 'language_level', language: 'zh-CN', description: 'HSK2 - 初中级', color: '#8BC34A', icon: '🟡', sort_order: 2 },
      { name: 'HSK3', key: 'zh_hsk3', type: 'language_level', language: 'zh-CN', description: 'HSK3 - 中级', color: '#FFC107', icon: '🟠', sort_order: 3 },
      { name: 'HSK4', key: 'zh_hsk4', type: 'language_level', language: 'zh-CN', description: 'HSK4 - 中高级', color: '#FF9800', icon: '🔴', sort_order: 4 },
      { name: 'HSK5', key: 'zh_hsk5', type: 'language_level', language: 'zh-CN', description: 'HSK5 - 高级', color: '#F44336', icon: '🟣', sort_order: 5 },
      { name: 'HSK6', key: 'zh_hsk6', type: 'language_level', language: 'zh-CN', description: 'HSK6 - 精通', color: '#9C27B0', icon: '⚫', sort_order: 6 }
    ];

    // 内容类型过滤器
    const contentTypeFilters = [
      { name: '对话', key: 'dialogue', type: 'content_type', language: null, description: '对话形式的内容', color: '#2196F3', icon: '💬', sort_order: 1 },
      { name: '独白', key: 'monologue', type: 'content_type', language: null, description: '独白形式的内容', color: '#3F51B5', icon: '🎤', sort_order: 2 },
      { name: '新闻', key: 'news', type: 'content_type', language: null, description: '新闻类内容', color: '#FF5722', icon: '📰', sort_order: 3 },
      { name: '故事', key: 'story', type: 'content_type', language: null, description: '故事类内容', color: '#9C27B0', icon: '📚', sort_order: 4 },
      { name: '教学', key: 'educational', type: 'content_type', language: null, description: '教学类内容', color: '#607D8B', icon: '🎓', sort_order: 5 }
    ];

    // 难度过滤器
    const difficultyFilters = [
      { name: '简单', key: 'easy', type: 'difficulty', language: null, description: '简单难度', color: '#4CAF50', icon: '⭐', sort_order: 1 },
      { name: '中等', key: 'medium', type: 'difficulty', language: null, description: '中等难度', color: '#FFC107', icon: '⭐⭐', sort_order: 2 },
      { name: '困难', key: 'hard', type: 'difficulty', language: null, description: '困难难度', color: '#F44336', icon: '⭐⭐⭐', sort_order: 3 }
    ];

    // 合并所有过滤器
    const allFilters = [...languageLevelFilters, ...contentTypeFilters, ...difficultyFilters].map(filter => ({
      ...filter,
      is_system: true,
      is_active: true,
      created_at: now,
      updated_at: now
    }));

    await queryInterface.bulkInsert('filters', allFilters);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('content_filters');
    await queryInterface.dropTable('filters');
  }
};
