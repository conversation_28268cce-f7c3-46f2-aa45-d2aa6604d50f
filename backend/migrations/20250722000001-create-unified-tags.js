'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 创建统一标签表
    await queryInterface.createTable('tags', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '标签名称'
      },
      key: {
        type: Sequelize.STRING(100),
        allowNull: false,
        unique: true,
        comment: '标签唯一标识符'
      },
      type: {
        type: Sequelize.ENUM('language_level', 'content_type', 'difficulty', 'topic', 'skill'),
        allowNull: false,
        comment: '标签类型：语言等级、内容类型、难度、主题、技能'
      },
      language: {
        type: Sequelize.STRING(10),
        allowNull: true,
        comment: '语言代码，null表示通用标签'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '标签描述'
      },
      color: {
        type: Sequelize.STRING(7),
        allowNull: true,
        comment: '标签颜色（十六进制）'
      },
      sort_order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '排序顺序'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: '是否激活'
      },
      is_system: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否为系统标签（不可删除）'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // 创建内容标签关联表
    await queryInterface.createTable('content_tags', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      content_id: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '内容ID'
      },
      tag_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'tags',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: '标签ID'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // 添加索引
    await queryInterface.addIndex('tags', ['type']);
    await queryInterface.addIndex('tags', ['language']);
    await queryInterface.addIndex('tags', ['type', 'language']);
    await queryInterface.addIndex('tags', ['is_active']);
    await queryInterface.addIndex('content_tags', ['content_id']);
    await queryInterface.addIndex('content_tags', ['tag_id']);
    await queryInterface.addIndex('content_tags', ['content_id', 'tag_id'], {
      unique: true
    });

    // 插入系统标签数据
    const now = new Date();
    
    // 语言等级标签
    const languageLevelTags = [
      // 日语等级
      { name: 'N5', key: 'ja_n5', type: 'language_level', language: 'ja', description: 'N5 - 初级', color: '#4CAF50', sort_order: 1 },
      { name: 'N4', key: 'ja_n4', type: 'language_level', language: 'ja', description: 'N4 - 初中级', color: '#8BC34A', sort_order: 2 },
      { name: 'N3', key: 'ja_n3', type: 'language_level', language: 'ja', description: 'N3 - 中级', color: '#FFC107', sort_order: 3 },
      { name: 'N2', key: 'ja_n2', type: 'language_level', language: 'ja', description: 'N2 - 中高级', color: '#FF9800', sort_order: 4 },
      { name: 'N1', key: 'ja_n1', type: 'language_level', language: 'ja', description: 'N1 - 高级', color: '#F44336', sort_order: 5 },
      
      // 英语等级
      { name: 'A1', key: 'en_a1', type: 'language_level', language: 'en', description: 'A1 - Beginner', color: '#4CAF50', sort_order: 1 },
      { name: 'A2', key: 'en_a2', type: 'language_level', language: 'en', description: 'A2 - Elementary', color: '#8BC34A', sort_order: 2 },
      { name: 'B1', key: 'en_b1', type: 'language_level', language: 'en', description: 'B1 - Intermediate', color: '#FFC107', sort_order: 3 },
      { name: 'B2', key: 'en_b2', type: 'language_level', language: 'en', description: 'B2 - Upper Intermediate', color: '#FF9800', sort_order: 4 },
      { name: 'C1', key: 'en_c1', type: 'language_level', language: 'en', description: 'C1 - Advanced', color: '#F44336', sort_order: 5 },
      { name: 'C2', key: 'en_c2', type: 'language_level', language: 'en', description: 'C2 - Proficient', color: '#9C27B0', sort_order: 6 },
      
      // 中文等级
      { name: 'HSK1', key: 'zh_hsk1', type: 'language_level', language: 'zh-CN', description: 'HSK1 - 初级', color: '#4CAF50', sort_order: 1 },
      { name: 'HSK2', key: 'zh_hsk2', type: 'language_level', language: 'zh-CN', description: 'HSK2 - 初中级', color: '#8BC34A', sort_order: 2 },
      { name: 'HSK3', key: 'zh_hsk3', type: 'language_level', language: 'zh-CN', description: 'HSK3 - 中级', color: '#FFC107', sort_order: 3 },
      { name: 'HSK4', key: 'zh_hsk4', type: 'language_level', language: 'zh-CN', description: 'HSK4 - 中高级', color: '#FF9800', sort_order: 4 },
      { name: 'HSK5', key: 'zh_hsk5', type: 'language_level', language: 'zh-CN', description: 'HSK5 - 高级', color: '#F44336', sort_order: 5 },
      { name: 'HSK6', key: 'zh_hsk6', type: 'language_level', language: 'zh-CN', description: 'HSK6 - 精通', color: '#9C27B0', sort_order: 6 }
    ];

    // 内容类型标签
    const contentTypeTags = [
      { name: '对话', key: 'dialogue', type: 'content_type', language: null, description: '对话形式的内容', color: '#2196F3', sort_order: 1 },
      { name: '独白', key: 'monologue', type: 'content_type', language: null, description: '独白形式的内容', color: '#3F51B5', sort_order: 2 },
      { name: '新闻', key: 'news', type: 'content_type', language: null, description: '新闻类内容', color: '#FF5722', sort_order: 3 },
      { name: '故事', key: 'story', type: 'content_type', language: null, description: '故事类内容', color: '#9C27B0', sort_order: 4 },
      { name: '教学', key: 'educational', type: 'content_type', language: null, description: '教学类内容', color: '#607D8B', sort_order: 5 }
    ];

    // 难度标签
    const difficultyTags = [
      { name: '简单', key: 'easy', type: 'difficulty', language: null, description: '简单难度', color: '#4CAF50', sort_order: 1 },
      { name: '中等', key: 'medium', type: 'difficulty', language: null, description: '中等难度', color: '#FFC107', sort_order: 2 },
      { name: '困难', key: 'hard', type: 'difficulty', language: null, description: '困难难度', color: '#F44336', sort_order: 3 }
    ];

    // 合并所有标签
    const allTags = [...languageLevelTags, ...contentTypeTags, ...difficultyTags].map(tag => ({
      ...tag,
      is_system: true,
      is_active: true,
      created_at: now,
      updated_at: now
    }));

    await queryInterface.bulkInsert('tags', allTags);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('content_tags');
    await queryInterface.dropTable('tags');
  }
};
