'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 1. 先删除难度过滤器
    await queryInterface.bulkDelete('filters', {
      type: 'difficulty'
    });

    // 2. 更新过滤器类型枚举，添加主题和教材维度，移除难度
    await queryInterface.changeColumn('filters', 'type', {
      type: Sequelize.ENUM('language_level', 'content_type', 'topic', 'material', 'skill'),
      allowNull: false,
      comment: '过滤器类型：语言等级、内容类型、主题、教材、技能'
    });

    // 3. 插入主题过滤器
    const now = new Date();
    const topicFilters = [
      { name: '日常生活', key: 'daily_life', type: 'topic', language: null, description: '日常生活相关话题', color: '#4CAF50', icon: '🏠', sort_order: 1 },
      { name: '工作职场', key: 'work', type: 'topic', language: null, description: '工作和职场相关话题', color: '#2196F3', icon: '💼', sort_order: 2 },
      { name: '学习教育', key: 'education', type: 'topic', language: null, description: '学习和教育相关话题', color: '#FF9800', icon: '📚', sort_order: 3 },
      { name: '旅行出行', key: 'travel', type: 'topic', language: null, description: '旅行和出行相关话题', color: '#9C27B0', icon: '✈️', sort_order: 4 },
      { name: '美食餐饮', key: 'food', type: 'topic', language: null, description: '美食和餐饮相关话题', color: '#FF5722', icon: '🍽️', sort_order: 5 },
      { name: '健康医疗', key: 'health', type: 'topic', language: null, description: '健康和医疗相关话题', color: '#607D8B', icon: '🏥', sort_order: 6 },
      { name: '娱乐休闲', key: 'entertainment', type: 'topic', language: null, description: '娱乐和休闲相关话题', color: '#E91E63', icon: '🎬', sort_order: 7 },
      { name: '科技数码', key: 'technology', type: 'topic', language: null, description: '科技和数码相关话题', color: '#00BCD4', icon: '💻', sort_order: 8 },
      { name: '文化艺术', key: 'culture', type: 'topic', language: null, description: '文化和艺术相关话题', color: '#795548', icon: '🎨', sort_order: 9 },
      { name: '体育运动', key: 'sports', type: 'topic', language: null, description: '体育和运动相关话题', color: '#8BC34A', icon: '⚽', sort_order: 10 }
    ];

    // 4. 插入教材维度过滤器（仅日语标准日本语系列）
    const materialFilters = [
      { name: '标准日本语初级上', key: 'standard_japanese_beginner_1', type: 'material', language: 'ja', description: '《标准日本语》初级上册', color: '#4CAF50', icon: '📗', sort_order: 1 },
      { name: '标准日本语初级下', key: 'standard_japanese_beginner_2', type: 'material', language: 'ja', description: '《标准日本语》初级下册', color: '#8BC34A', icon: '📗', sort_order: 2 },
      { name: '标准日本语中级上', key: 'standard_japanese_intermediate_1', type: 'material', language: 'ja', description: '《标准日本语》中级上册', color: '#FFC107', icon: '📙', sort_order: 3 },
      { name: '标准日本语中级下', key: 'standard_japanese_intermediate_2', type: 'material', language: 'ja', description: '《标准日本语》中级下册', color: '#FF9800', icon: '📙', sort_order: 4 },
      { name: '标准日本语高级上', key: 'standard_japanese_advanced_1', type: 'material', language: 'ja', description: '《标准日本语》高级上册', color: '#F44336', icon: '📕', sort_order: 5 },
      { name: '标准日本语高级下', key: 'standard_japanese_advanced_2', type: 'material', language: 'ja', description: '《标准日本语》高级下册', color: '#9C27B0', icon: '📕', sort_order: 6 }
    ];

    // 合并所有新过滤器
    const allNewFilters = [...topicFilters, ...materialFilters].map(filter => ({
      ...filter,
      is_system: true,
      is_active: true,
      created_at: now,
      updated_at: now
    }));

    await queryInterface.bulkInsert('filters', allNewFilters);
  },

  async down(queryInterface, Sequelize) {
    // 删除主题和教材过滤器
    await queryInterface.bulkDelete('filters', {
      type: ['topic', 'material']
    });

    // 恢复难度过滤器
    const now = new Date();
    const difficultyFilters = [
      { name: '简单', key: 'easy', type: 'difficulty', language: null, description: '简单难度', color: '#4CAF50', icon: '⭐', sort_order: 1 },
      { name: '中等', key: 'medium', type: 'difficulty', language: null, description: '中等难度', color: '#FFC107', icon: '⭐⭐', sort_order: 2 },
      { name: '困难', key: 'hard', type: 'difficulty', language: null, description: '困难难度', color: '#F44336', icon: '⭐⭐⭐', sort_order: 3 }
    ].map(filter => ({
      ...filter,
      is_system: true,
      is_active: true,
      created_at: now,
      updated_at: now
    }));

    await queryInterface.bulkInsert('filters', difficultyFilters);

    // 恢复原来的枚举类型
    await queryInterface.changeColumn('filters', 'type', {
      type: Sequelize.ENUM('language_level', 'content_type', 'difficulty', 'topic', 'skill'),
      allowNull: false,
      comment: '过滤器类型：语言等级、内容类型、难度、主题、技能'
    });
  }
};
