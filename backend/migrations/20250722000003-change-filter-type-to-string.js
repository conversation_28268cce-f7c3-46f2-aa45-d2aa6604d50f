'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 1. 先删除难度过滤器
    await queryInterface.bulkDelete('filters', {
      type: 'difficulty'
    });

    // 2. 将 type 字段改为字符串类型，支持动态添加新类型
    await queryInterface.changeColumn('filters', 'type', {
      type: Sequelize.STRING(50),
      allowNull: false,
      comment: '过滤器类型：language_level(语言等级)、content_type(内容类型)、topic(主题)、material(教材)、skill(技能)等，支持自定义'
    });

    // 3. 检查并插入主题过滤器（避免重复）
    const now = new Date();
    const topicFilters = [
      { name: '日常生活', key: 'daily_life', type: 'topic', language: null, description: '日常生活相关话题', color: '#4CAF50', icon: '🏠', sort_order: 1 },
      { name: '工作职场', key: 'work', type: 'topic', language: null, description: '工作和职场相关话题', color: '#2196F3', icon: '💼', sort_order: 2 },
      { name: '学习教育', key: 'education', type: 'topic', language: null, description: '学习和教育相关话题', color: '#FF9800', icon: '📚', sort_order: 3 },
      { name: '旅行出行', key: 'travel', type: 'topic', language: null, description: '旅行和出行相关话题', color: '#9C27B0', icon: '✈️', sort_order: 4 },
      { name: '美食餐饮', key: 'food', type: 'topic', language: null, description: '美食和餐饮相关话题', color: '#FF5722', icon: '🍽️', sort_order: 5 },
      { name: '健康医疗', key: 'health', type: 'topic', language: null, description: '健康和医疗相关话题', color: '#607D8B', icon: '🏥', sort_order: 6 },
      { name: '娱乐休闲', key: 'entertainment', type: 'topic', language: null, description: '娱乐和休闲相关话题', color: '#E91E63', icon: '🎬', sort_order: 7 },
      { name: '科技数码', key: 'technology', type: 'topic', language: null, description: '科技和数码相关话题', color: '#00BCD4', icon: '💻', sort_order: 8 },
      { name: '文化艺术', key: 'culture', type: 'topic', language: null, description: '文化和艺术相关话题', color: '#795548', icon: '🎨', sort_order: 9 },
      { name: '体育运动', key: 'sports', type: 'topic', language: null, description: '体育和运动相关话题', color: '#8BC34A', icon: '⚽', sort_order: 10 }
    ];

    // 4. 插入教材维度过滤器（仅日语标准日本语系列）
    const materialFilters = [
      { name: '标准日本语初级上', key: 'standard_japanese_beginner_1', type: 'material', language: 'ja', description: '《标准日本语》初级上册', color: '#4CAF50', icon: '📗', sort_order: 1 },
      { name: '标准日本语初级下', key: 'standard_japanese_beginner_2', type: 'material', language: 'ja', description: '《标准日本语》初级下册', color: '#8BC34A', icon: '📗', sort_order: 2 },
      { name: '标准日本语中级上', key: 'standard_japanese_intermediate_1', type: 'material', language: 'ja', description: '《标准日本语》中级上册', color: '#FFC107', icon: '📙', sort_order: 3 },
      { name: '标准日本语中级下', key: 'standard_japanese_intermediate_2', type: 'material', language: 'ja', description: '《标准日本语》中级下册', color: '#FF9800', icon: '📙', sort_order: 4 },
      { name: '标准日本语高级上', key: 'standard_japanese_advanced_1', type: 'material', language: 'ja', description: '《标准日本语》高级上册', color: '#F44336', icon: '📕', sort_order: 5 },
      { name: '标准日本语高级下', key: 'standard_japanese_advanced_2', type: 'material', language: 'ja', description: '《标准日本语》高级下册', color: '#9C27B0', icon: '📕', sort_order: 6 }
    ];

    // 检查是否已存在主题过滤器，避免重复插入
    const existingTopicCount = await queryInterface.sequelize.query(
      "SELECT COUNT(*) as count FROM filters WHERE type = 'topic'",
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    if (existingTopicCount[0].count === 0) {
      // 合并所有新过滤器
      const allNewFilters = [...topicFilters, ...materialFilters].map(filter => ({
        ...filter,
        is_system: true,
        is_active: true,
        created_at: now,
        updated_at: now
      }));

      await queryInterface.bulkInsert('filters', allNewFilters);
    }

    // 5. 创建过滤器类型表，用于管理过滤器类型的元数据
    await queryInterface.createTable('filter_types', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      type: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true,
        comment: '过滤器类型标识'
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '过滤器类型名称'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '过滤器类型描述'
      },
      icon: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '过滤器类型图标'
      },
      sort_order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '排序顺序'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: '是否激活'
      },
      is_system: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否为系统类型（不可删除）'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // 6. 插入默认的过滤器类型
    const filterTypes = [
      { type: 'language_level', name: '语言等级', description: '按语言学习等级分类', icon: '🎯', sort_order: 1, is_system: true },
      { type: 'content_type', name: '内容类型', description: '按内容形式分类', icon: '📝', sort_order: 2, is_system: true },
      { type: 'topic', name: '主题', description: '按话题内容分类', icon: '💭', sort_order: 3, is_system: true },
      { type: 'material', name: '教材', description: '按教材来源分类', icon: '📚', sort_order: 4, is_system: true },
      { type: 'skill', name: '技能', description: '按语言技能分类', icon: '🎪', sort_order: 5, is_system: true }
    ].map(type => ({
      ...type,
      is_active: true,
      created_at: now,
      updated_at: now
    }));

    await queryInterface.bulkInsert('filter_types', filterTypes);

    // 7. 添加索引
    await queryInterface.addIndex('filter_types', ['type']);
    await queryInterface.addIndex('filter_types', ['is_active']);
  },

  async down(queryInterface, Sequelize) {
    // 删除过滤器类型表
    await queryInterface.dropTable('filter_types');

    // 删除主题和教材过滤器
    await queryInterface.bulkDelete('filters', {
      type: ['topic', 'material']
    });

    // 恢复枚举类型
    await queryInterface.changeColumn('filters', 'type', {
      type: Sequelize.ENUM('language_level', 'content_type', 'difficulty', 'topic', 'skill'),
      allowNull: false,
      comment: '过滤器类型：语言等级、内容类型、难度、主题、技能'
    });

    // 恢复难度过滤器
    const now = new Date();
    const difficultyFilters = [
      { name: '简单', key: 'easy', type: 'difficulty', language: null, description: '简单难度', color: '#4CAF50', icon: '⭐', sort_order: 1 },
      { name: '中等', key: 'medium', type: 'difficulty', language: null, description: '中等难度', color: '#FFC107', icon: '⭐⭐', sort_order: 2 },
      { name: '困难', key: 'hard', type: 'difficulty', language: null, description: '困难难度', color: '#F44336', icon: '⭐⭐⭐', sort_order: 3 }
    ].map(filter => ({
      ...filter,
      is_system: true,
      is_active: true,
      created_at: now,
      updated_at: now
    }));

    await queryInterface.bulkInsert('filters', difficultyFilters);
  }
};
