'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 移除 filters 表的 icon 和 color 字段
    await queryInterface.removeColumn('filters', 'icon');
    await queryInterface.removeColumn('filters', 'color');
    
    // 移除 filter_types 表的 icon 字段
    await queryInterface.removeColumn('filter_types', 'icon');
  },

  async down(queryInterface, Sequelize) {
    // 恢复 filters 表的 icon 和 color 字段
    await queryInterface.addColumn('filters', 'icon', {
      type: Sequelize.STRING(50),
      allowNull: true,
      comment: '过滤器图标'
    });
    
    await queryInterface.addColumn('filters', 'color', {
      type: Sequelize.STRING(20),
      allowNull: true,
      comment: '过滤器颜色'
    });
    
    // 恢复 filter_types 表的 icon 字段
    await queryInterface.addColumn('filter_types', 'icon', {
      type: Sequelize.STRING(50),
      allowNull: true,
      comment: '过滤器类型图标'
    });
  }
};
