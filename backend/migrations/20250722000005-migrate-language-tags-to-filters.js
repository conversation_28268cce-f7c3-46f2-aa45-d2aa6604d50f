'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      console.log('开始迁移语言标签到过滤器系统...');
      
      // 1. 检查是否存在旧的tags字段
      const tableInfo = await queryInterface.describeTable('contents');
      if (!tableInfo.tags) {
        console.log('contents表中没有tags字段，跳过迁移');
        await transaction.commit();
        return;
      }
      
      // 2. 获取所有有tags的内容
      const contents = await queryInterface.sequelize.query(
        'SELECT id, tags, learning_language FROM contents WHERE tags IS NOT NULL AND tags != ""',
        {
          type: Sequelize.QueryTypes.SELECT,
          transaction
        }
      );
      
      console.log(`找到 ${contents.length} 个内容需要迁移标签`);
      
      // 3. 获取现有的语言等级过滤器
      const filters = await queryInterface.sequelize.query(
        'SELECT id, name, language FROM filters WHERE type = "language_level"',
        {
          type: Sequelize.QueryTypes.SELECT,
          transaction
        }
      );
      
      // 创建标签名到过滤器ID的映射
      const tagToFilterMap = {};
      filters.forEach(filter => {
        // 匹配各种可能的标签格式
        const possibleTags = [
          filter.name,                    // N5, A1, HSK1
          filter.name.toLowerCase(),      // n5, a1, hsk1
          filter.name.toUpperCase(),      // N5, A1, HSK1
        ].filter(Boolean);
        
        possibleTags.forEach(tag => {
          if (!tagToFilterMap[tag]) {
            tagToFilterMap[tag] = filter.id;
          }
        });
      });
      
      console.log('标签映射表:', tagToFilterMap);
      
      // 4. 处理每个内容的标签
      let migratedCount = 0;
      let skippedCount = 0;
      
      for (const content of contents) {
        const tags = content.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
        const filterIds = [];
        
        // 将标签转换为过滤器ID
        for (const tag of tags) {
          const filterId = tagToFilterMap[tag] || 
                          tagToFilterMap[tag.toUpperCase()] || 
                          tagToFilterMap[tag.toLowerCase()];
          
          if (filterId && !filterIds.includes(filterId)) {
            filterIds.push(filterId);
          }
        }
        
        if (filterIds.length > 0) {
          // 为每个过滤器创建关联记录
          for (const filterId of filterIds) {
            // 检查是否已存在关联
            const existing = await queryInterface.sequelize.query(
              'SELECT id FROM content_filters WHERE content_id = ? AND filter_id = ?',
              {
                replacements: [content.id, filterId],
                type: Sequelize.QueryTypes.SELECT,
                transaction
              }
            );
            
            if (existing.length === 0) {
              await queryInterface.sequelize.query(
                'INSERT INTO content_filters (content_id, filter_id, created_at, updated_at) VALUES (?, ?, NOW(), NOW())',
                {
                  replacements: [content.id, filterId],
                  transaction
                }
              );
            }
          }
          
          migratedCount++;
          console.log(`内容 ${content.id}: 标签 "${content.tags}" -> 过滤器 [${filterIds.join(', ')}]`);
        } else {
          skippedCount++;
          console.log(`内容 ${content.id}: 无法匹配标签 "${content.tags}"`);
        }
      }
      
      console.log(`迁移完成: ${migratedCount} 个内容成功迁移, ${skippedCount} 个内容跳过`);
      
      await transaction.commit();
      
    } catch (error) {
      await transaction.rollback();
      console.error('迁移失败:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      console.log('回滚语言标签迁移...');
      
      // 删除所有通过迁移创建的content_filters记录
      // 注意：这个回滚操作会删除所有content_filters记录，请谨慎使用
      await queryInterface.sequelize.query(
        'DELETE FROM content_filters WHERE created_at >= (SELECT created_at FROM content_filters ORDER BY created_at DESC LIMIT 1)',
        { transaction }
      );
      
      console.log('回滚完成');
      await transaction.commit();
      
    } catch (error) {
      await transaction.rollback();
      console.error('回滚失败:', error);
      throw error;
    }
  }
};
