'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 1. 添加新的 languages 字段（TEXT类型，用于存储JSON数组）
    await queryInterface.addColumn('filters', 'languages', {
      type: Sequelize.TEXT,
      allowNull: true,
      comment: '适用语言代码列表，JSON格式存储，如["ja","en"]，null表示通用'
    });

    // 2. 将现有的 language 字段数据迁移到 languages 字段
    // 获取所有有 language 值的记录
    const [results] = await queryInterface.sequelize.query(
      'SELECT id, language FROM filters WHERE language IS NOT NULL'
    );

    // 批量更新：将单个语言转换为数组格式
    for (const row of results) {
      const languageArray = JSON.stringify([row.language]);
      await queryInterface.sequelize.query(
        'UPDATE filters SET languages = ? WHERE id = ?',
        {
          replacements: [languageArray, row.id]
        }
      );
    }

    // 3. 删除旧的 language 字段
    await queryInterface.removeColumn('filters', 'language');

    // 4. 删除相关的索引（如果存在）
    try {
      await queryInterface.removeIndex('filters', ['language']);
    } catch (error) {
      // 索引可能不存在，忽略错误
      console.log('Language index does not exist, skipping removal');
    }

    try {
      await queryInterface.removeIndex('filters', ['type', 'language']);
    } catch (error) {
      // 索引可能不存在，忽略错误
      console.log('Type-language composite index does not exist, skipping removal');
    }
  },

  async down(queryInterface, Sequelize) {
    // 1. 添加回 language 字段
    await queryInterface.addColumn('filters', 'language', {
      type: Sequelize.STRING(10),
      allowNull: true,
      comment: '语言代码，null表示通用过滤器'
    });

    // 2. 将 languages 字段数据迁移回 language 字段
    // 获取所有有 languages 值的记录
    const [results] = await queryInterface.sequelize.query(
      'SELECT id, languages FROM filters WHERE languages IS NOT NULL'
    );

    // 批量更新：取数组的第一个元素作为单个语言
    for (const row of results) {
      try {
        const languageArray = JSON.parse(row.languages);
        if (Array.isArray(languageArray) && languageArray.length > 0) {
          await queryInterface.sequelize.query(
            'UPDATE filters SET language = ? WHERE id = ?',
            {
              replacements: [languageArray[0], row.id]
            }
          );
        }
      } catch (error) {
        console.log(`Failed to parse languages for filter ${row.id}:`, error);
      }
    }

    // 3. 删除 languages 字段
    await queryInterface.removeColumn('filters', 'languages');

    // 4. 重新创建索引
    await queryInterface.addIndex('filters', ['language']);
    await queryInterface.addIndex('filters', ['type', 'language']);
  }
};
