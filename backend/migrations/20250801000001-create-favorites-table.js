"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();

    if (!tables.includes('favorites')) {
      await queryInterface.createTable("favorites", {
      id: {
        type: Sequelize.STRING(21),
        allowNull: false,
        primaryKey: true,
      },
      user_id: {
        type: Sequelize.STRING(21),
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      content_id: {
        type: Sequelize.STRING(21),
        allowNull: false,
        references: {
          model: "contents",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      });
      console.log('✅ 已创建 favorites 表');
    } else {
      console.log('ℹ️ favorites 表已存在，跳过创建');
    }

    // 添加唯一索引，确保用户不能重复收藏同一内容（安全方式）
    try {
      await queryInterface.addIndex("favorites", ["user_id", "content_id"], {
        unique: true,
        name: "favorites_user_content_unique",
      });
      console.log('✅ 已添加 favorites 唯一索引');
    } catch (error) {
      if (error.message.includes('Duplicate key name')) {
        console.log('ℹ️ favorites 唯一索引已存在，跳过添加');
      } else {
        throw error;
      }
    }

    // 添加索引，提高查询性能（安全方式）
    try {
      await queryInterface.addIndex("favorites", ["user_id"], {
        name: "favorites_user_id_index",
      });
      console.log('✅ 已添加 favorites user_id 索引');
    } catch (error) {
      if (error.message.includes('Duplicate key name')) {
        console.log('ℹ️ favorites user_id 索引已存在，跳过添加');
      } else {
        throw error;
      }
    }

    try {
      await queryInterface.addIndex("favorites", ["content_id"], {
        name: "favorites_content_id_index",
      });
      console.log('✅ 已添加 favorites content_id 索引');
    } catch (error) {
      if (error.message.includes('Duplicate key name')) {
        console.log('ℹ️ favorites content_id 索引已存在，跳过添加');
      } else {
        throw error;
      }
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("favorites");
  },
};
