'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();

    if (!tables.includes('collections')) {
      await queryInterface.createTable('collections', {
      id: {
        type: Sequelize.STRING(21),
        primaryKey: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: '合集名称'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '合集描述'
      },
      cover_image_url: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: '封面图片URL'
      },
      user_id: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '创建者ID'
      },
      is_public: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否公开'
      },
      tags: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: '标签，逗号分隔'
      },
      status: {
        type: Sequelize.ENUM('draft', 'published'),
        allowNull: false,
        defaultValue: 'draft',
        comment: '合集状态'
      },
      view_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '浏览次数'
      },
      favorite_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '收藏次数'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
      });
      console.log('✅ 已创建 collections 表');
    } else {
      console.log('ℹ️ collections 表已存在，跳过创建');
    }

    // 添加索引（安全方式）
    const indexes = [
      { columns: ['user_id'], name: 'user_id' },
      { columns: ['status'], name: 'status' },
      { columns: ['is_public'], name: 'is_public' },
      { columns: ['created_at'], name: 'created_at' }
    ];

    for (const index of indexes) {
      try {
        await queryInterface.addIndex('collections', index.columns);
        console.log(`✅ 已添加 collections ${index.name} 索引`);
      } catch (error) {
        if (error.message.includes('Duplicate key name')) {
          console.log(`ℹ️ collections ${index.name} 索引已存在，跳过添加`);
        } else {
          throw error;
        }
      }
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('collections');
  }
};
