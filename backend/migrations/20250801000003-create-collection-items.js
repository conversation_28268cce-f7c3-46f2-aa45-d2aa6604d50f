'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();

    if (!tables.includes('collection_items')) {
      await queryInterface.createTable('collection_items', {
      id: {
        type: Sequelize.STRING(21),
        primaryKey: true,
        allowNull: false
      },
      collection_id: {
        type: Sequelize.STRING(21),
        allowNull: false,
        comment: '合集ID'
      },
      content_id: {
        type: Sequelize.STRING(21),
        allowNull: false,
        comment: '内容ID'
      },
      sort_order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '排序顺序'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
      });
      console.log('✅ 已创建 collection_items 表');

      // 添加外键约束（安全方式）
      try {
        await queryInterface.addConstraint('collection_items', {
          fields: ['collection_id'],
          type: 'foreign key',
          name: 'fk_collection_items_collection_id',
          references: {
            table: 'collections',
            field: 'id'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE'
        });
        console.log('✅ 已添加 collection_items collection_id 外键约束');
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log('ℹ️ collection_items collection_id 外键约束已存在，跳过添加');
        } else {
          throw error;
        }
      }

      try {
        await queryInterface.addConstraint('collection_items', {
          fields: ['content_id'],
          type: 'foreign key',
          name: 'fk_collection_items_content_id',
          references: {
            table: 'contents',
            field: 'id'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE'
        });
        console.log('✅ 已添加 collection_items content_id 外键约束');
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log('ℹ️ collection_items content_id 外键约束已存在，跳过添加');
        } else {
          throw error;
        }
      }
    } else {
      console.log('ℹ️ collection_items 表已存在，跳过创建');
    }

    // 添加索引（安全方式）
    const indexes = [
      { columns: ['collection_id'], name: 'collection_id' },
      { columns: ['content_id'], name: 'content_id' },
      { columns: ['sort_order'], name: 'sort_order' }
    ];

    for (const index of indexes) {
      try {
        await queryInterface.addIndex('collection_items', index.columns);
        console.log(`✅ 已添加 collection_items ${index.name} 索引`);
      } catch (error) {
        if (error.message.includes('Duplicate key name')) {
          console.log(`ℹ️ collection_items ${index.name} 索引已存在，跳过添加`);
        } else {
          throw error;
        }
      }
    }

    // 添加唯一约束（安全方式）
    try {
      await queryInterface.addConstraint('collection_items', {
        fields: ['collection_id', 'content_id'],
        type: 'unique',
        name: 'unique_collection_content'
      });
      console.log('✅ 已添加 collection_items 唯一约束');
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('Duplicate key name')) {
        console.log('ℹ️ collection_items 唯一约束已存在，跳过添加');
      } else {
        throw error;
      }
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('collection_items');
  }
};
