'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();

    if (!tables.includes('collection_favorites')) {
      await queryInterface.createTable('collection_favorites', {
        id: {
          type: Sequelize.STRING(21),
          primaryKey: true,
          allowNull: false
        },
        collection_id: {
          type: Sequelize.STRING(21),
          allowNull: false,
          comment: '合集ID'
        },
        user_id: {
          type: Sequelize.STRING(50),
          allowNull: false,
          comment: '用户ID'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      });
      console.log('✅ 已创建 collection_favorites 表');

      // 添加外键约束
      try {
        await queryInterface.addConstraint('collection_favorites', {
          fields: ['collection_id'],
          type: 'foreign key',
          name: 'fk_collection_favorites_collection_id',
          references: {
            table: 'collections',
            field: 'id'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE'
        });
        console.log('✅ 已添加 collection_favorites collection_id 外键约束');
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log('ℹ️ collection_favorites collection_id 外键约束已存在，跳过添加');
        } else {
          throw error;
        }
      }

      try {
        await queryInterface.addConstraint('collection_favorites', {
          fields: ['user_id'],
          type: 'foreign key',
          name: 'fk_collection_favorites_user_id',
          references: {
            table: 'users',
            field: 'id'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE'
        });
        console.log('✅ 已添加 collection_favorites user_id 外键约束');
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log('ℹ️ collection_favorites user_id 外键约束已存在，跳过添加');
        } else {
          throw error;
        }
      }
    } else {
      console.log('ℹ️ collection_favorites 表已存在，跳过创建');
    }

    // 添加索引（安全方式）
    const indexes = [
      { columns: ['collection_id'], name: 'collection_id' },
      { columns: ['user_id'], name: 'user_id' }
    ];

    for (const index of indexes) {
      try {
        await queryInterface.addIndex('collection_favorites', index.columns);
        console.log(`✅ 已添加 collection_favorites ${index.name} 索引`);
      } catch (error) {
        if (error.message.includes('Duplicate key name')) {
          console.log(`ℹ️ collection_favorites ${index.name} 索引已存在，跳过添加`);
        } else {
          throw error;
        }
      }
    }

    // 添加唯一约束（安全方式）
    try {
      await queryInterface.addConstraint('collection_favorites', {
        fields: ['user_id', 'collection_id'],
        type: 'unique',
        name: 'unique_user_collection'
      });
      console.log('✅ 已添加 collection_favorites 唯一约束');
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('Duplicate key name')) {
        console.log('ℹ️ collection_favorites 唯一约束已存在，跳过添加');
      } else {
        throw error;
      }
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('collection_favorites');
  }
};
