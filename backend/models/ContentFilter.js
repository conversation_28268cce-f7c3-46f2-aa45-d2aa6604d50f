const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ContentFilter = sequelize.define('ContentFilter', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    contentId: {
      type: DataTypes.STRING(50),
      allowNull: false,
      field: 'content_id',
      comment: '内容ID'
    },
    filterId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'filter_id',
      comment: '过滤器ID',
      references: {
        model: 'filters',
        key: 'id'
      }
    }
  }, {
    tableName: 'content_filters',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['content_id']
      },
      {
        fields: ['filter_id']
      },
      {
        unique: true,
        fields: ['content_id', 'filter_id']
      }
    ]
  });

  // 定义关联关系
  ContentFilter.associate = function(models) {
    // 关联到内容
    ContentFilter.belongsTo(models.Content, {
      foreignKey: 'content_id',
      as: 'content'
    });

    // 关联到过滤器
    ContentFilter.belongsTo(models.Filter, {
      foreignKey: 'filter_id',
      as: 'filter'
    });
  };

  // 静态方法：为内容添加过滤器
  ContentFilter.addFiltersToContent = async function(contentId, filterIds) {
    const contentFilters = filterIds.map(filterId => ({
      contentId: contentId,
      filterId: filterId
    }));

    return await ContentFilter.bulkCreate(contentFilters, {
      ignoreDuplicates: true
    });
  };

  // 静态方法：移除内容的过滤器
  ContentFilter.removeFiltersFromContent = async function(contentId, filterIds = null) {
    const whereClause = { contentId: contentId };
    
    if (filterIds) {
      whereClause.filterId = filterIds;
    }

    return await ContentFilter.destroy({
      where: whereClause
    });
  };

  // 静态方法：更新内容的过滤器
  ContentFilter.updateContentFilters = async function(contentId, filterIds) {
    // 先删除所有现有的过滤器关联
    await ContentFilter.removeFiltersFromContent(contentId);
    
    // 然后添加新的过滤器关联
    if (filterIds && filterIds.length > 0) {
      return await ContentFilter.addFiltersToContent(contentId, filterIds);
    }
    
    return [];
  };

  // 静态方法：获取内容的过滤器
  ContentFilter.getContentFilters = async function(contentId) {
    return await ContentFilter.findAll({
      where: { contentId: contentId },
      include: [{
        model: sequelize.models.Filter,
        as: 'filter',
        where: { isActive: true }
      }]
    });
  };

  // 静态方法：根据过滤器获取内容
  ContentFilter.getContentsByFilters = async function(filterIds, options = {}) {
    const {
      page = 1,
      pageSize = 20,
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = options;

    const offset = (page - 1) * pageSize;

    return await ContentFilter.findAndCountAll({
      where: {
        filterId: filterIds
      },
      include: [{
        model: sequelize.models.Content,
        as: 'content',
        where: { status: 'published' }
      }],
      limit: pageSize,
      offset: offset,
      order: [[{ model: sequelize.models.Content, as: 'content' }, sortBy, sortOrder]],
      distinct: true
    });
  };

  return ContentFilter;
};
