const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const FilterType = sequelize.define('FilterType', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    type: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '过滤器类型标识'
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '过滤器类型名称'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '过滤器类型描述'
    },

    sortOrder: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      field: 'sort_order',
      comment: '排序顺序'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'is_active',
      comment: '是否激活'
    },
    isSystem: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'is_system',
      comment: '是否为系统类型（不可删除）'
    }
  }, {
    tableName: 'filter_types',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['type']
      },
      {
        fields: ['is_active']
      }
    ]
  });

  // 定义关联关系
  FilterType.associate = function(models) {
    // 一个过滤器类型可以有多个过滤器
    FilterType.hasMany(models.Filter, {
      foreignKey: 'type',
      sourceKey: 'type',
      as: 'filters'
    });
  };

  // 静态方法：获取所有激活的过滤器类型
  FilterType.getActiveTypes = async function() {
    return await FilterType.findAll({
      where: { isActive: true },
      order: [['sortOrder', 'ASC'], ['name', 'ASC']]
    });
  };

  // 静态方法：根据类型标识获取过滤器类型
  FilterType.getByType = async function(type) {
    return await FilterType.findOne({
      where: { type: type, isActive: true }
    });
  };

  // 静态方法：检查类型是否存在
  FilterType.typeExists = async function(type) {
    const count = await FilterType.count({
      where: { type: type }
    });
    return count > 0;
  };

  return FilterType;
};
