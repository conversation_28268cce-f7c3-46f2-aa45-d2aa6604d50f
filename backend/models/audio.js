/**
 * 音频模型
 * 用于存储音频文件的元数据和OSS访问信息
 */
module.exports = (sequelize, DataTypes) => {
  const Audio = sequelize.define(
    "Audio",
    {
      // 原始文本内容
      text: {
        type: DataTypes.TEXT,
        allowNull: false,
      },

      // 语言代码(zh, ja, en等)
      language: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },

      // 语速，默认1.0
      speed: {
        type: DataTypes.FLOAT,
        defaultValue: 1.0,
      },

      // 说话者信息（保留用于向后兼容，但推荐使用voice_db_id）
      speaker: {
        type: DataTypes.STRING(50),
        allowNull: true, // 改为可空，因为新记录应该使用voice_db_id
      },

      // 声音数据库ID，关联voices表
      voice_db_id: {
        type: DataTypes.INTEGER,
        allowNull: true, // 暂时可空，用于兼容现有数据
        references: {
          model: 'voices',
          key: 'id'
        },
        field: "voice_db_id",
        comment: "关联voices表的ID，用于标识具体的声音配置",
      },

      // OSS音频文件访问URL
      ossUrl: {
        type: DataTypes.STRING(255),
        allowNull: false,
        field: "oss_url",
      },

      // OSS存储键，用于管理
      ossKey: {
        type: DataTypes.STRING(255),
        allowNull: false,
        field: "oss_key",
      },

      // 音频时长(秒)
      duration: {
        type: DataTypes.FLOAT,
        allowNull: false,
      },

      // 缓存键(文本+语言+语速+说话者的MD5)
      md5Hash: {
        type: DataTypes.STRING(32),
        allowNull: false,
        field: "md5_hash",
      },

      // 音频来源
      audioSource: {
        type: DataTypes.ENUM("tts", "manual", "upload"),
        allowNull: false,
        defaultValue: "tts",
        field: "audio_source",
        comment: "音频来源：tts-TTS生成，manual-手动切割，upload-直接上传",
      },
    },
    {
      tableName: "audios",
      underscored: true,
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      indexes: [
        {
          name: "idx_md5_hash",
          fields: ["md5_hash"],
        },
        {
          name: "idx_language",
          fields: ["language"],
        },
        {
          name: "idx_created_at",
          fields: ["created_at"],
        },
        {
          name: "idx_audio_source",
          fields: ["audio_source"],
        },
        {
          name: "idx_voice_db_id",
          fields: ["voice_db_id"],
        },
      ],
    }
  );

  return Audio;
};
