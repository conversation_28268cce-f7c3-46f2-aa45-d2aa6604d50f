/**
 * 签到模型
 */
module.exports = (sequelize, DataTypes) => {
  const CheckIn = sequelize.define(
    'CheckIn',
    {
      id: {
        type: DataTypes.STRING(21),
        primaryKey: true,
        allowNull: false,
      },
      userId: {
        type: DataTypes.STRING(21),
        allowNull: false,
        field: 'user_id',
      },
      checkInDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        field: 'check_in_date',
      },
      rewards: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: '获得的奖励（预留字段）',
      },
    },
    {
      tableName: 'check_ins',
      timestamps: true,
      underscored: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        {
          fields: ['user_id'],
        },
        {
          fields: ['check_in_date'],
        },
        {
          unique: true,
          fields: ['user_id', 'check_in_date'],
          name: 'unique_user_date',
        },
      ],
    }
  );

  CheckIn.associate = function (models) {
    CheckIn.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });
  };

  return CheckIn;
};