/**
 * 合集内容关联模型
 * 用于存储合集与内容的关联关系和排序
 */
module.exports = (sequelize, DataTypes) => {
  const CollectionItem = sequelize.define(
    "CollectionItem",
    {
      // 主键ID，使用nanoid
      id: {
        type: DataTypes.STRING(21),
        primaryKey: true,
        allowNull: false,
      },

      // 合集ID
      collectionId: {
        type: DataTypes.STRING(21),
        allowNull: false,
        field: "collection_id",
        comment: "合集ID",
      },

      // 内容ID
      contentId: {
        type: DataTypes.STRING(21),
        allowNull: false,
        field: "content_id",
        comment: "内容ID",
      },

      // 排序顺序
      sortOrder: {
        type: DataTypes.INTEGER,
        allowNull: false,
        field: "sort_order",
        comment: "排序顺序",
      },
    },
    {
      // 表名
      tableName: "collection_items",
      // 时间戳
      timestamps: true,
      underscored: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      // 索引
      indexes: [
        {
          fields: ["collection_id"],
        },
        {
          fields: ["content_id"],
        },
        {
          fields: ["sort_order"],
        },
        {
          unique: true,
          fields: ["collection_id", "content_id"],
          name: "unique_collection_content",
        },
      ],
    }
  );

  CollectionItem.associate = function (models) {
    // 与合集表关联
    CollectionItem.belongsTo(models.Collection, {
      foreignKey: "collectionId",
      as: "collection",
      constraints: false, // 禁用外键约束
    });

    // 与内容表关联
    CollectionItem.belongsTo(models.Content, {
      foreignKey: "contentId",
      as: "content",
      constraints: false, // 禁用外键约束
    });
  };

  return CollectionItem;
};
