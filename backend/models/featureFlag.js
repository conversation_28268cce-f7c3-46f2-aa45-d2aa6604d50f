"use strict";

module.exports = (sequelize, DataTypes) => {
  const FeatureFlag = sequelize.define(
    "FeatureFlag",
    {
      featureKey: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        field: "feature_key",
        comment: "功能标识符",
      },
      isEnabled: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: "is_enabled",
        comment: "是否全局启用",
      },
      description: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: "功能描述",
      },
      displayName: {
        type: DataTypes.STRING(100),
        allowNull: true,
        field: "display_name",
        comment: "功能显示名称",
      },
    },
    {
      tableName: "feature_flags",
      underscored: true,
      timestamps: true,
    }
  );

  return FeatureFlag;
};
