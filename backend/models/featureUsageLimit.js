/**
 * 功能使用限制模型
 * 定义不同等级用户对功能的使用限制
 */
module.exports = (sequelize, DataTypes) => {
  const FeatureUsageLimit = sequelize.define(
    "FeatureUsageLimit",
    {
      // 用户等级
      level: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '用户等级'
      },
      
      // 功能标识符
      featureKey: {
        type: DataTypes.STRING(50),
        allowNull: false,
        field: 'feature_key',
        comment: '功能标识符'
      },
      
      // 每日使用限制
      dailyLimit: {
        type: DataTypes.INTEGER,
        allowNull: true,
        field: 'daily_limit',
        comment: '每日使用限制，NULL表示无限制'
      },
      
      // 每月使用限制
      monthlyLimit: {
        type: DataTypes.INTEGER,
        allowNull: true,
        field: 'monthly_limit',
        comment: '每月使用限制，NULL表示无限制'
      }
    },
    {
      tableName: "feature_usage_limits",
      underscored: true,
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      indexes: [
        {
          unique: true,
          fields: ['level', 'feature_key']
        }
      ]
    }
  );

  return FeatureUsageLimit;
};
