/**
 * 功能使用记录模型
 * 记录用户对功能的使用情况
 */
module.exports = (sequelize, DataTypes) => {
  const FeatureUsageRecord = sequelize.define(
    "FeatureUsageRecord",
    {
      // 用户ID
      userId: {
        type: DataTypes.STRING(21),
        allowNull: false,
        field: 'user_id',
        comment: '用户ID'
      },
      
      // 功能标识符
      featureKey: {
        type: DataTypes.STRING(50),
        allowNull: false,
        field: 'feature_key',
        comment: '功能标识符'
      },
      
      // 使用时间
      usedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        field: 'used_at',
        comment: '使用时间'
      }
    },
    {
      tableName: "feature_usage_records",
      underscored: true,
      timestamps: true,
      createdAt: "created_at",
      updatedAt: false, // 不需要更新时间
      indexes: [
        {
          fields: ['user_id', 'feature_key']
        },
        {
          fields: ['used_at']
        }
      ]
    }
  );

  FeatureUsageRecord.associate = function(models) {
    // 与用户表关联
    FeatureUsageRecord.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  return FeatureUsageRecord;
};
