/**
 * 用户反馈模型
 * 用于存储用户提交的反馈信息
 */
module.exports = (sequelize, DataTypes) => {
  const Feedback = sequelize.define(
    "Feedback",
    {
      // 主键ID，自增
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },

      // 用户ID（可选，未登录用户也可以提交反馈）
      userId: {
        type: DataTypes.STRING(21),
        allowNull: true,
        field: "user_id",
      },

      // 反馈类型：suggestion(建议), bug(问题), feature(功能请求), other(其他)
      type: {
        type: DataTypes.ENUM("suggestion", "bug", "feature", "other"),
        allowNull: false,
        defaultValue: "suggestion",
      },

      // 反馈内容
      content: {
        type: DataTypes.TEXT,
        allowNull: false,
      },

      // 联系方式（邮箱或其他）
      contact: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },

      // 设备信息
      deviceInfo: {
        type: DataTypes.TEXT,
        allowNull: true,
        field: "device_info",
      },

      // 浏览器信息
      browserInfo: {
        type: DataTypes.TEXT,
        allowNull: true,
        field: "browser_info",
      },

      // 页面URL
      pageUrl: {
        type: DataTypes.STRING(255),
        allowNull: true,
        field: "page_url",
      },

      // 状态：pending(待处理), processing(处理中), resolved(已解决), rejected(已拒绝)
      status: {
        type: DataTypes.ENUM("pending", "processing", "resolved", "rejected"),
        allowNull: false,
        defaultValue: "pending",
      },

      // 管理员回复
      adminReply: {
        type: DataTypes.TEXT,
        allowNull: true,
        field: "admin_reply",
      },

      // 处理人ID
      processedBy: {
        type: DataTypes.STRING(21),
        allowNull: true,
        field: "processed_by",
      },

      // 处理时间
      processedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        field: "processed_at",
      },
    },
    {
      // 表名
      tableName: "feedbacks",
      // 时间戳
      timestamps: true,
      underscored: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      // 索引
      indexes: [
        {
          fields: ["user_id"],
        },
        {
          fields: ["type"],
        },
        {
          fields: ["status"],
        },
        {
          fields: ["created_at"],
        },
      ],
    }
  );

  // 关联关系
  Feedback.associate = function (models) {
    // 与用户表关联
    Feedback.belongsTo(models.User, {
      foreignKey: "userId",
      as: "user",
    });

    // 与处理人关联
    Feedback.belongsTo(models.User, {
      foreignKey: "processedBy",
      as: "processor",
    });
  };

  return Feedback;
};
