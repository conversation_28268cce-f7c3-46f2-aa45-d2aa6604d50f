/**
 * 等级权限模型
 * 定义不同等级用户可以访问的功能
 */
module.exports = (sequelize, DataTypes) => {
  const LevelPermission = sequelize.define(
    "LevelPermission",
    {
      // 用户等级
      level: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '用户等级'
      },
      
      // 功能标识符
      featureKey: {
        type: DataTypes.STRING(50),
        allowNull: false,
        field: 'feature_key',
        comment: '功能标识符'
      }
    },
    {
      tableName: "level_permissions",
      underscored: true,
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      indexes: [
        {
          unique: true,
          fields: ['level', 'feature_key']
        }
      ]
    }
  );

  return LevelPermission;
};
