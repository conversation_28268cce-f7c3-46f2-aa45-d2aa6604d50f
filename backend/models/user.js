/**
 * 用户模型
 * 用于存储用户信息和认证数据
 */
module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define(
    "User",
    {
      // 主键ID，使用nanoid
      id: {
        type: DataTypes.STRING(21),
        primaryKey: true,
        allowNull: false,
      },

      // 用户邮箱
      email: {
        type: DataTypes.STRING(255),
        allowNull: false,
        unique: true,
        validate: {
          isEmail: true,
        },
      },

      // 用户名（可选）
      username: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },

      // 最后登录时间
      lastLoginAt: {
        type: DataTypes.DATE,
        allowNull: true,
        field: "last_login_at",
      },

      // 用户状态：active, inactive, banned
      status: {
        type: DataTypes.ENUM("active", "inactive", "banned"),
        defaultValue: "active",
      },

      // 用户角色：user, admin
      role: {
        type: DataTypes.ENUM("user", "admin"),
        defaultValue: "user",
      },

      // 用户等级：0=免费用户, 1=基础会员, 2=高级会员, 3=专业会员
      level: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: "用户等级",
      },

      // 用户设置（JSON格式）
      settings: {
        type: DataTypes.JSON,
        allowNull: true,
      },
    },
    {
      // 表名
      tableName: "users",
      // 时间戳
      timestamps: true,
      underscored: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      // 索引
      indexes: [
        {
          unique: true,
          fields: ["email"],
        },
        {
          fields: ["level"],
        },
      ],
    }
  );

  User.associate = function (models) {
    // 与订阅表关联
    User.hasMany(models.UserSubscription, {
      foreignKey: "userId",
      as: "subscriptions",
    });

    // 与功能使用记录表关联
    User.hasMany(models.FeatureUsageRecord, {
      foreignKey: "userId",
      as: "usageRecords",
    });
  };

  return User;
};
