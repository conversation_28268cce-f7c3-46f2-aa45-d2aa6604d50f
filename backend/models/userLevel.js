/**
 * 用户等级模型
 * 定义系统中的用户等级
 */
module.exports = (sequelize, DataTypes) => {
  const UserLevel = sequelize.define(
    "UserLevel",
    {
      // 等级数值
      level: {
        type: DataTypes.INTEGER,
        allowNull: false,
        unique: true,
        comment: "等级数值，如0(免费)、1(基础)、2(高级)、3(专业)",
      },

      // 等级名称
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: "等级名称，如免费用户、基础会员、高级会员",
      },

      // 等级描述
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "等级描述",
      },

      // 是否为默认等级
      isDefault: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: "is_default",
        comment: "是否为默认等级",
      },
    },
    {
      tableName: "user_levels",
      underscored: true,
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
    }
  );

  UserLevel.associate = function (models) {
    // 与用户表关联 - 不创建外键约束
    UserLevel.hasMany(models.User, {
      foreignKey: "level",
      as: "users",
      constraints: false, // 不创建外键约束
    });
  };

  return UserLevel;
};
