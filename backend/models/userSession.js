'use strict';

module.exports = (sequelize, DataTypes) => {
  const UserSession = sequelize.define('UserSession', {
    userId: {
      type: DataTypes.STRING(50),
      allowNull: false,
      field: 'user_id',
      comment: '用户ID'
    },
    tokenId: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      field: 'token_id',
      comment: 'JWT token的唯一标识符'
    },
    deviceInfo: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'device_info',
      comment: '设备信息（浏览器、操作系统等）'
    },
    ipAddress: {
      type: DataTypes.STRING(45),
      allowNull: true,
      field: 'ip_address',
      comment: 'IP地址'
    },
    lastActive: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'last_active',
      comment: '最后活跃时间'
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'expires_at',
      comment: '会话过期时间'
    }
  }, {
    tableName: 'user_sessions',
    underscored: true,
    timestamps: true,
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['token_id'],
        unique: true
      },
      {
        fields: ['expires_at']
      }
    ]
  });

  UserSession.associate = function(models) {
    // 与用户表关联
    UserSession.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  return UserSession;
};
