/**
 * 简化的维度管理路由
 * 只支持基本的查看和创建功能
 */
const express = require('express');
const router = express.Router();
const db = require('../models');
const { Filter } = db;

/**
 * 获取所有维度类型
 * GET /api/admin/dimensions
 */
router.get('/', async (req, res) => {
  try {
    // 从过滤器表中获取所有不同的维度类型
    const dimensions = await Filter.findAll({
      attributes: [
        'type',
        [db.sequelize.fn('COUNT', db.sequelize.col('id')), 'filterCount']
      ],
      group: ['type'],
      order: [['type', 'ASC']],
      raw: true
    });

    // 简单的名称映射
    const dimensionNames = {
      'language_level': '语言等级',
      'content_type': '内容类型',
      'topic': '主题',
      'material': '教材'
    };

    const result = dimensions.map(dim => ({
      type: dim.type,
      name: dimensionNames[dim.type] || dim.type,
      filterCount: parseInt(dim.filterCount)
    }));

    res.json({
      success: true,
      dimensions: result
    });
  } catch (error) {
    console.error('获取维度列表失败:', error);
    res.status(500).json({
      success: false,
      error: '获取维度列表失败'
    });
  }
});

/**
 * 创建新维度类型（简化版）
 * POST /api/admin/dimensions
 */
router.post('/', async (req, res) => {
  try {
    const { type, name } = req.body;

    // 基本验证
    if (!type || !name) {
      return res.status(400).json({
        success: false,
        error: '维度类型和名称是必需的'
      });
    }

    // 检查是否已存在
    const existingDimension = await Filter.findOne({
      where: { type: type }
    });

    if (existingDimension) {
      return res.status(400).json({
        success: false,
        error: '该维度类型已存在'
      });
    }

    // 创建示例过滤器
    await Filter.create({
      name: `${name}示例`,
      key: `${type}_sample`,
      type: type,
      languages: null,
      description: `${name}维度的示例过滤器`,
      sortOrder: 1,
      isActive: true,
      isSystem: false
    });

    res.status(201).json({
      success: true,
      message: '维度创建成功'
    });
  } catch (error) {
    console.error('创建维度失败:', error);
    res.status(500).json({
      success: false,
      error: '创建维度失败'
    });
  }
});

/**
 * 删除维度类型
 * DELETE /api/admin/dimensions/:type
 */
router.delete('/:type', async (req, res) => {
  try {
    const { type } = req.params;

    // 检查是否为系统维度
    const systemDimensions = ['language_level', 'content_type', 'topic', 'material'];
    if (systemDimensions.includes(type)) {
      return res.status(400).json({
        success: false,
        error: '不能删除系统预设维度'
      });
    }

    // 删除该维度下的所有过滤器
    const deletedCount = await Filter.destroy({
      where: { type: type }
    });

    if (deletedCount === 0) {
      return res.status(404).json({
        success: false,
        error: '维度不存在'
      });
    }

    res.json({
      success: true,
      message: `已删除维度"${type}"及其下的${deletedCount}个过滤器`
    });
  } catch (error) {
    console.error('删除维度失败:', error);
    res.status(500).json({
      success: false,
      error: '删除维度失败'
    });
  }
});

/**
 * 重命名维度类型
 * PUT /api/admin/dimensions/:type
 */
router.put('/:type', async (req, res) => {
  try {
    const { type } = req.params;
    const { newType, newName } = req.body;

    // 检查是否为系统维度
    const systemDimensions = ['language_level', 'content_type', 'topic', 'material'];
    if (systemDimensions.includes(type)) {
      return res.status(400).json({
        success: false,
        error: '不能编辑系统预设维度'
      });
    }

    // 如果修改了类型键值，检查新类型是否已存在
    if (newType && newType !== type) {
      const existingFilter = await Filter.findOne({
        where: { type: newType }
      });

      if (existingFilter) {
        return res.status(400).json({
          success: false,
          error: '新的维度类型已存在'
        });
      }

      // 更新所有该类型的过滤器
      await Filter.update(
        { type: newType },
        { where: { type: type } }
      );
    }

    res.json({
      success: true,
      message: '维度更新成功'
    });
  } catch (error) {
    console.error('更新维度失败:', error);
    res.status(500).json({
      success: false,
      error: '更新维度失败'
    });
  }
});

module.exports = router;
