/**
 * 后台管理 - 过滤器管理路由
 * 提供过滤器的增删改查功能
 */

const express = require("express");
const router = express.Router();
const { Op } = require("sequelize");
const db = require("../models");
const { Filter } = db;

/**
 * 获取所有过滤器（包括禁用的）
 * GET /api/filters/all
 */
router.get("/all", async (req, res) => {
  try {
    const filters = await Filter.findAll({
      order: [
        ["type", "ASC"],
        ["created_at", "DESC"],
      ],
    });

    res.json({
      success: true,
      filters: filters.map((filter) => ({
        id: filter.id,
        name: filter.name,
        key: filter.key,
        type: filter.type,
        languages: filter.languages,
        description: filter.description,
        isActive: filter.isActive,
        createdAt: filter.created_at,
        updatedAt: filter.updated_at,
      })),
    });
  } catch (error) {
    console.error("获取所有过滤器失败:", error);
    res.status(500).json({
      success: false,
      error: "获取过滤器列表失败",
    });
  }
});

/**
 * 创建过滤器
 * POST /api/filters
 */
router.post("/", async (req, res) => {
  try {
    const { name, key, type, languages, description, isActive } = req.body;

    // 验证必填字段
    if (!name || !key || !type) {
      return res.status(400).json({
        success: false,
        error: "名称、键值和类型为必填字段",
      });
    }

    // 检查键值是否已存在
    const existingFilter = await Filter.findOne({
      where: { key },
    });

    if (existingFilter) {
      return res.status(400).json({
        success: false,
        error: "键值已存在，请使用其他键值",
      });
    }

    // 创建过滤器
    const filter = await Filter.create({
      name,
      key,
      type,
      languages: languages || [],
      description: description || null,
      isActive: isActive !== false,
    });

    res.status(201).json({
      success: true,
      filter: {
        id: filter.id,
        name: filter.name,
        key: filter.key,
        type: filter.type,
        languages: filter.languages,
        description: filter.description,
        isActive: filter.isActive,
        createdAt: filter.created_at,
        updatedAt: filter.updated_at,
      },
    });
  } catch (error) {
    console.error("创建过滤器失败:", error);
    res.status(500).json({
      success: false,
      error: "创建过滤器失败",
    });
  }
});

/**
 * 更新过滤器
 * PUT /api/filters/:id
 */
router.put("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const { name, key, type, languages, description, isActive } = req.body;

    // 查找过滤器
    const filter = await Filter.findByPk(id);
    if (!filter) {
      return res.status(404).json({
        success: false,
        error: "过滤器不存在",
      });
    }

    // 如果更新键值，检查是否与其他过滤器冲突
    if (key && key !== filter.key) {
      const existingFilter = await Filter.findOne({
        where: {
          key,
          id: { [Op.ne]: id },
        },
      });

      if (existingFilter) {
        return res.status(400).json({
          success: false,
          error: "键值已存在，请使用其他键值",
        });
      }
    }

    // 更新过滤器
    await filter.update({
      name: name || filter.name,
      key: key || filter.key,
      type: type || filter.type,
      languages: languages !== undefined ? languages : filter.languages,
      description: description !== undefined ? description : filter.description,
      isActive: isActive !== undefined ? isActive : filter.isActive,
    });

    res.json({
      success: true,
      filter: {
        id: filter.id,
        name: filter.name,
        key: filter.key,
        type: filter.type,
        languages: filter.languages,
        description: filter.description,
        isActive: filter.isActive,
        createdAt: filter.created_at,
        updatedAt: filter.updated_at,
      },
    });
  } catch (error) {
    console.error("更新过滤器失败:", error);
    res.status(500).json({
      success: false,
      error: "更新过滤器失败",
    });
  }
});

/**
 * 删除过滤器
 * DELETE /api/filters/:id
 */
router.delete("/:id", async (req, res) => {
  try {
    const { id } = req.params;

    // 查找过滤器
    const filter = await Filter.findByPk(id);
    if (!filter) {
      return res.status(404).json({
        success: false,
        error: "过滤器不存在",
      });
    }

    // 检查是否有内容使用了这个过滤器
    // 这里可以添加检查逻辑，防止删除正在使用的过滤器
    // const contentCount = await ContentFilter.count({
    //   where: { filterId: id }
    // });
    //
    // if (contentCount > 0) {
    //   return res.status(400).json({
    //     success: false,
    //     error: `无法删除，有 ${contentCount} 个内容正在使用此过滤器`
    //   });
    // }

    // 删除过滤器
    await filter.destroy();

    res.json({
      success: true,
      message: "过滤器已删除",
    });
  } catch (error) {
    console.error("删除过滤器失败:", error);
    res.status(500).json({
      success: false,
      error: "删除过滤器失败",
    });
  }
});

/**
 * 批量更新过滤器状态
 * PATCH /api/filters/batch-status
 */
router.patch("/batch-status", async (req, res) => {
  try {
    const { filterIds, isActive } = req.body;

    if (!Array.isArray(filterIds) || filterIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: "请提供有效的过滤器ID数组",
      });
    }

    // 批量更新状态
    const [updatedCount] = await Filter.update(
      { isActive },
      {
        where: {
          id: {
            [Op.in]: filterIds,
          },
        },
      }
    );

    res.json({
      success: true,
      message: `已更新 ${updatedCount} 个过滤器的状态`,
      updatedCount,
    });
  } catch (error) {
    console.error("批量更新过滤器状态失败:", error);
    res.status(500).json({
      success: false,
      error: "批量更新失败",
    });
  }
});

/**
 * 获取过滤器统计信息
 * GET /api/filters/statistics
 */
router.get("/statistics", async (req, res) => {
  try {
    // 总数统计
    const totalCount = await Filter.count();
    const activeCount = await Filter.count({
      where: { isActive: true },
    });

    // 按类型统计
    const typeStats = await Filter.findAll({
      attributes: [
        "type",
        [Filter.sequelize.fn("COUNT", Filter.sequelize.col("id")), "count"],
      ],
      group: ["type"],
      raw: true,
    });

    // 类型名称映射
    const typeNameMap = {
      language_level: "语言等级",
      content_type: "内容类型",
      topic: "主题",
      material: "教材",
    };

    const formattedTypeStats = typeStats.map((stat) => ({
      type: stat.type,
      name: typeNameMap[stat.type] || stat.type,
      count: parseInt(stat.count),
    }));

    res.json({
      success: true,
      statistics: {
        totalFilters: totalCount,
        activeFilters: activeCount,
        inactiveFilters: totalCount - activeCount,
        typeStats: formattedTypeStats,
      },
    });
  } catch (error) {
    console.error("获取过滤器统计失败:", error);
    res.status(500).json({
      success: false,
      error: "获取统计信息失败",
    });
  }
});

module.exports = router;
