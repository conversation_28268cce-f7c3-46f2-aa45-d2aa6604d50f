/**
 * 安全管理路由
 * 提供安全相关的管理功能
 */
const express = require("express");
const router = express.Router();
const db = require("../../models");
const {
  adminAuth,
  logAdminAction,
} = require("../../middleware/adminMiddleware");
const {
  ipFilter,
  addToDynamicBlacklist,
  isBlacklisted,
  isWhitelisted,
} = require("../../middleware/ipFilterMiddleware");

/**
 * 获取安全警报列表
 * GET /api/admin/security/alerts
 */
router.get("/alerts", adminAuth, async (req, res) => {
  try {
    const { page = 1, pageSize = 20, status, type, ip } = req.query;
    const offset = (page - 1) * pageSize;

    // 构建查询条件
    const where = {};
    if (status) where.status = status;
    if (type) where.type = type;
    if (ip) where.ip = { [db.Sequelize.Op.like]: `%${ip}%` };

    // 查询安全警报
    const { count, rows: alerts } = await db.SecurityAlert.findAndCountAll({
      where,
      order: [["createdAt", "DESC"]],
      limit: parseInt(pageSize),
      offset: parseInt(offset),
    });

    res.json({
      success: true,
      alerts,
      pagination: {
        total: count,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
      },
    });
  } catch (error) {
    console.error("获取安全警报列表失败:", error);
    res.status(500).json({
      success: false,
      error: `获取安全警报列表失败: ${error.message}`,
    });
  }
});

/**
 * 更新安全警报状态
 * PUT /api/admin/security/alerts/:id
 */
router.put(
  "/alerts/:id",
  adminAuth,
  logAdminAction("update_security_alert", {
    targetType: "security_alert",
    targetIdParam: "id",
    getBeforeData: async (req) => {
      const alert = await db.SecurityAlert.findByPk(req.params.id);
      return alert ? alert.toJSON() : null;
    },
    getAfterData: async (req) => {
      const alert = await db.SecurityAlert.findByPk(req.params.id);
      return alert ? alert.toJSON() : null;
    },
  }),
  async (req, res) => {
    try {
      const { id } = req.params;
      const { status, notes } = req.body;

      // 验证状态
      if (!["new", "reviewed", "resolved", "false_positive"].includes(status)) {
        return res.status(400).json({
          success: false,
          error: "无效的状态值",
        });
      }

      // 查找警报
      const alert = await db.SecurityAlert.findByPk(id);

      if (!alert) {
        return res.status(404).json({
          success: false,
          error: "安全警报不存在",
        });
      }

      // 更新警报
      await alert.update({
        status,
        reviewedBy: req.admin.email,
        reviewedAt: new Date(),
        details: notes
          ? `${
              alert.details
            }\n\n管理员备注 (${new Date().toISOString()}):\n${notes}`
          : alert.details,
      });

      res.json({
        success: true,
        alert,
        message: "安全警报状态已更新",
      });
    } catch (error) {
      console.error("更新安全警报状态失败:", error);
      res.status(500).json({
        success: false,
        error: `更新安全警报状态失败: ${error.message}`,
      });
    }
  }
);

/**
 * 获取IP黑名单
 * GET /api/admin/security/blacklist
 */
router.get("/blacklist", adminAuth, async (req, res) => {
  try {
    // 从数据库获取静态黑名单
    const staticBlacklistRows = await db.IpList.findAll({
      where: {
        type: "blacklist",
      },
      attributes: [
        "id",
        "ip",
        "reason",
        "expires_at",
        "created_by",
        "createdAt",
      ],
      order: [["createdAt", "DESC"]],
    });

    // 格式化静态黑名单
    const staticBlacklist = staticBlacklistRows.map((row) => ({
      id: row.id,
      ip: row.ip,
      reason: row.reason,
      expires_at: row.expires_at,
      created_by: row.created_by,
      created_at: row.createdAt,
    }));

    // 获取动态黑名单
    const dynamicBlacklist = [];

    // 如果ipFilterMiddleware提供了getDynamicBlacklist方法，则使用它
    if (typeof ipFilter.getDynamicBlacklist === "function") {
      const dynamicList = ipFilter.getDynamicBlacklist();
      for (const [ip, data] of dynamicList.entries()) {
        dynamicBlacklist.push({
          ip,
          reason: data.reason,
          expires: data.expires,
        });
      }
    }

    res.json({
      success: true,
      blacklist: {
        static: staticBlacklist,
        dynamic: dynamicBlacklist,
      },
    });
  } catch (error) {
    console.error("获取IP黑名单失败:", error);
    res.status(500).json({
      success: false,
      error: `获取IP黑名单失败: ${error.message}`,
    });
  }
});

/**
 * 添加IP到黑名单
 * POST /api/admin/security/blacklist
 */
router.post(
  "/blacklist",
  adminAuth,
  logAdminAction("add_to_blacklist", {
    targetType: "ip_blacklist",
    getDetails: (req) =>
      `IP: ${req.body.ip}, 类型: ${req.body.type}, 原因: ${req.body.reason}`,
  }),
  async (req, res) => {
    try {
      const { ip, type, reason, duration } = req.body;

      // 验证IP格式
      if (!ip || !/^(\d{1,3}\.){3}\d{1,3}(\/\d{1,2})?$/.test(ip)) {
        return res.status(400).json({
          success: false,
          error: "无效的IP地址格式",
        });
      }

      // 根据类型处理
      if (type === "static") {
        try {
          // 添加到静态黑名单（数据库）
          await ipFilter.batchAddToList(
            [ip],
            "blacklist",
            reason || "管理员手动添加",
            req.admin.email
          );

          res.json({
            success: true,
            message: `IP ${ip} 已添加到静态黑名单`,
          });
        } catch (dbError) {
          console.error("添加IP到数据库黑名单失败:", dbError);
          res.status(500).json({
            success: false,
            error: `添加IP到黑名单失败: ${dbError.message}`,
          });
        }
      } else if (type === "dynamic") {
        // 添加到动态黑名单
        const durationMs = duration
          ? parseInt(duration) * 60 * 60 * 1000
          : 24 * 60 * 60 * 1000; // 默认24小时
        addToDynamicBlacklist(ip, durationMs, reason || "管理员手动添加");

        res.json({
          success: true,
          message: `IP ${ip} 已添加到动态黑名单，持续时间: ${
            durationMs / (60 * 60 * 1000)
          }小时`,
        });
      } else {
        return res.status(400).json({
          success: false,
          error: "无效的黑名单类型",
        });
      }
    } catch (error) {
      console.error("添加IP到黑名单失败:", error);
      res.status(500).json({
        success: false,
        error: `添加IP到黑名单失败: ${error.message}`,
      });
    }
  }
);

/**
 * 从黑名单中移除IP
 * DELETE /api/admin/security/blacklist/:ip
 */
router.delete(
  "/blacklist/:ip",
  adminAuth,
  logAdminAction("remove_from_blacklist", {
    targetType: "ip_blacklist",
    targetIdParam: "ip",
  }),
  async (req, res) => {
    try {
      const { ip } = req.params;
      const { type } = req.query;

      if (type === "static") {
        try {
          // 从数据库黑名单中移除
          const deleted = await db.IpList.destroy({
            where: { ip, type: "blacklist" },
          });

          if (deleted === 0) {
            return res.status(404).json({
              success: false,
              error: `IP ${ip} 不在黑名单中`,
            });
          }

          // 强制刷新缓存
          ipFilter.resetCache();

          res.json({
            success: true,
            message: `IP ${ip} 已从静态黑名单中移除`,
          });
        } catch (dbError) {
          console.error("从数据库黑名单移除IP失败:", dbError);
          res.status(500).json({
            success: false,
            error: `从黑名单中移除IP失败: ${dbError.message}`,
          });
        }
      } else if (type === "dynamic") {
        // 从动态黑名单移除
        const removed = ipFilter.removeFromDynamicBlacklist(ip);

        if (removed) {
          res.json({
            success: true,
            message: `IP ${ip} 已从动态黑名单中移除`,
          });
        } else {
          res.status(404).json({
            success: false,
            error: `IP ${ip} 不在动态黑名单中`,
          });
        }
      } else {
        return res.status(400).json({
          success: false,
          error: "无效的黑名单类型",
        });
      }
    } catch (error) {
      console.error("从黑名单中移除IP失败:", error);
      res.status(500).json({
        success: false,
        error: `从黑名单中移除IP失败: ${error.message}`,
      });
    }
  }
);

/**
 * 获取IP白名单
 * GET /api/admin/security/whitelist
 */
router.get("/whitelist", adminAuth, async (req, res) => {
  try {
    // 从数据库获取白名单
    const whitelistRows = await db.IpList.findAll({
      where: {
        type: "whitelist",
      },
      attributes: ["id", "ip", "reason", "created_by", "createdAt"],
      order: [["createdAt", "DESC"]],
    });

    // 格式化白名单
    const whitelist = whitelistRows.map((row) => ({
      id: row.id,
      ip: row.ip,
      comment: row.reason, // 使用reason字段作为comment
      created_by: row.created_by,
      created_at: row.createdAt,
    }));

    res.json({
      success: true,
      whitelist,
    });
  } catch (error) {
    console.error("获取IP白名单失败:", error);
    res.status(500).json({
      success: false,
      error: `获取IP白名单失败: ${error.message}`,
    });
  }
});

/**
 * 添加IP到白名单
 * POST /api/admin/security/whitelist
 */
router.post(
  "/whitelist",
  adminAuth,
  logAdminAction("add_to_whitelist", {
    targetType: "ip_whitelist",
    getDetails: (req) =>
      `IP: ${req.body.ip}, 备注: ${req.body.comment || "无"}`,
  }),
  async (req, res) => {
    try {
      const { ip, comment } = req.body;

      // 验证IP格式
      if (!ip || !/^(\d{1,3}\.){3}\d{1,3}(\/\d{1,2})?$/.test(ip)) {
        return res.status(400).json({
          success: false,
          error: "无效的IP地址格式",
        });
      }

      try {
        // 添加到白名单（数据库）
        await ipFilter.batchAddToList(
          [ip],
          "whitelist",
          comment || "",
          req.admin.email
        );

        res.json({
          success: true,
          message: `IP ${ip} 已添加到白名单`,
        });
      } catch (dbError) {
        console.error("添加IP到数据库白名单失败:", dbError);
        res.status(500).json({
          success: false,
          error: `添加IP到白名单失败: ${dbError.message}`,
        });
      }
    } catch (error) {
      console.error("添加IP到白名单失败:", error);
      res.status(500).json({
        success: false,
        error: `添加IP到白名单失败: ${error.message}`,
      });
    }
  }
);

/**
 * 从白名单中移除IP
 * DELETE /api/admin/security/whitelist/:ip
 */
router.delete(
  "/whitelist/:ip",
  adminAuth,
  logAdminAction("remove_from_whitelist", {
    targetType: "ip_whitelist",
    targetIdParam: "ip",
  }),
  async (req, res) => {
    try {
      const { ip } = req.params;

      try {
        // 从数据库白名单中移除
        const deleted = await db.IpList.destroy({
          where: { ip, type: "whitelist" },
        });

        if (deleted === 0) {
          return res.status(404).json({
            success: false,
            error: `IP ${ip} 不在白名单中`,
          });
        }

        // 强制刷新缓存
        ipFilter.resetCache();

        res.json({
          success: true,
          message: `IP ${ip} 已从白名单中移除`,
        });
      } catch (dbError) {
        console.error("从数据库白名单移除IP失败:", dbError);
        res.status(500).json({
          success: false,
          error: `从白名单中移除IP失败: ${dbError.message}`,
        });
      }
    } catch (error) {
      console.error("从白名单中移除IP失败:", error);
      res.status(500).json({
        success: false,
        error: `从白名单中移除IP失败: ${error.message}`,
      });
    }
  }
);

/**
 * 检查IP状态
 * GET /api/admin/security/check-ip/:ip
 */
router.get("/check-ip/:ip", adminAuth, async (req, res) => {
  try {
    const { ip } = req.params;

    // 检查IP是否在黑名单中
    const isInBlacklist = await isBlacklisted(ip);

    // 检查IP是否在白名单中
    const isInWhitelist = await isWhitelisted(ip);

    // 获取失败登录记录
    const failedLogins = await db.FailedLogin.findAll({
      where: { ip },
      order: [["createdAt", "DESC"]],
      limit: 10,
    });

    // 获取安全警报
    const securityAlerts = await db.SecurityAlert.findAll({
      where: { ip },
      order: [["createdAt", "DESC"]],
      limit: 10,
    });

    res.json({
      success: true,
      ip,
      isInBlacklist,
      isInWhitelist,
      failedLogins,
      securityAlerts,
    });
  } catch (error) {
    console.error("检查IP状态失败:", error);
    res.status(500).json({
      success: false,
      error: `检查IP状态失败: ${error.message}`,
    });
  }
});

module.exports = router;
