/**
 * 管理员订阅路由
 * 提供用户订阅管理相关的API
 */
const express = require('express');
const router = express.Router();
const db = require('../../models');
const { adminOnly } = require('../../middleware/adminMiddleware');
const { authenticate } = require('../../middleware/authMiddleware');
const { Op } = require('sequelize');

/**
 * 获取订阅列表
 * GET /api/admin/subscriptions
 */
router.get('/', authenticate, adminOnly, async (req, res) => {
  try {
    const { page = 1, pageSize = 20, userQuery, level, status } = req.query;
    
    // 构建查询条件
    const where = {};
    
    if (level !== undefined) {
      where.level = level;
    }
    
    if (status) {
      where.status = status;
    }
    
    // 用户查询条件
    const userWhere = {};
    
    if (userQuery) {
      if (userQuery.includes('@')) {
        // 如果是邮箱
        userWhere.email = {
          [Op.like]: `%${userQuery}%`
        };
      } else {
        // 如果是用户ID
        userWhere.id = userQuery;
      }
    }
    
    // 查询订阅
    const { count, rows } = await db.UserSubscription.findAndCountAll({
      where,
      include: [
        {
          model: db.User,
          as: 'user',
          where: Object.keys(userWhere).length ? userWhere : undefined,
          attributes: ['id', 'email', 'username', 'level']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(pageSize),
      offset: (parseInt(page) - 1) * parseInt(pageSize),
      distinct: true // 确保计数时去重，避免因关联查询导致的重复计数
    });
    
    res.json({
      success: true,
      subscriptions: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    console.error('获取订阅列表失败:', error);
    res.status(500).json({
      success: false,
      error: '获取订阅列表失败'
    });
  }
});

/**
 * 创建订阅
 * POST /api/admin/subscriptions
 */
router.post('/', authenticate, adminOnly, async (req, res) => {
  const transaction = await db.sequelize.transaction();
  
  try {
    const { userId, level, startDate, endDate, paymentId } = req.body;
    
    // 验证必要字段
    if (!userId || level === undefined) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        error: '缺少必要参数: userId, level'
      });
    }
    
    // 检查用户是否存在
    const user = await db.User.findByPk(userId, { transaction });
    
    if (!user) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }
    
    // 检查等级是否存在
    const levelObj = await db.UserLevel.findOne({
      where: { level },
      transaction
    });
    
    if (!levelObj) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        error: '等级不存在'
      });
    }
    
    // 将用户当前活跃订阅设为过期
    await db.UserSubscription.update(
      { status: 'expired' },
      {
        where: {
          userId,
          status: 'active'
        },
        transaction
      }
    );
    
    // 创建新订阅
    const subscription = await db.UserSubscription.create({
      userId,
      level,
      startDate: startDate || new Date(),
      endDate: endDate || null,
      status: 'active',
      paymentId
    }, { transaction });
    
    // 更新用户等级
    await user.update({ level }, { transaction });
    
    // 提交事务
    await transaction.commit();
    
    res.json({
      success: true,
      subscription
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error('创建订阅失败:', error);
    res.status(500).json({
      success: false,
      error: '创建订阅失败'
    });
  }
});

/**
 * 更新订阅状态
 * PUT /api/admin/subscriptions/:id/status
 */
router.put('/:id/status', authenticate, adminOnly, async (req, res) => {
  const transaction = await db.sequelize.transaction();
  
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    // 验证状态
    if (!status || !['active', 'expired', 'cancelled'].includes(status)) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        error: '无效的状态值'
      });
    }
    
    // 查找订阅
    const subscription = await db.UserSubscription.findByPk(id, {
      include: [
        {
          model: db.User,
          as: 'user'
        }
      ],
      transaction
    });
    
    if (!subscription) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        error: '订阅不存在'
      });
    }
    
    // 更新订阅状态
    await subscription.update({ status }, { transaction });
    
    // 如果状态变为非活跃，且用户当前等级与订阅等级相同，则降级用户
    if (status !== 'active' && subscription.user.level === subscription.level) {
      // 查找用户其他活跃订阅
      const activeSubscription = await db.UserSubscription.findOne({
        where: {
          userId: subscription.userId,
          status: 'active',
          id: { [Op.ne]: id }
        },
        order: [['level', 'DESC']],
        transaction
      });
      
      // 如果有其他活跃订阅，使用其等级；否则降为免费用户
      const newLevel = activeSubscription ? activeSubscription.level : 0;
      
      // 更新用户等级
      await subscription.user.update({ level: newLevel }, { transaction });
    }
    
    // 提交事务
    await transaction.commit();
    
    res.json({
      success: true,
      subscription
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error('更新订阅状态失败:', error);
    res.status(500).json({
      success: false,
      error: '更新订阅状态失败'
    });
  }
});

/**
 * 删除订阅
 * DELETE /api/admin/subscriptions/:id
 */
router.delete('/:id', authenticate, adminOnly, async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找订阅
    const subscription = await db.UserSubscription.findByPk(id);
    
    if (!subscription) {
      return res.status(404).json({
        success: false,
        error: '订阅不存在'
      });
    }
    
    // 删除订阅
    await subscription.destroy();
    
    res.json({
      success: true,
      message: '订阅已删除'
    });
  } catch (error) {
    console.error('删除订阅失败:', error);
    res.status(500).json({
      success: false,
      error: '删除订阅失败'
    });
  }
});

module.exports = router;
