/**
 * 认证路由
 * 处理认证相关的请求
 */
const express = require("express");
const router = express.Router();
const authController = require("../controllers/authController");
const { authenticate } = require("../middleware/authMiddleware");

/**
 * 发送验证码
 * POST /api/auth/send-code
 * @body {string} email - 邮箱地址
 * @body {string} type - 验证码类型（login, register, reset_password）
 */
router.post("/send-code", authController.sendCode);

/**
 * 验证码登录
 * POST /api/auth/verify-code
 * @body {string} email - 邮箱地址
 * @body {string} code - 验证码
 * @body {string} type - 验证码类型（login, register, reset_password）
 */
router.post("/verify-code", authController.verifyCode);

/**
 * 获取当前用户信息
 * GET /api/auth/me
 * @header {string} Authorization - Bearer token
 */
router.get("/me", authenticate, authController.getCurrentUser);

/**
 * 更新用户信息
 * PUT /api/auth/me
 * @header {string} Authorization - Bearer token
 * @body {string} username - 用户名
 * @body {Object} settings - 用户设置
 */
router.put("/me", authenticate, authController.updateUser);

module.exports = router;
