/**
 * 内容API路由
 * 提供素材内容的保存、加载和管理功能
 */
const express = require("express");
const router = express.Router();
const db = require("../models");
const crypto = require("crypto");
const { authenticate } = require("../middleware/authMiddleware");
const {
  checkFeaturePermission,
} = require("../middleware/featurePermissionMiddleware");
const authService = require("../services/authService");

// 自定义 nanoid 函数，生成 21 位随机 ID
function nanoid() {
  return crypto
    .randomBytes(16)
    .toString("base64")
    .replace(/[+/=]/g, "")
    .substring(0, 21);
}

/**
 * 获取用户的素材内容
 * GET /api/contents
 */
router.get(
  "/",
  authenticate,
  checkFeaturePermission("content_creation"),
  async (req, res) => {
    try {
      // 获取分页参数
      const page = parseInt(req.query.page) || 1;
      const pageSize = parseInt(req.query.pageSize) || 10;
      const search = req.query.search || '';
      const status = req.query.status || '';
      const sortBy = req.query.sortBy || 'created_at';
      const sortOrder = req.query.sortOrder || 'DESC';
      const filters = req.query.filters; // 过滤器筛选

      // 构建查询条件 - 只显示当前用户创建的内容
      const where = {
        userId: req.user.id, // 只显示用户自己的内容
      };

      // 搜索过滤
      if (search) {
        where[db.Sequelize.Op.or] = [
          { name: { [db.Sequelize.Op.like]: `%${search}%` } },
          { description: { [db.Sequelize.Op.like]: `%${search}%` } }
        ];
      }

      // 状态过滤
      if (status && status !== 'all') {
        where.status = status;
      }

      // 语言过滤
      const learningLanguage = req.query.learningLanguage;
      if (learningLanguage && learningLanguage !== 'all') {
        where.learningLanguage = learningLanguage;
      }

      // 构建排序字段映射
      const sortFieldMap = {
        'createdAt': 'created_at',
        'updatedAt': 'updated_at',
        'name': 'name',
        'status': 'status'
      };
      const orderField = sortFieldMap[sortBy] || 'created_at';

      // 构建include数组 - 包含过滤器
      const include = [{
        model: db.Filter,
        as: 'filters',
        through: { attributes: [] }, // 不返回关联表字段
        where: { isActive: true },
        required: false
      }];

      // 过滤器筛选
      if (filters) {
        const filterIds = filters.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
        if (filterIds.length > 0) {
          include.push({
            model: db.ContentFilter,
            as: 'filterAssociations',
            where: { filterId: filterIds },
            attributes: []
          });
        }
      }

      const { count, rows } = await db.Content.findAndCountAll({
        where,
        include,
        attributes: [
          "id",
          "name",
          "description",
          "thumbnailUrl",
          "tags",
          "keywords",
          "learningLanguage", // 添加学习语言字段
          "status",
          ["created_at", "createdAt"],
          ["updated_at", "updatedAt"],
        ],
        order: [[orderField, sortOrder.toUpperCase()]],
        limit: pageSize,
        offset: (page - 1) * pageSize,
        distinct: true // 避免JOIN导致的重复计数
      });

      // 为每个内容处理标签分组
      const processedRows = rows.map(content => {
        const contentData = content.toJSON();

        if (contentData.filters) {
          const groupedFilters = {};
          contentData.filters.forEach(filter => {
            const filterType = filter.type;
            if (!groupedFilters[filterType]) {
              groupedFilters[filterType] = [];
            }
            groupedFilters[filterType].push({
              id: filter.id,
              name: filter.name,
              key: filter.key,
              type: filter.type,
              description: filter.description
            });
          });
          contentData.filtersGrouped = groupedFilters;
        }

        return contentData;
      });

      res.json({
        success: true,
        data: processedRows,
        pagination: {
          page,
          pageSize,
          total: count,
        },
      });
    } catch (error) {
      console.error("获取素材列表失败:", error);
      res.status(500).json({
        success: false,
        error: `获取素材列表失败: ${error.message}`,
      });
    }
  }
);

/**
 * 获取单个素材内容（包含观看次数）
 * GET /api/contents/:id
 * 注意：此接口有条件认证，未登录用户只能访问已发布的内容
 */
router.get("/:id", async (req, res) => {
  try {
    // 检查是否有认证信息
    const authHeader = req.headers.authorization;
    const isAuthenticated = authHeader && authHeader.startsWith("Bearer ");

    // 构建查询选项 - 包含过滤器
    const queryOptions = {
      include: [{
        model: db.Filter,
        as: 'filters',
        through: { attributes: [] },
        where: { isActive: true },
        required: false
      }]
    };

    // 根据认证状态决定查询条件
    let content;

    if (isAuthenticated) {
      try {
        // 尝试验证令牌
        const token = authHeader.split(" ")[1];
        const result = await authService.verifyToken(token);

        if (result.success) {
          // 已认证用户可以访问所有内容
          content = await db.Content.findByPk(req.params.id, queryOptions);

          // 如果是草稿内容，检查用户是否有权限访问
          if (content && content.status === "draft") {
            // 实时查询用户角色
            const user = await db.User.findByPk(result.decoded.id, {
              attributes: ['id', 'role']
            });

            // 检查用户是否为内容所有者或管理员
            if (
              content.userId !== result.decoded.id &&
              (!user || user.role !== "admin")
            ) {
              // 非所有者且非管理员，只能访问已发布内容
              return res.status(403).json({
                success: false,
                error: "权限不足，您不是内容所有者",
              });
            }
          }
        } else {
          // 令牌无效，视为未认证用户
          content = await db.Content.findOne({
            where: {
              id: req.params.id,
              status: "published",
            },
            ...queryOptions
          });
        }
      } catch (error) {
        // 令牌验证出错，视为未认证用户
        console.error("令牌验证失败:", error);
        content = await db.Content.findOne({
          where: {
            id: req.params.id,
            status: "published",
          },
          ...queryOptions
        });
      }
    } else {
      // 未认证用户只能访问已发布的内容
      content = await db.Content.findOne({
        where: {
          id: req.params.id,
          status: "published",
        },
        ...queryOptions
      });
    }

    if (!content) {
      return res.status(404).json({
        success: false,
        error: "内容不存在或未发布",
      });
    }

    // 获取观看次数
    const viewCount = await db.ContentView.count({
      where: { contentId: req.params.id }
    });

    // 处理通用标签数据
    let responseData = {
      ...content.toJSON(),
      viewCount,
    };

    // 处理关键词数据
    if (responseData.keywords) {
      try {
        responseData.keywords = JSON.parse(responseData.keywords);
      } catch (e) {
        responseData.keywords = [];
      }
    } else {
      responseData.keywords = [];
    }

    // 处理过滤器，按类型分组
    if (content.filters) {
      const groupedFilters = {};
      content.filters.forEach(filter => {
        const filterType = filter.type;
        if (!groupedFilters[filterType]) {
          groupedFilters[filterType] = [];
        }
        groupedFilters[filterType].push({
          id: filter.id,
          name: filter.name,
          key: filter.key,
          type: filter.type,
          description: filter.description
        });
      });
      responseData.filtersGrouped = groupedFilters;
    }

    res.json({
      success: true,
      content: responseData,
    });
  } catch (error) {
    console.error(`获取素材失败 (ID: ${req.params.id}):`, error);
    res.status(500).json({
      success: false,
      error: `获取素材失败: ${error.message}`,
    });
  }
});

/**
 * 创建新素材内容
 * POST /api/contents
 */
router.post(
  "/",
  authenticate,
  checkFeaturePermission("content_creation"),
  async (req, res) => {
    try {
      const { name, description, configJson, thumbnailUrl, tags, learningLanguage, filterIds, keywords } = req.body;

      if (!name) {
        return res.status(400).json({
          success: false,
          error: "缺少必要参数: name",
        });
      }

      if (!configJson) {
        return res.status(400).json({
          success: false,
          error: "缺少必要参数: configJson",
        });
      }

      // 生成nanoid作为ID
      const id = nanoid();

      // 从认证中间件获取用户ID
      const userId = req.user.id;

      // 从请求头获取用户学习语言作为默认值
      const defaultLearningLanguage = req.headers['x-user-language'] || 'ja';

      // 使用事务确保数据一致性
      const result = await db.sequelize.transaction(async (t) => {
        // 创建内容
        const content = await db.Content.create({
          id,
          name,
          description,
          configJson,
          thumbnailUrl,
          userId, // 使用从JWT令牌中获取的用户ID
          tags,
          keywords: keywords ? JSON.stringify(keywords) : null,
          learningLanguage: learningLanguage || defaultLearningLanguage, // 使用传入的语言或用户默认语言
          status: req.body.status || "draft",
        }, { transaction: t });

        // 如果有过滤器，创建关联
        if (filterIds && Array.isArray(filterIds) && filterIds.length > 0) {
          // 验证过滤器是否存在且有效
          const validFilters = await db.Filter.findAll({
            where: {
              id: filterIds,
              isActive: true
            },
            transaction: t
          });

          if (validFilters.length !== filterIds.length) {
            throw new Error('部分过滤器不存在或已禁用');
          }

          // 创建内容-过滤器关联
          await db.ContentFilter.updateContentFilters(content.id, filterIds, t);
        }

        return content;
      });

      res.status(201).json({
        success: true,
        content: result,
      });
    } catch (error) {
      console.error("创建素材失败:", error);
      res.status(500).json({
        success: false,
        error: `创建素材失败: ${error.message}`,
      });
    }
  }
);

/**
 * 更新素材内容
 * PUT /api/contents/:id
 */
router.put(
  "/:id",
  authenticate,
  checkFeaturePermission("content_creation"),
  async (req, res) => {
    try {
      const { name, description, configJson, thumbnailUrl, tags, learningLanguage, filterIds, keywords } = req.body;

      const content = await db.Content.findByPk(req.params.id);

      if (!content) {
        return res.status(404).json({
          success: false,
          error: "素材不存在",
        });
      }

      // 实时查询用户角色
      const user = await db.User.findByPk(req.user.id, {
        attributes: ['id', 'role']
      });

      // 检查用户是否为内容所有者或管理员
      if (
        content.userId &&
        content.userId !== req.user.id &&
        (!user || user.role !== "admin")
      ) {
        return res.status(403).json({
          success: false,
          error: "权限不足，您不是内容所有者",
        });
      }

      // 使用事务确保数据一致性
      const result = await db.sequelize.transaction(async (t) => {
        // 准备更新数据
        const updateData = {
          name: name || content.name,
          description:
            description !== undefined ? description : content.description,
          configJson: configJson || content.configJson,
          thumbnailUrl:
            thumbnailUrl !== undefined ? thumbnailUrl : content.thumbnailUrl,
          tags: tags !== undefined ? tags : content.tags,
          keywords: keywords !== undefined ? (keywords ? JSON.stringify(keywords) : null) : content.keywords,
          learningLanguage: learningLanguage !== undefined ? learningLanguage : content.learningLanguage,
          userId: content.userId || req.user.id, // 如果内容没有所有者，设置为当前用户
        };

        // 添加status字段
        if (req.body.status) {
          updateData.status = req.body.status;
        }

        // 更新素材
        await content.update(updateData, { transaction: t });

        // 如果传入了过滤器，更新过滤器关联
        if (filterIds !== undefined) {
          if (Array.isArray(filterIds) && filterIds.length > 0) {
            // 验证过滤器是否存在且有效
            const validFilters = await db.Filter.findAll({
              where: {
                id: filterIds,
                isActive: true
              },
              transaction: t
            });

            if (validFilters.length !== filterIds.length) {
              throw new Error('部分过滤器不存在或已禁用');
            }
          }

          // 更新内容-过滤器关联（包括清空的情况）
          await db.ContentFilter.updateContentFilters(content.id, filterIds || [], t);
        }

        return content;
      });

      res.json({
        success: true,
        content: result,
      });
    } catch (error) {
      console.error(`更新素材失败 (ID: ${req.params.id}):`, error);
      res.status(500).json({
        success: false,
        error: `更新素材失败: ${error.message}`,
      });
    }
  }
);

/**
 * 删除素材内容
 * DELETE /api/contents/:id
 */
router.delete("/:id", authenticate, async (req, res) => {
  try {
    const content = await db.Content.findByPk(req.params.id);

    if (!content) {
      return res.status(404).json({
        success: false,
        error: "素材不存在",
      });
    }

    // 实时查询用户角色
    const user = await db.User.findByPk(req.user.id, {
      attributes: ['id', 'role']
    });

    // 检查用户是否为内容所有者或管理员
    if (
      content.userId &&
      content.userId !== req.user.id &&
      (!user || user.role !== "admin")
    ) {
      return res.status(403).json({
        success: false,
        error: "权限不足，您不是内容所有者",
      });
    }

    await content.destroy();

    res.json({
      success: true,
      message: "素材已删除",
    });
  } catch (error) {
    console.error(`删除素材失败 (ID: ${req.params.id}):`, error);
    res.status(500).json({
      success: false,
      error: `删除素材失败: ${error.message}`,
    });
  }
});

/**
 * 发布内容（上线）
 * PUT /api/contents/:id/publish
 */
router.put("/:id/publish", authenticate, async (req, res) => {
  try {
    const content = await db.Content.findByPk(req.params.id);

    if (!content) {
      return res.status(404).json({
        success: false,
        error: "素材不存在",
      });
    }

    // 实时查询用户角色
    const user = await db.User.findByPk(req.user.id, {
      attributes: ['id', 'role']
    });

    // 检查用户是否为内容所有者或管理员
    if (
      content.userId &&
      content.userId !== req.user.id &&
      (!user || user.role !== "admin")
    ) {
      return res.status(403).json({
        success: false,
        error: "权限不足，您不是内容所有者",
      });
    }

    // 更新状态为已发布
    await content.update({
      status: "published",
      userId: content.userId || req.user.id, // 如果内容没有所有者，设置为当前用户
    });

    res.json({
      success: true,
      content,
      message: "内容已发布",
    });
  } catch (error) {
    console.error(`发布内容失败 (ID: ${req.params.id}):`, error);
    res.status(500).json({
      success: false,
      error: `发布内容失败: ${error.message}`,
    });
  }
});

/**
 * 下架内容
 * PUT /api/contents/:id/unpublish
 */
router.put("/:id/unpublish", authenticate, async (req, res) => {
  try {
    const content = await db.Content.findByPk(req.params.id);

    if (!content) {
      return res.status(404).json({
        success: false,
        error: "素材不存在",
      });
    }

    // 实时查询用户角色
    const user = await db.User.findByPk(req.user.id, {
      attributes: ['id', 'role']
    });

    // 检查用户是否为内容所有者或管理员
    if (
      content.userId &&
      content.userId !== req.user.id &&
      (!user || user.role !== "admin")
    ) {
      return res.status(403).json({
        success: false,
        error: "权限不足，您不是内容所有者",
      });
    }

    // 更新状态为草稿
    await content.update({
      status: "draft",
      userId: content.userId || req.user.id, // 如果内容没有所有者，设置为当前用户
    });

    res.json({
      success: true,
      content,
      message: "内容已下架",
    });
  } catch (error) {
    console.error(`下架内容失败 (ID: ${req.params.id}):`, error);
    res.status(500).json({
      success: false,
      error: `下架内容失败: ${error.message}`,
    });
  }
});

/**
 * 记录内容观看
 * POST /api/contents/:id/views
 */
router.post("/:id/views", async (req, res) => {
  try {
    const { viewDuration = 0 } = req.body;
    const contentId = req.params.id;
    
    // 获取用户信息（如果已登录）
    let userId = null;
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith("Bearer ")) {
      try {
        const token = authHeader.split(" ")[1];
        const result = await authService.verifyToken(token);
        if (result.success) {
          userId = result.decoded.id;
        }
      } catch (error) {
        // 忽略认证错误，继续记录观看
      }
    }

    // 获取IP地址
    const ipAddress = req.ip || req.connection.remoteAddress;

    // 记录观看
    await db.ContentView.create({
      contentId,
      userId,
      ipAddress,
      viewDuration,
    });

    res.json({
      success: true,
      message: "观看记录已保存",
    });
  } catch (error) {
    console.error(`记录观看失败 (ID: ${req.params.id}):`, error);
    res.status(500).json({
      success: false,
      error: `记录观看失败: ${error.message}`,
    });
  }
});

/**
 * 为内容设置通用标签
 * PUT /api/contents/:id/general-tags
 */
router.put('/:id/general-tags',
  authenticate,
  checkFeaturePermission('content_creation'),
  async (req, res) => {
    try {
      const { id: contentId } = req.params;
      const { tagIds } = req.body;

      // 检查内容是否存在且用户有权限
      const content = await db.Content.findByPk(contentId);
      if (!content) {
        return res.status(404).json({
          success: false,
          error: '内容不存在'
        });
      }

      // 检查用户权限（只能操作自己的内容，除非是管理员）
      if (content.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          error: '权限不足，只能操作自己的内容'
        });
      }

      // 验证标签ID
      if (!Array.isArray(tagIds)) {
        return res.status(400).json({
          success: false,
          error: 'tagIds必须是数组'
        });
      }

      // 这个API已废弃，请使用新的过滤器系统
      res.status(410).json({
        success: false,
        error: '此API已废弃，请使用新的过滤器系统'
      });
    } catch (error) {
      console.error('更新内容标签失败:', error);
      res.status(500).json({
        success: false,
        error: '更新内容标签失败'
      });
    }
  }
);

module.exports = router;
