/**
 * 收藏API路由
 * 提供收藏内容的添加、删除和查询功能
 */
const express = require("express");
const router = express.Router();
const db = require("../models");
const { nanoid } = require("nanoid");
const { authenticate } = require("../middleware/authMiddleware");

/**
 * 获取用户收藏列表
 * GET /api/favorites
 */
router.get("/", authenticate, async (req, res) => {
  try {
    const favorites = await db.Favorite.findAll({
      where: {
        userId: req.user.id,
      },
      include: [
        {
          model: db.Content,
          as: "content",
          attributes: [
            "id",
            "name",
            "description",
            "thumbnailUrl",
            "tags",
            "status",
            ["created_at", "createdAt"],
            ["updated_at", "updatedAt"],
            [
              db.sequelize.literal(
                `(SELECT COUNT(*) FROM content_views WHERE content_views.content_id = content.id)`
              ),
              "viewCount"
            ],
          ],
          include: [
            {
              model: db.User,
              as: "creator",
              attributes: ["id", "username"],
            },
          ],
        },
      ],
      order: [["created_at", "DESC"]],
    });

    // 处理收藏数据，保留所有记录但标记状态
    const contents = favorites.map((favorite) => {
      if (favorite.content === null) {
        // 内容已被删除
        return {
          id: favorite.contentId,
          name: '内容已删除',
          description: '该内容已被作者删除',
          thumbnailUrl: null,
          tags: null,
          status: 'deleted',
          isAvailable: false,
          unavailableReason: 'deleted',
          createdAt: favorite.createdAt,
          updatedAt: favorite.updatedAt,
        };
      } else if (favorite.content.status !== 'published') {
        // 内容已下架
        return {
          ...favorite.content.toJSON(),
          isAvailable: false,
          unavailableReason: 'unpublished',
        };
      } else {
        // 内容正常可用
        return {
          ...favorite.content.toJSON(),
          isAvailable: true,
          unavailableReason: null,
        };
      }
    });

    res.json({
      success: true,
      favorites: contents,
    });
  } catch (error) {
    console.error("获取收藏列表失败:", error);
    res.status(500).json({
      success: false,
      error: `获取收藏列表失败: ${error.message}`,
    });
  }
});

/**
 * 添加收藏
 * POST /api/favorites
 */
router.post("/", authenticate, async (req, res) => {
  try {
    const { contentId } = req.body;

    if (!contentId) {
      return res.status(400).json({
        success: false,
        error: "缺少必要参数: contentId",
      });
    }

    // 检查内容是否存在
    const content = await db.Content.findByPk(contentId);
    if (!content) {
      return res.status(404).json({
        success: false,
        error: "内容不存在",
      });
    }

    // 检查是否已收藏
    const existingFavorite = await db.Favorite.findOne({
      where: {
        userId: req.user.id,
        contentId,
      },
    });

    if (existingFavorite) {
      return res.status(400).json({
        success: false,
        error: "已经收藏过该内容",
      });
    }

    // 创建收藏记录
    const favorite = await db.Favorite.create({
      id: nanoid(),
      userId: req.user.id,
      contentId,
    });

    res.status(201).json({
      success: true,
      favorite,
    });
  } catch (error) {
    console.error("添加收藏失败:", error);
    res.status(500).json({
      success: false,
      error: `添加收藏失败: ${error.message}`,
    });
  }
});

/**
 * 删除收藏
 * DELETE /api/favorites/:contentId
 */
router.delete("/:contentId", authenticate, async (req, res) => {
  try {
    const { contentId } = req.params;

    // 查找收藏记录
    const favorite = await db.Favorite.findOne({
      where: {
        userId: req.user.id,
        contentId,
      },
    });

    if (!favorite) {
      return res.status(404).json({
        success: false,
        error: "收藏记录不存在",
      });
    }

    // 删除收藏记录
    await favorite.destroy();

    res.json({
      success: true,
      message: "收藏已删除",
    });
  } catch (error) {
    console.error("删除收藏失败:", error);
    res.status(500).json({
      success: false,
      error: `删除收藏失败: ${error.message}`,
    });
  }
});

/**
 * 检查内容是否已收藏
 * GET /api/favorites/check/:contentId
 */
router.get("/check/:contentId", authenticate, async (req, res) => {
  try {
    const { contentId } = req.params;

    // 查找收藏记录
    const favorite = await db.Favorite.findOne({
      where: {
        userId: req.user.id,
        contentId,
      },
    });

    res.json({
      success: true,
      isFavorite: !!favorite,
    });
  } catch (error) {
    console.error("检查收藏状态失败:", error);
    res.status(500).json({
      success: false,
      error: `检查收藏状态失败: ${error.message}`,
    });
  }
});

module.exports = router;
