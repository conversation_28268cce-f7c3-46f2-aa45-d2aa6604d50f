/**
 * 功能特性路由
 * 提供功能特性相关的API
 */
const express = require("express");
const router = express.Router();
const db = require("../models");
const { authenticate } = require("../middleware/authMiddleware");
const { checkUserLevel } = require("../middleware/userLevelMiddleware");

// 获取所有功能特性
router.get("/", async (req, res) => {
  try {
    // 从数据库获取所有功能特性
    const featureFlags = await db.FeatureFlag.findAll({
      attributes: ["featureKey", "displayName", "description"],
    });

    // 将功能特性转换为前端需要的格式
    const features = featureFlags.map((flag) => {
      // 使用数据库中的显示名称，如果没有则生成一个
      let name = flag.displayName;

      // 如果数据库中没有显示名称，则根据功能键名生成一个
      if (!name) {
        // 将下划线分隔的键名转换为空格分隔的名称
        name = flag.featureKey.replace(/_/g, " ");
        // 首字母大写
        name = name.charAt(0).toUpperCase() + name.slice(1);
      }

      return {
        key: flag.featureKey,
        name: name,
        description: flag.description || `访问${name}功能`,
      };
    });

    res.json({
      success: true,
      features,
    });
  } catch (error) {
    console.error("获取功能特性失败:", error);
    res.status(500).json({
      success: false,
      error: "获取功能特性失败",
    });
  }
});

// 获取等级功能映射
router.get("/level-mapping", async (req, res) => {
  try {
    // 从数据库获取所有等级权限
    const levelPermissions = await db.LevelPermission.findAll({
      attributes: ["level", "featureKey"],
    });

    // 将等级权限转换为前端需要的格式
    const levelFeatures = {};

    // 处理每个等级权限
    levelPermissions.forEach((permission) => {
      const level = permission.level;
      const featureKey = permission.featureKey;

      // 如果等级不存在，创建一个空数组
      if (!levelFeatures[level]) {
        levelFeatures[level] = [];
      }

      // 添加功能到等级
      if (!levelFeatures[level].includes(featureKey)) {
        levelFeatures[level].push(featureKey);
      }
    });

    // 确保每个等级都有 player_access 权限（如果数据库中没有）
    for (const level in levelFeatures) {
      if (!levelFeatures[level].includes("player_access")) {
        levelFeatures[level].push("player_access");
      }
    }

    // 确保至少有等级0，并且有 player_access 权限
    if (!levelFeatures[0]) {
      levelFeatures[0] = ["player_access"];
    }

    res.json({
      success: true,
      levelFeatures,
    });
  } catch (error) {
    console.error("获取等级功能映射失败:", error);
    res.status(500).json({
      success: false,
      error: "获取等级功能映射失败",
    });
  }
});

module.exports = router;
