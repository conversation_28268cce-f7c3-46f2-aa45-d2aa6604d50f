const express = require('express');
const router = express.Router();
const db = require('../models');
const { Filter, ContentFilter, FilterType } = db;

/**
 * 获取所有过滤器（按类型分组）
 * GET /api/filters
 * Query参数：
 * - language: 语言代码（可选）
 * - type: 过滤器类型（可选）
 */
router.get('/', async (req, res) => {
  try {
    const { language, type } = req.query;

    let filters;
    
    if (type) {
      // 获取特定类型的过滤器
      filters = await Filter.getByType(type, language);
      return res.json({
        success: true,
        data: filters
      });
    } else {
      // 获取分组的过滤器
      const groupedFilters = await Filter.getGrouped(language);
      return res.json({
        success: true,
        data: groupedFilters
      });
    }
  } catch (error) {
    console.error('获取过滤器失败:', error);
    res.status(500).json({
      success: false,
      message: '获取过滤器失败',
      error: error.message
    });
  }
});

/**
 * 获取语言等级过滤器
 * GET /api/filters/language-levels
 * Query参数：
 * - language: 语言代码（必需）
 */
router.get('/language-levels', async (req, res) => {
  try {
    const { language } = req.query;

    if (!language) {
      return res.status(400).json({
        success: false,
        message: '语言参数是必需的'
      });
    }

    const languageLevels = await Filter.getLanguageLevels(language);
    
    res.json({
      success: true,
      data: languageLevels
    });
  } catch (error) {
    console.error('获取语言等级过滤器失败:', error);
    res.status(500).json({
      success: false,
      message: '获取语言等级过滤器失败',
      error: error.message
    });
  }
});

/**
 * 获取内容类型过滤器
 * GET /api/filters/content-types
 */
router.get('/content-types', async (req, res) => {
  try {
    const contentTypes = await Filter.getContentTypes();
    
    res.json({
      success: true,
      data: contentTypes
    });
  } catch (error) {
    console.error('获取内容类型过滤器失败:', error);
    res.status(500).json({
      success: false,
      message: '获取内容类型过滤器失败',
      error: error.message
    });
  }
});

/**
 * 获取主题过滤器
 * GET /api/filters/topics
 */
router.get('/topics', async (req, res) => {
  try {
    const topics = await Filter.getTopics();

    res.json({
      success: true,
      data: topics
    });
  } catch (error) {
    console.error('获取主题过滤器失败:', error);
    res.status(500).json({
      success: false,
      message: '获取主题过滤器失败',
      error: error.message
    });
  }
});

/**
 * 获取教材过滤器
 * GET /api/filters/materials
 * Query参数：
 * - language: 语言代码（可选）
 */
router.get('/materials', async (req, res) => {
  try {
    const { language } = req.query;
    const materials = await Filter.getMaterials(language);

    res.json({
      success: true,
      data: materials
    });
  } catch (error) {
    console.error('获取教材过滤器失败:', error);
    res.status(500).json({
      success: false,
      message: '获取教材过滤器失败',
      error: error.message
    });
  }
});

/**
 * 根据过滤器获取内容
 * GET /api/filters/contents
 * Query参数：
 * - filters: 过滤器ID数组（逗号分隔）
 * - page: 页码（默认1）
 * - pageSize: 每页数量（默认20）
 * - sortBy: 排序字段（默认created_at）
 * - sortOrder: 排序方向（默认DESC）
 */
router.get('/contents', async (req, res) => {
  try {
    const { 
      filters, 
      page = 1, 
      pageSize = 20, 
      sortBy = 'created_at', 
      sortOrder = 'DESC' 
    } = req.query;

    if (!filters) {
      return res.status(400).json({
        success: false,
        message: '过滤器参数是必需的'
      });
    }

    const filterIds = filters.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
    
    if (filterIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '无效的过滤器ID'
      });
    }

    const result = await ContentFilter.getContentsByFilters(filterIds, {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      sortBy,
      sortOrder
    });

    res.json({
      success: true,
      data: {
        contents: result.rows.map(item => item.content),
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total: result.count,
          totalPages: Math.ceil(result.count / pageSize)
        }
      }
    });
  } catch (error) {
    console.error('根据过滤器获取内容失败:', error);
    res.status(500).json({
      success: false,
      message: '根据过滤器获取内容失败',
      error: error.message
    });
  }
});

/**
 * 获取内容的过滤器
 * GET /api/filters/content/:contentId
 */
router.get('/content/:contentId', async (req, res) => {
  try {
    const { contentId } = req.params;

    const contentFilters = await ContentFilter.getContentFilters(contentId);
    const filters = contentFilters.map(cf => cf.filter);

    res.json({
      success: true,
      data: filters
    });
  } catch (error) {
    console.error('获取内容过滤器失败:', error);
    res.status(500).json({
      success: false,
      message: '获取内容过滤器失败',
      error: error.message
    });
  }
});

/**
 * 为内容设置过滤器
 * POST /api/filters/content/:contentId
 * Body: { filterIds: [1, 2, 3] }
 */
router.post('/content/:contentId', async (req, res) => {
  try {
    const { contentId } = req.params;
    const { filterIds } = req.body;

    if (!Array.isArray(filterIds)) {
      return res.status(400).json({
        success: false,
        message: 'filterIds必须是数组'
      });
    }

    await ContentFilter.updateContentFilters(contentId, filterIds);

    res.json({
      success: true,
      message: '内容过滤器更新成功'
    });
  } catch (error) {
    console.error('设置内容过滤器失败:', error);
    res.status(500).json({
      success: false,
      message: '设置内容过滤器失败',
      error: error.message
    });
  }
});

// ==================== 后台管理API ====================

/**
 * 创建新过滤器
 * POST /api/filters
 * Body: { name, key, type, language, description, color, icon, sortOrder }
 */
router.post('/', async (req, res) => {
  try {
    const { name, key, type, language, description, sortOrder } = req.body;

    // 验证必需字段
    if (!name || !key || !type) {
      return res.status(400).json({
        success: false,
        message: '名称、标识符和类型是必需的'
      });
    }

    // 检查key是否已存在
    const existingFilter = await Filter.findOne({ where: { key } });
    if (existingFilter) {
      return res.status(400).json({
        success: false,
        message: '过滤器标识符已存在'
      });
    }

    const newFilter = await Filter.create({
      name,
      key,
      type,
      language: language || null,
      description: description || null,
      sortOrder: sortOrder || 0,
      isActive: true,
      isSystem: false
    });

    res.json({
      success: true,
      data: newFilter,
      message: '过滤器创建成功'
    });
  } catch (error) {
    console.error('创建过滤器失败:', error);
    res.status(500).json({
      success: false,
      message: '创建过滤器失败',
      error: error.message
    });
  }
});

/**
 * 更新过滤器
 * PUT /api/filters/:id
 * Body: { name, description, color, icon, sortOrder, isActive }
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, sortOrder, isActive } = req.body;

    const filter = await Filter.findByPk(id);
    if (!filter) {
      return res.status(404).json({
        success: false,
        message: '过滤器不存在'
      });
    }

    // 系统过滤器只能更新部分字段
    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (sortOrder !== undefined) updateData.sortOrder = sortOrder;
    if (isActive !== undefined) updateData.isActive = isActive;

    await filter.update(updateData);

    res.json({
      success: true,
      data: filter,
      message: '过滤器更新成功'
    });
  } catch (error) {
    console.error('更新过滤器失败:', error);
    res.status(500).json({
      success: false,
      message: '更新过滤器失败',
      error: error.message
    });
  }
});

/**
 * 删除过滤器
 * DELETE /api/filters/:id
 */
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const filter = await Filter.findByPk(id);
    if (!filter) {
      return res.status(404).json({
        success: false,
        message: '过滤器不存在'
      });
    }

    // 系统过滤器不能删除
    if (filter.isSystem) {
      return res.status(400).json({
        success: false,
        message: '系统过滤器不能删除'
      });
    }

    await filter.destroy();

    res.json({
      success: true,
      message: '过滤器删除成功'
    });
  } catch (error) {
    console.error('删除过滤器失败:', error);
    res.status(500).json({
      success: false,
      message: '删除过滤器失败',
      error: error.message
    });
  }
});

/**
 * 批量更新过滤器状态
 * PATCH /api/filters/batch-status
 * Body: { ids: [1, 2, 3], isActive: true/false }
 */
router.patch('/batch-status', async (req, res) => {
  try {
    const { ids, isActive } = req.body;

    if (!Array.isArray(ids) || typeof isActive !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: '参数格式错误'
      });
    }

    await Filter.update(
      { isActive },
      { where: { id: ids } }
    );

    res.json({
      success: true,
      message: `批量${isActive ? '启用' : '禁用'}过滤器成功`
    });
  } catch (error) {
    console.error('批量更新过滤器状态失败:', error);
    res.status(500).json({
      success: false,
      message: '批量更新过滤器状态失败',
      error: error.message
    });
  }
});

// ==================== 过滤器类型管理API ====================

/**
 * 获取所有过滤器类型
 * GET /api/filters/types
 */
router.get('/types', async (req, res) => {
  try {
    const filterTypes = await FilterType.getActiveTypes();

    res.json({
      success: true,
      data: filterTypes
    });
  } catch (error) {
    console.error('获取过滤器类型失败:', error);
    res.status(500).json({
      success: false,
      message: '获取过滤器类型失败',
      error: error.message
    });
  }
});

/**
 * 创建新的过滤器类型
 * POST /api/filters/types
 * Body: { type, name, description, icon, sortOrder }
 */
router.post('/types', async (req, res) => {
  try {
    const { type, name, description, sortOrder } = req.body;

    // 验证必需字段
    if (!type || !name) {
      return res.status(400).json({
        success: false,
        message: '类型标识和名称是必需的'
      });
    }

    // 检查类型是否已存在
    const exists = await FilterType.typeExists(type);
    if (exists) {
      return res.status(400).json({
        success: false,
        message: '过滤器类型已存在'
      });
    }

    const newFilterType = await FilterType.create({
      type,
      name,
      description: description || null,
      sortOrder: sortOrder || 0,
      isActive: true,
      isSystem: false
    });

    res.json({
      success: true,
      data: newFilterType,
      message: '过滤器类型创建成功'
    });
  } catch (error) {
    console.error('创建过滤器类型失败:', error);
    res.status(500).json({
      success: false,
      message: '创建过滤器类型失败',
      error: error.message
    });
  }
});

/**
 * 更新过滤器类型
 * PUT /api/filters/types/:id
 * Body: { name, description, icon, sortOrder, isActive }
 */
router.put('/types/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, sortOrder, isActive } = req.body;

    const filterType = await FilterType.findByPk(id);
    if (!filterType) {
      return res.status(404).json({
        success: false,
        message: '过滤器类型不存在'
      });
    }

    // 系统类型只能更新部分字段
    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (sortOrder !== undefined) updateData.sortOrder = sortOrder;
    if (isActive !== undefined) updateData.isActive = isActive;

    await filterType.update(updateData);

    res.json({
      success: true,
      data: filterType,
      message: '过滤器类型更新成功'
    });
  } catch (error) {
    console.error('更新过滤器类型失败:', error);
    res.status(500).json({
      success: false,
      message: '更新过滤器类型失败',
      error: error.message
    });
  }
});

/**
 * 删除过滤器类型
 * DELETE /api/filters/types/:id
 */
router.delete('/types/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const filterType = await FilterType.findByPk(id);
    if (!filterType) {
      return res.status(404).json({
        success: false,
        message: '过滤器类型不存在'
      });
    }

    // 系统类型不能删除
    if (filterType.isSystem) {
      return res.status(400).json({
        success: false,
        message: '系统过滤器类型不能删除'
      });
    }

    // 检查是否有过滤器使用此类型
    const filterCount = await Filter.count({
      where: { type: filterType.type }
    });

    if (filterCount > 0) {
      return res.status(400).json({
        success: false,
        message: '该过滤器类型下还有过滤器，无法删除'
      });
    }

    await filterType.destroy();

    res.json({
      success: true,
      message: '过滤器类型删除成功'
    });
  } catch (error) {
    console.error('删除过滤器类型失败:', error);
    res.status(500).json({
      success: false,
      message: '删除过滤器类型失败',
      error: error.message
    });
  }
});

module.exports = router;
