/**
 * 关键词API路由
 * 提供关键词提取功能
 */
const express = require("express");
const router = express.Router();
const { authenticate } = require("../middleware/authMiddleware");
const keywordExtractionService = require("../services/keywordExtractionService");

/**
 * 智能提取关键词
 * POST /api/keywords/extract
 */
router.post("/extract", authenticate, async (req, res) => {
  try {
    const { text } = req.body;

    if (!text) {
      return res.status(400).json({
        success: false,
        error: "缺少文本内容"
      });
    }

    const keywords = await keywordExtractionService.extractKeywords(text);

    res.json({
      success: true,
      keywords
    });
  } catch (error) {
    console.error("提取关键词失败:", error);
    res.status(500).json({
      success: false,
      error: `提取关键词失败: ${error.message}`
    });
  }
});

module.exports = router;