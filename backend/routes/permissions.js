/**
 * 权限路由
 * 提供权限检查和管理相关的API
 */
const express = require('express');
const router = express.Router();
const permissionService = require('../services/permissionService');
const { authenticate } = require('../middleware/authMiddleware');
const { adminOnly } = require('../middleware/adminMiddleware');
const db = require('../models');

/**
 * 检查当前用户是否有权限访问特定功能
 * GET /api/permissions/check/:featureKey
 */
router.get('/check/:featureKey', authenticate, async (req, res) => {
  try {
    const { featureKey } = req.params;
    
    // 检查权限
    const hasPermission = await permissionService.checkPermission(req.user.id, featureKey);
    
    res.json({
      success: true,
      hasPermission
    });
  } catch (error) {
    console.error('检查权限失败:', error);
    res.status(500).json({
      success: false,
      error: '检查权限失败'
    });
  }
});

/**
 * 获取当前用户的所有权限
 * GET /api/permissions/me
 */
router.get('/me', authenticate, async (req, res) => {
  try {
    // 获取用户可用的所有功能
    const features = await permissionService.getUserFeatures(req.user.id);
    
    res.json({
      success: true,
      features
    });
  } catch (error) {
    console.error('获取用户权限失败:', error);
    res.status(500).json({
      success: false,
      error: '获取用户权限失败'
    });
  }
});

/**
 * 获取当前用户的功能使用情况
 * GET /api/permissions/usage/:featureKey
 */
router.get('/usage/:featureKey', authenticate, async (req, res) => {
  try {
    const { featureKey } = req.params;
    
    // 获取功能使用情况
    const usage = await permissionService.getFeatureUsage(req.user.id, featureKey);
    
    if (usage.error) {
      return res.status(400).json({
        success: false,
        error: usage.error
      });
    }
    
    res.json({
      success: true,
      ...usage
    });
  } catch (error) {
    console.error('获取功能使用情况失败:', error);
    res.status(500).json({
      success: false,
      error: '获取功能使用情况失败'
    });
  }
});

/**
 * 获取所有等级权限（管理员）
 * GET /api/permissions/levels
 */
router.get('/levels', authenticate, adminOnly, async (req, res) => {
  try {
    // 获取所有等级权限
    const levelPermissions = await db.LevelPermission.findAll({
      order: [
        ['level', 'ASC'],
        ['featureKey', 'ASC']
      ]
    });
    
    res.json({
      success: true,
      permissions: levelPermissions
    });
  } catch (error) {
    console.error('获取等级权限失败:', error);
    res.status(500).json({
      success: false,
      error: '获取等级权限失败'
    });
  }
});

/**
 * 添加等级权限（管理员）
 * POST /api/permissions/levels
 */
router.post('/levels', authenticate, adminOnly, async (req, res) => {
  try {
    const { level, featureKey } = req.body;
    
    if (level === undefined || !featureKey) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数: level, featureKey'
      });
    }
    
    // 检查是否已存在
    const existingPermission = await db.LevelPermission.findOne({
      where: { level, featureKey }
    });
    
    if (existingPermission) {
      return res.status(400).json({
        success: false,
        error: '该等级权限已存在'
      });
    }
    
    // 创建等级权限
    const permission = await db.LevelPermission.create({
      level,
      featureKey
    });
    
    res.json({
      success: true,
      permission
    });
  } catch (error) {
    console.error('添加等级权限失败:', error);
    res.status(500).json({
      success: false,
      error: '添加等级权限失败'
    });
  }
});

/**
 * 删除等级权限（管理员）
 * DELETE /api/permissions/levels/:id
 */
router.delete('/levels/:id', authenticate, adminOnly, async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找权限
    const permission = await db.LevelPermission.findByPk(id);
    
    if (!permission) {
      return res.status(404).json({
        success: false,
        error: '权限不存在'
      });
    }
    
    // 删除权限
    await permission.destroy();
    
    res.json({
      success: true,
      message: '权限已删除'
    });
  } catch (error) {
    console.error('删除等级权限失败:', error);
    res.status(500).json({
      success: false,
      error: '删除等级权限失败'
    });
  }
});

module.exports = router;
