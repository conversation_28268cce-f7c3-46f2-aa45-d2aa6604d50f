/**
 * 播放策略模板API路由
 * 提供模板的CRUD操作接口
 */
const express = require("express");
const router = express.Router();
const playbackTemplateController = require("../controllers/playbackTemplateController");
const {
  authenticate,
  optionalAuthenticate,
} = require("../middleware/authMiddleware");

/**
 * 获取模板列表
 * GET /api/templates
 * 查询参数：
 * - type: 模板类型 (system|user)
 * - isPublic: 是否公开 (true|false)
 * 注意：未登录用户可以获取系统模板，登录用户可以获取自己的模板+系统模板
 */
router.get("/", optionalAuthenticate, playbackTemplateController.getTemplates);

/**
 * 获取单个模板
 * GET /api/templates/:id
 */
router.get("/:id", playbackTemplateController.getTemplateById);

/**
 * 创建模板
 * POST /api/templates
 * 需要认证
 */
router.post("/", authenticate, playbackTemplateController.createTemplate);

/**
 * 更新模板
 * PUT /api/templates/:id
 * 需要认证，只有创建者可以更新
 */
router.put("/:id", authenticate, playbackTemplateController.updateTemplate);

/**
 * 删除模板
 * DELETE /api/templates/:id
 * 需要认证，只有创建者可以删除
 */
router.delete("/:id", authenticate, playbackTemplateController.deleteTemplate);

/**
 * 使用模板（增加使用次数）
 * POST /api/templates/:id/use
 */
router.post("/:id/use", playbackTemplateController.useTemplate);

/**
 * 复制模板
 * POST /api/templates/:id/duplicate
 * 需要认证
 */
router.post(
  "/:id/duplicate",
  authenticate,
  playbackTemplateController.duplicateTemplate
);

module.exports = router;
