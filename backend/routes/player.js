const express = require("express");
const router = express.Router();
const db = require("../models");
const { generateBaseHTML } = require("../utils/htmlTemplate");

// 语言映射关系
const LANGUAGE_LABELS = {
  ja: "日语学习",
  en: "英语学习",
  "zh-CN": "中文学习",
  "zh-TW": "中文学习",
};

// 动态生成播放页面HTML
function generatePlayerHTML(content) {
  const filters = content.filters || [];

  // 生成SEO数据
  const title = content.name;
  const description = content.description || `${content.name} - Echo Lab`;

  // 处理关键词
  let contentKeywords = [];
  if (content.keywords) {
    try {
      contentKeywords = JSON.parse(content.keywords);
    } catch (e) {
      contentKeywords = [];
    }
  }

  const keywords = [
    ...contentKeywords,
    ...filters.map((f) => f.name),
    LANGUAGE_LABELS[content.learningLanguage] || "语言学习",
  ].join(", ");

  return generateBaseHTML({
    title: `${title} - Echo Lab`,
    description,
    keywords,
    ogUrl: `https://echolab.club/player/${content.id}`,
    ogImage: content.thumbnailUrl || "",
    ssrData: {
      content: content,
    },
    appDataId: content.id,
    loadingText: "别着急，正在努力加载中...",
  });
}

// 播放页面路由
router.get("/:id", async (req, res) => {
  try {
    const contentId = req.params.id;

    const content = await db.Content.findOne({
      where: {
        id: contentId,
      },
      include: [
        {
          model: db.Filter,
          as: "filters",
          through: { attributes: [] },
        },
      ],
    });

    if (!content) {
      const notFoundHtml = generateBaseHTML({
        title: "内容不存在 - Echo Lab",
        description: "该内容可能已被删除、下架或您没有访问权限",
        loadingText: "别着急，正在努力加载中...",
      });
      return res
        .status(200)
        .setHeader("Content-Type", "text/html")
        .send(notFoundHtml);
    }

    // 转换为普通对象并包含所有字段
    const contentData = content.toJSON();

    const html = generatePlayerHTML(contentData);
    res.setHeader("Content-Type", "text/html");
    res.send(html);
  } catch (error) {
    console.error("播放页面路由错误:", error);
    const notFoundHtml = generateBaseHTML({
      title: "内容不存在 - Echo Lab",
      description: "该内容可能已被删除、下架或您没有访问权限",
      loadingText: "别着急，正在努力加载中...",
    });
    res.status(200).setHeader("Content-Type", "text/html").send(notFoundHtml);
  }
});

module.exports = router;
