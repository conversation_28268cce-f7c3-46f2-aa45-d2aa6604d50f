/**
 * 公开内容API路由
 * 提供公开内容的获取和搜索功能
 */
const express = require("express");
const router = express.Router();
const db = require("../models");
const { Op } = require("sequelize");


/**
 * 获取所有公开内容
 * GET /api/public/contents
 */
router.get("/", async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;
    const sortBy = req.query.sortBy || "created_at";
    const sortOrder = req.query.sortOrder || "DESC";

    // 字段名映射：前端驼峰格式 -> 数据库下划线格式
    const fieldMapping = {
      'createdAt': 'created_at',
      'updatedAt': 'updated_at',
      'thumbnailUrl': 'thumbnail_url'
    };

    // 转换排序字段名
    const dbSortBy = fieldMapping[sortBy] || sortBy;
    const category = req.query.category; // 分类过滤
    const filters = req.query.filters; // 过滤器筛选

    // 从请求头获取用户学习语言
    const userLearningLanguage = req.headers['x-user-language'];

    // 构建查询条件
    const where = {
      status: "published",
    };

    // 添加学习语言过滤
    if (userLearningLanguage) {
      where.learningLanguage = userLearningLanguage;
    }

    // 构建复合查询条件
    const andConditions = [];



    // 添加过滤器筛选 - 支持同维度OR，不同维度AND
    if (filters) {
      const filterIds = filters.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
      if (filterIds.length > 0) {
        // 获取过滤器信息，按类型分组
        const filterDetails = await db.Filter.findAll({
          where: { id: filterIds, isActive: true },
          attributes: ['id', 'type']
        });

        // 按类型分组过滤器ID
        const filtersByType = {};
        filterDetails.forEach(filter => {
          if (!filtersByType[filter.type]) {
            filtersByType[filter.type] = [];
          }
          filtersByType[filter.type].push(filter.id);
        });

        // 为每个过滤器类型创建条件（同类型内OR关系）
        const filterTypeConditions = [];
        Object.keys(filtersByType).forEach(filterType => {
          const typeFilterIds = filtersByType[filterType];
          filterTypeConditions.push({
            id: {
              [Op.in]: db.sequelize.literal(`(
                SELECT DISTINCT content_id
                FROM content_filters
                WHERE filter_id IN (${typeFilterIds.join(',')})
              )`)
            }
          });
        });

        // 不同类型间使用AND关系
        if (filterTypeConditions.length > 0) {
          andConditions.push(...filterTypeConditions);
        }
      }
    }



    // 添加搜索功能
    if (req.query.search) {
      const searchTerm = req.query.search.trim();
      andConditions.push({
        [Op.or]: [
          { name: { [Op.like]: `%${searchTerm}%` } },
          { description: { [Op.like]: `%${searchTerm}%` } }
        ]
      });
    }

    // 合并所有条件
    if (andConditions.length > 0) {
      where[Op.and] = andConditions;
    }







    const { count, rows } = await db.Content.findAndCountAll({
      where,
      attributes: [
        "id",
        "name",
        "description",
        ["thumbnail_url", "thumbnailUrl"],
        "status",
        ["created_at", "createdAt"],
        ["updated_at", "updatedAt"],
      ],
      include: [
        {
          model: db.User,
          as: "creator",
          attributes: ["id", "username"],
        },
        {
          model: db.Filter,
          as: 'filters',
          through: { attributes: [] },
          where: { isActive: true },
          required: false
        }
      ],
      order: [[dbSortBy, sortOrder]],
      limit: pageSize,
      offset: (page - 1) * pageSize,
      distinct: true, // 确保计数时去重，避免因关联查询导致的重复计数
    });

    // 批量获取查看次数
    if (rows.length > 0) {
      const contentIds = rows.map(content => content.id);
      const viewCounts = await db.ContentView.findAll({
        where: { contentId: { [Op.in]: contentIds } },
        attributes: [
          'contentId',
          [db.sequelize.fn('COUNT', db.sequelize.col('id')), 'count']
        ],
        group: ['contentId'],
        raw: true
      });

      // 将查看次数合并到内容数据中
      const viewCountMap = {};
      viewCounts.forEach(vc => {
        viewCountMap[vc.contentId] = parseInt(vc.count) || 0;
      });

      rows.forEach(content => {
        content.dataValues.viewCount = viewCountMap[content.id] || 0;

        // 处理过滤器数据，按类型分组
        if (content.filters) {
          const groupedFilters = {};
          content.filters.forEach(filter => {
            const filterType = filter.type;
            if (!groupedFilters[filterType]) {
              groupedFilters[filterType] = [];
            }
            groupedFilters[filterType].push({
              id: filter.id,
              name: filter.name,
              key: filter.key,
              type: filter.type,
              description: filter.description
            });
          });
          content.dataValues.filtersGrouped = groupedFilters;
        }
      });
    }

    res.json({
      success: true,
      contents: rows,
      pagination: {
        total: count,
        page,
        pageSize,
      },
    });
  } catch (error) {
    console.error("获取公开内容列表失败:", error);
    res.status(500).json({
      success: false,
      error: `获取公开内容列表失败: ${error.message}`,
    });
  }
});





/**
 * 获取单个公开内容
 * GET /api/public/contents/:id
 */
router.get("/:id", async (req, res) => {
  try {
    const content = await db.Content.findOne({
      where: {
        id: req.params.id,
        status: "published",
      },
    });

    if (!content) {
      return res.status(404).json({
        success: false,
        error: "内容不存在或未发布",
      });
    }

    res.json({
      success: true,
      content,
    });
  } catch (error) {
    console.error(`获取公开内容失败 (ID: ${req.params.id}):`, error);
    res.status(500).json({
      success: false,
      error: `获取公开内容失败: ${error.message}`,
    });
  }
});


module.exports = router;
