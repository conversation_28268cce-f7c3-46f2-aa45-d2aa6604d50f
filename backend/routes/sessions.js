/**
 * 会话管理API路由
 * 提供用户会话的查询和管理功能
 */
const express = require("express");
const router = express.Router();
const sessionService = require("../services/sessionService");
const { authenticate } = require("../middleware/authMiddleware");
const { adminOnly } = require("../middleware/adminMiddleware");
const UAParser = require("ua-parser-js");

/**
 * 获取当前用户的所有活跃会话
 * GET /api/sessions
 */
router.get("/", authenticate, async (req, res) => {
  try {
    const sessions = await sessionService.getUserSessions(req.user.id);

    // 解析设备信息
    const enhancedSessions = sessions.map((session) => {
      let deviceInfo = { browser: "Unknown", os: "Unknown", device: "Unknown" };

      if (session.deviceInfo) {
        try {
          const parsedInfo =
            typeof session.deviceInfo === "string"
              ? JSON.parse(session.deviceInfo)
              : session.deviceInfo;

          if (parsedInfo.userAgent) {
            const parser = new UAParser(parsedInfo.userAgent);
            const result = parser.getResult();
            deviceInfo = {
              browser: `${result.browser.name || "Unknown"} ${
                result.browser.version || ""
              }`.trim(),
              os: `${result.os.name || "Unknown"} ${
                result.os.version || ""
              }`.trim(),
              device: result.device.type
                ? `${result.device.vendor || ""} ${
                    result.device.model || ""
                  } (${result.device.type})`.trim()
                : "Desktop",
            };
          }
        } catch (e) {
          console.error("解析设备信息失败:", e);
        }
      }

      // 标记当前会话
      const isCurrentSession = session.tokenId === req.user.tokenId;

      return {
        id: session.id,
        tokenId: session.tokenId, // 添加tokenId，用于删除操作
        deviceInfo,
        ipAddress: session.ipAddress,
        lastActive: session.lastActive,
        createdAt: session.createdAt,
        isCurrentSession,
      };
    });

    res.json({
      success: true,
      sessions: enhancedSessions,
      maxSessions: sessionService.MAX_SESSIONS_PER_USER,
    });
  } catch (error) {
    console.error("获取用户会话失败:", error);
    res.status(500).json({
      success: false,
      error: `获取用户会话失败: ${error.message}`,
    });
  }
});

/**
 * 删除会话（登出）
 * DELETE /api/sessions/:tokenId
 */
router.delete("/:tokenId", authenticate, async (req, res) => {
  try {
    const { tokenId } = req.params;

    // 检查是否是当前会话
    const isCurrentSession = tokenId === req.user.tokenId;

    // 删除会话
    const success = await sessionService.deleteSession(tokenId, req.user.id);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "会话不存在或您没有权限删除",
      });
    }

    res.json({
      success: true,
      message: isCurrentSession ? "您已成功登出" : "会话已删除",
      isCurrentSession,
    });
  } catch (error) {
    console.error("删除会话失败:", error);
    res.status(500).json({
      success: false,
      error: `删除会话失败: ${error.message}`,
    });
  }
});

/**
 * 删除所有会话（除了当前会话）
 * DELETE /api/sessions
 */
router.delete("/", authenticate, async (req, res) => {
  try {
    const sessions = await sessionService.getUserSessions(req.user.id);

    // 过滤出不是当前会话的会话
    const sessionsToDelete = sessions.filter(
      (session) => session.tokenId !== req.user.tokenId
    );

    // 删除会话
    let deletedCount = 0;
    for (const session of sessionsToDelete) {
      const success = await sessionService.deleteSession(
        session.tokenId,
        req.user.id
      );
      if (success) deletedCount++;
    }

    res.json({
      success: true,
      message: `已删除 ${deletedCount} 个其他设备的会话`,
      deletedCount,
    });
  } catch (error) {
    console.error("删除所有会话失败:", error);
    res.status(500).json({
      success: false,
      error: `删除所有会话失败: ${error.message}`,
    });
  }
});

/**
 * 管理员：获取所有用户会话
 * GET /api/admin/sessions
 */
router.get("/admin/all", authenticate, adminOnly, async (req, res) => {
  try {
    // 这里需要实现管理员查看所有会话的功能
    // 由于这超出了当前需求范围，暂不实现
    res.json({
      success: true,
      message: "管理员会话管理功能暂未实现",
    });
  } catch (error) {
    console.error("获取所有会话失败:", error);
    res.status(500).json({
      success: false,
      error: `获取所有会话失败: ${error.message}`,
    });
  }
});

module.exports = router;
