/**
 * 特殊词汇API路由
 * 提供特殊词汇的CRUD操作
 */
const express = require("express");
const router = express.Router();
const db = require("../models");
const { Op } = require("sequelize");

/**
 * 获取特殊词汇列表
 * GET /api/special-words
 *
 * 查询参数:
 * - service_id: TTS服务ID
 * - language_code: 语言代码
 * - is_system: 是否为系统词汇 (true/false)
 */
router.get("/", async (req, res) => {
  try {
    const { service_id, language_code, is_system } = req.query;

    // 构建查询条件
    const where = {};

    if (service_id) {
      where.serviceId = service_id;
    }

    if (language_code) {
      where.languageCode = language_code;
    }

    if (is_system !== undefined) {
      where.isSystem = is_system === "true";
    }

    // 查询特殊词汇
    const specialWords = await db.SpecialWord.findAll({
      where,
      order: [
        ["serviceId", "ASC"],
        ["languageCode", "ASC"],
        ["word", "ASC"],
      ],
    });

    // 按服务和语言分组
    const groupedWords = {};

    specialWords.forEach((word) => {
      const serviceId = word.serviceId;
      const languageCode = word.languageCode;

      if (!groupedWords[serviceId]) {
        groupedWords[serviceId] = {};
      }

      if (!groupedWords[serviceId][languageCode]) {
        groupedWords[serviceId][languageCode] = [];
      }

      groupedWords[serviceId][languageCode].push({
        id: word.id,
        word: word.word,
        isSystem: word.isSystem,
      });
    });

    res.json({
      success: true,
      data: groupedWords,
    });
  } catch (error) {
    console.error("获取特殊词汇列表失败:", error);
    res.status(500).json({
      success: false,
      error: `获取特殊词汇列表失败: ${error.message}`,
    });
  }
});

/**
 * 添加特殊词汇
 * POST /api/special-words
 *
 * 请求体:
 * {
 *   word: "特殊词汇",
 *   service_id: "TTS服务ID",
 *   language_code: "语言代码",
 *   is_system: false  // 可选，默认为false
 * }
 */
router.post("/", async (req, res) => {
  try {
    const { word, service_id, language_code, is_system = false } = req.body;

    // 验证必要参数
    if (!word) {
      return res.status(400).json({
        success: false,
        error: "缺少必要参数: word",
      });
    }

    if (!service_id) {
      return res.status(400).json({
        success: false,
        error: "缺少必要参数: service_id",
      });
    }

    if (!language_code) {
      return res.status(400).json({
        success: false,
        error: "缺少必要参数: language_code",
      });
    }

    // 检查是否已存在
    const existingWord = await db.SpecialWord.findOne({
      where: {
        word,
        serviceId: service_id,
      },
    });

    if (existingWord) {
      return res.status(409).json({
        success: false,
        error: "特殊词汇已存在",
      });
    }

    // 创建特殊词汇
    const specialWord = await db.SpecialWord.create({
      word,
      serviceId: service_id,
      languageCode: language_code,
      isSystem: is_system,
    });

    res.status(201).json({
      success: true,
      data: specialWord,
    });
  } catch (error) {
    console.error("添加特殊词汇失败:", error);
    res.status(500).json({
      success: false,
      error: `添加特殊词汇失败: ${error.message}`,
    });
  }
});

/**
 * 删除特殊词汇
 * DELETE /api/special-words/:id
 */
router.delete("/:id", async (req, res) => {
  try {
    const { id } = req.params;

    // 查找特殊词汇
    const specialWord = await db.SpecialWord.findByPk(id);

    if (!specialWord) {
      return res.status(404).json({
        success: false,
        error: "特殊词汇不存在",
      });
    }

    // 系统词汇不允许删除
    if (specialWord.isSystem) {
      return res.status(403).json({
        success: false,
        error: "系统词汇不允许删除",
      });
    }

    // 删除特殊词汇
    await specialWord.destroy();

    res.json({
      success: true,
      message: "特殊词汇已删除",
    });
  } catch (error) {
    console.error("删除特殊词汇失败:", error);
    res.status(500).json({
      success: false,
      error: `删除特殊词汇失败: ${error.message}`,
    });
  }
});

/**
 * 批量添加特殊词汇
 * POST /api/special-words/batch
 *
 * 请求体:
 * {
 *   words: [
 *     {
 *       word: "特殊词汇1",
 *       service_id: "TTS服务ID",
 *       language_code: "语言代码",
 *       is_system: false  // 可选，默认为false
 *     },
 *     ...
 *   ]
 * }
 */
router.post("/batch", async (req, res) => {
  try {
    const { words } = req.body;

    if (!words || !Array.isArray(words) || words.length === 0) {
      return res.status(400).json({
        success: false,
        error: "缺少有效的words参数",
      });
    }

    // 批量创建特殊词汇
    const createdWords = [];
    const errors = [];

    for (const item of words) {
      try {
        const { word, service_id, language_code, is_system = false } = item;

        // 验证必要参数
        if (!word || !service_id || !language_code) {
          errors.push({
            item,
            error: "缺少必要参数",
          });
          continue;
        }

        // 检查是否已存在
        const existingWord = await db.SpecialWord.findOne({
          where: {
            word,
            serviceId: service_id,
          },
        });

        if (existingWord) {
          errors.push({
            item,
            error: "特殊词汇已存在",
          });
          continue;
        }

        // 创建特殊词汇
        const specialWord = await db.SpecialWord.create({
          word,
          serviceId: service_id,
          languageCode: language_code,
          isSystem: is_system,
        });

        createdWords.push(specialWord);
      } catch (error) {
        errors.push({
          item,
          error: error.message,
        });
      }
    }

    res.status(201).json({
      success: true,
      data: {
        created: createdWords,
        errors,
      },
    });
  } catch (error) {
    console.error("批量添加特殊词汇失败:", error);
    res.status(500).json({
      success: false,
      error: `批量添加特殊词汇失败: ${error.message}`,
    });
  }
});

module.exports = router;
