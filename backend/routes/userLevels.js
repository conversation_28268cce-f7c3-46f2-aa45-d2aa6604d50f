/**
 * 用户等级路由
 * 提供用户等级和订阅相关的API
 */
const express = require('express');
const router = express.Router();
const userLevelService = require('../services/userLevelService');
const { authenticate } = require('../middleware/authMiddleware');
const { adminOnly } = require('../middleware/adminMiddleware');

/**
 * 获取所有用户等级
 * GET /api/user-levels
 */
router.get('/', async (req, res) => {
  try {
    const levels = await userLevelService.getAllLevels();
    
    res.json({
      success: true,
      levels
    });
  } catch (error) {
    console.error('获取用户等级失败:', error);
    res.status(500).json({
      success: false,
      error: '获取用户等级失败'
    });
  }
});

/**
 * 获取当前用户的等级信息
 * GET /api/user-levels/me
 */
router.get('/me', authenticate, async (req, res) => {
  try {
    const levelInfo = await userLevelService.getUserLevelInfo(req.user.id);
    
    res.json({
      success: true,
      ...levelInfo
    });
  } catch (error) {
    console.error('获取用户等级信息失败:', error);
    res.status(500).json({
      success: false,
      error: '获取用户等级信息失败'
    });
  }
});

/**
 * 更新用户等级（管理员）
 * PUT /api/user-levels/users/:userId
 */
router.put('/users/:userId', authenticate, adminOnly, async (req, res) => {
  try {
    const { userId } = req.params;
    const { level, expireCurrentSubscription } = req.body;
    
    if (level === undefined) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数: level'
      });
    }
    
    const result = await userLevelService.updateUserLevel(userId, level, {
      reason: req.body.reason,
      adminId: req.user.id,
      expireCurrentSubscription
    });
    
    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error('更新用户等级失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '更新用户等级失败'
    });
  }
});

/**
 * 创建用户订阅（管理员）
 * POST /api/user-levels/subscriptions
 */
router.post('/subscriptions', authenticate, adminOnly, async (req, res) => {
  try {
    const { userId, level, startDate, endDate, paymentId } = req.body;
    
    if (!userId || level === undefined) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数: userId, level'
      });
    }
    
    const result = await userLevelService.createSubscription(userId, level, {
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      paymentId
    });
    
    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error('创建订阅失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '创建订阅失败'
    });
  }
});

module.exports = router;
