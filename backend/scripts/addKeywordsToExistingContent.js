/**
 * 为现有内容添加关键词的脚本
 * 使用阿里云百炼API为数据库中的旧内容生成关键词
 */
const db = require('../models');
const keywordExtractionService = require('../services/keywordExtractionService');
const CryptoJS = require('crypto-js');

// 加密密钥（与前端保持一致）
const SECRET_KEY = 'echo-lab-secure-key-2024';

/**
 * 解密JSON数据
 */
function decryptJSON(encryptedData) {
  try {
    if (!encryptedData || typeof encryptedData !== 'string') {
      return null;
    }

    // 尝试直接解析为JSON（检查是否已经是JSON字符串）
    try {
      return JSON.parse(encryptedData);
    } catch (e) {
      // 解析失败，说明可能是加密数据
    }

    // 使用AES解密
    const decrypted = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY).toString(CryptoJS.enc.Utf8);
    return JSON.parse(decrypted);
  } catch (error) {
    console.error('解密数据失败:', error.message);
    return null;
  }
}

async function addKeywordsToExistingContent() {
  try {
    console.log('开始为现有内容添加关键词...');

    // 查找所有内容，重新生成关键词
    const contents = await db.Content.findAll();

    console.log(`找到 ${contents.length} 个内容，将重新生成关键词`);

    let processed = 0;
    let success = 0;
    let failed = 0;

    for (const content of contents) {
      try {
        console.log(`处理内容 ${content.id}: ${content.name}`);
        console.log(`  当前关键词: ${content.keywords || '无'}`);

        // 从configJson中提取文本
        let text = '';
        if (content.configJson) {
          let config;
          
          // 处理不同的数据格式
          if (typeof content.configJson === 'string') {
            // 如果是字符串，尝试解密
            config = decryptJSON(content.configJson);
          } else if (typeof content.configJson === 'object') {
            // 如果已经是对象，直接使用
            config = content.configJson;
          }
          
          if (config && config.nodes) {
            const texts = [];
            Object.values(config.nodes).forEach(node => {
              if (node.type === 'textContent' && node.params?.text) {
                texts.push(node.params.text);
              }
            });
            text = texts.join('\n').trim();
          }
        }

        if (!text) {
          console.log(`  跳过: 没有找到文本内容`);
          failed++;
          processed++;
          continue;
        }

        // 提取关键词
        const keywords = await keywordExtractionService.extractKeywords(text);
        
        if (keywords && keywords.length > 0) {
          // 更新数据库
          await content.update({
            keywords: JSON.stringify(keywords)
          });
          
          console.log(`  成功: 添加了 ${keywords.length} 个关键词: ${keywords.join(', ')}`);
          success++;
        } else {
          console.log(`  失败: 未能提取到关键词`);
          failed++;
        }

        processed++;
        
        // 每处理10个内容暂停1秒，避免API限制
        if (processed % 10 === 0) {
          console.log(`已处理 ${processed}/${contents.length} 个内容，暂停1秒...`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (error) {
        console.error(`  错误: ${error.message}`);
        failed++;
        processed++;
      }
    }

    console.log('\n处理完成!');
    console.log(`总计: ${contents.length} 个内容`);
    console.log(`成功: ${success} 个`);
    console.log(`失败: ${failed} 个`);
    console.log(`跳过: ${contents.length - processed} 个`);

  } catch (error) {
    console.error('脚本执行失败:', error);
  } finally {
    process.exit(0);
  }
}

// 运行脚本
addKeywordsToExistingContent();