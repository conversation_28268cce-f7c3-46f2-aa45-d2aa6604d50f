/**
 * 检查JWT token内容的脚本
 */

const jwt = require('jsonwebtoken');

// 从命令行参数获取token
const token = process.argv[2];

if (!token) {
  console.log('❌ 请提供JWT token');
  console.log('用法: node scripts/checkToken.js <token>');
  process.exit(1);
}

try {
  // 解码token（不验证签名，只查看内容）
  const decoded = jwt.decode(token);
  
  console.log('🔍 JWT Token 内容:');
  console.log('='.repeat(50));
  console.log('用户ID:', decoded.id);
  console.log('邮箱:', decoded.email);
  console.log('角色:', decoded.role || '(已移除，现在实时查询)');
  console.log('Token ID:', decoded.tokenId);
  console.log('过期时间:', new Date(decoded.exp * 1000).toLocaleString());
  console.log('签发时间:', new Date(decoded.iat * 1000).toLocaleString());
  console.log('='.repeat(50));

  if (decoded.role) {
    console.log('⚠️  Token仍包含角色信息，建议重新登录获取新token');
  } else {
    console.log('✅ Token已优化，角色信息已移除，现在实时从数据库查询');
  }
  
} catch (error) {
  console.error('❌ 解析token失败:', error.message);
}
