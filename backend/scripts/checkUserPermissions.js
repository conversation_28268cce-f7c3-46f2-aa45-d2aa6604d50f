/**
 * 检查用户权限脚本
 * 用于诊断和修复用户权限问题
 * 
 * 🚀 使用教程:
 * 
 * 1. 进入后端目录:
 *    cd backend
 * 
 * 2. 检查用户权限状态:
 *    node scripts/checkUserPermissions.js check <用户ID>
 *    示例: node scripts/checkUserPermissions.js check d3izO0bRLRY8ntee2P0wg
 * 
 * 3. 修复内容创建权限:
 *    node scripts/checkUserPermissions.js fix <用户ID>
 *    示例: node scripts/checkUserPermissions.js fix d3izO0bRLRY8ntee2P0wg
 * 
 * 4. 查看所有功能标志:
 *    node scripts/checkUserPermissions.js flags
 * 
 * 5. 创建内容创建功能标志:
 *    node scripts/checkUserPermissions.js create-flag
 */

const db = require('../models');

// 检查用户权限状态
async function checkUserPermissions(userId) {
  try {
    console.log(`🔍 检查用户权限状态: ${userId}`);
    console.log('='.repeat(60));
    
    // 1. 检查用户信息
    const user = await db.User.findByPk(userId);
    if (!user) {
      console.error(`❌ 用户不存在: ${userId}`);
      return false;
    }
    
    console.log(`👤 用户信息:`);
    console.log(`   ID: ${user.id}`);
    console.log(`   邮箱: ${user.email}`);
    console.log(`   角色: ${user.role}`);
    console.log(`   等级: ${user.level}`);
    console.log(`   状态: ${user.status}`);
    console.log('');
    
    // 2. 检查 content_creation 功能标志
    const contentCreationFlag = await db.FeatureFlag.findOne({
      where: { featureKey: 'content_creation' }
    });
    
    console.log(`🚩 content_creation 功能标志:`);
    if (contentCreationFlag) {
      console.log(`   ✅ 存在`);
      console.log(`   启用状态: ${contentCreationFlag.isEnabled ? '✅ 已启用' : '❌ 未启用'}`);
      console.log(`   显示名称: ${contentCreationFlag.displayName || '未设置'}`);
      console.log(`   描述: ${contentCreationFlag.description || '未设置'}`);
    } else {
      console.log(`   ❌ 不存在`);
    }
    console.log('');
    
    // 3. 检查用户特定权限
    const userPermission = await db.FeaturePermission.findOne({
      where: {
        featureKey: 'content_creation',
        userId: userId
      }
    });
    
    console.log(`🔐 用户特定权限:`);
    if (userPermission) {
      console.log(`   ✅ 用户有 content_creation 特定权限`);
    } else {
      console.log(`   ❌ 用户没有 content_creation 特定权限`);
    }
    console.log('');
    
    // 4. 模拟权限检查逻辑
    console.log(`🧮 权限检查结果:`);
    
    let hasPermission = false;
    let reason = '';
    
    if (user.role === 'admin') {
      hasPermission = true;
      reason = '管理员角色，自动拥有所有权限';
    } else if (contentCreationFlag && contentCreationFlag.isEnabled) {
      hasPermission = true;
      reason = 'content_creation 功能全局启用';
    } else if (userPermission) {
      hasPermission = true;
      reason = '用户有特定的 content_creation 权限';
    } else {
      hasPermission = false;
      reason = '无权限：不是管理员，功能未全局启用，且无特定权限';
    }
    
    console.log(`   结果: ${hasPermission ? '✅ 有权限' : '❌ 无权限'}`);
    console.log(`   原因: ${reason}`);
    
    return hasPermission;
    
  } catch (error) {
    console.error('❌ 检查权限失败:', error.message);
    return false;
  }
}

// 修复权限问题
async function fixUserPermissions(userId) {
  try {
    console.log(`🔧 修复用户权限: ${userId}`);
    console.log('='.repeat(60));
    
    // 1. 检查用户是否存在
    const user = await db.User.findByPk(userId);
    if (!user) {
      console.error(`❌ 用户不存在: ${userId}`);
      return false;
    }
    
    // 2. 确保 content_creation 功能标志存在
    let contentCreationFlag = await db.FeatureFlag.findOne({
      where: { featureKey: 'content_creation' }
    });
    
    if (!contentCreationFlag) {
      console.log(`📝 创建 content_creation 功能标志...`);
      contentCreationFlag = await db.FeatureFlag.create({
        featureKey: 'content_creation',
        displayName: '内容创建与管理',
        description: '允许用户创建和管理学习内容',
        isEnabled: false // 默认不全局启用
      });
      console.log(`   ✅ 功能标志创建成功`);
    }
    
    // 3. 如果用户是管理员，确保功能标志启用或给予特定权限
    if (user.role === 'admin') {
      console.log(`👑 用户是管理员，启用 content_creation 功能...`);
      
      if (!contentCreationFlag.isEnabled) {
        await contentCreationFlag.update({ isEnabled: true });
        console.log(`   ✅ content_creation 功能已全局启用`);
      } else {
        console.log(`   ℹ️  content_creation 功能已经是启用状态`);
      }
    } else {
      // 4. 如果不是管理员，给予特定权限
      console.log(`🔑 给用户添加 content_creation 特定权限...`);
      
      const existingPermission = await db.FeaturePermission.findOne({
        where: {
          featureKey: 'content_creation',
          userId: userId
        }
      });
      
      if (!existingPermission) {
        await db.FeaturePermission.create({
          featureKey: 'content_creation',
          userId: userId
        });
        console.log(`   ✅ 特定权限添加成功`);
      } else {
        console.log(`   ℹ️  用户已有特定权限`);
      }
    }
    
    console.log(`🎉 权限修复完成！`);
    return true;
    
  } catch (error) {
    console.error('❌ 修复权限失败:', error.message);
    return false;
  }
}

// 查看所有功能标志
async function showAllFlags() {
  try {
    console.log(`🚩 所有功能标志:`);
    console.log('='.repeat(60));
    
    const flags = await db.FeatureFlag.findAll({
      order: [['featureKey', 'ASC']]
    });
    
    if (flags.length === 0) {
      console.log(`   📭 没有找到任何功能标志`);
      return;
    }
    
    flags.forEach((flag, index) => {
      console.log(`${index + 1}. ${flag.featureKey}`);
      console.log(`   显示名称: ${flag.displayName || '未设置'}`);
      console.log(`   启用状态: ${flag.isEnabled ? '✅ 已启用' : '❌ 未启用'}`);
      console.log(`   描述: ${flag.description || '未设置'}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ 获取功能标志失败:', error.message);
  }
}

// 创建内容创建功能标志
async function createContentCreationFlag() {
  try {
    console.log(`📝 创建 content_creation 功能标志...`);
    
    const existingFlag = await db.FeatureFlag.findOne({
      where: { featureKey: 'content_creation' }
    });
    
    if (existingFlag) {
      console.log(`   ⚠️  功能标志已存在`);
      return;
    }
    
    const flag = await db.FeatureFlag.create({
      featureKey: 'content_creation',
      displayName: '内容创建与管理',
      description: '允许用户创建和管理学习内容，包括访问内容管理页面和编辑器',
      isEnabled: true // 全局启用
    });
    
    console.log(`   ✅ 功能标志创建成功`);
    console.log(`   标识符: ${flag.featureKey}`);
    console.log(`   显示名称: ${flag.displayName}`);
    console.log(`   启用状态: ${flag.isEnabled ? '已启用' : '未启用'}`);
    
  } catch (error) {
    console.error('❌ 创建功能标志失败:', error.message);
  }
}

// 显示帮助信息
function showHelp() {
  console.log('📖 用户权限检查脚本使用说明:');
  console.log('');
  console.log('检查用户权限状态:');
  console.log('  node scripts/checkUserPermissions.js check <userId>');
  console.log('');
  console.log('修复用户权限问题:');
  console.log('  node scripts/checkUserPermissions.js fix <userId>');
  console.log('');
  console.log('查看所有功能标志:');
  console.log('  node scripts/checkUserPermissions.js flags');
  console.log('');
  console.log('创建内容创建功能标志:');
  console.log('  node scripts/checkUserPermissions.js create-flag');
  console.log('');
  console.log('示例:');
  console.log('  node scripts/checkUserPermissions.js check d3izO0bRLRY8ntee2P0wg');
  console.log('  node scripts/checkUserPermissions.js fix d3izO0bRLRY8ntee2P0wg');
}

// 主函数
async function main() {
  const [command, userId] = process.argv.slice(2);
  
  if (!command) {
    showHelp();
    process.exit(0);
  }
  
  console.log('🚀 用户权限检查脚本启动...');
  console.log('');
  
  let success = false;
  
  switch (command) {
    case 'check':
      if (!userId) {
        console.error('❌ 请提供用户 ID');
        process.exit(1);
      }
      success = await checkUserPermissions(userId);
      break;
      
    case 'fix':
      if (!userId) {
        console.error('❌ 请提供用户 ID');
        process.exit(1);
      }
      success = await fixUserPermissions(userId);
      break;
      
    case 'flags':
      await showAllFlags();
      success = true;
      break;
      
    case 'create-flag':
      await createContentCreationFlag();
      success = true;
      break;
      
    default:
      console.error(`❌ 未知命令: ${command}`);
      showHelp();
      process.exit(1);
  }
  
  console.log('='.repeat(60));
  console.log(success ? '✅ 操作完成' : '❌ 操作失败');
  
  // 关闭数据库连接
  await db.sequelize.close();
}

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  checkUserPermissions,
  fixUserPermissions,
  showAllFlags,
  createContentCreationFlag
};
