#!/usr/bin/env node

/**
 * 音频缓存清理脚本
 * 用于定期清理过期的倍速音频缓存文件
 */

const audioSpeedService = require('../services/audioSpeedService');

async function main() {
  try {
    console.log('开始清理音频缓存...');
    
    // 清理30天前的缓存文件
    const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天
    await audioSpeedService.cleanupExpiredCache(maxAge);
    
    console.log('音频缓存清理完成');
    process.exit(0);
  } catch (error) {
    console.error('音频缓存清理失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = main;
