#!/bin/bash
# deploy.sh - Echo Lab 部署脚本

# 显示执行的命令
set -x

# 确定环境
if [ "$2" == "--env=production" ]; then
  ENV="production"
elif [ "$2" == "--env=test" ]; then
  ENV="test"
else
  ENV="development"
fi

echo "使用环境: $ENV"

# 数据库配置
DB_USER="root"
DB_PASSWORD=""
DB_HOST="localhost"
DB_NAME="echo-lab"

# 如果提供了数据库密码参数
if [ -n "$3" ]; then
  DB_PASSWORD="$3"
else
  # 提示用户输入MySQL密码
  read -sp "请输入MySQL密码: " DB_PASSWORD
  echo ""
fi

# 切换到项目根目录
cd "$(dirname "$0")/.."

echo "===== 开始部署 Echo Lab 后端 ====="

# 1. 拉取最新代码（如果是通过 Git 部署）
# git pull

# 2. 安装依赖
echo "安装依赖..."
npm install

# 3. 重置数据库（仅在需要完全重置时使用）
if [ "$1" == "--reset-db" ]; then
  echo "重置数据库..."
  NODE_ENV=$ENV npx sequelize-cli db:drop
  NODE_ENV=$ENV npx sequelize-cli db:create
fi

# 4. 执行数据库迁移
echo "执行数据库迁移..."
NODE_ENV=$ENV npx sequelize-cli db:migrate

# 检查迁移是否成功
if [ $? -ne 0 ]; then
  echo "数据库迁移失败，请检查错误信息"
  exit 1
fi

# 显示数据库表
echo "数据库表列表:"
if [ -z "$DB_PASSWORD" ]; then
  mysql -u $DB_USER -e "SHOW TABLES;" echo-lab
else
  mysql -u $DB_USER -p$DB_PASSWORD -e "SHOW TABLES;" echo-lab
fi

# 5. 初始化必要数据
echo "初始化声音信息..."
NODE_ENV=$ENV node scripts/initVoices.js

echo "初始化功能标志..."
NODE_ENV=$ENV node scripts/initFeatureFlags.js

# 如果需要创建管理员用户
if [ "$1" == "--reset-db" ] || [ "$3" == "--create-admin" ]; then
  echo "创建管理员用户..."
  NODE_ENV=$ENV node -e "
    const db = require('./models');
    const { nanoid } = require('nanoid');
    const { initFeatures } = require('./scripts/initFeatureFlags');

    (async () => {
      try {
        // 创建管理员用户
        const adminUser = await db.User.create({
          id: nanoid(),
          email: '<EMAIL>',
          username: 'Admin',
          status: 'active',
          role: 'admin',
          settings: JSON.stringify({})
        });

        console.log('管理员用户创建成功！ID:', adminUser.id);

        // 为管理员授予所有功能权限
        await initFeatures(adminUser.id);

        process.exit(0);
      } catch (error) {
        console.error('创建管理员用户失败:', error);
        process.exit(1);
      } finally {
        await db.sequelize.close();
      }
    })();
  "
fi

# 6. 重启服务
echo "重启服务..."
if [ "$ENV" == "production" ]; then
  # 生产环境使用 PM2
  pm2 restart echo-lab-backend || pm2 start app.js --name echo-lab-backend
else
  # 开发环境不重启服务
  echo "开发环境不自动重启服务，请手动重启"
fi

echo "===== Echo Lab 后端部署完成 ====="
