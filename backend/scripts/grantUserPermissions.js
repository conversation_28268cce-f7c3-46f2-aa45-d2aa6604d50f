/**
 * 为用户授予功能权限
 * 用法: node grantUserPermissions.js <userId> <featureKey1> <featureKey2> ...
 * 示例: node grantUserPermissions.js user123 content_management editor_access
 */
const db = require("../models");

/**
 * 为用户授予功能权限
 * @param {string} userId 用户ID
 * @param {string[]} featureKeys 功能标识符数组
 */
async function grantUserPermissions(userId, featureKeys) {
  try {
    if (!userId) {
      console.error("错误: 未提供用户ID");
      return false;
    }

    if (!featureKeys || featureKeys.length === 0) {
      console.error("错误: 未提供功能标识符");
      return false;
    }

    // 检查用户是否存在
    const user = await db.User.findByPk(userId);
    if (!user) {
      console.error(`错误: 用户不存在 (ID: ${userId})`);
      return false;
    }

    console.log(`开始为用户 ${userId} 授予功能权限...`);

    // 为每个功能标识符创建权限
    for (const featureKey of featureKeys) {
      // 检查功能标志是否存在
      const featureFlag = await db.FeatureFlag.findOne({
        where: { featureKey }
      });

      if (!featureFlag) {
        console.warn(`警告: 功能标志不存在 (${featureKey}), 跳过`);
        continue;
      }

      // 创建或更新权限
      const [permission, created] = await db.FeaturePermission.findOrCreate({
        where: {
          userId,
          featureKey
        },
        defaults: {
          userId,
          featureKey
        }
      });

      if (created) {
        console.log(`已授予权限: ${featureKey}`);
      } else {
        console.log(`权限已存在: ${featureKey}`);
      }
    }

    console.log(`用户 ${userId} 权限授予完成`);
    return true;
  } catch (error) {
    console.error("授予用户权限失败:", error);
    return false;
  }
}

// 如果直接执行此脚本，则从命令行参数获取用户ID和功能标识符
if (require.main === module) {
  (async () => {
    try {
      // 获取命令行参数
      const args = process.argv.slice(2);
      
      if (args.length < 2) {
        console.error("用法: node grantUserPermissions.js <userId> <featureKey1> <featureKey2> ...");
        process.exit(1);
      }

      const userId = args[0];
      const featureKeys = args.slice(1);

      // 授予用户权限
      const success = await grantUserPermissions(userId, featureKeys);

      if (success) {
        console.log("权限授予成功");
      } else {
        console.error("权限授予失败");
        process.exit(1);
      }
    } catch (error) {
      console.error("执行脚本失败:", error);
      process.exit(1);
    } finally {
      // 关闭数据库连接
      await db.sequelize.close();
    }
  })();
}

// 导出函数
module.exports = grantUserPermissions;
