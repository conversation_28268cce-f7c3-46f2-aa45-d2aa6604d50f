/**
 * 初始化数据库
 * 同步数据库模型并填充基础数据
 */
const db = require("../models");
const initVoices = require("./initVoices");
const { initFeatureFlags } = require("./initFeatureFlags");

/**
 * 初始化数据库
 */
async function initDb() {
  try {
    console.log("开始初始化数据库...");

    // 同步数据库模型（强制重建表）
    await db.sequelize.sync({ force: true });
    console.log("数据库模型同步完成（表已重建）");

    // 初始化声音信息
    await initVoices();
    console.log("声音信息初始化完成");

    // 初始化功能标志
    await initFeatureFlags();
    console.log("功能标志初始化完成");

    console.log("数据库初始化完成");
  } catch (error) {
    console.error("初始化数据库失败:", error);
  } finally {
    // 关闭数据库连接
    await db.sequelize.close();
  }
}

// 执行初始化
initDb();
