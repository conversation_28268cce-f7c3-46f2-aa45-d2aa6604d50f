/**
 * 初始化功能标志
 * 创建默认的功能标志和权限
 */
const db = require("../models");

// 默认功能标志列表
const defaultFeatureFlags = [
  {
    featureKey: "content_creation",
    isEnabled: true, // 默认启用内容创建功能
    description: "内容创建与管理 - 允许用户创建、编辑和管理学习内容，包括访问内容管理页面和编辑器",
    displayName: "内容创建与管理"
  },
  {
    featureKey: "content_management",
    isEnabled: true, // 默认启用内容管理功能
    description: "内容管理功能 - 允许用户访问和管理内容列表",
  },
  {
    featureKey: "editor_access",
    isEnabled: true, // 默认启用编辑器访问权限
    description: "编辑器访问权限 - 允许用户使用编辑器创建和编辑内容",
  },
  // 注意：special_word_management 功能不需要权限限制，已移除
];

/**
 * 初始化功能标志
 */
async function initFeatureFlags() {
  try {
    console.log("开始初始化功能标志...");

    // 清空现有功能标志（可选）
    // await db.FeatureFlag.destroy({ where: {} });
    // console.log("已清空现有功能标志");

    // 遍历默认功能标志列表
    for (const flagData of defaultFeatureFlags) {
      // 查找或创建功能标志
      const [flag, created] = await db.FeatureFlag.findOrCreate({
        where: { featureKey: flagData.featureKey },
        defaults: flagData,
      });

      if (created) {
        console.log(`创建功能标志: ${flagData.featureKey}`);
      } else {
        // 更新现有功能标志
        await flag.update({
          isEnabled: flagData.isEnabled,
          description: flagData.description,
        });
        console.log(`更新功能标志: ${flagData.featureKey}`);
      }
    }

    console.log("功能标志初始化完成");
    return true;
  } catch (error) {
    console.error("初始化功能标志失败:", error);
    return false;
  }
}

/**
 * 为管理员用户授予所有功能权限
 * @param {string} adminUserId 管理员用户ID
 */
async function grantAdminPermissions(adminUserId) {
  try {
    if (!adminUserId) {
      console.log("未提供管理员用户ID，跳过授权");
      return false;
    }

    console.log(`开始为管理员(${adminUserId})授予功能权限...`);

    // 获取所有功能标志
    const featureFlags = await db.FeatureFlag.findAll();

    // 为每个功能标志创建权限
    for (const flag of featureFlags) {
      const [_, created] = await db.FeaturePermission.findOrCreate({
        where: {
          userId: adminUserId,
          featureKey: flag.featureKey,
        },
        defaults: {
          userId: adminUserId,
          featureKey: flag.featureKey,
        },
      });

      if (created) {
        console.log(`为管理员授予权限: ${flag.featureKey}`);
      } else {
        console.log(`管理员已有权限: ${flag.featureKey}`);
      }
    }

    console.log("管理员权限授予完成");
    return true;
  } catch (error) {
    console.error("授予管理员权限失败:", error);
    return false;
  }
}

/**
 * 初始化功能标志和权限
 * @param {string} adminUserId 可选的管理员用户ID
 */
async function initFeatures(adminUserId) {
  try {
    // 初始化功能标志
    await initFeatureFlags();

    // 如果提供了管理员用户ID，为其授予所有权限
    if (adminUserId) {
      await grantAdminPermissions(adminUserId);
    }

    console.log("功能初始化完成");
  } catch (error) {
    console.error("初始化功能失败:", error);
  }
}

// 如果直接执行此脚本，则初始化功能标志
if (require.main === module) {
  (async () => {
    try {
      // 获取命令行参数中的管理员用户ID（如果有）
      const adminUserId = process.argv[2];

      // 初始化功能标志和权限
      await initFeatures(adminUserId);
    } catch (error) {
      console.error("执行脚本失败:", error);
    } finally {
      // 关闭数据库连接
      await db.sequelize.close();
    }
  })();
}

// 导出函数
module.exports = {
  initFeatureFlags,
  grantAdminPermissions,
  initFeatures,
};
