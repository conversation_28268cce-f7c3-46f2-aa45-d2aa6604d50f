/**
 * 初始化过滤器数据
 * 创建默认的过滤器标签，支持多维度和多语言
 */
const db = require("../models");

// 默认过滤器数据
const defaultFilters = [
  // 1. 语言等级过滤器 - 日语
  {
    name: "N5",
    key: "n5",
    type: "language_level",
    languages: ["ja"],
    description: "日语能力考试N5等级 - 基础词汇和语法",
    sortOrder: 1,
    isActive: true,
    isSystem: true,
  },
  {
    name: "N4",
    key: "n4",
    type: "language_level",
    languages: ["ja"],
    description: "日语能力考试N4等级 - 初中级日常对话",
    sortOrder: 2,
    isActive: true,
    isSystem: true,
  },
  {
    name: "N3",
    key: "n3",
    type: "language_level",
    languages: ["ja"],
    description: "日语能力考试N3等级 - 中级复杂表达",
    sortOrder: 3,
    isActive: true,
    isSystem: true,
  },
  {
    name: "N2",
    key: "n2",
    type: "language_level",
    languages: ["ja"],
    description: "日语能力考试N2等级 - 中高级流利交流",
    sortOrder: 4,
    isActive: true,
    isSystem: true,
  },
  {
    name: "N1",
    key: "n1",
    type: "language_level",
    languages: ["ja"],
    description: "日语能力考试N1等级 - 高级接近母语",
    sortOrder: 5,
    isActive: true,
    isSystem: true,
  },

  // 2. 语言等级过滤器 - 英语
  {
    name: "A1",
    key: "a1",
    type: "language_level",
    languages: ["en"],
    description: "欧洲语言共同参考框架A1等级 - 基础词汇",
    sortOrder: 6,
    isActive: true,
    isSystem: true,
  },
  {
    name: "A2",
    key: "a2",
    type: "language_level",
    languages: ["en"],
    description: "欧洲语言共同参考框架A2等级 - 初中级简单交流",
    sortOrder: 7,
    isActive: true,
    isSystem: true,
  },
  {
    name: "B1",
    key: "b1",
    type: "language_level",
    languages: ["en"],
    description: "欧洲语言共同参考框架B1等级 - 中级日常对话",
    sortOrder: 8,
    isActive: true,
    isSystem: true,
  },
  {
    name: "B2",
    key: "b2",
    type: "language_level",
    languages: ["en"],
    description: "欧洲语言共同参考框架B2等级 - 中高级流利表达",
    sortOrder: 9,
    isActive: true,
    isSystem: true,
  },
  {
    name: "C1",
    key: "c1",
    type: "language_level",
    languages: ["en"],
    description: "欧洲语言共同参考框架C1等级 - 高级专业交流",
    sortOrder: 10,
    isActive: true,
    isSystem: true,
  },
  {
    name: "C2",
    key: "c2",
    type: "language_level",
    languages: ["en"],
    description: "欧洲语言共同参考框架C2等级 - 精通接近母语",
    sortOrder: 11,
    isActive: true,
    isSystem: true,
  },

  // 3. 语言等级过滤器 - 中文
  {
    name: "HSK1",
    key: "hsk1",
    type: "language_level",
    languages: ["zh-CN", "zh-TW"],
    description: "汉语水平考试HSK1等级 - 基础汉字",
    sortOrder: 12,
    isActive: true,
    isSystem: true,
  },
  {
    name: "HSK2",
    key: "hsk2",
    type: "language_level",
    languages: ["zh-CN", "zh-TW"],
    description: "汉语水平考试HSK2等级 - 初中级简单对话",
    sortOrder: 13,
    isActive: true,
    isSystem: true,
  },
  {
    name: "HSK3",
    key: "hsk3",
    type: "language_level",
    languages: ["zh-CN", "zh-TW"],
    description: "汉语水平考试HSK3等级 - 中级日常交流",
    sortOrder: 14,
    isActive: true,
    isSystem: true,
  },

  // 4. 内容类型过滤器（通用）
  {
    name: "对话",
    key: "dialogue",
    type: "content_type",
    languages: null,
    description: "对话形式的学习内容，适合练习口语交流",
    sortOrder: 20,
    isActive: true,
    isSystem: false,
  },
  {
    name: "文章",
    key: "article",
    type: "content_type",
    languages: null,
    description: "文章阅读内容，提升阅读理解能力",
    sortOrder: 21,
    isActive: true,
    isSystem: false,
  },
  {
    name: "新闻",
    key: "news",
    type: "content_type",
    languages: null,
    description: "新闻资讯内容，了解时事和正式语言",
    sortOrder: 22,
    isActive: true,
    isSystem: false,
  },
  {
    name: "故事",
    key: "story",
    type: "content_type",
    languages: null,
    description: "故事叙述内容，增强语言感知力",
    sortOrder: 23,
    isActive: true,
    isSystem: false,
  },

  // 5. 主题过滤器（通用）
  {
    name: "日常生活",
    key: "daily_life",
    type: "topic",
    languages: null,
    description: "日常生活相关主题，包括购物、饮食、交通等",
    sortOrder: 30,
    isActive: true,
    isSystem: false,
  },
  {
    name: "工作职场",
    key: "workplace",
    type: "topic",
    languages: null,
    description: "工作和职场相关主题，商务交流和职业发展",
    sortOrder: 31,
    isActive: true,
    isSystem: false,
  },
  {
    name: "旅游出行",
    key: "travel",
    type: "topic",
    languages: null,
    description: "旅游和出行相关主题，旅行经历和文化体验",
    sortOrder: 32,
    isActive: true,
    isSystem: false,
  },
  {
    name: "学习教育",
    key: "education",
    type: "topic",
    languages: null,
    description: "学习和教育相关主题，学术讨论和知识分享",
    sortOrder: 33,
    isActive: true,
    isSystem: false,
  },
  {
    name: "美食餐饮",
    key: "food_dining",
    type: "topic",
    languages: null,
    description: "美食、餐饮、烹饪相关主题",
    sortOrder: 34,
    isActive: true,
    isSystem: false,
  },
  {
    name: "健康医疗",
    key: "health_medical",
    type: "topic",
    languages: null,
    description: "健康、医疗、养生相关主题",
    sortOrder: 35,
    isActive: true,
    isSystem: false,
  },
  {
    name: "娱乐休闲",
    key: "entertainment",
    type: "topic",
    languages: null,
    description: "娱乐、休闲、兴趣爱好相关主题",
    sortOrder: 36,
    isActive: true,
    isSystem: false,
  },
  {
    name: "科技数码",
    key: "technology",
    type: "topic",
    languages: null,
    description: "科技、数码、互联网相关主题",
    sortOrder: 37,
    isActive: true,
    isSystem: false,
  },
  {
    name: "文化艺术",
    key: "culture_art",
    type: "topic",
    languages: null,
    description: "文化、艺术、历史相关主题",
    sortOrder: 38,
    isActive: true,
    isSystem: false,
  },
  {
    name: "体育运动",
    key: "sports",
    type: "topic",
    languages: null,
    description: "体育、运动、健身相关主题",
    sortOrder: 39,
    isActive: true,
    isSystem: false,
  },
  {
    name: "家庭生活",
    key: "family_life",
    type: "topic",
    languages: null,
    description: "家庭、亲情、育儿相关主题",
    sortOrder: 34,
    isActive: true,
    isSystem: false,
  },
  {
    name: "购物消费",
    key: "shopping",
    type: "topic",
    languages: null,
    description: "购物、消费、商品相关主题",
    sortOrder: 35,
    isActive: true,
    isSystem: false,
  },
  {
    name: "交通出行",
    key: "transportation",
    type: "topic",
    languages: null,
    description: "交通工具、出行方式相关主题",
    sortOrder: 36,
    isActive: true,
    isSystem: false,
  },
  {
    name: "住房居住",
    key: "housing",
    type: "topic",
    languages: null,
    description: "住房、居住、装修相关主题",
    sortOrder: 37,
    isActive: true,
    isSystem: false,
  },
  {
    name: "节日庆典",
    key: "festivals",
    type: "topic",
    languages: null,
    description: "节日、庆典、传统文化相关主题",
    sortOrder: 38,
    isActive: true,
    isSystem: false,
  },

  // 6. 教材过滤器
  {
    name: "大家的日语1",
    key: "minna_no_nihongo_1",
    type: "material",
    languages: ["ja"],
    description: "《大家的日语1》教材内容",
    sortOrder: 40,
    isActive: true,
    isSystem: false,
  },
  {
    name: "大家的日语2",
    key: "minna_no_nihongo_2",
    type: "material",
    languages: ["ja"],
    description: "《大家的日语2》教材内容",
    sortOrder: 41,
    isActive: true,
    isSystem: false,
  },
  {
    name: "新概念英语第一册",
    key: "new_concept_english_1",
    type: "material",
    languages: ["en"],
    description: "《新概念英语第一册》First Things First",
    sortOrder: 42,
    isActive: true,
    isSystem: false,
  },
  {
    name: "新概念英语第二册",
    key: "new_concept_english_2",
    type: "material",
    languages: ["en"],
    description: "《新概念英语第二册》Practice and Progress",
    sortOrder: 43,
    isActive: true,
    isSystem: false,
  },
  {
    name: "新概念英语第三册",
    key: "new_concept_english_3",
    type: "material",
    languages: ["en"],
    description: "《新概念英语第三册》Developing Skills",
    sortOrder: 44,
    isActive: true,
    isSystem: false,
  },
  {
    name: "新概念英语第四册",
    key: "new_concept_english_4",
    type: "material",
    languages: ["en"],
    description: "《新概念英语第四册》Fluency in English",
    sortOrder: 45,
    isActive: true,
    isSystem: false,
  },
  {
    name: "标准日本语初级上",
    key: "standard_japanese_beginner_1",
    type: "material",
    languages: ["ja"],
    description: "《标准日本语初级上》教材内容",
    sortOrder: 46,
    isActive: true,
    isSystem: false,
  },
  {
    name: "标准日本语初级下",
    key: "standard_japanese_beginner_2",
    type: "material",
    languages: ["ja"],
    description: "《标准日本语初级下》教材内容",
    sortOrder: 47,
    isActive: true,
    isSystem: false,
  },
  {
    name: "标准日本语中级上",
    key: "standard_japanese_intermediate_1",
    type: "material",
    languages: ["ja"],
    description: "《标准日本语中级上》教材内容",
    sortOrder: 48,
    isActive: true,
    isSystem: false,
  },
  {
    name: "标准日本语中级下",
    key: "standard_japanese_intermediate_2",
    type: "material",
    languages: ["ja"],
    description: "《标准日本语中级下》教材内容",
    sortOrder: 49,
    isActive: true,
    isSystem: false,
  },
  {
    name: "标准日本语高级上",
    key: "standard_japanese_advanced_1",
    type: "material",
    languages: ["ja"],
    description: "《标准日本语高级上》教材内容",
    sortOrder: 50,
    isActive: true,
    isSystem: false,
  },
  {
    name: "标准日本语高级下",
    key: "standard_japanese_advanced_2",
    type: "material",
    languages: ["ja"],
    description: "《标准日本语高级下》教材内容",
    sortOrder: 51,
    isActive: true,
    isSystem: false,
  },
  {
    name: "新编日语第一册",
    key: "new_japanese_1",
    type: "material",
    languages: ["ja"],
    description: "《新编日语第一册》教材内容",
    sortOrder: 52,
    isActive: true,
    isSystem: false,
  },
  {
    name: "新编日语第二册",
    key: "new_japanese_2",
    type: "material",
    languages: ["ja"],
    description: "《新编日语第二册》教材内容",
    sortOrder: 53,
    isActive: true,
    isSystem: false,
  },
  {
    name: "新编日语第三册",
    key: "new_japanese_3",
    type: "material",
    languages: ["ja"],
    description: "《新编日语第三册》教材内容",
    sortOrder: 54,
    isActive: true,
    isSystem: false,
  },
  {
    name: "新编日语第四册",
    key: "new_japanese_4",
    type: "material",
    languages: ["ja"],
    description: "《新编日语第四册》教材内容",
    sortOrder: 55,
    isActive: true,
    isSystem: false,
  },
  {
    name: "综合日语第一册",
    key: "comprehensive_japanese_1",
    type: "material",
    languages: ["ja"],
    description: "《综合日语第一册》教材内容",
    sortOrder: 56,
    isActive: true,
    isSystem: false,
  },
  {
    name: "综合日语第二册",
    key: "comprehensive_japanese_2",
    type: "material",
    languages: ["ja"],
    description: "《综合日语第二册》教材内容",
    sortOrder: 57,
    isActive: true,
    isSystem: false,
  },
  {
    name: "综合日语第三册",
    key: "comprehensive_japanese_3",
    type: "material",
    languages: ["ja"],
    description: "《综合日语第三册》教材内容",
    sortOrder: 58,
    isActive: true,
    isSystem: false,
  },
  {
    name: "综合日语第四册",
    key: "comprehensive_japanese_4",
    type: "material",
    languages: ["ja"],
    description: "《综合日语第四册》教材内容",
    sortOrder: 59,
    isActive: true,
    isSystem: false,
  },

  // 7. 多语言混合示例
  {
    name: "商务交流",
    key: "business_communication",
    type: "topic",
    languages: ["ja", "en"],
    description: "商务场景下的交流内容，适用于日语和英语学习",
    sortOrder: 60,
    isActive: true,
    isSystem: false,
  },
];

/**
 * 初始化过滤器数据
 */
async function initFilters() {
  try {
    console.log("开始初始化过滤器数据...");

    // 检查是否已有数据
    const existingCount = await db.Filter.count();
    if (existingCount > 0) {
      console.log(`数据库中已有${existingCount}个过滤器，跳过初始化`);
      console.log("如需重新初始化，请先清空filters表");
      return true;
    }

    // 批量创建过滤器
    const createdFilters = await db.Filter.bulkCreate(defaultFilters);
    console.log(`已创建${createdFilters.length}个过滤器`);

    // 统计各类型数量
    const stats = await db.Filter.findAll({
      attributes: [
        'type',
        [db.sequelize.fn('COUNT', db.sequelize.col('id')), 'count']
      ],
      group: ['type'],
      raw: true
    });

    console.log("\n过滤器统计:");
    stats.forEach(stat => {
      console.log(`  ${stat.type}: ${stat.count}个`);
    });

    console.log("\n过滤器初始化完成");
    return true;
  } catch (error) {
    console.error("初始化过滤器失败:", error);
    return false;
  }
}

/**
 * 清空并重新初始化过滤器数据
 */
async function resetFilters() {
  try {
    console.log("开始重置过滤器数据...");
    
    // 清空现有数据
    await db.Filter.destroy({ where: {} });
    console.log("已清空现有过滤器数据");
    
    // 重新初始化
    return await initFilters();
  } catch (error) {
    console.error("重置过滤器失败:", error);
    return false;
  }
}

// 如果直接执行此脚本
if (require.main === module) {
  (async () => {
    try {
      // 检查命令行参数
      const args = process.argv.slice(2);
      const shouldReset = args.includes('--reset') || args.includes('-r');
      
      if (shouldReset) {
        await resetFilters();
      } else {
        await initFilters();
      }
    } catch (error) {
      console.error("执行脚本失败:", error);
    } finally {
      // 关闭数据库连接
      await db.sequelize.close();
    }
  })();
}

// 导出函数
module.exports = {
  initFilters,
  resetFilters
};
