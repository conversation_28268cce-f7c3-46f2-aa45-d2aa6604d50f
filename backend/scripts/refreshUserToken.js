/**
 * 刷新用户token脚本
 * 使指定用户的所有会话失效，强制重新登录以获取新的角色信息
 */

const db = require('../models');

async function refreshUserToken(userId) {
  try {
    console.log(`🔄 开始刷新用户token: ${userId}`);
    console.log('='.repeat(50));
    
    // 查找用户
    const user = await db.User.findByPk(userId);
    if (!user) {
      console.error(`❌ 用户不存在: ${userId}`);
      process.exit(1);
    }
    
    console.log(`✅ 找到用户:`);
    console.log(`   ID: ${user.id}`);
    console.log(`   邮箱: ${user.email}`);
    console.log(`   角色: ${user.role}`);
    
    // 删除用户的所有会话
    const deletedSessions = await db.UserSession.destroy({
      where: { userId: userId }
    });
    
    console.log(`🗑️  已删除 ${deletedSessions} 个会话`);
    console.log(`✅ 用户需要重新登录以获取新的角色权限`);
    console.log('='.repeat(50));
    
    process.exit(0);
  } catch (error) {
    console.error('❌ 刷新token失败:', error);
    process.exit(1);
  }
}

// 获取命令行参数
const userId = process.argv[2];

if (!userId) {
  console.log('❌ 请提供用户ID');
  console.log('用法: node scripts/refreshUserToken.js <用户ID>');
  console.log('示例: node scripts/refreshUserToken.js dH7ky7p0p_FBA3qmtW8OS');
  process.exit(1);
}

// 连接数据库并执行
db.sequelize.authenticate()
  .then(() => {
    console.log('✅ 数据库连接成功');
    return refreshUserToken(userId);
  })
  .catch(error => {
    console.error('❌ 数据库连接失败:', error);
    process.exit(1);
  });
