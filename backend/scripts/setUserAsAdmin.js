/**
 * 设置用户为管理员脚本
 * 使用方法: node scripts/setUserAsAdmin.js <userId>
 */

const db = require('../models');

async function setUserAsAdmin(userId) {
  try {
    console.log(`正在查找用户 ID: ${userId}`);
    
    // 查找用户
    const user = await db.User.findByPk(userId);
    
    if (!user) {
      console.error(`❌ 用户不存在: ${userId}`);
      process.exit(1);
    }
    
    console.log(`✅ 找到用户:`);
    console.log(`   ID: ${user.id}`);
    console.log(`   邮箱: ${user.email}`);
    console.log(`   用户名: ${user.username || '未设置'}`);
    console.log(`   当前角色: ${user.role}`);
    console.log(`   当前等级: ${user.level}`);
    console.log(`   状态: ${user.status}`);
    
    // 检查是否已经是管理员
    if (user.role === 'admin') {
      console.log(`⚠️  用户已经是管理员，无需修改`);
      process.exit(0);
    }
    
    // 更新用户角色为管理员
    await user.update({
      role: 'admin'
    });
    
    console.log(`🎉 成功将用户设置为管理员!`);
    console.log(`   用户 ID: ${userId}`);
    console.log(`   邮箱: ${user.email}`);
    console.log(`   新角色: admin`);
    
  } catch (error) {
    console.error('❌ 设置管理员失败:', error.message);
    console.error(error);
    process.exit(1);
  }
}

// 主函数
async function main() {
  // 获取命令行参数
  const userId = process.argv[2];
  
  if (!userId) {
    console.error('❌ 请提供用户 ID');
    console.log('使用方法: node scripts/setUserAsAdmin.js <userId>');
    console.log('示例: node scripts/setUserAsAdmin.js d3izO0bRLRY8ntee2P0wg');
    process.exit(1);
  }
  
  console.log('🚀 开始设置用户为管理员...');
  console.log('='.repeat(50));
  
  await setUserAsAdmin(userId);
  
  console.log('='.repeat(50));
  console.log('✅ 操作完成');
  
  // 关闭数据库连接
  await db.sequelize.close();
}

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { setUserAsAdmin };
