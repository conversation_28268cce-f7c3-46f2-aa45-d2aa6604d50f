/**
 * 用户管理脚本
 * 提供查看、修改用户信息的功能
 *
 * 🚀 快速使用教程:
 *
 * 1. 进入后端目录:
 *    cd backend
 *
 * 2. 查看用户信息:
 *    node scripts/userManager.js show <用户ID>
 *    示例: node scripts/userManager.js show d3izO0bRLRY8ntee2P0wg
 *
 * 3. 设置用户为管理员:
 *    node scripts/userManager.js role <用户ID> admin
 *    示例: node scripts/userManager.js role d3izO0bRLRY8ntee2P0wg admin
 *
 * 4. 设置用户等级:
 *    node scripts/userManager.js level <用户ID> <等级>
 *    等级: 0=免费, 1=基础, 2=高级, 3=专业
 *    示例: node scripts/userManager.js level d3izO0bRLRY8ntee2P0wg 3
 *
 * 5. 设置用户状态:
 *    node scripts/userManager.js status <用户ID> <状态>
 *    状态: active=正常, inactive=未激活, banned=封禁
 *    示例: node scripts/userManager.js status d3izO0bRLRY8ntee2P0wg active
 *
 * 💡 提示: 运行 node scripts/userManager.js 可查看完整帮助
 */

const db = require('../models');

// 显示用户信息
async function showUser(userId) {
  try {
    const user = await db.User.findByPk(userId);
    
    if (!user) {
      console.error(`❌ 用户不存在: ${userId}`);
      return false;
    }
    
    console.log(`📋 用户信息:`);
    console.log(`   ID: ${user.id}`);
    console.log(`   邮箱: ${user.email}`);
    console.log(`   用户名: ${user.username || '未设置'}`);
    console.log(`   角色: ${user.role}`);
    console.log(`   等级: ${user.level}`);
    console.log(`   状态: ${user.status}`);
    console.log(`   最后登录: ${user.lastLoginAt || '从未登录'}`);
    console.log(`   创建时间: ${user.createdAt}`);
    console.log(`   更新时间: ${user.updatedAt}`);
    
    return true;
  } catch (error) {
    console.error('❌ 查询用户失败:', error.message);
    return false;
  }
}

// 设置用户角色
async function setUserRole(userId, role) {
  try {
    const user = await db.User.findByPk(userId);
    
    if (!user) {
      console.error(`❌ 用户不存在: ${userId}`);
      return false;
    }
    
    const oldRole = user.role;
    await user.update({ role });
    
    console.log(`✅ 角色更新成功:`);
    console.log(`   用户: ${user.email}`);
    console.log(`   ${oldRole} → ${role}`);
    
    return true;
  } catch (error) {
    console.error('❌ 更新角色失败:', error.message);
    return false;
  }
}

// 设置用户等级
async function setUserLevel(userId, level) {
  try {
    const user = await db.User.findByPk(userId);
    
    if (!user) {
      console.error(`❌ 用户不存在: ${userId}`);
      return false;
    }
    
    const oldLevel = user.level;
    await user.update({ level: parseInt(level) });
    
    console.log(`✅ 等级更新成功:`);
    console.log(`   用户: ${user.email}`);
    console.log(`   ${oldLevel} → ${level}`);
    
    return true;
  } catch (error) {
    console.error('❌ 更新等级失败:', error.message);
    return false;
  }
}

// 设置用户状态
async function setUserStatus(userId, status) {
  try {
    const user = await db.User.findByPk(userId);
    
    if (!user) {
      console.error(`❌ 用户不存在: ${userId}`);
      return false;
    }
    
    const oldStatus = user.status;
    await user.update({ status });
    
    console.log(`✅ 状态更新成功:`);
    console.log(`   用户: ${user.email}`);
    console.log(`   ${oldStatus} → ${status}`);
    
    return true;
  } catch (error) {
    console.error('❌ 更新状态失败:', error.message);
    return false;
  }
}

// 显示帮助信息
function showHelp() {
  console.log('📖 用户管理脚本使用说明:');
  console.log('');
  console.log('查看用户信息:');
  console.log('  node scripts/userManager.js show <userId>');
  console.log('');
  console.log('设置用户角色:');
  console.log('  node scripts/userManager.js role <userId> <role>');
  console.log('  角色选项: user, admin');
  console.log('');
  console.log('设置用户等级:');
  console.log('  node scripts/userManager.js level <userId> <level>');
  console.log('  等级选项: 0=免费, 1=基础, 2=高级, 3=专业');
  console.log('');
  console.log('设置用户状态:');
  console.log('  node scripts/userManager.js status <userId> <status>');
  console.log('  状态选项: active, inactive, banned');
  console.log('');
  console.log('示例:');
  console.log('  node scripts/userManager.js show d3izO0bRLRY8ntee2P0wg');
  console.log('  node scripts/userManager.js role d3izO0bRLRY8ntee2P0wg admin');
  console.log('  node scripts/userManager.js level d3izO0bRLRY8ntee2P0wg 3');
}

// 主函数
async function main() {
  const [command, userId, value] = process.argv.slice(2);
  
  if (!command) {
    showHelp();
    process.exit(0);
  }
  
  console.log('🚀 用户管理脚本启动...');
  console.log('='.repeat(50));
  
  let success = false;
  
  switch (command) {
    case 'show':
      if (!userId) {
        console.error('❌ 请提供用户 ID');
        process.exit(1);
      }
      success = await showUser(userId);
      break;
      
    case 'role':
      if (!userId || !value) {
        console.error('❌ 请提供用户 ID 和角色');
        process.exit(1);
      }
      if (!['user', 'admin'].includes(value)) {
        console.error('❌ 角色必须是 user 或 admin');
        process.exit(1);
      }
      success = await setUserRole(userId, value);
      break;
      
    case 'level':
      if (!userId || !value) {
        console.error('❌ 请提供用户 ID 和等级');
        process.exit(1);
      }
      if (![0, 1, 2, 3].includes(parseInt(value))) {
        console.error('❌ 等级必须是 0, 1, 2, 3 中的一个');
        process.exit(1);
      }
      success = await setUserLevel(userId, value);
      break;
      
    case 'status':
      if (!userId || !value) {
        console.error('❌ 请提供用户 ID 和状态');
        process.exit(1);
      }
      if (!['active', 'inactive', 'banned'].includes(value)) {
        console.error('❌ 状态必须是 active, inactive, banned 中的一个');
        process.exit(1);
      }
      success = await setUserStatus(userId, value);
      break;
      
    default:
      console.error(`❌ 未知命令: ${command}`);
      showHelp();
      process.exit(1);
  }
  
  console.log('='.repeat(50));
  console.log(success ? '✅ 操作完成' : '❌ 操作失败');
  
  // 关闭数据库连接
  await db.sequelize.close();
}

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  showUser,
  setUserRole,
  setUserLevel,
  setUserStatus
};
