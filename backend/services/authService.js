/**
 * 认证服务
 * 处理用户认证相关的逻辑
 */
const jwt = require("jsonwebtoken");
const crypto = require("crypto");
const db = require("../models");
const nanoid = require("nanoid");
const emailService = require("./emailService");
const sessionService = require("./sessionService");
const dotenv = require("dotenv");

// 加载环境变量
dotenv.config();

// JWT密钥
const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";
// JWT过期时间（默认7天）
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "7d";
// 验证码有效期（默认10分钟）
const CODE_EXPIRES_IN = parseInt(process.env.CODE_EXPIRES_IN || "600", 10);
// 验证码长度
const CODE_LENGTH = parseInt(process.env.CODE_LENGTH || "6", 10);
// 验证码尝试次数限制
const MAX_ATTEMPTS = parseInt(process.env.MAX_ATTEMPTS || "5", 10);
// IP锁定时间（默认1小时）
const IP_LOCK_TIME = parseInt(process.env.IP_LOCK_TIME || "3600", 10);

// 存储IP尝试次数
const ipAttempts = {};

/**
 * 生成随机验证码
 * @param {number} length 验证码长度
 * @returns {string} 验证码
 */
function generateVerificationCode(length = CODE_LENGTH) {
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  return Math.floor(min + Math.random() * (max - min + 1)).toString();
}

/**
 * 检查IP是否被锁定
 * @param {string} ip IP地址
 * @returns {boolean} 是否被锁定
 */
function isIpLocked(ip) {
  if (!ipAttempts[ip]) {
    return false;
  }

  if (ipAttempts[ip].attempts >= MAX_ATTEMPTS) {
    const now = Date.now();
    if (now - ipAttempts[ip].timestamp < IP_LOCK_TIME * 1000) {
      return true;
    }
    // 重置尝试次数
    ipAttempts[ip] = { attempts: 0, timestamp: now };
  }

  return false;
}

/**
 * 记录IP尝试次数
 * @param {string} ip IP地址
 */
function recordIpAttempt(ip) {
  const now = Date.now();
  if (!ipAttempts[ip]) {
    ipAttempts[ip] = { attempts: 1, timestamp: now };
  } else {
    ipAttempts[ip].attempts += 1;
    ipAttempts[ip].timestamp = now;
  }
}

/**
 * 发送验证码
 * @param {string} email 邮箱
 * @param {string} type 验证码类型
 * @param {string} ip IP地址
 * @returns {Promise<Object>} 发送结果
 */
async function sendCode(email, type = "login", ip = "") {
  try {
    // 检查IP是否被锁定
    if (ip && isIpLocked(ip)) {
      return {
        success: false,
        error: "请求过于频繁，请稍后再试",
      };
    }

    // 生成验证码
    const code = generateVerificationCode();
    // 计算过期时间
    const expiresAt = new Date(Date.now() + CODE_EXPIRES_IN * 1000);

    // 检查是否已存在未过期的验证码
    const existingCode = await db.VerificationCode.findOne({
      where: {
        email,
        type,
        expiresAt: { [db.Sequelize.Op.gt]: new Date() },
        used: false,
      },
    });

    if (existingCode) {
      // 如果存在未过期的验证码，更新它
      await existingCode.update({
        code,
        expiresAt,
        used: false,
      });
    } else {
      // 否则创建新的验证码
      await db.VerificationCode.create({
        id: nanoid.nanoid(),
        email,
        code,
        type,
        expiresAt,
      });
    }

    // 发送验证码邮件
    const sent = await emailService.sendVerificationCode(email, code, type);

    if (!sent) {
      return {
        success: false,
        error: "验证码发送失败，请稍后再试",
      };
    }

    return {
      success: true,
      message: "验证码已发送",
    };
  } catch (error) {
    console.error("发送验证码失败:", error);
    return {
      success: false,
      error: `发送验证码失败: ${error.message}`,
    };
  }
}

/**
 * 验证验证码
 * @param {string} email 邮箱
 * @param {string} code 验证码
 * @param {string} type 验证码类型
 * @param {string} ip IP地址
 * @param {Object} req 请求对象，用于获取用户代理等信息
 * @returns {Promise<Object>} 验证结果
 */
async function verifyCode(email, code, type = "login", ip = "", req = null) {
  try {
    // 检查IP是否被锁定
    if (ip && isIpLocked(ip)) {
      return {
        success: false,
        error: "请求过于频繁，请稍后再试",
      };
    }

    // 查找验证码
    const verificationCode = await db.VerificationCode.findOne({
      where: {
        email,
        code,
        type,
        expiresAt: { [db.Sequelize.Op.gt]: new Date() },
        used: false,
      },
    });

    if (!verificationCode) {
      // 记录IP尝试次数
      if (ip) {
        recordIpAttempt(ip);
      }
      return {
        success: false,
        error: "验证码无效或已过期",
      };
    }

    // 标记验证码为已使用
    await verificationCode.update({ used: true });

    // 查找或创建用户
    let user = await db.User.findOne({ where: { email } });

    if (!user) {
      // 如果用户不存在，创建新用户
      user = await db.User.create({
        id: nanoid.nanoid(),
        email,
        status: "active",
        role: "user",
        lastLoginAt: new Date(),
      });
    } else {
      // 更新最后登录时间
      await user.update({ lastLoginAt: new Date() });
    }

    // 创建会话
    const deviceInfo = {
      userAgent:
        req && req.headers && req.headers["user-agent"]
          ? req.headers["user-agent"]
          : "Unknown",
      platform: "Unknown",
    };

    // 解析JWT过期时间，将"7d"转换为秒数
    let expiresInSeconds = 31536000; // 默认1年
    if (typeof JWT_EXPIRES_IN === "string") {
      if (JWT_EXPIRES_IN.endsWith("d")) {
        // 如果是天数，如"7d"
        const days = parseInt(JWT_EXPIRES_IN);
        if (!isNaN(days)) {
          expiresInSeconds = days * 24 * 60 * 60; // 转换为秒
        }
      } else if (JWT_EXPIRES_IN.endsWith("h")) {
        // 如果是小时，如"24h"
        const hours = parseInt(JWT_EXPIRES_IN);
        if (!isNaN(hours)) {
          expiresInSeconds = hours * 60 * 60; // 转换为秒
        }
      } else if (JWT_EXPIRES_IN.endsWith("m")) {
        // 如果是分钟，如"60m"
        const minutes = parseInt(JWT_EXPIRES_IN);
        if (!isNaN(minutes)) {
          expiresInSeconds = minutes * 60; // 转换为秒
        }
      } else if (JWT_EXPIRES_IN.endsWith("s")) {
        // 如果是秒，如"3600s"
        const seconds = parseInt(JWT_EXPIRES_IN);
        if (!isNaN(seconds)) {
          expiresInSeconds = seconds; // 已经是秒
        }
      } else {
        // 如果是纯数字，假设是秒
        const seconds = parseInt(JWT_EXPIRES_IN);
        if (!isNaN(seconds)) {
          expiresInSeconds = seconds;
        }
      }
    }

    console.log(
      `[AuthService] JWT过期时间: ${JWT_EXPIRES_IN}, 转换为秒: ${expiresInSeconds}`
    );

    const session = await sessionService.createSession(
      user.id,
      deviceInfo,
      ip,
      expiresInSeconds // 使用转换后的秒数
    );

    // 生成JWT令牌，只包含身份信息，不包含角色信息
    const token = jwt.sign(
      {
        id: user.id,
        email: user.email,
        tokenId: session.tokenId, // 添加会话ID
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    return {
      success: true,
      token,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
        status: user.status,
        lastLoginAt: user.lastLoginAt,
      },
    };
  } catch (error) {
    console.error("验证码验证失败:", error);
    return {
      success: false,
      error: `验证码验证失败: ${error.message}`,
    };
  }
}

/**
 * 验证JWT令牌
 * @param {string} token JWT令牌
 * @returns {Promise<Object>} 验证结果
 */
async function verifyToken(token) {
  try {
    // 验证JWT令牌
    const decoded = jwt.verify(token, JWT_SECRET);
    console.log(
      `[AuthService] verifyToken: 令牌解码成功，用户ID: ${decoded.id}`
    );

    // 验证会话是否有效
    if (decoded.tokenId) {
      console.log(
        `[AuthService] verifyToken: 令牌包含会话ID: ${decoded.tokenId}`
      );
      const session = await sessionService.validateSession(decoded.tokenId);
      if (!session) {
        console.log(
          `[AuthService] verifyToken: 会话验证失败，会话ID: ${decoded.tokenId}`
        );
        return {
          success: false,
          error: "会话已过期或无效，请重新登录",
          code: "SESSION_EXPIRED",
        };
      }
      console.log(
        `[AuthService] verifyToken: 会话验证成功，会话ID: ${decoded.tokenId}`
      );
    } else {
      // 兼容旧令牌（没有tokenId的令牌）
      console.warn(
        "[AuthService] verifyToken: 检测到旧格式令牌（无会话ID），仍然允许访问"
      );
    }

    return {
      success: true,
      decoded,
    };
  } catch (error) {
    console.error(`[AuthService] verifyToken: 令牌验证失败: ${error.message}`);
    return {
      success: false,
      error: "无效的令牌，请重新登录",
      code: "TOKEN_INVALID",
    };
  }
}

module.exports = {
  sendCode,
  verifyCode,
  verifyToken,
};
