/**
 * 合集服务
 * 处理合集相关的业务逻辑
 */
const { nanoid } = require("nanoid");
const db = require("../models");

class CollectionService {
  /**
   * 获取用户的合集列表
   * @param {string} userId 用户ID
   * @param {Object} options 查询选项
   * @returns {Object} 合集列表
   */
  async getUserCollections(userId, options = {}) {
    const {
      page = 1,
      pageSize = 20,
      search = "",
      status,
      learningLanguage,
      sortBy = "updated_at",
      sortOrder = "DESC",
    } = options;

    // 构建查询条件
    const where = { userId };

    if (search) {
      where[db.Sequelize.Op.or] = [
        { name: { [db.Sequelize.Op.like]: `%${search}%` } },
        { description: { [db.Sequelize.Op.like]: `%${search}%` } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (learningLanguage) {
      where.learningLanguage = learningLanguage;
    }

    // 直接使用数据库字段名，简单直接
    const dbSortBy = sortBy;
    const order = [
      [dbSortBy, sortOrder],
      [{ model: db.CollectionItem, as: "items" }, "sortOrder", "ASC"],
    ];

    // 查询合集
    const { count, rows } = await db.Collection.findAndCountAll({
      where,
      order,
      limit: parseInt(pageSize),
      offset: (parseInt(page) - 1) * parseInt(pageSize),
      include: [
        {
          model: db.CollectionItem,
          as: "items",
          include: [
            {
              model: db.Content,
              as: "content",
              attributes: ["id", "name", ["thumbnail_url", "thumbnailUrl"]],
            },
          ],
        },
        {
          model: db.User,
          as: "creator",
          attributes: ["id", "username"],
        },
      ],
      distinct: true, // 确保计数时去重，避免因关联查询导致的重复计数
    });

    // 计算每个合集的内容数量并确保排序
    const collections = rows.map((collection) => {
      const collectionData = collection.toJSON();
      collectionData.itemCount = collection.items ? collection.items.length : 0;
      // 确保合集项按sortOrder排序
      if (collectionData.items && collectionData.items.length > 0) {
        collectionData.items.sort((a, b) => a.sortOrder - b.sortOrder);
      }
      return collectionData;
    });

    return {
      collections,
      pagination: {
        total: count,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
      },
    };
  }

  /**
   * 获取公开合集列表
   * @param {Object} options 查询选项
   * @returns {Object} 公开合集列表
   */
  async getPublicCollections(options = {}) {
    const {
      page = 1,
      pageSize = 20,
      search = "",
      learningLanguage,
      sortBy = "updated_at",
      sortOrder = "DESC",
    } = options;

    // 构建查询条件
    const where = {
      isPublic: true,
      status: "published",
    };

    if (search) {
      where[db.Sequelize.Op.or] = [
        { name: { [db.Sequelize.Op.like]: `%${search}%` } },
        { description: { [db.Sequelize.Op.like]: `%${search}%` } },
      ];
    }

    if (learningLanguage) {
      where.learningLanguage = learningLanguage;
    }

    // 直接使用数据库字段名，简单直接
    const dbSortBy = sortBy;
    const order = [
      [dbSortBy, sortOrder],
      [{ model: db.CollectionItem, as: "items" }, "sortOrder", "ASC"],
    ];

    // 查询合集
    const { count, rows } = await db.Collection.findAndCountAll({
      where,
      order,
      limit: parseInt(pageSize),
      offset: (parseInt(page) - 1) * parseInt(pageSize),
      distinct: true,
      col: "id",
      include: [
        {
          model: db.CollectionItem,
          as: "items",
          include: [
            {
              model: db.Content,
              as: "content",
              attributes: ["id", "name", ["thumbnail_url", "thumbnailUrl"]],
            },
          ],
        },
        {
          model: db.User,
          as: "creator",
          attributes: ["id", "username"],
        },
      ],
    });

    // 计算每个合集的内容数量并确保排序
    const collections = rows.map((collection) => {
      const collectionData = collection.toJSON();
      collectionData.itemCount = collection.items ? collection.items.length : 0;
      // 确保合集项按sortOrder排序
      if (collectionData.items && collectionData.items.length > 0) {
        collectionData.items.sort((a, b) => a.sortOrder - b.sortOrder);
      }
      return collectionData;
    });

    return {
      collections,
      pagination: {
        total: count,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
      },
    };
  }

  /**
   * 根据ID获取合集详情
   * @param {string} collectionId 合集ID
   * @param {string} userId 当前用户ID（可选）
   * @returns {Object} 合集详情
   */
  async getCollectionById(collectionId, userId = null) {
    const collection = await db.Collection.findByPk(collectionId, {
      include: [
        {
          model: db.CollectionItem,
          as: "items",
          include: [
            {
              model: db.Content,
              as: "content",
              attributes: [
                "id",
                "name",
                "description",
                ["thumbnail_url", "thumbnailUrl"],
                "status",
                ["created_at", "createdAt"],
                ["updated_at", "updatedAt"],
              ],
            },
          ],
        },
        {
          model: db.User,
          as: "creator",
          attributes: ["id", "username"],
        },
      ],
      order: [[{ model: db.CollectionItem, as: "items" }, "sortOrder", "ASC"]],
    });

    if (!collection) {
      throw new Error("合集不存在");
    }

    // 检查访问权限
    if (!collection.isPublic && collection.userId !== userId) {
      throw new Error("无权访问此合集");
    }

    const collectionData = collection.toJSON();
    collectionData.itemCount = collection.items ? collection.items.length : 0;

    // 确保合集项按sortOrder排序
    if (collectionData.items && collectionData.items.length > 0) {
      collectionData.items.sort((a, b) => a.sortOrder - b.sortOrder);
    }

    // 如果有用户ID，检查是否已收藏
    if (userId) {
      const favorite = await db.CollectionFavorite.findOne({
        where: { collectionId, userId },
      });
      collectionData.isFavorited = !!favorite;
    }

    return collectionData;
  }

  /**
   * 创建合集
   * @param {Object} collectionData 合集数据
   * @param {string} userId 创建者ID
   * @returns {Object} 创建的合集
   */
  async createCollection(collectionData, userId) {
    const {
      name,
      description,
      coverImageUrl,
      tags,
      isPublic = false,
      learningLanguage = "ja", // 默认为日语
    } = collectionData;

    if (!name) {
      throw new Error("合集名称不能为空");
    }

    const id = nanoid();

    const collection = await db.Collection.create({
      id,
      name,
      description,
      coverImageUrl,
      userId,
      tags,
      isPublic,
      learningLanguage,
      status: "draft",
    });

    return collection;
  }

  /**
   * 更新合集
   * @param {string} collectionId 合集ID
   * @param {Object} updateData 更新数据
   * @param {string} userId 用户ID
   * @returns {Object} 更新后的合集
   */
  async updateCollection(collectionId, updateData, userId) {
    const collection = await db.Collection.findByPk(collectionId);

    if (!collection) {
      throw new Error("合集不存在");
    }

    // 检查权限
    if (collection.userId !== userId) {
      throw new Error("无权编辑此合集");
    }

    const { name, description, coverImageUrl, tags, isPublic, status } =
      updateData;

    await collection.update({
      name: name !== undefined ? name : collection.name,
      description:
        description !== undefined ? description : collection.description,
      coverImageUrl:
        coverImageUrl !== undefined ? coverImageUrl : collection.coverImageUrl,
      tags: tags !== undefined ? tags : collection.tags,
      isPublic: isPublic !== undefined ? isPublic : collection.isPublic,
      status: status !== undefined ? status : collection.status,
    });

    return collection;
  }

  /**
   * 删除合集
   * @param {string} collectionId 合集ID
   * @param {string} userId 用户ID
   */
  async deleteCollection(collectionId, userId) {
    const collection = await db.Collection.findByPk(collectionId);

    if (!collection) {
      throw new Error("合集不存在");
    }

    // 检查权限
    if (collection.userId !== userId) {
      throw new Error("无权删除此合集");
    }

    await collection.destroy();
  }

  /**
   * 添加内容到合集
   * @param {string} collectionId 合集ID
   * @param {string} contentId 内容ID
   * @param {string} userId 用户ID
   * @returns {Object} 添加的合集项
   */
  async addContentToCollection(collectionId, contentId, userId) {
    const collection = await db.Collection.findByPk(collectionId);
    if (!collection) {
      throw new Error("合集不存在");
    }

    // 检查权限
    if (collection.userId !== userId) {
      throw new Error("无权编辑此合集");
    }

    // 检查内容是否存在且属于用户
    const content = await db.Content.findOne({
      where: { id: contentId, userId, status: "published" },
    });
    if (!content) {
      throw new Error("内容不存在或无权访问");
    }

    // 检查是否已存在
    const existingItem = await db.CollectionItem.findOne({
      where: { collectionId, contentId },
    });
    if (existingItem) {
      throw new Error("内容已存在于合集中");
    }

    // 获取下一个排序号
    const maxOrder = await db.CollectionItem.max("sortOrder", {
      where: { collectionId },
    });
    const sortOrder = (maxOrder || 0) + 1;

    const id = nanoid();
    const collectionItem = await db.CollectionItem.create({
      id,
      collectionId,
      contentId,
      sortOrder,
    });

    return collectionItem;
  }

  /**
   * 从合集中移除内容
   * @param {string} collectionId 合集ID
   * @param {string} contentId 内容ID
   * @param {string} userId 用户ID
   */
  async removeContentFromCollection(collectionId, contentId, userId) {
    const collection = await db.Collection.findByPk(collectionId);
    if (!collection) {
      throw new Error("合集不存在");
    }

    // 检查权限
    if (collection.userId !== userId) {
      throw new Error("无权编辑此合集");
    }

    const collectionItem = await db.CollectionItem.findOne({
      where: { collectionId, contentId },
    });
    if (!collectionItem) {
      throw new Error("内容不在合集中");
    }

    await collectionItem.destroy();

    // 重新排序剩余项目
    await this.reorderCollectionItems(collectionId);
  }

  /**
   * 更新合集内容排序
   * @param {string} collectionId 合集ID
   * @param {Array} itemOrders 排序数组 [{contentId, sortOrder}]
   * @param {string} userId 用户ID
   */
  async updateCollectionItemOrder(collectionId, itemOrders, userId) {
    const collection = await db.Collection.findByPk(collectionId);
    if (!collection) {
      throw new Error("合集不存在");
    }

    // 检查权限
    if (collection.userId !== userId) {
      throw new Error("无权编辑此合集");
    }

    // 批量更新排序
    const transaction = await db.sequelize.transaction();
    try {
      for (const item of itemOrders) {
        await db.CollectionItem.update(
          { sortOrder: item.sortOrder },
          {
            where: { collectionId, contentId: item.contentId },
            transaction,
          }
        );
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 重新排序合集项目
   * @param {string} collectionId 合集ID
   */
  async reorderCollectionItems(collectionId) {
    const items = await db.CollectionItem.findAll({
      where: { collectionId },
      order: [["sortOrder", "ASC"]],
    });

    const transaction = await db.sequelize.transaction();
    try {
      for (let i = 0; i < items.length; i++) {
        await items[i].update({ sortOrder: i + 1 }, { transaction });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 收藏/取消收藏合集
   * @param {string} collectionId 合集ID
   * @param {string} userId 用户ID
   * @returns {Object} 操作结果
   */
  async toggleCollectionFavorite(collectionId, userId) {
    const collection = await db.Collection.findByPk(collectionId);
    if (!collection) {
      throw new Error("合集不存在");
    }

    // 检查合集是否公开
    if (!collection.isPublic) {
      throw new Error("无法收藏私有合集");
    }

    const existingFavorite = await db.CollectionFavorite.findOne({
      where: { collectionId, userId },
    });

    if (existingFavorite) {
      // 取消收藏
      await existingFavorite.destroy();
      await collection.decrement("favoriteCount");
      return { isFavorited: false };
    } else {
      // 添加收藏
      const id = nanoid();
      await db.CollectionFavorite.create({
        id,
        collectionId,
        userId,
      });
      await collection.increment("favoriteCount");
      return { isFavorited: true };
    }
  }

  /**
   * 增加合集浏览次数
   * @param {string} collectionId 合集ID
   */
  async incrementViewCount(collectionId) {
    await db.Collection.increment("viewCount", {
      where: { id: collectionId },
    });
  }

  /**
   * 批量添加内容到合集
   * @param {string} collectionId 合集ID
   * @param {Array} contentIds 内容ID数组
   * @param {string} userId 用户ID
   * @returns {Array} 添加结果
   */
  async addMultipleContentsToCollection(collectionId, contentIds, userId) {
    const collection = await db.Collection.findByPk(collectionId);
    if (!collection) {
      throw new Error("合集不存在");
    }

    // 检查权限
    if (collection.userId !== userId) {
      throw new Error("无权编辑此合集");
    }

    const results = [];
    const transaction = await db.sequelize.transaction();

    try {
      // 获取当前最大排序号
      let maxOrder =
        (await db.CollectionItem.max("sortOrder", {
          where: { collectionId },
          transaction,
        })) || 0;

      for (const contentId of contentIds) {
        try {
          // 检查内容是否存在且属于用户
          const content = await db.Content.findOne({
            where: { id: contentId, userId, status: "published" },
            transaction,
          });

          if (!content) {
            results.push({
              contentId,
              success: false,
              error: "内容不存在或无权访问",
            });
            continue;
          }

          // 检查是否已存在
          const existingItem = await db.CollectionItem.findOne({
            where: { collectionId, contentId },
            transaction,
          });

          if (existingItem) {
            results.push({
              contentId,
              success: false,
              error: "内容已存在于合集中",
            });
            continue;
          }

          // 添加到合集
          maxOrder += 1;
          const id = nanoid();
          const collectionItem = await db.CollectionItem.create(
            {
              id,
              collectionId,
              contentId,
              sortOrder: maxOrder,
            },
            { transaction }
          );

          results.push({ contentId, success: true, collectionItem });
        } catch (error) {
          results.push({ contentId, success: false, error: error.message });
        }
      }

      await transaction.commit();
      return results;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 获取用户收藏的合集列表
   * @param {string} userId 用户ID
   * @param {Object} options 查询选项
   * @returns {Object} 收藏合集列表
   */
  async getUserFavoriteCollections(userId, options = {}) {
    // 简化实现，与普通收藏API保持一致
    const favorites = await db.CollectionFavorite.findAll({
      where: {
        userId: userId,
      },
      include: [
        {
          model: db.Collection,
          as: "collection",
          attributes: [
            "id",
            "name",
            "description",
            "coverImageUrl",
            "tags",
            "status",
            "isPublic",
            "viewCount",
            "favoriteCount",
            ["created_at", "createdAt"],
            ["updated_at", "updatedAt"],
          ],
          include: [
            {
              model: db.User,
              as: "creator",
              attributes: ["id", "username"],
            },
            {
              model: db.CollectionItem,
              as: "items",
              include: [
                {
                  model: db.Content,
                  as: "content",
                  attributes: ["id", "name", ["thumbnail_url", "thumbnailUrl"]],
                },
              ],
            },
          ],
        },
      ],
      order: [
        ["created_at", "DESC"],
        [
          { model: db.Collection, as: "collection" },
          { model: db.CollectionItem, as: "items" },
          "sortOrder",
          "ASC",
        ],
      ],
    });

    // 处理收藏数据，保留所有记录但标记状态
    const collections = favorites.map((favorite) => {
      if (!favorite.collection) {
        // 合集已被删除
        return {
          id: favorite.collectionId,
          name: "合集已删除",
          description: "该合集已被作者删除",
          coverImageUrl: null,
          tags: null,
          status: "deleted",
          isPublic: false,
          viewCount: 0,
          favoriteCount: 0,
          itemCount: 0,
          isAvailable: false,
          unavailableReason: "deleted",
          isFavorited: true,
          creator: null,
          createdAt: favorite.createdAt,
          updatedAt: favorite.updatedAt,
        };
      } else if (
        favorite.collection.status !== "published" ||
        !favorite.collection.isPublic
      ) {
        // 合集已下架或设为私有
        const collectionData = favorite.collection.toJSON();
        collectionData.itemCount = favorite.collection.items
          ? favorite.collection.items.length
          : 0;
        collectionData.isFavorited = true;
        collectionData.isAvailable = false;
        collectionData.unavailableReason =
          favorite.collection.status !== "published"
            ? "unpublished"
            : "private";
        return collectionData;
      } else {
        // 合集正常可用
        const collectionData = favorite.collection.toJSON();
        collectionData.itemCount = favorite.collection.items
          ? favorite.collection.items.length
          : 0;
        // 确保合集项按sortOrder排序
        if (collectionData.items && collectionData.items.length > 0) {
          collectionData.items.sort((a, b) => a.sortOrder - b.sortOrder);
        }
        collectionData.isFavorited = true;
        collectionData.isAvailable = true;
        collectionData.unavailableReason = null;
        return collectionData;
      }
    });

    return {
      success: true,
      collections: collections,
    };
  }
}

module.exports = new CollectionService();
