/**
 * 邮件服务
 * 使用 Nodemailer 发送验证码邮件
 */
const nodemailer = require("nodemailer");
const Core = require("@alicloud/pop-core");
const dotenv = require("dotenv");

// 加载环境变量
dotenv.config();

// 创建阿里云客户端
const client = new Core({
  accessKeyId: process.env.ALIYUN_ACCESS_KEY,
  accessKeySecret: process.env.ALIYUN_SECRET_KEY,
  endpoint: "https://dm.aliyuncs.com",
  apiVersion: "2015-11-23",
  regionId: process.env.ALIYUN_EMAIL_REGION || "cn-hangzhou",
});

// 创建邮件传输器 (备用方案)
const transporter = nodemailer.createTransport({
  service: "QQ", // 使用内置的QQ邮箱配置
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS, // QQ邮箱的授权码，不是登录密码
  },
});

/**
 * 发送验证码邮件
 * @param {string} to 收件人邮箱
 * @param {string} code 验证码
 * @param {string} type 验证码类型
 * @returns {Promise<boolean>} 发送结果
 */
async function sendVerificationCode(to, code, type = "login") {
  try {
    // 根据类型设置不同的主题和内容
    let subject = "";
    let text = "";
    let html = "";

    switch (type) {
      case "login":
        subject = "Echo Lab - 登录验证码";
        text = `您的登录验证码是: ${code}，有效期10分钟。如非本人操作，请忽略此邮件。`;
        html = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
            <h2 style="color: #333; text-align: center;">Echo Lab 登录验证码</h2>
            <p style="color: #666; font-size: 16px;">您好，</p>
            <p style="color: #666; font-size: 16px;">您的登录验证码是：</p>
            <div style="background-color: #f5f5f5; padding: 15px; text-align: center; border-radius: 5px; margin: 20px 0;">
              <span style="font-size: 24px; font-weight: bold; letter-spacing: 5px;">${code}</span>
            </div>
            <p style="color: #666; font-size: 16px;">验证码有效期为10分钟。如非本人操作，请忽略此邮件。</p>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #999; font-size: 14px;">
              <p>此邮件由系统自动发送，请勿回复。</p>
              <p>© ${new Date().getFullYear()} Echo Lab</p>
            </div>
          </div>
        `;
        break;
      case "register":
        subject = "Echo Lab - 注册验证码";
        text = `您的注册验证码是: ${code}，有效期10分钟。如非本人操作，请忽略此邮件。`;
        html = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
            <h2 style="color: #333; text-align: center;">Echo Lab 注册验证码</h2>
            <p style="color: #666; font-size: 16px;">您好，</p>
            <p style="color: #666; font-size: 16px;">您的注册验证码是：</p>
            <div style="background-color: #f5f5f5; padding: 15px; text-align: center; border-radius: 5px; margin: 20px 0;">
              <span style="font-size: 24px; font-weight: bold; letter-spacing: 5px;">${code}</span>
            </div>
            <p style="color: #666; font-size: 16px;">验证码有效期为10分钟。如非本人操作，请忽略此邮件。</p>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #999; font-size: 14px;">
              <p>此邮件由系统自动发送，请勿回复。</p>
              <p>© ${new Date().getFullYear()} Echo Lab</p>
            </div>
          </div>
        `;
        break;
      case "reset_password":
        subject = "Echo Lab - 重置密码验证码";
        text = `您的重置密码验证码是: ${code}，有效期10分钟。如非本人操作，请忽略此邮件。`;
        html = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
            <h2 style="color: #333; text-align: center;">Echo Lab 重置密码验证码</h2>
            <p style="color: #666; font-size: 16px;">您好，</p>
            <p style="color: #666; font-size: 16px;">您的重置密码验证码是：</p>
            <div style="background-color: #f5f5f5; padding: 15px; text-align: center; border-radius: 5px; margin: 20px 0;">
              <span style="font-size: 24px; font-weight: bold; letter-spacing: 5px;">${code}</span>
            </div>
            <p style="color: #666; font-size: 16px;">验证码有效期为10分钟。如非本人操作，请忽略此邮件。</p>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #999; font-size: 14px;">
              <p>此邮件由系统自动发送，请勿回复。</p>
              <p>© ${new Date().getFullYear()} Echo Lab</p>
            </div>
          </div>
        `;
        break;
      default:
        subject = "Echo Lab - 验证码";
        text = `您的验证码是: ${code}，有效期10分钟。如非本人操作，请忽略此邮件。`;
        html = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
            <h2 style="color: #333; text-align: center;">Echo Lab 验证码</h2>
            <p style="color: #666; font-size: 16px;">您好，</p>
            <p style="color: #666; font-size: 16px;">您的验证码是：</p>
            <div style="background-color: #f5f5f5; padding: 15px; text-align: center; border-radius: 5px; margin: 20px 0;">
              <span style="font-size: 24px; font-weight: bold; letter-spacing: 5px;">${code}</span>
            </div>
            <p style="color: #666; font-size: 16px;">验证码有效期为10分钟。如非本人操作，请忽略此邮件。</p>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #999; font-size: 14px;">
              <p>此邮件由系统自动发送，请勿回复。</p>
              <p>© ${new Date().getFullYear()} Echo Lab</p>
            </div>
          </div>
        `;
    }

    // 发送邮件
    const params = {
      AccountName: process.env.ALIYUN_EMAIL_ACCOUNT,
      AddressType: 1, // 0: 随机地址，1: 发信地址
      ReplyToAddress: false,
      ToAddress: to,
      Subject: subject,
      HtmlBody: html,
      TextBody: text,
      FromAlias: "Echo Lab",
    };

    try {
      const result = await client.request("SingleSendMail", params, {
        method: "POST",
      });
      console.log(`邮件发送成功: ${result.RequestId}`);
      return true;
    } catch (error) {
      console.error("阿里云邮件发送失败:", error);
      return false;
    }
  } catch (error) {
    console.error("邮件发送失败:", error);
    return false;
  }
}

module.exports = {
  sendVerificationCode,
};
