/**
 * 会话管理服务
 * 提供用户会话的创建、验证和管理功能
 */
const db = require("../models");
const { Op } = require("sequelize");
const { v4: uuidv4 } = require("uuid");

// 最大允许的设备登录数量
const MAX_SESSIONS_PER_USER = 2;

/**
 * 创建新会话
 * @param {string} userId - 用户ID
 * @param {Object} deviceInfo - 设备信息
 * @param {string} ipAddress - IP地址
 * @param {number} expiresIn - 过期时间（秒）
 * @returns {Promise<Object>} 会话信息，包含tokenId
 */
async function createSession(
  userId,
  deviceInfo,
  ipAddress,
  expiresIn = 31536000
) {
  // 默认1年
  try {
    // 生成唯一的token ID
    const tokenId = uuidv4();

    // 计算过期时间（默认1年）
    const expiresAt = new Date(Date.now() + expiresIn * 1000);

    // 打印过期时间
    console.log(
      `[SessionService] 会话过期时间: ${expiresAt}, 当前时间: ${new Date()}, 过期时间（秒）: ${expiresIn}`
    );

    // 检查用户现有会话数量
    const activeSessions = await db.UserSession.findAll({
      where: {
        userId,
        expiresAt: { [Op.gt]: new Date() },
      },
      order: [["created_at", "ASC"]], // 最早创建的在前
    });

    // 打印当前活跃会话数量
    console.log(
      `[SessionService] 用户 ${userId} 当前有 ${activeSessions.length} 个活跃会话，最大允许 ${MAX_SESSIONS_PER_USER} 个`
    );

    // 如果会话数量已经达到或超过限制，删除最早的会话
    if (activeSessions.length >= MAX_SESSIONS_PER_USER) {
      // 计算需要删除的会话数量：当前会话数量 + 1（新会话） - 最大允许数量
      const numToRemove = activeSessions.length + 1 - MAX_SESSIONS_PER_USER;
      // 取最早活跃的几个会话删除
      const sessionsToRemove = activeSessions.slice(0, numToRemove);

      console.log(
        `[SessionService] 用户 ${userId} 的会话数量将超过限制，需要删除 ${numToRemove} 个会话`
      );

      // 记录要删除的会话ID，用于后续通知
      const removedSessionIds = [];

      // 打印所有活跃会话的信息，帮助调试
      console.log("[SessionService] 所有活跃会话:");
      for (let i = 0; i < activeSessions.length; i++) {
        const s = activeSessions[i];
        console.log(
          `[SessionService] 会话 ${i + 1}: ID=${s.tokenId}, 创建于=${
            s.created_at
          }, 最后活跃=${s.lastActive}`
        );
      }

      for (const session of sessionsToRemove) {
        console.log(
          `[SessionService] 删除会话 ${session.tokenId}，创建于 ${session.created_at}，最后活跃于 ${session.lastActive}`
        );
        removedSessionIds.push(session.tokenId);
        await session.destroy();
      }

      // 这里可以添加通知机制，通知被删除会话的设备（如WebSocket）
      // 暂时不实现，因为需要额外的WebSocket服务
    }

    // 创建新会话
    const session = await db.UserSession.create({
      userId,
      tokenId,
      deviceInfo: deviceInfo ? JSON.stringify(deviceInfo) : null,
      ipAddress,
      lastActive: new Date(),
      expiresAt,
    });

    return {
      tokenId,
      expiresAt,
    };
  } catch (error) {
    console.error("[SessionService] 创建会话失败:", error);
    throw error;
  }
}

/**
 * 验证会话
 * @param {string} tokenId - Token ID
 * @returns {Promise<Object|null>} 会话信息，如果会话无效则返回null
 */
async function validateSession(tokenId) {
  try {
    if (!tokenId) {
      console.log("[SessionService] validateSession: tokenId为空");
      return null;
    }

    console.log(`[SessionService] validateSession: 验证会话 ${tokenId}`);

    // 查找会话
    const session = await db.UserSession.findOne({
      where: {
        tokenId,
        expiresAt: { [Op.gt]: new Date() },
      },
    });

    if (!session) {
      console.log(`[SessionService] validateSession: 未找到会话 ${tokenId}`);
      return null;
    }

    console.log(
      `[SessionService] validateSession: 找到会话 ${tokenId}，用户ID: ${session.userId}`
    );

    // 更新最后活跃时间
    await session.update({
      lastActive: new Date(),
    });

    return session;
  } catch (error) {
    console.error("[SessionService] 验证会话失败:", error);
    return null;
  }
}

/**
 * 获取用户的所有活跃会话
 * @param {string} userId - 用户ID
 * @returns {Promise<Array>} 会话列表
 */
async function getUserSessions(userId) {
  try {
    const sessions = await db.UserSession.findAll({
      where: {
        userId,
        expiresAt: { [Op.gt]: new Date() },
      },
      order: [["lastActive", "DESC"]],
    });

    return sessions.map((session) => ({
      id: session.id,
      tokenId: session.tokenId,
      deviceInfo: session.deviceInfo ? JSON.parse(session.deviceInfo) : null,
      ipAddress: session.ipAddress,
      lastActive: session.lastActive,
      createdAt: session.created_at,
      expiresAt: session.expiresAt,
    }));
  } catch (error) {
    console.error("[SessionService] 获取用户会话失败:", error);
    throw error;
  }
}

/**
 * 删除会话
 * @param {string} tokenId - Token ID
 * @param {string} userId - 用户ID（用于权限验证）
 * @returns {Promise<boolean>} 是否成功
 */
async function deleteSession(tokenId, userId) {
  try {
    const session = await db.UserSession.findOne({
      where: { tokenId },
    });

    if (!session) {
      return false;
    }

    // 检查权限（只有会话所有者或管理员可以删除）
    if (session.userId !== userId) {
      const user = await db.User.findByPk(userId);
      if (!user || user.role !== "admin") {
        return false;
      }
    }

    await session.destroy();
    return true;
  } catch (error) {
    console.error("[SessionService] 删除会话失败:", error);
    return false;
  }
}

/**
 * 清理过期会话
 * @returns {Promise<number>} 清理的会话数量
 */
async function cleanupExpiredSessions() {
  try {
    const result = await db.UserSession.destroy({
      where: {
        expiresAt: { [Op.lte]: new Date() },
      },
    });

    console.log(`[SessionService] 清理了 ${result} 个过期会话`);
    return result;
  } catch (error) {
    console.error("[SessionService] 清理过期会话失败:", error);
    throw error;
  }
}

module.exports = {
  createSession,
  validateSession,
  getUserSessions,
  deleteSession,
  cleanupExpiredSessions,
  MAX_SESSIONS_PER_USER,
};
