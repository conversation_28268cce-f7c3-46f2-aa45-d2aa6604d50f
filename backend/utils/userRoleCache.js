/**
 * 用户角色缓存工具
 * 用于缓存用户角色信息，减少数据库查询频率
 */

const db = require('../models');

class UserRoleCache {
  constructor() {
    // 内存缓存
    this.cache = new Map();
    
    // 缓存配置
    this.config = {
      ttl: 5 * 60 * 1000, // 5分钟过期
      maxSize: 1000, // 最大缓存1000个用户
      cleanupInterval: 10 * 60 * 1000, // 10分钟清理一次过期缓存
    };
    
    // 启动定期清理
    this.startCleanup();
    
    // 统计信息
    this.stats = {
      hits: 0,
      misses: 0,
      queries: 0,
    };
  }
  
  /**
   * 获取用户角色
   * @param {string} userId - 用户ID
   * @returns {Promise<string|null>} 用户角色
   */
  async getUserRole(userId) {
    this.stats.queries++;
    
    // 检查缓存
    const cached = this.cache.get(userId);
    if (cached && !this.isExpired(cached)) {
      this.stats.hits++;
      return cached.role;
    }
    
    // 缓存未命中，查询数据库
    this.stats.misses++;
    try {
      const user = await db.User.findByPk(userId, {
        attributes: ['id', 'role']
      });
      
      if (!user) {
        return null;
      }
      
      // 存入缓存
      this.set(userId, user.role);
      return user.role;
      
    } catch (error) {
      console.error('[UserRoleCache] 查询用户角色失败:', error);
      return null;
    }
  }
  
  /**
   * 设置缓存
   * @param {string} userId - 用户ID
   * @param {string} role - 用户角色
   */
  set(userId, role) {
    // 检查缓存大小限制
    if (this.cache.size >= this.config.maxSize) {
      // 删除最旧的缓存项
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(userId, {
      role,
      timestamp: Date.now()
    });
  }
  
  /**
   * 检查缓存是否过期
   * @param {Object} cached - 缓存项
   * @returns {boolean} 是否过期
   */
  isExpired(cached) {
    return Date.now() - cached.timestamp > this.config.ttl;
  }
  
  /**
   * 清除指定用户的缓存
   * @param {string} userId - 用户ID
   */
  invalidate(userId) {
    this.cache.delete(userId);
  }
  
  /**
   * 清除所有缓存
   */
  clear() {
    this.cache.clear();
  }
  
  /**
   * 启动定期清理过期缓存
   */
  startCleanup() {
    setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }
  
  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [userId, cached] of this.cache.entries()) {
      if (this.isExpired(cached)) {
        this.cache.delete(userId);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`[UserRoleCache] 清理了 ${cleanedCount} 个过期缓存项`);
    }
  }
  
  /**
   * 获取缓存统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const hitRate = this.stats.queries > 0 ? 
      (this.stats.hits / this.stats.queries * 100).toFixed(2) : 0;
    
    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      cacheSize: this.cache.size,
      maxSize: this.config.maxSize
    };
  }
}

// 创建全局实例
const userRoleCache = new UserRoleCache();

module.exports = {
  userRoleCache,
  UserRoleCache
};
