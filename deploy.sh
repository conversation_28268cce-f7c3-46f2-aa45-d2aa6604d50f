#!/bin/bash

# Echo Lab 部署脚本
# 用于在阿里云ECS服务器上部署Echo Lab应用
# 支持选择部署前端、后端或两者都部署

# 显示彩色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT=~/JapRepeater
FRONTEND_DIR=$PROJECT_ROOT/echo-lab
BACKEND_DIR=$PROJECT_ROOT/backend
NGINX_DIR=/var/www/echo-lab

# 默认部署选项
DEPLOY_FRONTEND=true
DEPLOY_BACKEND=true
SKIP_GIT_PULL=false

# 显示帮助信息
show_help() {
  echo -e "${BLUE}Echo Lab 部署脚本${NC}"
  echo -e "用法: $0 [选项]"
  echo -e "\n选项:"
  echo -e "  -f, --frontend    仅部署前端"
  echo -e "  -b, --backend     仅部署后端"
  echo -e "  -a, --all         部署前端和后端（默认）"
  echo -e "  -s, --skip-pull   跳过Git拉取步骤"
  echo -e "  -h, --help        显示此帮助信息"
  echo -e "\n示例:"
  echo -e "  $0                 部署前端和后端"
  echo -e "  $0 -f              仅部署前端"
  echo -e "  $0 -b              仅部署后端"
  echo -e "  $0 -s              部署前端和后端，但跳过Git拉取"
  echo -e "  $0 -f -s           仅部署前端，并跳过Git拉取"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    -f|--frontend)
      DEPLOY_FRONTEND=true
      DEPLOY_BACKEND=false
      shift
      ;;
    -b|--backend)
      DEPLOY_FRONTEND=false
      DEPLOY_BACKEND=true
      shift
      ;;
    -a|--all)
      DEPLOY_FRONTEND=true
      DEPLOY_BACKEND=true
      shift
      ;;
    -s|--skip-pull)
      SKIP_GIT_PULL=true
      shift
      ;;
    -h|--help)
      show_help
      exit 0
      ;;
    *)
      echo -e "${RED}错误: 未知选项 $1${NC}"
      show_help
      exit 1
      ;;
  esac
done

# 检查目录是否存在
check_directory() {
  if [ ! -d "$1" ]; then
    echo -e "${RED}错误: 目录 $1 不存在${NC}"
    exit 1
  fi
}

# 显示部署配置
echo -e "${GREEN}===== 开始部署 Echo Lab 应用 =====${NC}"
echo -e "${YELLOW}部署时间: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
echo -e "${YELLOW}部署配置:${NC}"
if [ "$DEPLOY_FRONTEND" = true ] && [ "$DEPLOY_BACKEND" = true ]; then
  echo -e "  - 部署类型: ${GREEN}前端和后端${NC}"
elif [ "$DEPLOY_FRONTEND" = true ]; then
  echo -e "  - 部署类型: ${GREEN}仅前端${NC}"
elif [ "$DEPLOY_BACKEND" = true ]; then
  echo -e "  - 部署类型: ${GREEN}仅后端${NC}"
fi
if [ "$SKIP_GIT_PULL" = true ]; then
  echo -e "  - Git拉取: ${YELLOW}跳过${NC}"
else
  echo -e "  - Git拉取: ${GREEN}执行${NC}"
fi

# 确认部署
echo -e "\n确认以上配置并继续部署? (y/n)"
read -r response
if [[ ! "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
  echo -e "${RED}部署已取消${NC}"
  exit 1
fi

# 计算总步骤数
TOTAL_STEPS=1  # 初始值为1，表示更新代码步骤
if [ "$DEPLOY_FRONTEND" = true ]; then
  TOTAL_STEPS=$((TOTAL_STEPS + 2))  # 构建前端 + 复制到Nginx
fi
if [ "$DEPLOY_BACKEND" = true ]; then
  TOTAL_STEPS=$((TOTAL_STEPS + 1))  # 更新后端
fi
TOTAL_STEPS=$((TOTAL_STEPS + 1))  # 显示服务状态

CURRENT_STEP=1

# 1. 更新代码
echo -e "\n${YELLOW}[$CURRENT_STEP/$TOTAL_STEPS] 正在更新代码...${NC}"
check_directory $PROJECT_ROOT
cd $PROJECT_ROOT

if [ "$SKIP_GIT_PULL" = false ]; then
  # 检查是否有未提交的更改
  if [ -n "$(git status --porcelain)" ]; then
    echo -e "${RED}警告: 本地有未提交的更改，可能会导致冲突${NC}"
    echo -e "继续部署? (y/n)"
    read -r response
    if [[ ! "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
      echo -e "${RED}部署已取消${NC}"
      exit 1
    fi
  fi

  # 拉取最新代码
  echo -e "${YELLOW}正在拉取最新代码...${NC}"
  git pull
  if [ $? -ne 0 ]; then
    echo -e "${RED}更新代码失败，请检查网络或仓库权限${NC}"
    exit 1
  fi
  echo -e "${GREEN}代码已更新到最新版本${NC}"
else
  echo -e "${YELLOW}已跳过Git拉取步骤${NC}"
fi

CURRENT_STEP=$((CURRENT_STEP + 1))

# 2. 构建前端项目
if [ "$DEPLOY_FRONTEND" = true ]; then
  echo -e "\n${YELLOW}[$CURRENT_STEP/$TOTAL_STEPS] 正在构建前端项目...${NC}"
  check_directory $FRONTEND_DIR
  cd $FRONTEND_DIR

  echo -e "${YELLOW}正在安装前端依赖（包括开发依赖）...${NC}"
  # 使用 --include=dev 确保安装开发依赖
  npm install --include=dev
  if [ $? -ne 0 ]; then
    echo -e "${RED}安装前端依赖失败${NC}"
    exit 1
  fi

  echo -e "${YELLOW}正在构建前端项目...${NC}"
  echo -e "${YELLOW}设置生产环境变量...${NC}"
  echo -e "${YELLOW}当前目录: $(pwd)${NC}"
  echo -e "${YELLOW}Node.js 版本: $(node -v)${NC}"
  echo -e "${YELLOW}npm 版本: $(npm -v)${NC}"

  # 构建前端项目
  echo -e "${YELLOW}运行构建命令...${NC}"

  # 临时取消 NODE_ENV 环境变量，确保安装开发依赖
  echo -e "${YELLOW}确保安装开发依赖...${NC}"
  NODE_ENV= npm install

  # 设置生产环境变量并构建
  echo -e "${YELLOW}开始构建...${NC}"
  NODE_ENV=production npm run build

  if [ $? -ne 0 ]; then
    echo -e "${RED}构建前端项目失败，请检查错误信息${NC}"
    exit 1
  fi
  echo -e "${GREEN}前端项目构建成功${NC}"

  CURRENT_STEP=$((CURRENT_STEP + 1))

  # 3. 复制到 Nginx 目录
  echo -e "\n${YELLOW}[$CURRENT_STEP/$TOTAL_STEPS] 正在复制前端文件到Nginx目录...${NC}"
  # 确保Nginx目录存在
  sudo mkdir -p $NGINX_DIR
  sudo cp -r $FRONTEND_DIR/dist/* $NGINX_DIR/
  if [ $? -ne 0 ]; then
    echo -e "${RED}复制前端文件失败，请检查权限${NC}"
    exit 1
  fi
  echo -e "${GREEN}前端文件已复制到 $NGINX_DIR${NC}"



  CURRENT_STEP=$((CURRENT_STEP + 1))
fi

# 4. 更新后端
if [ "$DEPLOY_BACKEND" = true ]; then
  echo -e "\n${YELLOW}[$CURRENT_STEP/$TOTAL_STEPS] 正在更新后端...${NC}"
  check_directory $BACKEND_DIR
  cd $BACKEND_DIR

  echo -e "${YELLOW}正在安装后端依赖...${NC}"
  npm install
  if [ $? -ne 0 ]; then
    echo -e "${RED}安装后端依赖失败${NC}"
    exit 1
  fi

  # 检查PM2是否已安装
  if ! command -v pm2 &> /dev/null; then
    echo -e "${RED}PM2未安装，正在全局安装...${NC}"
    npm install -g pm2
    if [ $? -ne 0 ]; then
      echo -e "${RED}安装PM2失败${NC}"
      exit 1
    fi
  fi

  # 使用PM2启动后端服务
  echo -e "${YELLOW}正在重启后端服务...${NC}"
  pm2 stop echo-lab-backend 2>/dev/null || true
  NODE_ENV=production pm2 start app.js --name "echo-lab-backend" --update-env
  if [ $? -ne 0 ]; then
    echo -e "${RED}启动后端服务失败${NC}"
    exit 1
  fi

  # 保存PM2进程列表，确保服务器重启后自动启动
  pm2 save
  echo -e "${GREEN}后端服务已重启${NC}"

  CURRENT_STEP=$((CURRENT_STEP + 1))
fi

# 5. 复制 Nginx 配置文件
echo -e "\n${YELLOW}[$CURRENT_STEP/$TOTAL_STEPS] 复制 Nginx 配置文件:${NC}"

# 返回项目根目录
cd $PROJECT_ROOT
echo -e "${YELLOW}当前目录: $(pwd)${NC}"

# 5.1 复制 Echo Lab Nginx 配置文件
if [ -f "echolab.club.conf" ]; then
  echo -e "${YELLOW}发现 Echo Lab Nginx 配置文件，是否更新服务器配置? (y/n)${NC}"
  read -r update_nginx
  if [[ "$update_nginx" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    echo -e "${YELLOW}正在复制 Echo Lab Nginx 配置...${NC}"
    sudo cp echolab.club.conf /etc/nginx/conf.d/echolab.club.conf
    if [ $? -ne 0 ]; then
      echo -e "${RED}复制 Echo Lab Nginx 配置失败，请检查权限${NC}"
    else
      echo -e "${GREEN}Echo Lab Nginx 配置已更新${NC}"
    fi
  else
    echo -e "${YELLOW}跳过 Echo Lab Nginx 配置更新${NC}"
  fi
else
  echo -e "${YELLOW}未找到 Echo Lab Nginx 配置文件，跳过更新${NC}"
fi



CURRENT_STEP=$((CURRENT_STEP + 1))

# 6. 显示服务状态
echo -e "\n${YELLOW}[$CURRENT_STEP/$TOTAL_STEPS] 服务状态:${NC}"

if [ "$DEPLOY_BACKEND" = true ]; then
  echo -e "${YELLOW}后端服务状态:${NC}"
  pm2 status
fi

# 检查Nginx配置和状态
echo -e "\n${YELLOW}检查Nginx配置...${NC}"
sudo nginx -t
if [ $? -eq 0 ]; then
  echo -e "${GREEN}Nginx配置正确${NC}"
  # 重新加载Nginx配置
  echo -e "${YELLOW}是否重新加载Nginx配置? (y/n)${NC}"
  read -r reload_nginx
  if [[ "$reload_nginx" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    sudo systemctl reload nginx
    echo -e "${GREEN}Nginx已重新加载配置${NC}"
  else
    echo -e "${YELLOW}跳过Nginx配置重新加载${NC}"
  fi
else
  echo -e "${RED}Nginx配置有误，请检查${NC}"
fi

echo -e "\n${GREEN}===== 部署完成 =====${NC}"
if [ "$DEPLOY_FRONTEND" = true ]; then
  echo -e "前端访问地址: https://echolab.club"
fi
if [ "$DEPLOY_BACKEND" = true ]; then
  echo -e "后端API地址: https://echolab.club/api"
  echo -e "${YELLOW}如需查看后端日志，请运行: pm2 logs echo-lab-backend${NC}"
fi
echo -e "${YELLOW}部署完成时间: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
