# 错误上报功能增强总结

## 概述

本次更新大幅增强了错误上报功能，新增了多个维度的信息收集，以便更好地分析和定位问题。

## 主要增强内容

## 新增信息类别

### 1. 性能相关信息

#### 内存使用情况
- `used`: 当前已使用的JS堆内存大小
- `total`: 总的JS堆内存大小
- `limit`: JS堆内存限制

#### 页面性能指标
- `dom_ready`: DOM构建完成时间
- `load_complete`: 页面完全加载时间
- `dns_lookup`: DNS查询时间
- `tcp_connect`: TCP连接时间

#### 网络信息
- `effective_type`: 有效连接类型（4g、3g、2g等）
- `downlink`: 下行带宽估算
- `rtt`: 往返时间
- `save_data`: 是否启用数据节省模式

### 2. 环境状态信息

#### 屏幕信息
- `width/height`: 屏幕尺寸
- `available_width/height`: 可用屏幕尺寸
- `color_depth`: 颜色深度
- `pixel_ratio`: 设备像素比

#### 视口信息
- `width/height`: 当前视口大小
- `scroll_x/y`: 滚动位置

#### 页面可见性
- `hidden`: 页面是否隐藏
- `visibility_state`: 页面可见性状态

#### 在线状态
- `online`: 是否在线
- `connection_type`: 连接类型

#### 时区信息
- `offset`: 时区偏移
- `timezone`: 时区名称

### 3. 用户行为信息

#### 页面停留时间
- `page_stay_time`: 用户在当前页面的停留时长

#### 导航历史
- `navigation_history`: 最近5个页面的访问记录

#### 用户交互统计
- `clicks`: 点击次数
- `scrolls`: 滚动次数
- `key_presses`: 按键次数

#### 页面焦点状态
- `has_focus`: 当前页面是否有焦点
- `last_focus_time`: 最后获得焦点的时间

### 4. 应用状态信息

#### Vue路由信息
- `path`: 当前路由路径
- `name`: 路由名称
- `params`: 路由参数
- `query`: 查询参数

#### 本地存储使用情况
- `localStorage_size/keys`: localStorage使用大小和键数量
- `sessionStorage_size/keys`: sessionStorage使用大小和键数量

#### Service Worker状态
- `controller`: 是否有活跃的Service Worker
- `ready`: Service Worker是否就绪

#### 页面资源加载状态
- `total_count`: 总资源数量
- `failed_count`: 加载失败的资源数量
- `avg_load_time`: 平均加载时间

### 5. 错误严重程度分级

#### 自动分级算法
基于多个因素计算错误严重程度：

**基础分数**: 50分

**错误类型调整**:
- `network`: +20分（影响用户体验）
- `vue`: +30分（可能导致页面崩溃）
- `resource`: +10分（影响相对较小）
- `promise`: +25分（可能导致功能异常）

**错误内容调整**:
- 属性访问错误: +15分
- 函数调用错误: +15分

**环境因素调整**:
- 内存使用率>80%: +20分
- 内存使用率>60%: +10分
- 页面在后台: -10分
- 网络连接慢: +5分
- 数据节省模式: +5分

**严重程度等级**:
- `critical`: 80-100分
- `high`: 60-79分
- `medium`: 40-59分
- `low`: 0-39分

## 数据库变更

### 新增字段

#### error_logs表
- `enhanced_context`: JSON类型，存储增强的上下文信息
- `severity_level`: 枚举类型，错误严重程度等级
- `severity_score`: 整数类型，错误严重程度分数

#### 新增索引
- `idx_error_logs_severity_level`: 严重程度等级索引
- `idx_error_logs_severity_score`: 严重程度分数索引

## 用户行为追踪

### 新增组件
- `UserBehaviorTracker`: 用户行为追踪器
  - 自动追踪用户点击、滚动、按键等交互
  - 记录页面导航历史
  - 监控页面焦点变化
  - 数据存储在sessionStorage中

### 隐私保护
- 按键内容不记录具体字符，字符键统一标记为`[char]`
- 元素文本内容限制在50字符以内
- 历史记录限制数量，避免过度存储

## 使用方式

### 自动收集
错误上报功能会自动收集所有增强信息，无需额外配置。

### 手动上报
```javascript
import { reportError } from '@/utils/errorHandler';

// 手动上报错误
reportError('自定义错误消息', {
  type: 'custom',
  additional_context: {
    feature: 'video_player',
    action: 'play_video'
  }
});
```

### 配置选项
```javascript
import { configureErrorHandler } from '@/utils/errorHandler';

// 配置错误处理器
configureErrorHandler({
  enabled: true,
  logToConsole: true,
  sampleRate: 1.0
});
```

## 性能影响

### 优化措施
- 使用防抖机制减少滚动事件频率
- 限制历史记录数量
- 异步收集上下文信息
- 批量上报减少网络请求

### 内存使用
- sessionStorage数据在页面关闭时自动清理
- 历史记录限制在10条以内
- JSON数据压缩存储

## 分析价值

### 问题定位
- 通过性能信息识别内存泄漏
- 通过网络信息分析网络相关错误
- 通过用户行为了解错误触发场景

### 优先级排序
- 自动严重程度分级帮助优先处理重要错误
- 影响用户数量统计
- 错误频率趋势分析

### 用户体验优化
- 识别低性能设备上的问题
- 分析不同网络环境下的表现
- 了解用户操作习惯

## 注意事项

1. **隐私保护**: 不收集敏感的用户输入内容
2. **性能影响**: 增强信息收集会有轻微的性能开销
3. **存储空间**: 增强上下文信息会增加数据库存储需求
4. **浏览器兼容**: 某些API在老旧浏览器中可能不可用

## 后续计划

1. 添加错误趋势分析功能
2. 实现错误自动分类
3. 增加错误影响范围评估
4. 开发错误预警机制
