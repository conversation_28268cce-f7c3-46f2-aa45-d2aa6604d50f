# 错误统计功能设计文档

## 概述
设计一个专门的错误统计功能，替代目前使用umami上报错误的方案，提供更专业的错误收集、分析和管理能力。

## 核心功能需求

### 1. 错误收集能力
- JavaScript运行时错误
- Vue组件错误
- 网络请求错误
- 资源加载错误
- Promise拒绝错误

### 2. 错误分析能力
- 错误频率统计
- 错误趋势分析
- 影响用户数统计
- 错误分布分析（按页面、浏览器、设备等）

### 3. 错误管理能力
- 错误状态管理（新增、已知、已修复、忽略）
- 错误分组合并
- 错误优先级设置
- 错误通知机制

## 数据库设计

### 1. 错误记录表 (error_logs)
```sql
CREATE TABLE `error_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `error_hash` varchar(32) NOT NULL COMMENT '错误唯一标识(基于message+stack的MD5)',
  `error_type` enum('javascript','vue','network','resource','promise') NOT NULL COMMENT '错误类型',
  `message` text NOT NULL COMMENT '错误消息',
  `stack` text COMMENT '错误堆栈',
  `url` varchar(500) NOT NULL COMMENT '发生错误的页面URL',
  `line_number` int(11) DEFAULT NULL COMMENT '错误行号',
  `column_number` int(11) DEFAULT NULL COMMENT '错误列号',
  `filename` varchar(500) DEFAULT NULL COMMENT '错误文件名',
  `user_agent` text COMMENT '用户代理字符串',
  `user_id` varchar(50) DEFAULT NULL COMMENT '用户ID（如果已登录）',
  `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `browser_info` json DEFAULT NULL COMMENT '浏览器信息',
  `device_info` json DEFAULT NULL COMMENT '设备信息',
  `context_data` json DEFAULT NULL COMMENT '错误上下文数据',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_error_hash` (`error_hash`),
  KEY `idx_error_type` (`error_type`),
  KEY `idx_url` (`url`(255)),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='错误日志记录表';
```

### 2. 错误汇总表 (error_summaries)
```sql
CREATE TABLE `error_summaries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `error_hash` varchar(32) NOT NULL COMMENT '错误唯一标识',
  `error_type` enum('javascript','vue','network','resource','promise') NOT NULL,
  `message` text NOT NULL COMMENT '错误消息',
  `stack_sample` text COMMENT '错误堆栈示例',
  `first_seen` datetime NOT NULL COMMENT '首次发现时间',
  `last_seen` datetime NOT NULL COMMENT '最后发现时间',
  `occurrence_count` int(11) NOT NULL DEFAULT 1 COMMENT '发生次数',
  `affected_users` int(11) NOT NULL DEFAULT 0 COMMENT '影响用户数',
  `status` enum('new','acknowledged','resolved','ignored') NOT NULL DEFAULT 'new' COMMENT '处理状态',
  `priority` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium' COMMENT '优先级',
  `assigned_to` varchar(50) DEFAULT NULL COMMENT '分配给谁处理',
  `resolution_notes` text COMMENT '解决方案备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_error_hash` (`error_hash`),
  KEY `idx_error_type` (`error_type`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_first_seen` (`first_seen`),
  KEY `idx_last_seen` (`last_seen`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='错误汇总统计表';
```

### 3. 错误统计表 (error_statistics)
```sql
CREATE TABLE `error_statistics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '统计日期',
  `error_hash` varchar(32) NOT NULL COMMENT '错误标识',
  `error_type` enum('javascript','vue','network','resource','promise') NOT NULL,
  `occurrence_count` int(11) NOT NULL DEFAULT 0 COMMENT '当日发生次数',
  `affected_users` int(11) NOT NULL DEFAULT 0 COMMENT '当日影响用户数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_date_hash` (`date`, `error_hash`),
  KEY `idx_date` (`date`),
  KEY `idx_error_type` (`error_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='错误按日统计表';
```

## API接口设计

### 1. 错误上报接口
- `POST /api/errors/report` - 上报错误
- 支持批量上报
- 自动去重和聚合

### 2. 错误查询接口
- `GET /api/errors/summaries` - 获取错误汇总列表
- `GET /api/errors/:hash/details` - 获取错误详情
- `GET /api/errors/statistics` - 获取错误统计数据

### 3. 错误管理接口
- `PUT /api/errors/:hash/status` - 更新错误状态
- `PUT /api/errors/:hash/priority` - 更新错误优先级
- `POST /api/errors/:hash/notes` - 添加处理备注

## 前端集成方案

### 1. 错误收集器改造
- 修改现有的 `errorHandler.js`
- 将错误上报从umami改为自建API
- 增加错误去重和批量上报机制

### 2. 管理界面
- 错误列表页面
- 错误详情页面
- 错误统计图表
- 错误处理工作流

## 实施计划

### 第一阶段：基础功能
1. 创建数据库表
2. 实现错误上报API
3. 改造前端错误收集器

### 第二阶段：管理功能
1. 实现错误查询API
2. 开发管理界面
3. 添加错误处理工作流

### 第三阶段：高级功能
1. 错误趋势分析
2. 智能错误分组
3. 错误告警机制

## 技术要点

### 1. 性能考虑
- 错误上报采用异步批量处理
- 定期清理过期错误日志
- 使用索引优化查询性能

### 2. 存储优化
- 错误堆栈信息压缩存储
- 相同错误合并存储
- 定期归档历史数据

### 3. 安全考虑
- 错误信息脱敏处理
- 限制错误上报频率
- 防止恶意错误攻击
