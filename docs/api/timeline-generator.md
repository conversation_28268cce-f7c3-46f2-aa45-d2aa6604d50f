# 时间线生成器 API

本文档详细说明了 Echo Lab 中时间线生成器的工作原理、算法和 API。

## 概述

时间线生成器是 Echo Lab 的核心组件之一，负责将文本序列节点的环节设置转换为可播放的时间线数据。时间线数据包含了每个内容项的开始时间、持续时间、音频 URL 等信息，供播放器组件使用。

## 核心函数

### generateTimeline

```javascript
function generateTimeline(configJson, settings)
```

生成时间线的主函数，接收内容配置 JSON 和环节设置，返回时间线数组。

#### 参数

- `configJson`: 内容配置 JSON，包含节点、资源等信息
- `settings`: 环节设置，包含 sections 数组

#### 返回值

返回时间线数组，每个元素包含以下属性：
- `id`: 内容项 ID
- `content`: 文本内容
- `audioUrl`: 音频 URL
- `startTime`: 开始时间(秒)
- `duration`: 持续时间(秒)
- `speed`: 播放速度
- `sectionId`: 所属环节 ID
- `sectionName`: 所属环节名称
- `processingMode`: 处理方式
- `repeatIndex`: 重复索引(仅重复环节)
- `isTranslation`: 是否为翻译(仅翻译项)
- `language`: 语言代码(仅翻译项)

### extractSequencesFromNodes

```javascript
function extractSequencesFromNodes(configJson)
```

从节点中提取序列数据，用于生成时间线。

#### 参数

- `configJson`: 内容配置 JSON，包含节点、资源等信息

#### 返回值

返回序列数组，每个序列包含以下属性：
- `id`: 序列 ID(通常是源节点 ID)
- `name`: 序列名称
- `items`: 内容项数组

### getSourceItems

```javascript
function getSourceItems(configJson, sequences, section, processingMode)
```

根据处理方式获取源内容项，用于生成时间线。

#### 参数

- `configJson`: 内容配置 JSON
- `sequences`: 序列数组
- `section`: 环节设置
- `processingMode`: 处理方式

#### 返回值

返回源内容项数组，每个内容项包含以下属性：
- `id`: 内容项 ID
- `content`: 文本内容
- `language`: 语言代码
- `audioUrl`: 音频 URL
- `duration`: 音频时长

### processReadthroughSection

```javascript
function processReadthroughSection(timeline, sourceItems, section, processingMode, startTime)
```

处理通读环节，将内容项添加到时间线。

#### 参数

- `timeline`: 时间线数组
- `sourceItems`: 源内容项数组
- `section`: 环节设置
- `processingMode`: 处理方式
- `startTime`: 开始时间(秒)

### processRepeatSection

```javascript
function processRepeatSection(timeline, sourceItems, section, processingMode, startTime, configJson)
```

处理重复环节，将内容项和翻译添加到时间线。

#### 参数

- `timeline`: 时间线数组
- `sourceItems`: 源内容项数组
- `section`: 环节设置
- `processingMode`: 处理方式
- `startTime`: 开始时间(秒)
- `configJson`: 内容配置 JSON

## 处理流程

1. **序列提取**:
   - 从所有文本内容节点生成序列
   - 如果有多个源节点，生成一个合并序列

2. **环节处理**:
   - 遍历所有启用的环节
   - 根据处理方式获取源内容项
   - 根据环节类型处理内容项

3. **时间线生成**:
   - 通读环节: 按顺序添加内容项到时间线
   - 重复环节: 为每个内容项添加多个重复项到时间线，并可能添加翻译

4. **时间计算**:
   - 每个内容项的开始时间 = 前一项的结束时间 + 间隔时间
   - 每个内容项的持续时间 = 原始时长 / 播放速度

## 处理方式详解

### 基于序列模式 (sequence)

基于序列模式下，系统按以下顺序查找序列：

1. 首先查找文本序列节点的自定义序列
2. 如果没有找到自定义序列，使用合并序列
3. 如果没有合并序列，返回空数组

```javascript
// 基于序列模式处理逻辑
if (processingMode === "sequence") {
  // 查找文本序列节点的自定义序列
  const textSequenceNodes = Object.values(configJson.nodes || {})
    .filter(node => node.type === "textSequence");

  for (const node of textSequenceNodes) {
    if (node.params && node.params.sequence && node.params.sequence.length > 0) {
      // 使用自定义序列
      return node.params.sequence.map(item => ({
        id: item.id,
        content: item.content,
        language: item.language || "auto",
        audioUrl: findAudioUrl(configJson, item.content),
        duration: findAudioDuration(configJson, item.content) || 2
      }));
    }
  }

  // 如果没有找到自定义序列，使用合并序列
  if (sequences.length > 0) {
    const mergedSequence = sequences[sequences.length - 1];
    if (mergedSequence && mergedSequence.items && mergedSequence.items.length > 0) {
      return mergedSequence.items;
    }
  }

  // 如果没有合并序列，返回空数组
  return [];
}
```

### 基于源节点模式 (source)

基于源节点模式下，系统按以下顺序查找内容：

1. 首先查找源节点对应的序列
2. 如果没有找到序列，尝试直接从源节点获取内容
3. 如果源节点有分句，使用分句
4. 如果源节点没有分句但有文本，使用整个文本
5. 如果源节点不包含有效内容，返回空数组

```javascript
// 基于源节点模式处理逻辑
if (processingMode === "source") {
  const sourceNodeId = section.sourceNodeId;

  if (!sourceNodeId) {
    return [];
  }

  // 查找源节点对应的序列
  const sourceSequence = sequences.find(seq => seq.id === sourceNodeId);

  if (sourceSequence && sourceSequence.items && sourceSequence.items.length > 0) {
    return sourceSequence.items;
  }

  // 尝试直接从源节点获取内容
  const sourceNode = Object.values(configJson.nodes || {})
    .find(n => n.id === sourceNodeId);

  if (!sourceNode) {
    return [];
  }

  // 如果源节点有分句，使用分句
  if (sourceNode.type === "textContent" &&
      sourceNode.params &&
      sourceNode.params.segments &&
      sourceNode.params.segments.length > 0) {

    return sourceNode.params.segments.map(segment => ({
      id: segment.id,
      content: segment.content,
      language: segment.language || "auto",
      audioUrl: findAudioUrl(configJson, segment.content),
      duration: 2
    }));
  }
  // 如果源节点没有分句但有文本，使用整个文本
  else if (sourceNode.type === "textContent" &&
           sourceNode.params &&
           sourceNode.params.text) {

    return [{
      id: `seg_${Date.now()}`,
      content: sourceNode.params.text,
      language: sourceNode.params.language || "auto",
      audioUrl: findAudioUrl(configJson, sourceNode.params.text),
      duration: 2
    }];
  }

  // 如果源节点不包含有效内容，返回空数组
  return [];
}
```

## 环节处理详解

### 通读环节处理

通读环节按顺序播放内容，每个内容项播放一次。

```javascript
function processReadthroughSection(timeline, sourceItems, section, processingMode, startTime) {
  let currentTime = startTime;

  // 获取环节配置
  const speed = section.config?.speed || 1.0;
  const pauseDuration = section.config?.interval || 1000;

  // 处理每个内容项
  sourceItems.forEach(item => {
    // 添加到时间线
    timeline.push({
      id: item.id,
      content: item.content,
      audioUrl: item.audioUrl,
      startTime: currentTime,
      duration: (item.duration || 2) * (1 / speed),
      speed: speed,
      sectionId: section.id,
      sectionName: section.name || section.title,
      processingMode: processingMode
    });

    // 更新当前时间
    currentTime += (item.duration || 2) * (1 / speed) + pauseDuration / 1000;
  });
}
```

### 重复环节处理

重复环节重复播放每个内容项，可以在最后一次重复后插入翻译。

```javascript
function processRepeatSection(timeline, sourceItems, section, processingMode, startTime, configJson) {
  let currentTime = startTime;

  // 获取环节配置
  const repeatCount = section.config?.repeatCount || 3;
  const speed = section.config?.speed || section.speed || 1.0;
  const pauseDuration = section.config?.interval || section.pauseDuration || 3000;
  const enableTranslation = section.config?.enableTranslation === true || section.enableTranslation === true;
  const translationLanguage = section.config?.translationLanguage || section.translationLanguage || "";
  const translationPosition = section.config?.translationPosition || section.translationPosition || 2;

  // 处理每个内容项
  sourceItems.forEach(item => {
    // 重复指定次数
    for (let i = 0; i < repeatCount; i++) {
      // 添加到时间线
      timeline.push({
        id: `${item.id}_repeat_${i}`,
        content: item.content,
        audioUrl: item.audioUrl,
        startTime: currentTime,
        duration: (item.duration || 2) * (1 / speed),
        speed: speed,
        sectionId: section.id,
        sectionName: section.name || section.title,
        repeatIndex: i,
        processingMode: processingMode
      });

      // 更新当前时间
      currentTime += (item.duration || 2) * (1 / speed) + pauseDuration / 1000;

      // 如果达到指定的翻译插入位置，并且启用了翻译，添加翻译内容
      if (i === translationPosition - 1 && enableTranslation && translationLanguage) {
        // 查找翻译内容
        let translationText = null;

        // 从resources中查找翻译
        if (configJson.resources &&
            configJson.resources.translations &&
            configJson.resources.translations[translationLanguage]) {

          const translations = configJson.resources.translations[translationLanguage];

          // 通过ID查找
          if (translations[item.id]) {
            translationText = translations[item.id];
          }
        }

        if (translationText) {
          // 添加翻译到时间线
          timeline.push({
            id: `${item.id}_translation`,
            content: translationText,
            audioUrl: findAudioUrl(configJson, translationText),
            startTime: currentTime,
            duration: 2 * (1 / speed),
            speed: speed,
            sectionId: section.id,
            sectionName: section.name || section.title,
            isTranslation: true,
            language: translationLanguage,
            processingMode: processingMode
          });

          // 更新当前时间
          currentTime += 2 * (1 / speed) + pauseDuration / 1000;
        }
      }
    }
  });
}
```

## 最佳实践

1. **性能优化**:
   - 避免在循环中重复计算相同的值
   - 使用缓存减少重复查找
   - 提前计算可复用的值

2. **错误处理**:
   - 检查输入参数的有效性
   - 处理边缘情况，如空序列、无效节点 ID 等
   - 提供有意义的错误消息和日志

3. **可维护性**:
   - 将复杂逻辑拆分为小函数
   - 使用明确的变量名和注释
   - 遵循一致的代码风格
