# 翻译服务文档

本文档说明了 Echo Lab 翻译服务的实现和使用方式。

## 概述

Echo Lab 的翻译服务使用 DeepSeek API 提供高质量的翻译服务：

- **DeepSeek翻译**：基于大语言模型的翻译服务，支持上下文理解和一次性多语言翻译
- **智能分组**：按源语言自动分组，提高翻译效率
- **批量处理**：支持多个文本同时翻译到多种目标语言

## 翻译服务特性

### DeepSeek翻译优势
- **上下文理解**：能够理解整篇文档的语境，保持翻译一致性
- **一次性多语言**：单次请求可同时翻译到多种目标语言
- **智能分组**：按源语言自动分组处理，提高效率
- **高质量翻译**：基于大语言模型，翻译质量更自然流畅

### 支持的语言
- **简体中文** (zh-CN)
- **繁体中文** (zh-TW)
- **英语** (en)
- **日语** (ja)

## API配置

### DeepSeek API配置
```javascript
const DEEPSEEK_API_KEY = "***********************************";
const DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions";
```

### 语言名称映射
```javascript
const LANGUAGE_NAMES = {
  "zh-CN": "简体中文",
  "zh-TW": "繁体中文",
  "en": "英语",
  "ja": "日语"
};
```

## 翻译流程

### 1. 请求处理
1. 接收前端发送的文本数组和目标语言数组
2. 验证请求参数格式
3. 按源语言对文本进行分组

### 2. DeepSeek翻译
1. 为每种源语言构建翻译prompt
2. 将多个文本合并为一个上下文完整的请求
3. 调用DeepSeek API进行一次性多语言翻译
4. 解析JSON格式的翻译结果

### 3. 结果处理
1. 将DeepSeek结果映射回原始文本ID
2. 转换为前端期望的数据格式
3. 返回完整的翻译结果

## API使用示例

### 请求格式
```javascript
POST /api/translate
Content-Type: application/json

{
  "texts": [
    {
      "id": "text1",
      "content": "こんにちは",
      "language": "ja"
    }
  ],
  "targetLanguages": ["zh-CN", "en"]
}
```

### 响应格式
```javascript
{
  "success": true,
  "translations": [
    {
      "id": "text1",
      "content": "こんにちは",
      "translations": {
        "zh-CN": "你好",    // DeepSeek翻译结果
        "en": "Hello",      // DeepSeek翻译结果
        "zh-TW": "你好"     // DeepSeek翻译结果
      }
    }
  ]
}
```

## 实现细节

### DeepSeek Prompt构建
```javascript
const prompt = `请将以下${sourceLanguageName}文本翻译为${targetLanguageNames}，保持段落对应关系：

[text1] 原文内容1
[text2] 原文内容2

请严格按以下JSON格式返回，不要添加任何其他内容：
{
  "text1": {
    "zh-CN": "翻译内容",
    "en": "Translation content"
  },
  "text2": {
    "zh-CN": "翻译内容",
    "en": "Translation content"
  }
}`;
```

### 错误处理
- DeepSeek API请求失败时返回空翻译内容
- JSON解析失败时返回空翻译内容
- 后端DeepSeek API超时设置为10分钟（600秒）
- 前端翻译请求超时设置为11分钟（660秒）
- 智能内容清理：自动移除Markdown代码块标记和格式化字符
- 详细的错误日志记录

### 内容清理机制
为了确保DeepSeek返回的内容能够正确解析，系统会自动清理以下格式：
- Markdown代码块标记（```json 和 ```）
- 多余的前缀文字（如 "json"）
- 非JSON内容的前后缀
- 自动定位JSON的开始和结束位置

## 超时配置

### 分层超时设计
```
用户请求 → 前端(11分钟) → 后端(10分钟) → DeepSeek API
```

### 超时时间说明
- **前端翻译请求**：11分钟（660秒）
  - 比后端稍长，确保后端有足够时间处理
  - 配置位置：`echo-lab/src/config/translation.js`
- **后端DeepSeek API**：10分钟（600秒）
  - 给DeepSeek充足的处理时间
  - 配置位置：`backend/routes/translate.js`
- **用户提示时间**：30秒
  - 超过30秒显示"翻译可能需要较长时间"提示

### 超时处理策略
- **前端**：显示友好的超时提示，建议分批处理
- **后端**：记录超时日志，返回部分完成的翻译结果
- **用户体验**：提供重试选项和分批处理建议

## 性能考虑

- **批量处理**：一次请求可处理多个文本到多种语言
- **智能分组**：按源语言分组，减少API调用次数
- **上下文优化**：保持文本间的语义连贯性
- **Token限制**：设置max_tokens为8000，适合处理长文本
- **分批建议**：单次翻译建议不超过50个文本项

## 测试验证

DeepSeek翻译服务支持以下测试场景：

1. ✅ 日语→多语言（简中、英语、繁中）
2. ✅ 英语→多语言（简中、日语、繁中）
3. ✅ 中文→多语言（英语、日语）
4. ✅ 混合源语言批量翻译

## 注意事项

1. DeepSeek API有token使用限制，建议监控使用量
2. 翻译质量依赖于prompt设计和模型能力
3. JSON格式要求严格，解析失败会返回空结果
4. 建议定期检查API密钥的有效性和余额
