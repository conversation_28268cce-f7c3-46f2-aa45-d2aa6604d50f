# Echo Lab 数据流

本文档详细说明了 Echo Lab 中的数据流转过程，包括编辑器数据流、播放数据流和资源生成数据流。

## 编辑器数据流

编辑器数据流描述了用户在编辑器中创建和编辑内容的数据流转过程。

### 1. 节点创建和配置

1. 用户在编辑器中创建节点
2. 节点工厂创建节点实例
3. 节点数据保存在 nodeStore 中
4. 节点在画布上渲染

```
用户操作 -> 节点工厂 -> 节点实例 -> nodeStore -> 画布渲染
```

### 2. 节点连接

1. 用户连接两个节点
2. 源节点的 ID 添加到目标节点的 sourceIds 数组中
3. 连接在画布上渲染

```
用户操作 -> 更新目标节点的 sourceIds -> nodeStore -> 画布渲染
```

### 3. 节点配置

1. 用户配置节点参数
2. 节点参数更新到 nodeStore 中
3. 节点重新渲染

```
用户操作 -> 更新节点参数 -> nodeStore -> 节点重新渲染
```

### 4. 内容保存

1. 用户保存内容
2. 前端收集所有节点数据和资源数据
3. 数据发送到后端
4. 后端保存数据到数据库
5. 返回保存结果给前端

```
用户操作 -> 收集数据 -> API 请求 -> 后端处理 -> 数据库保存 -> 返回结果
```

### 5. 内容加载

1. 用户加载内容
2. 前端发送请求到后端
3. 后端从数据库获取数据
4. 数据返回给前端
5. 前端解析数据并创建节点
6. 节点在画布上渲染

```
用户操作 -> API 请求 -> 后端处理 -> 数据库查询 -> 返回数据 -> 解析数据 -> 创建节点 -> 画布渲染
```

## 播放数据流

播放数据流描述了用户播放内容的数据流转过程。

### 1. 时间线生成

1. 用户请求播放内容
2. 前端从后端获取内容数据
3. 前端解析文本序列节点的环节设置
4. 前端生成时间线数据
5. 时间线数据保存在 playerStore 中

```
用户操作 -> API 请求 -> 后端处理 -> 返回数据 -> 解析环节设置 -> 生成时间线 -> playerStore
```

### 2. 内容播放

1. 播放器根据时间线数据播放内容
2. 播放器更新当前播放位置和状态
3. 播放器根据当前位置显示对应的内容和控制

```
时间线数据 -> 播放器播放 -> 更新播放状态 -> 显示内容和控制
```

### 3. 播放控制

1. 用户进行播放控制（播放、暂停、跳转等）
2. 播放器更新播放状态
3. 播放器根据新状态调整播放行为

```
用户操作 -> 更新播放状态 -> 调整播放行为
```

## 资源生成数据流

资源生成数据流描述了系统生成和管理资源的数据流转过程。

### 1. 音频生成

1. 用户请求生成音频
2. 前端发送文本和语言信息到后端
3. 后端计算文本的 MD5 值作为缓存键
4. 后端检查缓存，如果存在则直接返回
5. 如果缓存不存在，后端选择合适的 TTS 服务
6. TTS 服务生成音频
7. 后端上传音频到存储服务
8. 后端保存音频资源记录
9. 后端返回音频 URL 和时长给前端
10. 前端更新资源数据

```
用户操作 -> API 请求 -> 检查缓存 -> TTS 服务 -> 存储服务 -> 保存记录 -> 返回结果 -> 更新资源数据
```

### 2. 翻译生成

1. 用户请求生成翻译
2. 前端发送文本和目标语言信息到后端
3. 后端计算文本的 MD5 值作为缓存键
4. 后端检查缓存，如果存在则直接返回
5. 如果缓存不存在，后端调用翻译服务
6. 翻译服务生成翻译文本
7. 后端保存翻译资源记录
8. 后端返回翻译文本给前端
9. 前端更新资源数据

```
用户操作 -> API 请求 -> 检查缓存 -> 翻译服务 -> 保存记录 -> 返回结果 -> 更新资源数据
```

### 3. 标注生成

1. 用户请求生成标注
2. 前端发送文本和标注方法信息到后端
3. 后端计算文本的 MD5 值作为缓存键
4. 后端检查缓存，如果存在则直接返回
5. 如果缓存不存在，后端调用标注服务
6. 标注服务生成标注文本
7. 后端保存标注资源记录
8. 后端返回标注文本给前端
9. 前端更新资源数据

```
用户操作 -> API 请求 -> 检查缓存 -> 标注服务 -> 保存记录 -> 返回结果 -> 更新资源数据
```

## JSON 数据结构流转

Echo Lab 中的 JSON 数据结构经过以下流程转换：

### 1. 编辑器 JSON 数据

编辑器生成的 JSON 数据包含节点、连接和资源信息：

```javascript
{
  "nodes": {
    // 节点数据
  },
  "nodeTypeCounters": {
    // 节点类型计数器
  },
  "resources": {
    // 资源数据
  }
}
```

### 2. 时间线数据

根据编辑器 JSON 数据和环节设置，生成时间线数据：

```javascript
[
  {
    "id": "seq_abc123",
    "content": "今日は良い天気ですね。",
    "audioUrl": "https://example.com/audio/abc123.mp3",
    "startTime": 0,
    "duration": 2.5,
    "speed": 1.0,
    "sectionId": "section_read_123",
    "sectionName": "通读环节: 基于序列",
    "processingMode": "sequence"
  },
  // 更多时间线项...
]
```

### 3. 播放数据

根据时间线数据，生成播放数据：

```javascript
{
  "currentItem": {
    "id": "seq_abc123",
    "content": "今日は良い天気ですね。",
    "audioUrl": "https://example.com/audio/abc123.mp3",
    "startTime": 0,
    "duration": 2.5,
    "speed": 1.0,
    "sectionId": "section_read_123",
    "sectionName": "通读环节: 基于序列",
    "processingMode": "sequence"
  },
  "currentTime": 1.2,
  "isPlaying": true,
  "volume": 1.0,
  "speed": 1.0
}
```

## 数据持久化

Echo Lab 中的数据持久化流程：

### 1. 前端临时存储

- 使用 localStorage 存储编辑器状态
- 使用 Pinia 状态管理存储运行时数据

### 2. 后端数据库存储

- 内容数据存储在 Content 表中
- 资源数据存储在 Resource 表中
- 用户数据存储在 User 表中
- 反馈数据存储在 Feedback 表中

### 3. 文件存储

- 音频文件存储在阿里云 OSS 中
- 图片文件存储在阿里云 OSS 中

## 数据同步

Echo Lab 中的数据同步机制：

### 1. 自动保存

- 编辑器状态定期自动保存到 localStorage
- 用户手动保存时，数据同步到后端

### 2. 数据加载

- 页面加载时，从 localStorage 恢复编辑器状态
- 用户加载内容时，从后端获取数据

### 3. 资源缓存

- 音频资源使用 MD5 缓存
- 翻译和标注资源使用 MD5 缓存
- 图片资源使用 URL 缓存
