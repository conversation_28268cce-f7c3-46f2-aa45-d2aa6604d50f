# Echo Lab 前端架构

本文档详细说明了 Echo Lab 前端的架构设计、组件结构和工作流程。

## 技术栈

- **框架**：Vue 3.5+
- **构建工具**：Vite 6.x
- **UI组件库**：Element Plus (按需导入)
- **状态管理**：Pinia
- **路由**：Vue Router
- **HTTP客户端**：Fetch API
- **CSS预处理器**：SCSS
- **工具库**：
  - nanoid：生成唯一ID
  - crypto-js：MD5计算
  - p-limit：并发控制

## 目录结构

```
echo-lab/
├── src/
│   ├── assets/         # 静态资源
│   │   ├── icons/      # 图标
│   │   └── images/     # 图片
│   ├── components/     # 组件
│   │   ├── common/     # 通用组件
│   │   ├── editor/     # 编辑器组件
│   │   ├── nodes/      # 节点组件
│   │   └── player/     # 播放器组件
│   ├── composables/    # 组合式函数
│   ├── config/         # 配置文件
│   ├── core/           # 核心功能
│   │   ├── editor/     # 编辑器核心
│   │   ├── nodes/      # 节点工厂和定义
│   │   └── player/     # 播放器核心
│   ├── router/         # 路由配置
│   ├── services/       # 服务层
│   │   ├── api/        # API服务
│   │   └── utils/      # 服务工具
│   ├── stores/         # 状态管理
│   ├── styles/         # 样式文件
│   ├── utils/          # 工具函数
│   └── views/          # 页面视图
```

## 核心模块

### 1. 编辑器模块

编辑器模块是 Echo Lab 的核心功能，用于创建和编辑内容。

#### 主要组件

- **Canvas**：画布组件，用于显示和交互节点
- **Node**：节点组件，不同类型的节点有不同的实现
- **Connection**：连接组件，用于连接节点
- **NodePanel**：节点面板，用于配置节点参数

#### 节点工厂模式

Echo Lab 使用节点工厂模式创建和管理不同类型的节点：

```javascript
// 节点工厂
class NodeFactory {
  // 注册节点类型
  registerNodeType(type, nodeClass) {
    this.nodeTypes[type] = nodeClass;
  }
  
  // 创建节点
  createNode(type, params = {}) {
    if (!this.nodeTypes[type]) {
      throw new Error(`未知节点类型: ${type}`);
    }
    
    const NodeClass = this.nodeTypes[type];
    const node = new NodeClass(params);
    node.id = `${type}_${nanoid()}`;
    
    return node;
  }
}
```

#### 节点类型

- **textContent**：文本内容节点，用于输入和管理文本
- **textSequence**：文本序列节点，用于处理文本序列和环节设置
- **resource**：资源管理节点，用于管理音频、翻译等资源
- **videoConfig**：视频配置节点，用于配置视频参数

### 2. 播放器模块

播放器模块用于播放生成的内容，支持桌面和移动设备。

#### 主要组件

- **VideoPlayer**：视频播放器组件
- **ControlBar**：控制栏组件
- **Timeline**：时间线组件
- **SettingsPanel**：设置面板组件

#### 播放控制

播放器通过时间线数据控制内容的播放：

```javascript
// 播放控制
function playContent(timeline, currentIndex) {
  const item = timeline[currentIndex];
  
  // 播放当前内容
  playAudio(item.audioUrl, item.speed);
  
  // 显示当前内容
  displayContent(item.content);
  
  // 设置定时器，播放下一项
  setTimeout(() => {
    if (currentIndex < timeline.length - 1) {
      playContent(timeline, currentIndex + 1);
    } else {
      // 播放结束
      onPlayEnd();
    }
  }, item.duration * 1000);
}
```

### 3. 状态管理

Echo Lab 使用 Pinia 进行状态管理，主要包括以下几个 store：

#### nodeStore

管理节点数据和操作：

```javascript
// 节点状态管理
const useNodeStore = defineStore('node', {
  state: () => ({
    nodes: {},
    selectedNodeId: null,
    nodeTypeCounters: {}
  }),
  
  actions: {
    addNode(node) {
      this.nodes[node.id] = node;
    },
    
    removeNode(nodeId) {
      delete this.nodes[nodeId];
    },
    
    updateNode(nodeId, updates) {
      this.nodes[nodeId] = { ...this.nodes[nodeId], ...updates };
    },
    
    selectNode(nodeId) {
      this.selectedNodeId = nodeId;
    }
  }
});
```

#### editorStore

管理编辑器状态和操作：

```javascript
// 编辑器状态管理
const useEditorStore = defineStore('editor', {
  state: () => ({
    canvasPosition: { x: 0, y: 0 },
    scale: 1,
    isDragging: false,
    isConnecting: false,
    sourceNodeId: null,
    targetNodeId: null
  }),
  
  actions: {
    moveCanvas(x, y) {
      this.canvasPosition.x += x;
      this.canvasPosition.y += y;
    },
    
    zoomCanvas(scale) {
      this.scale = Math.max(0.5, Math.min(2, this.scale + scale));
    },
    
    startConnecting(nodeId) {
      this.isConnecting = true;
      this.sourceNodeId = nodeId;
    },
    
    finishConnecting(nodeId) {
      if (this.isConnecting && this.sourceNodeId !== nodeId) {
        this.targetNodeId = nodeId;
        // 创建连接
        connectNodes(this.sourceNodeId, nodeId);
      }
      
      this.isConnecting = false;
      this.sourceNodeId = null;
      this.targetNodeId = null;
    }
  }
});
```

#### playerStore

管理播放器状态和操作：

```javascript
// 播放器状态管理
const usePlayerStore = defineStore('player', {
  state: () => ({
    timeline: [],
    currentIndex: 0,
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    speed: 1
  }),
  
  actions: {
    setTimeline(timeline) {
      this.timeline = timeline;
      this.duration = timeline.reduce((total, item) => total + item.duration, 0);
    },
    
    play() {
      this.isPlaying = true;
    },
    
    pause() {
      this.isPlaying = false;
    },
    
    seek(time) {
      this.currentTime = time;
      // 查找对应的索引
      this.currentIndex = findIndexByTime(this.timeline, time);
    },
    
    setVolume(volume) {
      this.volume = volume;
    },
    
    setSpeed(speed) {
      this.speed = speed;
    }
  }
});
```

## 前端路由

Echo Lab 使用 Vue Router 进行路由管理，主要包括以下几个路由：

```javascript
const routes = [
  {
    path: '/',
    component: Home
  },
  {
    path: '/editor',
    component: Editor,
    meta: { requiresAuth: true }
  },
  {
    path: '/player/:id',
    component: Player
  },
  {
    path: '/content',
    component: ContentManagement,
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    component: Login
  },
  {
    path: '/feedback',
    component: Feedback
  }
];
```

## API 服务

Echo Lab 前端通过 Fetch API 与后端进行通信，使用 Vite 代理配置：

```javascript
// vite.config.js
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true
      }
    }
  }
});
```

API 服务封装：

```javascript
// API 服务
const apiService = {
  async get(url, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const response = await fetch(`/api${url}?${queryString}`);
    return handleResponse(response);
  },
  
  async post(url, data = {}) {
    const response = await fetch(`/api${url}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    return handleResponse(response);
  },
  
  async put(url, data = {}) {
    const response = await fetch(`/api${url}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    return handleResponse(response);
  },
  
  async delete(url) {
    const response = await fetch(`/api${url}`, {
      method: 'DELETE'
    });
    return handleResponse(response);
  }
};
```

## 响应式设计

Echo Lab 前端采用响应式设计，支持桌面和移动设备：

- 使用 rem 单位进行布局
- 为桌面和移动设备提供不同的组件实现
- 使用媒体查询适配不同屏幕尺寸

## 性能优化

- Element Plus 组件按需导入
- 图片懒加载
- 路由懒加载
- 大型组件异步加载
- 使用 p-limit 控制并发请求
