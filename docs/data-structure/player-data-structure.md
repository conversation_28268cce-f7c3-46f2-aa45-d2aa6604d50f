# Echo Lab 播放器数据结构

本文档详细说明了 Echo Lab 中播放器数据的结构、状态管理和交互方式。

## 概述

播放器数据是 Echo Lab 中用于控制播放器行为的数据结构。它包含了播放器的状态、当前播放项、播放设置等信息，由播放器组件使用。

## 播放器状态

播放器状态包含以下属性：

```javascript
{
  "timeline": [],           // 时间线数据
  "currentIndex": 0,        // 当前播放索引
  "isPlaying": false,       // 是否正在播放
  "currentTime": 0,         // 当前播放时间(秒)
  "duration": 0,            // 总时长(秒)
  "volume": 1,              // 音量(0-1)
  "speed": 1,               // 播放速度
  "loop": false,            // 是否循环播放
  "autoPlay": true,         // 是否自动播放
  "showTranslation": true,  // 是否显示翻译
  "showAnnotation": true,   // 是否显示标注
  "timerEnabled": false,    // 是否启用定时关闭
  "timerDuration": 0        // 定时关闭时长(分钟)
}
```

## 播放器状态属性

| 属性 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| `timeline` | 数组 | 时间线数据 | `[]` |
| `currentIndex` | 数字 | 当前播放索引 | `0` |
| `isPlaying` | 布尔值 | 是否正在播放 | `false` |
| `currentTime` | 数字 | 当前播放时间(秒) | `0` |
| `duration` | 数字 | 总时长(秒) | `0` |
| `volume` | 数字 | 音量(0-1) | `1` |
| `speed` | 数字 | 播放速度 | `1` |
| `loop` | 布尔值 | 是否循环播放 | `false` |
| `autoPlay` | 布尔值 | 是否自动播放 | `true` |
| `showTranslation` | 布尔值 | 是否显示翻译 | `true` |
| `showAnnotation` | 布尔值 | 是否显示标注 | `true` |
| `timerEnabled` | 布尔值 | 是否启用定时关闭 | `false` |
| `timerDuration` | 数字 | 定时关闭时长(分钟) | `0` |

## 当前播放项

当前播放项是时间线数据中的一个元素，包含以下属性：

```javascript
{
  "id": "seq_abc123",                          // 内容项ID
  "content": "今日は良い天気ですね。",           // 文本内容
  "audioUrl": "https://example.com/audio/abc123.mp3", // 音频URL
  "startTime": 0,                              // 开始时间(秒)
  "duration": 2.5,                             // 持续时间(秒)
  "speed": 1.0,                                // 播放速度
  "sectionId": "section_read_123",             // 所属环节ID
  "sectionName": "通读环节: 基于序列",           // 所属环节名称
  "processingMode": "sequence",                // 处理方式
  "repeatIndex": 0,                            // 重复索引(仅重复环节)
  "isTranslation": false,                      // 是否为翻译
  "language": "ja"                             // 语言代码
}
```

## 播放设置

播放设置包含以下属性：

```javascript
{
  "volume": 1,              // 音量(0-1)
  "speed": 1,               // 播放速度
  "loop": false,            // 是否循环播放
  "autoPlay": true,         // 是否自动播放
  "showTranslation": true,  // 是否显示翻译
  "showAnnotation": true,   // 是否显示标注
  "timerEnabled": false,    // 是否启用定时关闭
  "timerDuration": 0        // 定时关闭时长(分钟)
}
```

## 播放器状态管理

播放器状态通过 Pinia store 进行管理：

```javascript
// 播放器状态管理
const usePlayerStore = defineStore('player', {
  state: () => ({
    timeline: [],
    currentIndex: 0,
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    speed: 1,
    loop: false,
    autoPlay: true,
    showTranslation: true,
    showAnnotation: true,
    timerEnabled: false,
    timerDuration: 0,
    timerEndTime: null
  }),
  
  getters: {
    currentItem: (state) => {
      return state.timeline[state.currentIndex] || null;
    },
    
    progress: (state) => {
      return state.duration > 0 ? state.currentTime / state.duration : 0;
    },
    
    formattedCurrentTime: (state) => {
      return formatTime(state.currentTime);
    },
    
    formattedDuration: (state) => {
      return formatTime(state.duration);
    },
    
    timerRemaining: (state) => {
      if (!state.timerEnabled || !state.timerEndTime) {
        return 0;
      }
      
      const remaining = Math.max(0, state.timerEndTime - Date.now()) / 1000;
      return Math.floor(remaining);
    },
    
    formattedTimerRemaining: (state) => {
      return formatTime(state.timerRemaining);
    }
  },
  
  actions: {
    setTimeline(timeline) {
      this.timeline = timeline;
      this.duration = timeline.reduce((total, item) => total + item.duration, 0);
      this.currentIndex = 0;
      this.currentTime = 0;
    },
    
    play() {
      this.isPlaying = true;
    },
    
    pause() {
      this.isPlaying = false;
    },
    
    togglePlay() {
      this.isPlaying = !this.isPlaying;
    },
    
    seek(time) {
      this.currentTime = Math.max(0, Math.min(this.duration, time));
      this.currentIndex = this.findIndexByTime(this.currentTime);
    },
    
    setCurrentIndex(index) {
      if (index >= 0 && index < this.timeline.length) {
        this.currentIndex = index;
        this.currentTime = this.timeline[index].startTime;
      }
    },
    
    next() {
      if (this.currentIndex < this.timeline.length - 1) {
        this.setCurrentIndex(this.currentIndex + 1);
      } else if (this.loop) {
        this.setCurrentIndex(0);
      }
    },
    
    prev() {
      // 如果当前时间接近内容开头（小于0.3秒），则跳转到上一项
      const currentItem = this.currentItem;
      const timeFromStart = this.currentTime - (currentItem?.startTime || 0);
      
      if (timeFromStart <= 0.3 && this.currentIndex > 0) {
        this.setCurrentIndex(this.currentIndex - 1);
      } else {
        // 否则回到当前内容开头
        this.seek(currentItem?.startTime || 0);
      }
    },
    
    setVolume(volume) {
      this.volume = Math.max(0, Math.min(1, volume));
    },
    
    setSpeed(speed) {
      this.speed = speed;
    },
    
    toggleLoop() {
      this.loop = !this.loop;
    },
    
    toggleAutoPlay() {
      this.autoPlay = !this.autoPlay;
    },
    
    toggleTranslation() {
      this.showTranslation = !this.showTranslation;
    },
    
    toggleAnnotation() {
      this.showAnnotation = !this.showAnnotation;
    },
    
    enableTimer(duration) {
      this.timerEnabled = true;
      this.timerDuration = duration;
      this.timerEndTime = Date.now() + duration * 60 * 1000;
    },
    
    disableTimer() {
      this.timerEnabled = false;
      this.timerEndTime = null;
    },
    
    updateCurrentTime(time) {
      this.currentTime = time;
      
      // 检查是否需要更新当前索引
      const newIndex = this.findIndexByTime(time);
      if (newIndex !== this.currentIndex) {
        this.currentIndex = newIndex;
      }
      
      // 检查是否播放结束
      if (time >= this.duration) {
        this.isPlaying = false;
        
        // 如果启用了循环播放，重新开始播放
        if (this.loop) {
          this.currentTime = 0;
          this.currentIndex = 0;
          this.isPlaying = true;
        }
      }
      
      // 检查定时器
      if (this.timerEnabled && this.timerEndTime && Date.now() >= this.timerEndTime) {
        this.isPlaying = false;
        this.timerEnabled = false;
        this.timerEndTime = null;
      }
    },
    
    findIndexByTime(time) {
      for (let i = 0; i < this.timeline.length; i++) {
        const item = this.timeline[i];
        const endTime = item.startTime + item.duration;
        
        if (time >= item.startTime && time < endTime) {
          return i;
        }
      }
      
      // 如果时间超出范围，返回最后一项
      return this.timeline.length - 1;
    },
    
    jumpToSection(sectionId) {
      // 查找环节的第一项
      const index = this.timeline.findIndex(item => item.sectionId === sectionId);
      
      if (index !== -1) {
        this.setCurrentIndex(index);
      }
    },
    
    reset() {
      this.timeline = [];
      this.currentIndex = 0;
      this.isPlaying = false;
      this.currentTime = 0;
      this.duration = 0;
      this.timerEnabled = false;
      this.timerEndTime = null;
    }
  }
});
```

## 播放器交互

播放器交互包括播放控制、导航、设置等操作：

### 播放控制

```javascript
// 播放控制
const playerStore = usePlayerStore();

// 播放
function play() {
  playerStore.play();
}

// 暂停
function pause() {
  playerStore.pause();
}

// 切换播放/暂停
function togglePlay() {
  playerStore.togglePlay();
}

// 跳转
function seek(time) {
  playerStore.seek(time);
}

// 下一项
function next() {
  playerStore.next();
}

// 上一项
function prev() {
  playerStore.prev();
}
```

### 播放设置

```javascript
// 播放设置
const playerStore = usePlayerStore();

// 设置音量
function setVolume(volume) {
  playerStore.setVolume(volume);
}

// 设置播放速度
function setSpeed(speed) {
  playerStore.setSpeed(speed);
}

// 切换循环播放
function toggleLoop() {
  playerStore.toggleLoop();
}

// 切换自动播放
function toggleAutoPlay() {
  playerStore.toggleAutoPlay();
}

// 切换显示翻译
function toggleTranslation() {
  playerStore.toggleTranslation();
}

// 切换显示标注
function toggleAnnotation() {
  playerStore.toggleAnnotation();
}

// 启用定时关闭
function enableTimer(duration) {
  playerStore.enableTimer(duration);
}

// 禁用定时关闭
function disableTimer() {
  playerStore.disableTimer();
}
```

### 环节导航

```javascript
// 环节导航
const playerStore = usePlayerStore();

// 跳转到环节
function jumpToSection(sectionId) {
  playerStore.jumpToSection(sectionId);
}

// 获取所有环节
function getSections() {
  const sections = [];
  const timeline = playerStore.timeline;
  
  // 提取所有不重复的环节
  timeline.forEach(item => {
    if (!sections.some(s => s.id === item.sectionId)) {
      sections.push({
        id: item.sectionId,
        name: item.sectionName
      });
    }
  });
  
  return sections;
}
```

## 播放器 UI

播放器 UI 包括视频播放区域、控制栏、设置面板等组件：

```vue
<!-- 播放器组件 -->
<template>
  <div class="video-player" :class="{ 'is-playing': isPlaying }">
    <div class="player-content">
      <div class="content-display">
        <div class="content-text" v-if="currentItem">
          {{ currentItem.content }}
        </div>
      </div>
    </div>
    
    <div class="player-controls">
      <div class="progress-bar">
        <div class="progress-track" @click="onProgressClick">
          <div class="progress-fill" :style="{ width: `${progress * 100}%` }"></div>
        </div>
        <div class="time-display">
          <span class="current-time">{{ formattedCurrentTime }}</span>
          <span class="duration">{{ formattedDuration }}</span>
        </div>
      </div>
      
      <div class="control-buttons">
        <button class="prev-button" @click="prev">
          <i class="icon-prev"></i>
        </button>
        <button class="play-button" @click="togglePlay">
          <i :class="isPlaying ? 'icon-pause' : 'icon-play'"></i>
        </button>
        <button class="next-button" @click="next">
          <i class="icon-next"></i>
        </button>
        <div class="volume-control">
          <button class="volume-button" @click="toggleMute">
            <i :class="isMuted ? 'icon-volume-mute' : 'icon-volume'"></i>
          </button>
          <div class="volume-slider">
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              v-model.number="volume"
              @input="setVolume"
            />
          </div>
        </div>
        <button class="settings-button" @click="toggleSettings">
          <i class="icon-settings"></i>
        </button>
      </div>
    </div>
    
    <div class="player-settings" v-if="showSettings">
      <div class="settings-panel">
        <div class="settings-section">
          <h3>播放设置</h3>
          <div class="settings-item">
            <label>播放速度:</label>
            <select v-model.number="speed" @change="setSpeed">
              <option value="0.5">0.5x</option>
              <option value="0.8">0.8x</option>
              <option value="1.0">1.0x</option>
              <option value="1.2">1.2x</option>
              <option value="1.5">1.5x</option>
              <option value="2.0">2.0x</option>
            </select>
          </div>
          <div class="settings-item">
            <label>
              <input type="checkbox" v-model="loop" @change="toggleLoop" />
              循环播放
            </label>
          </div>
          <div class="settings-item">
            <label>
              <input type="checkbox" v-model="autoPlay" @change="toggleAutoPlay" />
              自动播放
            </label>
          </div>
        </div>
        
        <div class="settings-section">
          <h3>显示设置</h3>
          <div class="settings-item">
            <label>
              <input type="checkbox" v-model="showTranslation" @change="toggleTranslation" />
              显示翻译
            </label>
          </div>
          <div class="settings-item">
            <label>
              <input type="checkbox" v-model="showAnnotation" @change="toggleAnnotation" />
              显示标注
            </label>
          </div>
        </div>
        
        <div class="settings-section">
          <h3>定时关闭</h3>
          <div class="settings-item">
            <label>
              <input type="checkbox" v-model="timerEnabled" @change="toggleTimer" />
              启用定时关闭
            </label>
          </div>
          <div class="settings-item" v-if="timerEnabled">
            <label>关闭时长(分钟):</label>
            <select v-model.number="timerDuration" @change="updateTimer">
              <option value="5">5分钟</option>
              <option value="10">10分钟</option>
              <option value="15">15分钟</option>
              <option value="30">30分钟</option>
              <option value="60">60分钟</option>
            </select>
          </div>
          <div class="settings-item" v-if="timerEnabled && timerRemaining > 0">
            <div class="timer-display">
              剩余时间: {{ formattedTimerRemaining }}
            </div>
          </div>
        </div>
        
        <div class="settings-section">
          <h3>环节导航</h3>
          <div class="sections-list">
            <button
              v-for="section in sections"
              :key="section.id"
              class="section-button"
              :class="{ 'active': isCurrentSection(section.id) }"
              @click="jumpToSection(section.id)"
            >
              {{ section.name }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
```

## 播放器示例

以下是一个完整的播放器数据示例：

```javascript
// 播放器数据示例
const playerData = {
  timeline: [
    {
      id: "seq_abc123",
      content: "今日は良い天気ですね。",
      audioUrl: "https://example.com/audio/abc123.mp3",
      startTime: 0,
      duration: 2.5,
      speed: 1.0,
      sectionId: "section_read_123",
      sectionName: "通读环节: 基于序列",
      processingMode: "sequence",
      language: "ja"
    },
    {
      id: "seq_def456",
      content: "日本語の勉強は楽しいです。",
      audioUrl: "https://example.com/audio/def456.mp3",
      startTime: 3.5,
      duration: 3.0,
      speed: 1.0,
      sectionId: "section_read_123",
      sectionName: "通读环节: 基于序列",
      processingMode: "sequence",
      language: "ja"
    }
  ],
  currentIndex: 0,
  isPlaying: false,
  currentTime: 0,
  duration: 5.5,
  volume: 1,
  speed: 1,
  loop: false,
  autoPlay: true,
  showTranslation: true,
  showAnnotation: true,
  timerEnabled: false,
  timerDuration: 0
};
```
