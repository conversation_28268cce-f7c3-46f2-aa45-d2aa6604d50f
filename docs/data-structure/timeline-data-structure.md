# Echo Lab 时间线数据结构

本文档详细说明了 Echo Lab 中时间线数据的结构、生成过程和使用方法。

## 概述

时间线数据是 Echo Lab 中用于控制内容播放的核心数据结构。它由时间线生成器根据编辑器 JSON 数据和环节设置生成，包含了每个内容项的开始时间、持续时间、音频 URL 等信息，供播放器组件使用。

## 时间线数据结构

时间线数据是一个数组，每个元素表示一个内容项的播放信息：

```javascript
[
  {
    "id": "seq_abc123",                          // 内容项ID
    "content": "今日は良い天気ですね。",           // 文本内容
    "audioUrl": "https://example.com/audio/abc123.mp3", // 音频URL
    "startTime": 0,                              // 开始时间(秒)
    "duration": 2.5,                             // 持续时间(秒)
    "speed": 1.0,                                // 播放速度
    "sectionId": "section_read_123",             // 所属环节ID
    "sectionName": "通读环节: 基于序列",           // 所属环节名称
    "processingMode": "sequence",                // 处理方式
    "repeatIndex": 0,                            // 重复索引(仅重复环节)
    "isTranslation": false,                      // 是否为翻译
    "language": "ja"                             // 语言代码
  },
  {
    "id": "seq_def456",
    "content": "日本語の勉強は楽しいです。",
    "audioUrl": "https://example.com/audio/def456.mp3",
    "startTime": 3.5,
    "duration": 3.0,
    "speed": 1.0,
    "sectionId": "section_read_123",
    "sectionName": "通读环节: 基于序列",
    "processingMode": "sequence",
    "repeatIndex": 0,
    "isTranslation": false,
    "language": "ja"
  },
  // 更多时间线项...
]
```

## 时间线项属性

每个时间线项包含以下属性：

| 属性 | 类型 | 说明 | 必需 |
|------|------|------|------|
| `id` | 字符串 | 内容项ID | 是 |
| `content` | 字符串 | 文本内容 | 是 |
| `audioUrl` | 字符串 | 音频URL | 是 |
| `startTime` | 数字 | 开始时间(秒) | 是 |
| `duration` | 数字 | 持续时间(秒) | 是 |
| `speed` | 数字 | 播放速度 | 是 |
| `sectionId` | 字符串 | 所属环节ID | 是 |
| `sectionName` | 字符串 | 所属环节名称 | 是 |
| `processingMode` | 字符串 | 处理方式 | 是 |
| `repeatIndex` | 数字 | 重复索引(仅重复环节) | 否 |
| `isTranslation` | 布尔值 | 是否为翻译 | 否 |
| `language` | 字符串 | 语言代码 | 否 |

## 时间线生成过程

时间线数据由时间线生成器根据编辑器 JSON 数据和环节设置生成，生成过程如下：

1. **提取序列数据**：从编辑器 JSON 数据中提取序列数据
2. **处理环节设置**：根据环节设置处理序列数据
3. **计算时间信息**：计算每个内容项的开始时间和持续时间
4. **生成时间线数据**：生成最终的时间线数据

### 提取序列数据

从编辑器 JSON 数据中提取序列数据的过程如下：

```javascript
function extractSequencesFromNodes(configJson) {
  // 获取所有文本内容节点
  const textContentNodes = Object.values(configJson.nodes || {})
    .filter(node => node.type === 'textContent');

  // 从每个文本内容节点提取序列
  const sequences = textContentNodes.map(node => {
    // 获取分句
    const segments = node.params?.segments || [];

    // 创建序列项
    const items = segments.map(segment => ({
      id: segment.id,
      content: segment.content,
      language: segment.language || 'auto',
      audioUrl: findAudioUrl(configJson, segment.content),
      duration: findAudioDuration(configJson, segment.content) || 2
    }));

    // 返回序列
    return {
      id: node.id,
      name: node.customName || node.id,
      items
    };
  });

  // 如果有多个序列，创建一个合并序列
  if (sequences.length > 1) {
    const mergedItems = sequences.flatMap(seq => seq.items);

    sequences.push({
      id: 'merged_sequence',
      name: '合并序列',
      items: mergedItems
    });
  }

  return sequences;
}
```

### 处理环节设置

根据环节设置处理序列数据的过程如下：

```javascript
function generateTimeline(configJson, settings) {
  // 提取序列数据
  const sequences = extractSequencesFromNodes(configJson);

  // 初始化时间线
  const timeline = [];

  // 初始化开始时间
  let currentTime = 0;

  // 处理每个环节
  settings.sections.forEach(section => {
    // 跳过禁用的环节
    if (section.enabled === false) {
      return;
    }

    // 获取处理方式
    const processingMode = section.processingMode || 'sequence';

    // 获取源内容项
    const sourceItems = getSourceItems(configJson, sequences, section, processingMode);

    // 根据环节类型处理内容项
    if (section.type === 'readthrough' || section.type === 'read') {
      // 处理通读环节
      processReadthroughSection(timeline, sourceItems, section, processingMode, currentTime);
    } else if (section.type === 'repeat') {
      // 处理重复环节
      processRepeatSection(timeline, sourceItems, section, processingMode, currentTime, configJson);
    }

    // 更新当前时间
    if (timeline.length > 0) {
      const lastItem = timeline[timeline.length - 1];
      currentTime = lastItem.startTime + lastItem.duration;
    }
  });

  return timeline;
}
```

### 处理通读环节

处理通读环节的过程如下：

```javascript
function processReadthroughSection(timeline, sourceItems, section, processingMode, startTime) {
  let currentTime = startTime;

  // 获取环节配置
  const speed = section.config?.speed || 1.0;
  const pauseDuration = section.config?.interval || 1000;

  // 处理每个内容项
  sourceItems.forEach(item => {
    // 添加到时间线
    timeline.push({
      id: item.id,
      content: item.content,
      audioUrl: item.audioUrl,
      startTime: currentTime,
      duration: (item.duration || 2) * (1 / speed),
      speed: speed,
      sectionId: section.id,
      sectionName: section.name || section.title,
      processingMode: processingMode,
      language: item.language
    });

    // 更新当前时间
    currentTime += (item.duration || 2) * (1 / speed) + pauseDuration / 1000;
  });
}
```

### 处理重复环节

处理重复环节的过程如下：

```javascript
function processRepeatSection(timeline, sourceItems, section, processingMode, startTime, configJson) {
  let currentTime = startTime;

  // 获取环节配置
  const repeatCount = section.config?.repeatCount || 3;
  const speed = section.config?.speed || section.speed || 1.0;
  const pauseDuration = section.config?.interval || section.pauseDuration || 3000;
  const insertTranslation = section.config?.insertTranslation === true || section.insertTranslation === true;
  const translationLanguage = section.config?.translationLanguage || section.translationLanguage || "";

  // 处理每个内容项
  sourceItems.forEach(item => {
    // 重复指定次数
    for (let i = 0; i < repeatCount; i++) {
      // 添加到时间线
      timeline.push({
        id: `${item.id}_repeat_${i}`,
        content: item.content,
        audioUrl: item.audioUrl,
        startTime: currentTime,
        duration: (item.duration || 2) * (1 / speed),
        speed: speed,
        sectionId: section.id,
        sectionName: section.name || section.title,
        repeatIndex: i,
        processingMode: processingMode,
        language: item.language
      });

      // 更新当前时间
      currentTime += (item.duration || 2) * (1 / speed) + pauseDuration / 1000;

      // 如果是最后一次重复，并且启用了翻译，添加翻译内容
      if (i === repeatCount - 1 && insertTranslation && translationLanguage) {
        // 查找翻译内容
        let translationText = null;

        // 从resources中查找翻译
        if (configJson.resources &&
            configJson.resources.translations &&
            configJson.resources.translations[translationLanguage]) {

          const translations = configJson.resources.translations[translationLanguage];

          // 通过ID查找
          if (translations[item.id]) {
            translationText = translations[item.id];
          }
        }

        if (translationText) {
          // 添加翻译到时间线
          timeline.push({
            id: `${item.id}_translation`,
            content: translationText,
            audioUrl: findAudioUrl(configJson, translationText),
            startTime: currentTime,
            duration: 2 * (1 / speed),
            speed: speed,
            sectionId: section.id,
            sectionName: section.name || section.title,
            isTranslation: true,
            language: translationLanguage,
            processingMode: processingMode
          });

          // 更新当前时间
          currentTime += 2 * (1 / speed) + pauseDuration / 1000;
        }
      }
    }
  });
}
```

## 时间线使用方法

时间线数据用于控制播放器的播放行为，主要用法如下：

### 播放控制

```javascript
// 播放控制
function playTimeline(timeline) {
  // 初始化播放状态
  let currentIndex = 0;
  let isPlaying = false;

  // 播放函数
  function play() {
    if (currentIndex >= timeline.length) {
      return;
    }

    isPlaying = true;

    // 获取当前项
    const item = timeline[currentIndex];

    // 播放音频
    const audio = new Audio(item.audioUrl);
    audio.playbackRate = item.speed;

    // 显示内容
    displayContent(item.content);

    // 音频播放结束后播放下一项
    audio.onended = () => {
      currentIndex++;

      if (currentIndex < timeline.length) {
        // 等待间隔时间后播放下一项
        setTimeout(() => {
          play();
        }, getInterval(currentIndex));
      } else {
        // 播放结束
        isPlaying = false;
        onPlayEnd();
      }
    };

    // 开始播放
    audio.play();
  }

  // 暂停函数
  function pause() {
    isPlaying = false;
    // 暂停音频
    // ...
  }

  // 跳转函数
  function seek(time) {
    // 查找对应的索引
    currentIndex = findIndexByTime(timeline, time);

    // 如果正在播放，重新开始播放
    if (isPlaying) {
      pause();
      play();
    }
  }

  // 获取间隔时间
  function getInterval(index) {
    if (index <= 0 || index >= timeline.length) {
      return 0;
    }

    const prevItem = timeline[index - 1];
    const currentItem = timeline[index];

    // 如果是同一个环节，使用环节的间隔时间
    if (prevItem.sectionId === currentItem.sectionId) {
      // 查找环节设置
      const section = settings.sections.find(s => s.id === currentItem.sectionId);

      if (section) {
        return section.config?.interval || 1000;
      }
    }

    // 默认间隔时间
    return 1000;
  }

  // 根据时间查找索引
  function findIndexByTime(timeline, time) {
    for (let i = 0; i < timeline.length; i++) {
      const item = timeline[i];
      const endTime = item.startTime + item.duration;

      if (time >= item.startTime && time < endTime) {
        return i;
      }
    }

    // 如果时间超出范围，返回最后一项
    return timeline.length - 1;
  }

  // 显示内容
  function displayContent(content) {
    // 显示内容
    // ...
  }

  // 播放结束回调
  function onPlayEnd() {
    // 播放结束处理
    // ...
  }

  // 返回控制接口
  return {
    play,
    pause,
    seek,
    get currentIndex() {
      return currentIndex;
    },
    get isPlaying() {
      return isPlaying;
    }
  };
}
```

### 时间线导航

```javascript
// 时间线导航
function navigateTimeline(timeline, controller) {
  // 下一项
  function next() {
    if (controller.currentIndex < timeline.length - 1) {
      controller.seek(timeline[controller.currentIndex + 1].startTime);
    }
  }

  // 上一项
  function prev() {
    if (controller.currentIndex > 0) {
      controller.seek(timeline[controller.currentIndex - 1].startTime);
    }
  }

  // 跳转到环节
  function jumpToSection(sectionId) {
    // 查找环节的第一项
    const index = timeline.findIndex(item => item.sectionId === sectionId);

    if (index !== -1) {
      controller.seek(timeline[index].startTime);
    }
  }

  // 返回导航接口
  return {
    next,
    prev,
    jumpToSection
  };
}
```

## 时间线示例

以下是一个完整的时间线数据示例：

```javascript
[
  // 通读环节
  {
    "id": "seq_abc123",
    "content": "今日は良い天気ですね。",
    "audioUrl": "https://example.com/audio/abc123.mp3",
    "startTime": 0,
    "duration": 2.5,
    "speed": 1.0,
    "sectionId": "section_read_123",
    "sectionName": "通读环节: 基于序列",
    "processingMode": "sequence",
    "language": "ja"
  },
  {
    "id": "seq_def456",
    "content": "日本語の勉強は楽しいです。",
    "audioUrl": "https://example.com/audio/def456.mp3",
    "startTime": 3.5,
    "duration": 3.0,
    "speed": 1.0,
    "sectionId": "section_read_123",
    "sectionName": "通读环节: 基于序列",
    "processingMode": "sequence",
    "language": "ja"
  },

  // 重复环节 - 第一项
  {
    "id": "seq_abc123_repeat_0",
    "content": "今日は良い天気ですね。",
    "audioUrl": "https://example.com/audio/abc123.mp3",
    "startTime": 7.5,
    "duration": 2.5,
    "speed": 1.0,
    "sectionId": "section_repeat_456",
    "sectionName": "重复环节: 基于序列",
    "repeatIndex": 0,
    "processingMode": "sequence",
    "language": "ja"
  },
  {
    "id": "seq_abc123_repeat_1",
    "content": "今日は良い天気ですね。",
    "audioUrl": "https://example.com/audio/abc123.mp3",
    "startTime": 11.0,
    "duration": 2.5,
    "speed": 1.0,
    "sectionId": "section_repeat_456",
    "sectionName": "重复环节: 基于序列",
    "repeatIndex": 1,
    "processingMode": "sequence",
    "language": "ja"
  },
  {
    "id": "seq_abc123_repeat_2",
    "content": "今日は良い天気ですね。",
    "audioUrl": "https://example.com/audio/abc123.mp3",
    "startTime": 14.5,
    "duration": 2.5,
    "speed": 1.0,
    "sectionId": "section_repeat_456",
    "sectionName": "重复环节: 基于序列",
    "repeatIndex": 2,
    "processingMode": "sequence",
    "language": "ja"
  },
  {
    "id": "seq_abc123_translation",
    "content": "今天天气真好。",
    "audioUrl": "https://example.com/audio/abc123_zh.mp3",
    "startTime": 18.0,
    "duration": 2.0,
    "speed": 1.0,
    "sectionId": "section_repeat_456",
    "sectionName": "重复环节: 基于序列",
    "isTranslation": true,
    "language": "zh-CN",
    "processingMode": "sequence"
  },

  // 重复环节 - 第二项
  {
    "id": "seq_def456_repeat_0",
    "content": "日本語の勉強は楽しいです。",
    "audioUrl": "https://example.com/audio/def456.mp3",
    "startTime": 21.0,
    "duration": 3.0,
    "speed": 1.0,
    "sectionId": "section_repeat_456",
    "sectionName": "重复环节: 基于序列",
    "repeatIndex": 0,
    "processingMode": "sequence",
    "language": "ja"
  },
  {
    "id": "seq_def456_repeat_1",
    "content": "日本語の勉強は楽しいです。",
    "audioUrl": "https://example.com/audio/def456.mp3",
    "startTime": 25.0,
    "duration": 3.0,
    "speed": 1.0,
    "sectionId": "section_repeat_456",
    "sectionName": "重复环节: 基于序列",
    "repeatIndex": 1,
    "processingMode": "sequence",
    "language": "ja"
  },
  {
    "id": "seq_def456_repeat_2",
    "content": "日本語の勉強は楽しいです。",
    "audioUrl": "https://example.com/audio/def456.mp3",
    "startTime": 29.0,
    "duration": 3.0,
    "speed": 1.0,
    "sectionId": "section_repeat_456",
    "sectionName": "重复环节: 基于序列",
    "repeatIndex": 2,
    "processingMode": "sequence",
    "language": "ja"
  },
  {
    "id": "seq_def456_translation",
    "content": "学习日语很有趣。",
    "audioUrl": "https://example.com/audio/def456_zh.mp3",
    "startTime": 33.0,
    "duration": 2.0,
    "speed": 1.0,
    "sectionId": "section_repeat_456",
    "sectionName": "重复环节: 基于序列",
    "isTranslation": true,
    "language": "zh-CN",
    "processingMode": "sequence"
  }
]
```
