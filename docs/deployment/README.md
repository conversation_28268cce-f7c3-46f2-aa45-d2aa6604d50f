# Echo Lab 部署文档

本文档概述了 Echo Lab 的部署方法和环境配置。

## 部署概述

Echo Lab 由前端和后端两部分组成，需要分别部署：

1. **前端**：Vue 3 应用，部署在 Nginx 服务器上
2. **后端**：Node.js 应用，使用 Express 框架，连接 MySQL 数据库

## 部署环境要求

### 服务器要求

- **操作系统**：Linux（推荐 Alibaba Cloud Linux 3）
- **CPU**：2 核或以上
- **内存**：4GB 或以上
- **存储**：50GB 或以上
- **带宽**：5Mbps 或以上

### 软件要求

- **Node.js**：v18.x 或以上
- **MySQL**：v8.0 或以上
- **Nginx**：v1.20 或以上
- **Git**：v2.30 或以上

## 部署方法

Echo Lab 支持以下部署方法：

1. [直接部署](./direct-deployment.md)：直接在服务器上部署前端和后端
2. [自动部署](./auto-deployment.md)：使用脚本自动部署前端和后端

## 部署步骤

### 1. 准备环境

在部署 Echo Lab 之前，需要准备好以下环境：

- 安装 Node.js
- 安装 MySQL
- 安装 Nginx
- 安装 Git

### 2. 克隆代码

```bash
# 克隆代码
git clone https://github.com/yourusername/JapRepeater.git
cd JapRepeater
```

### 3. 配置环境变量

创建 `.env` 文件，配置环境变量：

```bash
# 后端环境变量
cd backend
cp .env.example .env
```

编辑 `.env` 文件，配置以下环境变量：

```
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=echo-lab
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# JWT 配置
JWT_SECRET=your_jwt_secret

# 阿里云 OSS 配置
OSS_REGION=your_oss_region
OSS_ACCESS_KEY_ID=your_oss_access_key_id
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
OSS_BUCKET=your_oss_bucket

# Google Cloud 配置
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/google-credentials.json

# 百度 AI 配置
BAIDU_APP_ID=your_baidu_app_id
BAIDU_API_KEY=your_baidu_api_key
BAIDU_SECRET_KEY=your_baidu_secret_key

# 邮件配置
EMAIL_HOST=your_email_host
EMAIL_PORT=your_email_port
EMAIL_USER=your_email_user
EMAIL_PASSWORD=your_email_password
EMAIL_FROM=your_email_from
```

### 4. 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../echo-lab
npm install
```

### 5. 初始化数据库

```bash
# 初始化数据库
cd backend
npx sequelize-cli db:create
npx sequelize-cli db:migrate
npx sequelize-cli db:seed:all
```

### 6. 构建前端

```bash
# 构建前端
cd ../echo-lab
npm run build
```

### 7. 配置 Nginx

创建 Nginx 配置文件：

```bash
# 创建 Nginx 配置文件
sudo nano /etc/nginx/conf.d/echolab.club.conf
```

配置文件内容：

```nginx
server {
    listen 80;
    server_name echolab.club www.echolab.club;

    # 重定向到 HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name echolab.club www.echolab.club;

    # SSL 配置
    ssl_certificate /etc/letsencrypt/live/echolab.club/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/echolab.club/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;

    # 前端配置
    location / {
        root /var/www/echo-lab;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # API 配置
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 8. 启动服务

```bash
# 启动后端服务
cd backend
npm run dev

# 重启 Nginx
sudo systemctl restart nginx
```

## 部署脚本

为了简化部署过程，Echo Lab 提供了部署脚本：

```bash
# 部署脚本
./deploy.sh
```

脚本内容：

```bash
#!/bin/bash

# Echo Lab 部署脚本

# 设置变量
FRONTEND_DIR="echo-lab"
BACKEND_DIR="backend"
NGINX_CONF_DIR="/etc/nginx/conf.d"
NGINX_CONF_FILE="echolab.club.conf"
WWW_DIR="/var/www/echo-lab"

# 输出彩色文本
function echo_color() {
  case $1 in
    "red") COLOR='\033[0;31m' ;;
    "green") COLOR='\033[0;32m' ;;
    "yellow") COLOR='\033[0;33m' ;;
    "blue") COLOR='\033[0;34m' ;;
    "purple") COLOR='\033[0;35m' ;;
    "cyan") COLOR='\033[0;36m' ;;
    *) COLOR='\033[0m' ;;
  esac
  
  echo -e "${COLOR}$2${NC}"
}

# 检查命令是否存在
function check_command() {
  if ! command -v $1 &> /dev/null; then
    echo_color "red" "错误: $1 命令未找到，请先安装。"
    exit 1
  fi
}

# 检查必要的命令
check_command "git"
check_command "node"
check_command "npm"
check_command "mysql"
check_command "nginx"

# 更新代码
echo_color "blue" "正在更新代码..."
git pull

# 构建前端
echo_color "blue" "正在构建前端..."
cd $FRONTEND_DIR
npm install
npm run build
cd ..

# 部署前端
echo_color "blue" "正在部署前端..."
sudo mkdir -p $WWW_DIR
sudo cp -r $FRONTEND_DIR/dist/* $WWW_DIR/

# 安装后端依赖
echo_color "blue" "正在安装后端依赖..."
cd $BACKEND_DIR
npm install
cd ..

# 复制 Nginx 配置文件
echo_color "blue" "正在复制 Nginx 配置文件..."
sudo cp $NGINX_CONF_FILE $NGINX_CONF_DIR/

# 重启 Nginx
echo_color "blue" "正在重启 Nginx..."
sudo systemctl restart nginx

# 重启后端服务
echo_color "blue" "正在重启后端服务..."
cd $BACKEND_DIR
pm2 restart ecosystem.config.js

echo_color "green" "部署完成！"
```

## SSL 证书配置

Echo Lab 使用 Let's Encrypt 提供的免费 SSL 证书：

```bash
# 安装 Certbot
sudo apt-get update
sudo apt-get install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d echolab.club -d www.echolab.club

# 自动续期证书
sudo certbot renew --dry-run
```

## 数据库备份

定期备份数据库是很重要的：

```bash
# 备份数据库
mysqldump -u your_db_user -p echo-lab > backup_$(date +%Y%m%d).sql

# 恢复数据库
mysql -u your_db_user -p echo-lab < backup_20230101.sql
```

## 日志管理

Echo Lab 的日志文件位于以下位置：

- **Nginx 日志**：`/var/log/nginx/access.log` 和 `/var/log/nginx/error.log`
- **后端日志**：`backend/logs/`

## 故障排除

### 前端无法访问

1. 检查 Nginx 是否正在运行：`sudo systemctl status nginx`
2. 检查 Nginx 配置是否正确：`sudo nginx -t`
3. 检查前端文件是否正确部署：`ls -la /var/www/echo-lab`

### 后端无法访问

1. 检查后端服务是否正在运行：`pm2 status`
2. 检查后端日志：`pm2 logs`
3. 检查数据库连接：`mysql -u your_db_user -p -e "SHOW DATABASES;"`

### 数据库问题

1. 检查数据库服务是否正在运行：`sudo systemctl status mysql`
2. 检查数据库连接：`mysql -u your_db_user -p -e "SHOW DATABASES;"`
3. 检查数据库表：`mysql -u your_db_user -p -e "USE echo-lab; SHOW TABLES;"`

## 性能优化

### Nginx 优化

```nginx
# Nginx 性能优化
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 65535;
    multi_accept on;
    use epoll;
}

http {
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # 缓存设置
    open_file_cache max=1000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
```

### Node.js 优化

```javascript
// PM2 配置
module.exports = {
  apps: [{
    name: 'echo-lab-backend',
    script: 'app.js',
    instances: 'max',
    exec_mode: 'cluster',
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }]
};
```

### MySQL 优化

```ini
# MySQL 性能优化
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
max_connections = 1000
```

## 安全配置

### 防火墙配置

```bash
# 配置防火墙
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 安全头部配置

```nginx
# Nginx 安全头部配置
add_header X-Frame-Options "SAMEORIGIN";
add_header X-XSS-Protection "1; mode=block";
add_header X-Content-Type-Options "nosniff";
add_header Referrer-Policy "no-referrer-when-downgrade";
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'";
```

### 数据库安全配置

```bash
# MySQL 安全配置
mysql_secure_installation
```
