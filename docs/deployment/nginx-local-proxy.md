# Nginx 本地代理配置

## 概述
使用 Nginx 在本地环境中同时提供静态文件服务和 API 代理转发功能。

## 配置文件

创建 `nginx.conf` 文件：

```nginx
events {
    worker_connections 1024;
}

http {
    server {
        listen 56675;
        server_name localhost;

        # API 转发到 3000 端口
        location /api {
            proxy_pass http://localhost:3000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 静态文件服务
        location / {
            root /Users/<USER>/JapRepeater/echo-lab/dist;
            try_files $uri $uri/ /index.html;
            index index.html;
        }
    }
}
```

## 使用步骤

1. **构建前端项目**
   ```bash
   npm run build
   ```

2. **启动后端服务**（端口3000）
   ```bash
   npm run start
   ```

3. **检查端口占用**
   ```bash
   lsof -i :56675
   # 如果有占用，杀死进程
   sudo kill -9 $(lsof -t -i:56675)
   ```

4. **启动 Nginx**
   ```bash
   sudo nginx -c $(pwd)/local.echolab.club.conf
   ```

5. **访问应用**
   ```
   http://localhost:56675
   ```

## 功能说明

- **静态文件服务**: 提供前端构建后的静态文件
- **API 代理**: 将 `/api` 请求转发到后端服务（端口3000）
- **SPA 路由支持**: `try_files` 配置支持 Vue Router 的 history 模式

## 停止服务

```bash
sudo nginx -s quit
```

## 重新加载配置

```bash
sudo nginx -s reload
```

## 故障排除

1. **端口被占用**: 使用 `lsof -i :56675` 查看占用进程
2. **权限问题**: 使用 `sudo` 启动 nginx
3. **配置语法错误**: 使用 `nginx -t -c $(pwd)/local.echolab.club.conf` 测试配置