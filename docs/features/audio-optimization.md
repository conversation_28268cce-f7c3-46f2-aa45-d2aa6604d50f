# 音频文件大小优化

本文档详细说明了 Echo Lab 中音频文件大小优化的实现和使用方法。

## 问题描述

在 Echo Lab 项目中，导出的音频文件比较大，并且显示为"波形音频"（WAV 格式）。这是因为原始的 `audioBufferToWav` 函数将 AudioBuffer 转换为 WAV 格式时，使用了以下特性：

1. 使用原始的通道数（通常是双声道或更多）
2. 使用原始的采样率（通常是 44.1kHz 或 48kHz）
3. 使用 16 位 PCM 格式（位深度为 16 位）
4. 没有进行任何压缩

这导致导出的音频文件比较大，特别是对于较长的音频内容。

## 解决方案

我们对 `audioBufferToWav` 函数进行了优化，添加了以下功能：

1. **使用固定采样率**：使用固定的 24kHz 采样率，提供良好的音质和文件大小平衡
2. **转换为单声道**：将多声道音频混合为单声道
3. **保持 16 位 PCM 格式**：这是 WAV 格式的标准位深度，提供良好的音质
4. **添加详细的日志**：记录转换前后的文件大小，便于调试和优化

这些优化可以显著减小导出的音频文件大小，同时保持良好的音质。

## 技术实现

### 优化的 `audioBufferToWav` 函数

新的 `audioBufferToWav` 函数接受一个选项对象，允许自定义转换参数：

```javascript
/**
 * 将 AudioBuffer 转换为 WAV 格式（优化大小）
 * @param {AudioBuffer} buffer - 音频缓冲区
 * @param {Object} options - 转换选项
 * @returns {ArrayBuffer} WAV 格式的 ArrayBuffer
 */
const audioBufferToWav = (buffer, options = {}) => {
  // 默认配置
  const config = {
    // 使用固定的 24kHz 采样率
    targetSampleRate: options.targetSampleRate || 24000,
    // 使用单声道以减小文件大小
    channels: options.channels || 1,
    // 使用 16 位 PCM 格式
    bitDepth: options.bitDepth || 16
  };

  // 获取原始参数
  const originalNumOfChan = buffer.numberOfChannels;
  const originalSampleRate = buffer.sampleRate;
  
  // 计算目标参数
  const targetSampleRate = config.targetSampleRate;
  const channels = Math.min(config.channels, originalNumOfChan);
  const bitDepth = config.bitDepth;
  
  // 计算重采样比例
  const resampleRatio = targetSampleRate / originalSampleRate;
  
  // 计算新的采样点数量
  const newLength = Math.floor(buffer.length * resampleRatio);
  
  // 创建新的 AudioBuffer
  const offlineCtx = new OfflineAudioContext(channels, newLength, targetSampleRate);
  const newBuffer = offlineCtx.createBuffer(channels, newLength, targetSampleRate);
  
  // 重采样和通道混合
  for (let newChannel = 0; newChannel < channels; newChannel++) {
    const newData = newBuffer.getChannelData(newChannel);
    
    for (let newIndex = 0; newIndex < newLength; newIndex++) {
      // 计算原始索引
      const originalIndex = Math.floor(newIndex / resampleRatio);
      
      // 混合所有通道的样本
      let mixedSample = 0;
      for (let channel = 0; channel < originalNumOfChan; channel++) {
        mixedSample += buffer.getChannelData(channel)[originalIndex];
      }
      // 平均值
      mixedSample /= originalNumOfChan;
      
      // 设置新样本
      newData[newIndex] = mixedSample;
    }
  }
  
  // 转换为 WAV 格式
  // ... WAV 格式转换代码 ...
  
  return wavBuffer;
};
```

### 降采样和通道混合

函数实现了两个关键的优化技术：

1. **固定采样率**：使用固定的 24kHz 采样率，通过重采样减少采样点数量
   ```javascript
   // 使用固定的 24kHz 采样率
   const targetSampleRate = 24000;
   const resampleRatio = targetSampleRate / originalSampleRate;
   const newLength = Math.floor(buffer.length * resampleRatio);
   ```

2. **通道混合**：将多声道音频混合为单声道
   ```javascript
   // 混合所有通道的样本
   let mixedSample = 0;
   for (let channel = 0; channel < originalNumOfChan; channel++) {
     mixedSample += buffer.getChannelData(channel)[originalIndex];
   }
   // 平均值
   mixedSample /= originalNumOfChan;
   ```

### 应用优化

在两个关键位置应用了优化：

1. **初始化音频元素时**：
   ```javascript
   const wavData = audioBufferToWav(props.audioBuffer, {
     channels: 1,
     targetSampleRate: Math.max(22050, Math.floor(props.audioBuffer.sampleRate / 2))
   });
   ```

2. **导出音频时**：
   ```javascript
   const wavData = audioBufferToWav(newBuffer, {
     channels: 1,
     targetSampleRate: Math.max(22050, Math.floor(newBuffer.sampleRate / 2))
   });
   ```

## 优化效果

通过这些优化，我们可以显著减小导出的音频文件大小：

1. **通道数减半**：从双声道到单声道，文件大小减少 50%
2. **采样率减半**：从 44.1kHz 到 22.05kHz，文件大小再减少 50%

综合效果是文件大小减少约 75%，同时保持良好的音质。例如，一个原本 20MB 的 WAV 文件可能会减小到约 5MB。

## 注意事项

1. **音质平衡**：我们设置了最低采样率为 22050Hz，以确保音质不会过度降低
2. **格式限制**：WAV 格式本身不支持压缩，如果需要更小的文件大小，未来可以考虑添加 MP3 或 OGG 格式的支持
3. **兼容性**：优化后的 WAV 文件仍然兼容所有支持 WAV 格式的播放器和应用程序

## 未来改进

如果需要进一步减小文件大小，可以考虑以下改进：

1. **添加 MP3 格式支持**：使用 Web Audio API 的 MediaRecorder 或第三方库将 AudioBuffer 转换为 MP3 格式
2. **添加 OGG 格式支持**：OGG 是一种开源的压缩音频格式，可以提供比 WAV 更小的文件大小
3. **自适应采样率**：根据音频内容自动选择最佳采样率，例如语音内容可以使用更低的采样率

## 使用方法

### 在资源管理节点中使用

1. 在编辑器中创建或选择资源管理节点
2. 在音频设置中启用音频生成
3. 系统会自动使用优化的音频格式

### 在导出音频时使用

1. 在播放器中点击"导出音频"按钮
2. 系统会自动使用优化的音频格式生成音频文件
3. 下载生成的音频文件

## 最佳实践

1. **音质与文件大小平衡**：
   - 对于语音内容，可以使用较低的采样率（22050Hz）
   - 对于音乐内容，可以使用较高的采样率（24000Hz 或更高）

2. **格式选择**：
   - 对于需要高兼容性的场景，使用 WAV 格式
   - 对于需要小文件大小的场景，考虑使用 MP3 或 OGG 格式（未来支持）

3. **性能优化**：
   - 对于大型音频文件，考虑使用 Web Worker 进行音频处理
   - 使用缓存减少重复处理
