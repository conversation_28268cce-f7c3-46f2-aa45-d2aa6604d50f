# 用户反馈功能

本文档详细说明了 Echo Lab 中用户反馈功能的实现和使用方法。

## 概述

用户反馈功能允许用户提交对 Echo Lab 的意见、建议和问题报告。这些反馈将被收集并由管理员处理，以改进产品和解决用户问题。

## 功能特点

- 允许用户提交反馈内容
- 支持匿名反馈和带联系方式的反馈
- 已登录用户可以查看自己的反馈历史
- 管理员可以查看和管理所有用户的反馈
- 支持反馈状态跟踪（待处理、处理中、已完成、已拒绝）

## 数据结构

### 反馈模型

```javascript
// 反馈模型
const Feedback = sequelize.define('Feedback', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  email: {
    type: DataTypes.STRING,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'rejected'),
    defaultValue: 'pending'
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id'
    }
  }
});
```

## 后端实现

### 创建反馈

```javascript
// 创建反馈
async function createFeedback(req, res) {
  const { content, email } = req.body;
  
  // 检查参数
  if (!content) {
    return res.status(400).json({ message: '反馈内容不能为空' });
  }
  
  try {
    // 创建反馈
    const feedback = await Feedback.create({
      content,
      email: email || null,
      userId: req.user ? req.user.id : null
    });
    
    // 返回结果
    return res.status(201).json(feedback);
  } catch (error) {
    console.error('创建反馈失败:', error);
    return res.status(500).json({ message: '服务器错误' });
  }
}
```

### 获取用户反馈列表

```javascript
// 获取用户反馈列表
async function getFeedbackList(req, res) {
  // 检查用户是否已登录
  if (!req.user) {
    return res.status(401).json({ message: '未授权' });
  }
  
  try {
    // 查询用户的反馈列表
    const feedbackList = await Feedback.findAll({
      where: { userId: req.user.id },
      order: [['createdAt', 'DESC']]
    });
    
    // 返回结果
    return res.json(feedbackList);
  } catch (error) {
    console.error('获取反馈列表失败:', error);
    return res.status(500).json({ message: '服务器错误' });
  }
}
```

### 获取所有反馈（管理员）

```javascript
// 获取所有反馈（管理员）
async function getAllFeedback(req, res) {
  // 检查用户是否是管理员
  if (!req.user || req.user.role !== 'admin') {
    return res.status(403).json({ message: '权限不足' });
  }
  
  try {
    // 查询所有反馈
    const feedbackList = await Feedback.findAll({
      include: [
        {
          model: User,
          attributes: ['id', 'username', 'email']
        }
      ],
      order: [['createdAt', 'DESC']]
    });
    
    // 返回结果
    return res.json(feedbackList);
  } catch (error) {
    console.error('获取所有反馈失败:', error);
    return res.status(500).json({ message: '服务器错误' });
  }
}
```

### 更新反馈状态（管理员）

```javascript
// 更新反馈状态（管理员）
async function updateFeedbackStatus(req, res) {
  const { id } = req.params;
  const { status } = req.body;
  
  // 检查用户是否是管理员
  if (!req.user || req.user.role !== 'admin') {
    return res.status(403).json({ message: '权限不足' });
  }
  
  // 检查状态是否有效
  const validStatuses = ['pending', 'processing', 'completed', 'rejected'];
  if (!validStatuses.includes(status)) {
    return res.status(400).json({ message: '无效的状态' });
  }
  
  try {
    // 查找反馈
    const feedback = await Feedback.findByPk(id);
    
    if (!feedback) {
      return res.status(404).json({ message: '反馈不存在' });
    }
    
    // 更新状态
    feedback.status = status;
    await feedback.save();
    
    // 返回结果
    return res.json(feedback);
  } catch (error) {
    console.error('更新反馈状态失败:', error);
    return res.status(500).json({ message: '服务器错误' });
  }
}
```

## 前端实现

### 反馈表单组件

```vue
<!-- 反馈表单组件 -->
<template>
  <div class="feedback-form">
    <h2>提交反馈</h2>
    <div class="form-group">
      <label for="feedback-content">反馈内容</label>
      <textarea
        id="feedback-content"
        v-model="content"
        placeholder="请输入您的反馈内容..."
        rows="5"
      ></textarea>
    </div>
    <div class="form-group" v-if="!isLoggedIn">
      <label for="feedback-email">联系邮箱（可选）</label>
      <input
        id="feedback-email"
        type="email"
        v-model="email"
        placeholder="请输入您的联系邮箱..."
      />
    </div>
    <div class="form-actions">
      <button @click="submitFeedback" :disabled="isSubmitting">
        {{ isSubmitting ? '提交中...' : '提交反馈' }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useUserStore } from '@/stores/user';
import { ElMessage } from 'element-plus';
import apiService from '@/services/api';

// 状态
const content = ref('');
const email = ref('');
const isSubmitting = ref(false);

// 用户状态
const userStore = useUserStore();
const isLoggedIn = computed(() => userStore.isLoggedIn);

// 提交反馈
async function submitFeedback() {
  // 检查内容
  if (!content.value.trim()) {
    ElMessage.warning('请输入反馈内容');
    return;
  }
  
  // 检查邮箱格式
  if (email.value && !validateEmail(email.value)) {
    ElMessage.warning('请输入有效的邮箱地址');
    return;
  }
  
  try {
    isSubmitting.value = true;
    
    // 构建请求数据
    const data = {
      content: content.value.trim()
    };
    
    // 如果未登录且提供了邮箱，添加邮箱
    if (!isLoggedIn.value && email.value) {
      data.email = email.value.trim();
    }
    
    // 发送请求
    await apiService.post('/feedback', data);
    
    // 提示成功
    ElMessage.success('反馈提交成功，感谢您的反馈！');
    
    // 重置表单
    content.value = '';
    email.value = '';
  } catch (error) {
    console.error('提交反馈失败:', error);
    ElMessage.error('提交反馈失败，请稍后重试');
  } finally {
    isSubmitting.value = false;
  }
}

// 验证邮箱
function validateEmail(email) {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}
</script>
```

### 反馈列表组件（用户）

```vue
<!-- 反馈列表组件（用户） -->
<template>
  <div class="feedback-list">
    <h2>我的反馈</h2>
    <div v-if="isLoading" class="loading">
      加载中...
    </div>
    <div v-else-if="feedbackList.length === 0" class="empty">
      暂无反馈记录
    </div>
    <div v-else class="feedback-items">
      <div
        v-for="item in feedbackList"
        :key="item.id"
        class="feedback-item"
        :class="{ [`status-${item.status}`]: true }"
      >
        <div class="feedback-content">{{ item.content }}</div>
        <div class="feedback-meta">
          <div class="feedback-time">{{ formatTime(item.createdAt) }}</div>
          <div class="feedback-status">{{ getStatusText(item.status) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import apiService from '@/services/api';

// 状态
const feedbackList = ref([]);
const isLoading = ref(false);

// 获取反馈列表
async function getFeedbackList() {
  try {
    isLoading.value = true;
    
    // 发送请求
    const response = await apiService.get('/feedback');
    
    // 更新列表
    feedbackList.value = response;
  } catch (error) {
    console.error('获取反馈列表失败:', error);
    ElMessage.error('获取反馈列表失败，请稍后重试');
  } finally {
    isLoading.value = false;
  }
}

// 格式化时间
function formatTime(timestamp) {
  const date = new Date(timestamp);
  return date.toLocaleString();
}

// 获取状态文本
function getStatusText(status) {
  const statusMap = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    rejected: '已拒绝'
  };
  
  return statusMap[status] || status;
}

// 生命周期
onMounted(() => {
  getFeedbackList();
});
</script>
```

### 反馈管理组件（管理员）

```vue
<!-- 反馈管理组件（管理员） -->
<template>
  <div class="feedback-management">
    <h2>反馈管理</h2>
    <div class="filter-bar">
      <div class="filter-item">
        <label>状态:</label>
        <select v-model="statusFilter">
          <option value="">全部</option>
          <option value="pending">待处理</option>
          <option value="processing">处理中</option>
          <option value="completed">已完成</option>
          <option value="rejected">已拒绝</option>
        </select>
      </div>
      <button @click="refreshList">刷新</button>
    </div>
    <div v-if="isLoading" class="loading">
      加载中...
    </div>
    <div v-else-if="filteredList.length === 0" class="empty">
      暂无反馈记录
    </div>
    <div v-else class="feedback-items">
      <div
        v-for="item in filteredList"
        :key="item.id"
        class="feedback-item"
        :class="{ [`status-${item.status}`]: true }"
      >
        <div class="feedback-content">{{ item.content }}</div>
        <div class="feedback-meta">
          <div class="feedback-user">
            {{ item.User ? item.User.username : '匿名用户' }}
            {{ item.email ? `(${item.email})` : '' }}
          </div>
          <div class="feedback-time">{{ formatTime(item.createdAt) }}</div>
          <div class="feedback-status">
            <select
              v-model="item.status"
              @change="updateStatus(item.id, item.status)"
            >
              <option value="pending">待处理</option>
              <option value="processing">处理中</option>
              <option value="completed">已完成</option>
              <option value="rejected">已拒绝</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import apiService from '@/services/api';

// 状态
const feedbackList = ref([]);
const isLoading = ref(false);
const statusFilter = ref('');

// 过滤后的列表
const filteredList = computed(() => {
  if (!statusFilter.value) {
    return feedbackList.value;
  }
  
  return feedbackList.value.filter(item => item.status === statusFilter.value);
});

// 获取所有反馈
async function getAllFeedback() {
  try {
    isLoading.value = true;
    
    // 发送请求
    const response = await apiService.get('/feedback/admin');
    
    // 更新列表
    feedbackList.value = response;
  } catch (error) {
    console.error('获取反馈列表失败:', error);
    ElMessage.error('获取反馈列表失败，请稍后重试');
  } finally {
    isLoading.value = false;
  }
}

// 更新反馈状态
async function updateStatus(id, status) {
  try {
    // 发送请求
    await apiService.put(`/feedback/${id}`, { status });
    
    // 提示成功
    ElMessage.success('更新状态成功');
  } catch (error) {
    console.error('更新反馈状态失败:', error);
    ElMessage.error('更新反馈状态失败，请稍后重试');
    
    // 刷新列表
    refreshList();
  }
}

// 刷新列表
function refreshList() {
  getAllFeedback();
}

// 格式化时间
function formatTime(timestamp) {
  const date = new Date(timestamp);
  return date.toLocaleString();
}

// 生命周期
onMounted(() => {
  getAllFeedback();
});
</script>
```

## 路由配置

```javascript
// 反馈相关路由
const routes = [
  {
    path: '/feedback',
    component: FeedbackPage,
    children: [
      {
        path: '',
        component: FeedbackForm
      },
      {
        path: 'list',
        component: FeedbackList,
        meta: { requiresAuth: true }
      },
      {
        path: 'admin',
        component: FeedbackManagement,
        meta: { requiresAuth: true, requiresAdmin: true }
      }
    ]
  }
];
```

## API 路由

```javascript
// 反馈相关 API 路由
const router = express.Router();

// 创建反馈
router.post('/', feedbackController.createFeedback);

// 获取用户反馈列表
router.get('/', authMiddleware, feedbackController.getFeedbackList);

// 获取所有反馈（管理员）
router.get('/admin', authMiddleware, adminMiddleware, feedbackController.getAllFeedback);

// 更新反馈状态（管理员）
router.put('/:id', authMiddleware, adminMiddleware, feedbackController.updateFeedbackStatus);

module.exports = router;
```

## 使用方法

### 提交反馈

1. 访问反馈页面（`/feedback`）
2. 填写反馈内容
3. 如果未登录，可以选择填写联系邮箱
4. 点击"提交反馈"按钮

### 查看个人反馈

1. 登录系统
2. 访问个人反馈页面（`/feedback/list`）
3. 查看反馈列表和处理状态

### 管理反馈（管理员）

1. 以管理员身份登录系统
2. 访问反馈管理页面（`/feedback/admin`）
3. 查看所有用户的反馈
4. 更新反馈状态

## 最佳实践

1. **反馈内容**：
   - 提供清晰、具体的反馈内容
   - 描述问题的具体表现和复现步骤
   - 提供相关的截图或日志

2. **联系方式**：
   - 提供有效的联系方式，以便管理员联系您
   - 如果是已登录用户，系统会自动关联您的账号

3. **反馈管理**：
   - 及时处理用户反馈
   - 更新反馈状态，让用户了解处理进度
   - 对于重要的反馈，可以直接联系用户了解详情
