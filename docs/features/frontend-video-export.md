# 前端视频导出功能

本文档描述了Echo Lab中基于WebCodecs API的前端视频导出功能，该功能完全在浏览器端进行视频合成，无需上传大量数据到服务器。

## 功能概述

前端视频导出功能使用WebCodecs API在浏览器端直接进行视频合成，具有以下优势：

- **节省流量**：无需上传帧图片到服务器，大幅减少数据传输
- **处理速度快**：利用浏览器原生编码能力，性能优于WebAssembly方案
- **完全本地处理**：所有视频合成在本地完成，保护用户隐私
- **实时进度反馈**：提供详细的处理进度信息

## 浏览器兼容性

### 支持的浏览器
- **Chrome 94+** ✅ 完全支持
- **Edge 94+** ✅ 完全支持

### 不支持的浏览器
- **Firefox** ❌ 不支持WebCodecs API
- **Safari** ❌ 不支持WebCodecs API

### 检测方法

在浏览器控制台中运行以下命令检测支持情况：

```javascript
// 开发环境下可用
webCodecsTest.runFullCompatibilityTest().then(result => {
  console.log('兼容性测试结果:', result);
  console.log('报告:', webCodecsTest.generateCompatibilityReport(result));
});

// 或者简单检测
console.log('WebCodecs支持:', webCodecsTest.checkWebCodecsSupport());
```

## 技术架构

### 核心组件

1. **WebCodecsVideoExporter** - WebCodecs视频导出器
2. **FrontendVideoExporter** - 前端导出统一接口
3. **VideoExportDialog** - 导出对话框（已集成前端导出）

### 依赖库

- `mp4-muxer` - MP4容器格式封装
- `jszip` - 字幕文件打包（如果需要）

### 处理流程

```
1. 生成视频帧 (30%)
   ├── 渲染ContentDisplay组件
   ├── 转换为Canvas
   └── 创建VideoFrame对象

2. 初始化编码器 (5%)
   ├── 配置VideoEncoder
   ├── 配置AudioEncoder
   └── 创建MP4Muxer

3. 编码视频 (40%)
   ├── 逐帧编码视频
   └── 实时进度反馈

4. 编码音频 (15%)
   ├── 处理AudioBuffer
   └── 分块编码音频

5. 生成字幕 (5%)
   ├── 从timeline提取文本
   └── 生成SRT格式

6. 完成导出 (5%)
   ├── 封装MP4文件
   └── 自动下载
```

## 使用方法

### 自动检测和切换

系统会自动检测浏览器对WebCodecs的支持情况：

- **支持WebCodecs**：自动使用前端导出，显示绿色提示
- **不支持WebCodecs**：显示黄色警告，提示升级浏览器

### 导出选项

前端导出支持所有现有的导出选项：

- **视频质量**：低质量(480p)、中等质量(720p)、高质量(1080p)
- **字幕生成**：支持多语言字幕生成
- **文件命名**：自定义输出文件名

### 进度监控

前端导出提供详细的进度信息：

- 生成帧：显示帧渲染进度
- 编码视频：显示视频编码进度  
- 编码音频：显示音频编码进度
- 生成字幕：显示字幕处理进度
- 完成导出：显示最终封装进度

## 配置参数

### 视频编码配置

```javascript
const videoConfig = {
  codec: 'avc1.42E01E',        // H.264 Baseline
  width: 1280,                 // 视频宽度
  height: 720,                 // 视频高度
  bitrate: 2500000,           // 码率 (2.5Mbps)
  framerate: 30,              // 帧率
  hardwareAcceleration: 'prefer-hardware'
};
```

### 音频编码配置

```javascript
const audioConfig = {
  codec: 'mp4a.40.2',         // AAC-LC
  sampleRate: 48000,          // 采样率
  numberOfChannels: 1,        // 声道数
  bitrate: 128000             // 码率 (128kbps)
};
```

### 质量预设

```javascript
const qualityPresets = {
  low: { 
    width: 854, 
    height: 480, 
    bitrate: 1000000    // 1Mbps
  },
  medium: { 
    width: 1280, 
    height: 720, 
    bitrate: 2500000    // 2.5Mbps
  },
  high: { 
    width: 1920, 
    height: 1080, 
    bitrate: 5000000    // 5Mbps
  }
};
```

## 错误处理

### 常见错误

1. **浏览器不支持**
   ```
   错误：当前浏览器不支持WebCodecs API，请使用Chrome 94+
   解决：升级到支持的浏览器版本
   ```

2. **MP4Muxer加载失败**
   ```
   错误：MP4Muxer库加载失败，请确保已安装 mp4-muxer 依赖
   解决：检查依赖安装情况
   ```

3. **编码配置不支持**
   ```
   错误：视频编码配置不被支持
   解决：检查硬件加速设置，尝试软件编码
   ```

### 调试方法

1. **开启详细日志**
   ```javascript
   // 在控制台中设置
   localStorage.setItem('debug-video-export', 'true');
   ```

2. **检查编码器支持**
   ```javascript
   // 测试视频编码器
   VideoEncoder.isConfigSupported({
     codec: 'avc1.42E01E',
     width: 1280,
     height: 720,
     bitrate: 2500000,
     framerate: 30
   }).then(result => console.log('视频编码器支持:', result));
   ```

## 性能优化

### 内存管理

- 及时释放VideoFrame对象
- 避免同时创建过多Canvas元素
- 使用对象池减少GC压力

### 编码优化

- 优先使用硬件加速
- 合理设置码率和质量参数
- 分批处理长视频内容

## 与后端方案对比

| 特性 | 前端导出 | 后端导出 |
|------|----------|----------|
| 流量消耗 | 极低 | 高 |
| 处理速度 | 快 | 中等 |
| 浏览器要求 | Chrome 94+ | 任意 |
| 服务器负载 | 无 | 高 |
| 隐私保护 | 完全本地 | 需上传数据 |
| 功能完整性 | 完整 | 完整 |

## 未来规划

1. **扩展浏览器支持**：等待Firefox和Safari支持WebCodecs
2. **性能优化**：使用Web Workers进行并行处理
3. **格式扩展**：支持更多视频格式和编码选项
4. **质量提升**：添加更多视频质量预设和自定义选项
