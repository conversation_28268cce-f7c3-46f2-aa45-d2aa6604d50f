# Echo Lab 播放策略模板系统设计文档

## 1. 背景与问题

### 1.1 当前问题
- **配置覆盖问题**：用户自定义播放设置后，服务器配置更新无法生效
- **重置逻辑缺陷**：重置功能不彻底，localStorage残留导致问题持续
- **配置管理混乱**：缺乏清晰的配置层级和优先级规则
- **用户体验割裂**：需要为每个内容重复配置相似的播放策略

### 1.2 核心洞察
- **配置的本质**：播放策略模板，而非特定内容的个人设置
- **模板特性**：可复用的完整播放策略，包含多环节配置
- **策略完整性**：模板定义完整的学习策略，包括环节数量

## 2. 系统设计原则

### 2.1 核心原则
1. **模板决定环节数量**：模板几个环节就是几个环节，不做适配
2. **策略完整性**：模板定义完整的多环节播放策略
3. **配置分离**：内容结构与播放策略完全分离
4. **用户可编辑性**：只有用户可编辑的字段才保存到模板

### 2.2 设计理念
- 配置模板 = 完整的多环节播放策略
- 不是单一配置应用到所有环节
- 而是一套完整的环节组合策略

## 3. 配置字段分类

### 3.1 用户可编辑字段（需要保存到模板）

#### 播放设置
```javascript
{
  "speed": 1,                    // 语速 (0.9-1.1)
  "pauseDuration": 3000,         // 停顿时长 (0-5000ms)
}
```

#### 重复设置
```javascript
{
  "repeatCount": 1,              // 重复次数 (1-10)
  "repeatSpeeds": [1],           // 每次重复的播放速度数组
  "repeatPauses": [3000]         // 每次重复的停顿时长数组
}
```

#### 翻译设置
```javascript
{
  "enableTranslation": false,    // 是否启用翻译
  "translationLanguage": "",     // 翻译语言
  "translationPosition": 1,      // 插入位置 (1-重复次数)
}
```

#### 关键词设置
```javascript
{
  "enableKeywords": true,        // 是否启用关键词
  "keywordPosition": 1,          // 插入位置 (1-重复次数)
  "keywordRepeatCount": 2,       // 重复次数 (1-5)

}
```

### 3.2 服务器控制字段（不保存到模板）
```javascript
{
  "id": "section_xxx",           // 系统生成的ID
  "title": "标题",               // 环节标题（内容固定）
  "description": "描述",         // 环节描述（内容固定）
  "processingMode": "sequence",  // 处理模式（内容固定）
  "userEditable": true,          // 权限控制（系统控制）
}
```

## 4. 模板数据结构

### 4.1 模板配置结构
```javascript
{
  "id": "template_xxx",
  "name": "渐进式学习",
  "description": "先通读理解，再重复练习，最后巩固",
  "type": "user",               // system | user
  "isPublic": false,
  "config": {
    "sections": [
      {
        // 第1环节：通读理解
        "speed": 1.0,
        "pauseDuration": 3000,
        "repeatCount": 1,
        "enableTranslation": true,
        "translationLanguage": "zh-CN",
        "translationPosition": 1,
        "enableKeywords": false,
        "keywordPosition": 1,
        "keywordRepeatCount": 2,

        "repeatSpeeds": [1.0],
        "repeatPauses": [3000]
      },
      {
        // 第2环节：重复练习
        "speed": 0.9,
        "pauseDuration": 4000,
        "repeatCount": 5,
        "enableTranslation": false,
        "translationLanguage": "",
        "translationPosition": 2,
        "enableKeywords": true,
        "keywordPosition": 3,
        "keywordRepeatCount": 2,
        "keywordSpeed": 1,
        "repeatSpeeds": [0.9, 1.0, 1.0, 1.0, 1.1],
        "repeatPauses": [4000, 3000, 3000, 3000, 2000]
      },
      {
        // 第3环节：最终巩固
        "speed": 1.0,
        "pauseDuration": 2000,
        "repeatCount": 1,
        "enableTranslation": false,
        "translationLanguage": "",
        "translationPosition": 1,
        "enableKeywords": false,
        "keywordPosition": 1,
        "keywordRepeatCount": 2,
        "keywordSpeed": 1,
        "repeatSpeeds": [1.0],
        "repeatPauses": [2000]
      }
    ]
  },
  "usageCount": 0,
  "createdAt": "2024-01-01",
  "updatedAt": "2024-01-01"
}
```

### 4.2 预设模板示例

#### 初学者模式
```javascript
{
  "name": "初学者模式",
  "description": "慢速多重复，带翻译辅助",
  "config": {
    "sections": [
      {
        "speed": 0.9,
        "pauseDuration": 4000,
        "repeatCount": 4,
        "enableTranslation": true,
        "translationLanguage": "zh-CN",
        "translationPosition": 2,
        "enableKeywords": true,
        "keywordPosition": 3,
        "keywordRepeatCount": 2,
        "keywordSpeed": 0.9,
        "repeatSpeeds": [0.9, 0.9, 1.0, 1.0],
        "repeatPauses": [4000, 4000, 3000, 3000]
      }
    ]
  }
}
```

#### 强化练习模式
```javascript
{
  "name": "强化练习",
  "description": "先强化练习，再通读验证",
  "config": {
    "sections": [
      {
        // 强化练习环节
        "speed": 0.8,
        "pauseDuration": 5000,
        "repeatCount": 6,
        "enableTranslation": true,
        "translationLanguage": "zh-CN",
        "translationPosition": 2,
        "enableKeywords": true,
        "keywordPosition": 4,
        "keywordRepeatCount": 3,
        "keywordSpeed": 0.8,
        "repeatSpeeds": [0.8, 0.8, 0.9, 0.9, 1.0, 1.0],
        "repeatPauses": [5000, 4000, 4000, 3000, 3000, 2000]
      },
      {
        // 通读验证环节
        "speed": 1.1,
        "pauseDuration": 2000,
        "repeatCount": 1,
        "enableTranslation": false,
        "translationLanguage": "",
        "translationPosition": 1,
        "enableKeywords": false,
        "keywordPosition": 1,
        "keywordRepeatCount": 2,
        "keywordSpeed": 1,
        "repeatSpeeds": [1.1],
        "repeatPauses": [2000]
      }
    ]
  }
}
```

## 5. 模板应用逻辑

### 5.1 核心应用规则
**模板几个环节就是几个环节，严格按照模板执行**

### 5.2 应用算法
```javascript
function applyTemplateToContent(serverConfig, template) {
  const maxSections = Math.min(
    serverConfig.sections.length,
    template.config.sections.length
  );

  return {
    sections: serverConfig.sections
      .slice(0, maxSections)  // 只取前N个环节
      .map((serverSection, index) => ({
        // 服务器控制的字段
        id: serverSection.id,
        title: serverSection.title,
        description: serverSection.description,
        processingMode: serverSection.processingMode,
        userEditable: serverSection.userEditable,

        // 模板配置字段
        ...template.config.sections[index]
      }))
  };
}
```

### 5.3 环节数量不匹配处理

#### 情况1：模板环节少于内容环节
- **结果**：只播放模板定义的环节数量
- **示例**：内容3个环节，模板2个环节 → 最终播放2个环节

#### 情况2：模板环节多于内容环节
- **结果**：只应用内容支持的环节数量
- **示例**：内容2个环节，模板3个环节 → 最终播放2个环节

#### 用户提示
```
应用模板时显示：
"当前内容有3个环节，选择的模板有2个环节
应用后将只播放前2个环节

○ 继续应用模板（只播放2个环节）
○ 取消，使用服务器默认配置（播放3个环节）"
```

## 6. 数据库设计

### 6.1 模板表结构
```sql
CREATE TABLE playback_templates (
  id VARCHAR(21) PRIMARY KEY,           -- nanoid
  name VARCHAR(100) NOT NULL,           -- 模板名称
  description TEXT,                     -- 模板描述
  config JSON NOT NULL,                 -- 模板配置（JSON格式）
  type ENUM('system', 'user') NOT NULL, -- 模板类型
  user_id VARCHAR(50),                  -- 创建者ID（系统模板为NULL）
  is_public BOOLEAN DEFAULT FALSE,      -- 是否公开
  usage_count INT DEFAULT 0,            -- 使用次数
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_user_id (user_id),
  INDEX idx_type (type),
  INDEX idx_public (is_public),
  INDEX idx_usage (usage_count)
);
```

### 6.2 存储策略
```javascript
// 系统预设模板 - 代码中定义
const SYSTEM_TEMPLATES = {
  beginner: { name: "初学者模式", config: {...} },
  intermediate: { name: "进阶模式", config: {...} },
  advanced: { name: "高级模式", config: {...} }
};

// 用户模板 - 数据库存储
// 公开模板 - 数据库存储
```

## 7. 权限管理

### 7.1 权限分级

#### 未登录用户
```javascript
const GUEST_PERMISSIONS = {
  view: ['system'],           // 只能查看系统模板
  use: ['system'],            // 只能使用系统模板
  create: false,              // 不能创建模板
  edit: false,                // 不能编辑模板
  delete: false               // 不能删除模板
};
```

#### 登录用户
```javascript
const USER_PERMISSIONS = {
  view: ['system', 'own', 'public'],    // 查看系统、自己的、公开的模板
  use: ['system', 'own', 'public'],     // 使用系统、自己的、公开的模板
  create: true,                         // 可以创建模板
  edit: ['own'],                        // 只能编辑自己的模板
  delete: ['own']                       // 只能删除自己的模板
};
```

#### 管理员
```javascript
const ADMIN_PERMISSIONS = {
  view: ['all'],
  use: ['all'],
  create: true,
  edit: ['all'],
  delete: ['all']
};
```

### 7.2 本地存储策略
```javascript
// 未登录用户的临时模板
localStorage: {
  "playback_templates_guest": [
    {
      id: "temp_1",
      name: "我的临时配置",
      config: {...},
      isTemporary: true
    }
  ]
}

// 登录后提示用户保存临时模板
```

## 8. API接口设计

### 8.1 模板CRUD接口
```javascript
// 获取模板列表
GET /api/templates
Query: { type?, userId?, isPublic?, language?, page?, pageSize? }
Response: {
  success: true,
  data: {
    system: [...],
    user: [...],
    public: [...]
  }
}

// 创建模板
POST /api/templates
Body: { name, description, config, isPublic, language }
Response: { success: true, data: template }

// 更新模板
PUT /api/templates/:id
Body: { name?, description?, config?, isPublic? }
Response: { success: true, data: template }

// 删除模板
DELETE /api/templates/:id
Response: { success: true }

// 使用模板（增加使用次数）
POST /api/templates/:id/use
Response: { success: true }

// 复制模板
POST /api/templates/:id/duplicate
Body: { name }
Response: { success: true, data: newTemplate }
```

### 8.2 权限验证
```javascript
// 中间件验证用户权限
const checkTemplatePermission = (action) => {
  return (req, res, next) => {
    const { user } = req;
    const { templateId } = req.params;

    // 验证用户是否有权限执行该操作
    if (!hasPermission(user, action, templateId)) {
      return res.status(403).json({
        success: false,
        error: '权限不足'
      });
    }

    next();
  };
};
```

## 9. 前端状态管理

### 9.1 Pinia Store设计
```javascript
// templateStore.js
export const useTemplateStore = defineStore('template', {
  state: () => ({
    systemTemplates: [],
    userTemplates: [],
    publicTemplates: [],
    currentTemplate: null,
    loading: false,
    error: null
  }),

  getters: {
    allTemplates: (state) => [
      ...state.systemTemplates,
      ...state.userTemplates,
      ...state.publicTemplates
    ],

    templateById: (state) => (id) => {
      return state.allTemplates.find(t => t.id === id);
    }
  },

  actions: {
    async loadTemplates() {
      this.loading = true;
      try {
        const response = await templateAPI.getTemplates();
        this.systemTemplates = response.data.system || [];
        this.userTemplates = response.data.user || [];
        this.publicTemplates = response.data.public || [];
      } catch (error) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    async createTemplate(template) {
      const response = await templateAPI.createTemplate(template);
      this.userTemplates.push(response.data);
      return response.data;
    },

    async updateTemplate(id, template) {
      const response = await templateAPI.updateTemplate(id, template);
      const index = this.userTemplates.findIndex(t => t.id === id);
      if (index !== -1) {
        this.userTemplates[index] = response.data;
      }
      return response.data;
    },

    async deleteTemplate(id) {
      await templateAPI.deleteTemplate(id);
      this.userTemplates = this.userTemplates.filter(t => t.id !== id);
    },

    selectTemplate(template) {
      this.currentTemplate = template;
    }
  }
});
```

## 10. 用户交互设计

### 10.1 模板选择界面
```vue
<template>
  <div class="template-selector">
    <!-- 快速选择 -->
    <div class="quick-templates">
      <h4>快速选择</h4>
      <div class="template-grid">
        <div
          v-for="template in systemTemplates"
          :key="template.id"
          class="template-card"
          :class="{ active: selectedTemplate?.id === template.id }"
          @click="selectTemplate(template)"
        >
          <div class="template-name">{{ template.name }}</div>
          <div class="template-desc">{{ template.description }}</div>
          <div class="template-stats">
            {{ template.config.sections.length }}个环节
          </div>
        </div>
      </div>
    </div>

    <!-- 我的模板 -->
    <div class="my-templates" v-if="isLoggedIn">
      <div class="section-header">
        <h4>我的模板</h4>
        <el-button size="small" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon> 新建模板
        </el-button>
      </div>

      <div class="template-list">
        <div
          v-for="template in userTemplates"
          :key="template.id"
          class="template-item"
        >
          <div class="template-info" @click="selectTemplate(template)">
            <div class="template-name">{{ template.name }}</div>
            <div class="template-meta">
              {{ template.config.sections.length }}个环节 ·
              使用{{ template.usage_count }}次
            </div>
          </div>

          <div class="template-actions">
            <el-button size="small" @click="editTemplate(template)">编辑</el-button>
            <el-button size="small" @click="duplicateTemplate(template)">复制</el-button>
            <el-button size="small" type="danger" @click="deleteTemplate(template)">删除</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 公开模板 -->
    <div class="public-templates">
      <h4>社区模板</h4>
      <div class="template-list">
        <!-- 公开模板列表 -->
      </div>
    </div>
  </div>
</template>
```

### 10.2 模板编辑界面
```vue
<template>
  <el-dialog v-model="dialogVisible" title="编辑模板" width="60%">
    <el-form :model="templateForm" label-width="100px">
      <el-form-item label="模板名称" required>
        <el-input v-model="templateForm.name" placeholder="请输入模板名称" />
      </el-form-item>

      <el-form-item label="模板描述">
        <el-input
          v-model="templateForm.description"
          type="textarea"
          placeholder="请描述这个模板的特点和适用场景"
        />
      </el-form-item>

      <el-form-item label="是否公开" v-if="isLoggedIn">
        <el-switch v-model="templateForm.isPublic" />
        <div class="form-help">公开后其他用户可以使用这个模板</div>
      </el-form-item>

      <!-- 环节配置 -->
      <el-form-item label="环节配置">
        <div class="sections-editor">
          <div
            v-for="(section, index) in templateForm.config.sections"
            :key="index"
            class="section-editor"
          >
            <div class="section-header">
              <h5>环节 {{ index + 1 }}</h5>
              <el-button
                size="small"
                type="danger"
                @click="removeSection(index)"
                :disabled="templateForm.config.sections.length <= 1"
              >
                删除
              </el-button>
            </div>

            <!-- 使用现有的环节配置组件 -->
            <SectionConfigEditor v-model="section" />
          </div>

          <el-button @click="addSection" class="add-section-btn">
            <el-icon><Plus /></el-icon> 添加环节
          </el-button>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveTemplate">保存模板</el-button>
    </template>
  </el-dialog>
</template>
```

### 10.3 播放页模板应用
```vue
<template>
  <div class="playback-template-selector">
    <div class="current-strategy">
      <div class="strategy-info">
        <span v-if="currentTemplate">
          当前策略：{{ currentTemplate.name }}
        </span>
        <span v-else>
          使用服务器默认配置
        </span>
      </div>

      <el-button @click="showTemplateSelector = true">
        选择策略
      </el-button>
    </div>

    <!-- 模板选择弹窗 -->
    <el-dialog v-model="showTemplateSelector" title="选择播放策略" width="50%">
      <TemplateSelector
        @select="applyTemplate"
        @close="showTemplateSelector = false"
      />
    </el-dialog>
  </div>
</template>
```

## 11. 完整用户流程

### 11.1 未登录用户流程
1. **查看系统模板**：只能查看和使用系统预设模板
2. **临时修改**：可以基于系统模板进行临时修改
3. **本地保存**：临时修改保存在localStorage
4. **登录提示**：提示登录后可保存为个人模板

### 11.2 登录用户流程
1. **查看所有模板**：系统模板、个人模板、公开模板
2. **创建模板**：
   - 从默认配置开始创建
   - 基于现有模板复制创建
   - 从当前播放配置保存为模板
3. **编辑模板**：修改环节配置、名称、描述
4. **管理模板**：删除、复制、设置公开状态
5. **应用模板**：选择模板应用到当前内容

### 11.3 模板应用流程
1. **选择模板**：从模板列表中选择
2. **环节匹配检查**：检查模板环节数与内容环节数
3. **用户确认**：如果环节数不匹配，提示用户确认
4. **应用配置**：将模板配置应用到播放设置
5. **重新生成**：重新生成时间线和音频

## 12. 实施优先级

### 12.1 第一阶段（核心功能）
1. **数据结构设计**：确定模板数据结构
2. **数据库设计**：创建模板表和相关索引
3. **基础API**：模板CRUD接口
4. **前端Store**：模板状态管理
5. **模板应用逻辑**：核心应用算法

### 12.2 第二阶段（用户界面）
1. **模板选择器**：播放页模板选择界面
2. **模板编辑器**：创建和编辑模板界面
3. **模板管理**：我的模板列表和管理
4. **权限控制**：登录状态和权限验证

### 12.3 第三阶段（增强功能）
1. **公开模板**：社区模板分享功能
2. **模板统计**：使用次数和热门模板
3. **模板导入导出**：模板文件导入导出
4. **模板推荐**：基于用户行为的模板推荐

### 12.4 第四阶段（优化完善）
1. **性能优化**：模板缓存和加载优化
2. **用户体验**：界面优化和交互改进
3. **数据分析**：模板使用情况分析
4. **功能扩展**：高级模板功能

## 13. 技术要点

### 13.1 关键技术决策
1. **模板存储**：JSON格式存储配置，便于扩展
2. **权限控制**：基于用户角色的权限管理
3. **数据同步**：前端状态与后端数据的同步策略
4. **缓存策略**：模板数据的缓存和更新机制

### 13.2 注意事项
1. **数据验证**：模板配置的完整性和有效性验证
2. **版本兼容**：模板格式的向后兼容性
3. **性能考虑**：大量模板的加载和渲染性能
4. **用户体验**：模板选择和应用的流畅性

## 14. 总结

播放策略模板系统通过以下核心设计解决了当前的配置管理问题：

1. **配置分离**：将播放策略与内容结构完全分离
2. **模板化管理**：提供可复用的播放策略模板
3. **权限分级**：支持系统、用户、公开三级模板
4. **严格应用**：模板定义几个环节就播放几个环节
5. **用户友好**：简化配置选择，提升用户体验

这个系统不仅解决了当前的技术问题，还为未来的功能扩展提供了坚实的基础。
