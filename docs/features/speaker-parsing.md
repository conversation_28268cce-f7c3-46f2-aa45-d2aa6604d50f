# 说话者解析功能

本文档详细说明了 Echo Lab 中说话者解析功能的实现和使用方法。

## 概述

说话者解析功能用于识别和处理对话文本中的说话者信息，将对话文本分割为不同说话者的语句。这对于处理对话类型的内容（如对话练习、角色扮演等）非常有用。

## 功能特点

- 自动识别对话文本中的说话者
- 支持多种说话者标记格式
- 将对话文本分割为不同说话者的语句
- 支持自定义说话者名称和颜色

## 实现方式

Echo Lab 使用正则表达式和文本解析算法识别对话文本中的说话者信息。

### 说话者标记格式

Echo Lab 支持以下说话者标记格式：

1. **冒号格式**：`A: 你好，B。`
2. **括号格式**：`（A）你好，B。`
3. **引号格式**：`A「你好，B。」`
4. **日语格式**：`A：「你好，B。」`

### 解析算法

```javascript
// 解析对话文本中的说话者
function parseSpeakers(text) {
  // 分割文本为行
  const lines = text.split('\n');
  
  // 初始化结果
  const segments = [];
  
  // 处理每一行
  lines.forEach(line => {
    // 跳过空行
    if (!line.trim()) {
      return;
    }
    
    // 尝试匹配不同的说话者标记格式
    
    // 冒号格式：A: 你好，B。
    const colonMatch = line.match(/^([^:]+):\s*(.+)$/);
    if (colonMatch) {
      segments.push({
        id: `seg_${nanoid()}`,
        speaker: colonMatch[1].trim(),
        content: colonMatch[2].trim(),
        language: 'auto'
      });
      return;
    }
    
    // 括号格式：（A）你好，B。
    const bracketMatch = line.match(/^（([^）]+)）\s*(.+)$/);
    if (bracketMatch) {
      segments.push({
        id: `seg_${nanoid()}`,
        speaker: bracketMatch[1].trim(),
        content: bracketMatch[2].trim(),
        language: 'auto'
      });
      return;
    }
    
    // 引号格式：A「你好，B。」
    const quoteMatch = line.match(/^([^「]+)「([^」]+)」$/);
    if (quoteMatch) {
      segments.push({
        id: `seg_${nanoid()}`,
        speaker: quoteMatch[1].trim(),
        content: quoteMatch[2].trim(),
        language: 'auto'
      });
      return;
    }
    
    // 日语格式：A：「你好，B。」
    const japaneseMatch = line.match(/^([^：]+)：「([^」]+)」$/);
    if (japaneseMatch) {
      segments.push({
        id: `seg_${nanoid()}`,
        speaker: japaneseMatch[1].trim(),
        content: japaneseMatch[2].trim(),
        language: 'auto'
      });
      return;
    }
    
    // 如果没有匹配到说话者，将整行作为内容
    segments.push({
      id: `seg_${nanoid()}`,
      speaker: null,
      content: line.trim(),
      language: 'auto'
    });
  });
  
  return segments;
}
```

### 文本内容节点集成

文本内容节点集成了说话者解析功能，可以自动识别和处理对话文本中的说话者信息：

```javascript
// 文本内容节点中的说话者解析
function parseTextWithSpeakers(text) {
  // 解析说话者
  const segments = parseSpeakers(text);
  
  // 检测语言
  segments.forEach(async segment => {
    segment.language = await detectLanguage(segment.content);
  });
  
  // 更新分句结果
  this.params.segments = segments;
  this.params.segmentsCustomized = true;
}
```

## 前端实现

### 文本内容节点 UI

文本内容节点 UI 提供了说话者解析的配置和预览功能：

```vue
<!-- 文本内容节点 UI 中的说话者解析 -->
<template>
  <div class="text-content-node">
    <div class="text-editor">
      <textarea
        v-model="text"
        @input="updateText"
        placeholder="输入文本内容..."
      ></textarea>
    </div>
    <div class="segment-settings">
      <div class="mode-selector">
        <label>分句模式:</label>
        <select v-model="mode" @change="updateMode">
          <option value="paragraph">段落</option>
          <option value="sentence">句子</option>
          <option value="speaker">说话者</option>
          <option value="none">不分句</option>
        </select>
      </div>
    </div>
    <div class="segments-list">
      <div
        v-for="segment in segments"
        :key="segment.id"
        class="segment-item"
        :class="{ 'has-speaker': segment.speaker }"
      >
        <div v-if="segment.speaker" class="segment-speaker">
          {{ segment.speaker }}
        </div>
        <div class="segment-content">{{ segment.content }}</div>
        <div class="segment-language">{{ segment.language }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 更新文本
function updateText() {
  // 更新文本
  this.params.text = text;
  
  // 如果是说话者模式，解析说话者
  if (this.params.mode === 'speaker') {
    this.parseTextWithSpeakers(text);
  } else if (!this.params.segmentsCustomized) {
    // 否则，如果没有自定义分句，重新分句
    this.resegment();
  }
}

// 更新分句模式
function updateMode() {
  // 更新分句模式
  this.params.mode = mode;
  
  // 如果是说话者模式，解析说话者
  if (mode === 'speaker') {
    this.parseTextWithSpeakers(this.params.text);
  } else {
    // 否则，重新分句
    this.resegment();
  }
}
</script>
```

### 文本序列节点集成

文本序列节点集成了说话者信息，可以在序列中保存和使用说话者信息：

```javascript
// 文本序列节点中的说话者信息
function updateSequence() {
  // 获取源节点
  const sourceNodes = this.getSourceNodes();
  
  // 如果没有源节点，清空序列
  if (sourceNodes.length === 0) {
    this.params.sequence = [];
    return;
  }
  
  // 合并所有源节点的分句
  const allSegments = [];
  
  sourceNodes.forEach(node => {
    if (node.type === 'textContent' && node.params.segments) {
      node.params.segments.forEach(segment => {
        allSegments.push({
          id: `seq_${nanoid()}`,
          sourceId: segment.id,
          content: segment.content,
          language: segment.language || 'auto',
          type: 'text',
          speaker: segment.speaker || null
        });
      });
    }
  });
  
  // 更新序列
  this.params.sequence = allSegments;
}
```

### 播放器组件集成

播放器组件集成了说话者信息的显示功能：

```vue
<!-- 播放器组件中的说话者显示 -->
<template>
  <div class="content-display">
    <div v-if="currentItem && currentItem.speaker" class="speaker-name" :style="getSpeakerStyle(currentItem.speaker)">
      {{ currentItem.speaker }}
    </div>
    <div class="content-text" v-if="currentItem">
      {{ currentItem.content }}
    </div>
  </div>
</template>

<script setup>
// 获取说话者样式
function getSpeakerStyle(speaker) {
  // 根据说话者名称生成颜色
  const color = getSpeakerColor(speaker);
  
  return {
    color: color
  };
}

// 根据说话者名称生成颜色
function getSpeakerColor(speaker) {
  // 使用简单的哈希算法生成颜色
  const hash = speaker.split('').reduce((acc, char) => {
    return char.charCodeAt(0) + ((acc << 5) - acc);
  }, 0);
  
  const hue = Math.abs(hash) % 360;
  
  return `hsl(${hue}, 70%, 50%)`;
}
</script>
```

## 使用方法

### 在文本内容节点中使用说话者解析

1. 在编辑器中创建或选择文本内容节点
2. 在分句模式中选择"说话者"
3. 输入带有说话者标记的对话文本，如：
   ```
   A: 你好，B。
   B: 你好，A。很高兴认识你。
   A: 我也很高兴认识你。
   ```
4. 系统会自动识别说话者并分割文本

### 自定义说话者样式

1. 在播放器设置中可以自定义说话者的显示样式
2. 可以为不同的说话者设置不同的颜色
3. 可以调整说话者名称的字体大小和位置

## 最佳实践

1. **一致的标记格式**：
   - 在同一个文本中使用一致的说话者标记格式
   - 避免混合使用不同的标记格式

2. **清晰的说话者名称**：
   - 使用简短、清晰的说话者名称
   - 避免使用特殊字符或过长的名称

3. **检查解析结果**：
   - 检查自动解析的结果是否正确
   - 必要时手动修正分句结果

## 常见问题

### 说话者识别不正确

问题：系统无法正确识别说话者。

解决方案：
- 检查说话者标记格式是否正确
- 尝试使用其他标记格式
- 手动修正分句结果

### 分句不正确

问题：系统将一个说话者的多行文本分割为多个分句。

解决方案：
- 确保每个说话者的文本在同一行
- 使用适当的标点符号分隔句子
- 手动修正分句结果

### 说话者名称显示问题

问题：说话者名称显示不正确或样式有问题。

解决方案：
- 检查说话者名称是否包含特殊字符
- 调整说话者名称的显示样式
- 确保说话者名称不会与内容重叠
