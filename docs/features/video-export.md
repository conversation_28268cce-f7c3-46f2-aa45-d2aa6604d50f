# 视频导出功能设计

本文档描述了Echo Lab中视频导出功能的设计思路和实现流程。该功能允许用户将播放内容和音频导出为视频文件，同时生成对应的字幕文件。

## 1. 功能概述

视频导出功能允许用户：
- 将Echo Lab的播放内容（文本、图片）和音频合成为视频文件
- 生成与视频内容同步的字幕文件（SRT格式）
- 支持多语言字幕和双语字幕

## 2. 设计思路

### 2.1 基于时间线直接生成

不同于传统的"播放并录制"方法，我们采用基于时间线直接生成帧图片的方式：

1. 分析时间线数据，确定每个内容项的持续时间和内容
2. 为每个内容项生成对应的静态帧图片
3. 将帧图片序列与音频文件合成为视频
4. 同时从时间线数据生成字幕文件

这种方法的优势：
- 效率更高：不需要实时播放和捕获
- 质量可控：可以精确控制每一帧的渲染质量
- 资源占用低：不需要实时编码，减少CPU和内存压力
- 更灵活：可以轻松调整分辨率、帧率等参数

### 2.2 利用现有组件渲染

核心思路是利用现有的ContentDisplay组件渲染内容，然后将渲染结果转换为Canvas图像：

1. 为每个时间线项目设置ContentDisplay组件的props
2. 渲染组件
3. 将渲染结果转换为Canvas图像
4. 保存为帧图片

这种方法的优势：
- 代码复用：充分利用已有的渲染逻辑
- 一致性保证：生成的帧图片与用户在播放器中看到的内容完全一致
- 维护简便：如果ContentDisplay组件更新，帧生成也会自动反映这些更改

## 3. 实现流程

### 3.1 用户交互流程

1. 用户点击"导出视频"按钮
2. 显示导出选项面板，用户可以选择：
   - 视频质量/分辨率
   - 字幕语言选项
   - 是否生成双语字幕
3. 用户确认后，开始生成过程
4. 显示进度指示器
5. 完成后提供视频和字幕文件下载

### 3.2 技术实现流程

#### 帧生成阶段

```javascript
// 概念性代码，展示统一处理思路
async function generateFramesFromTimeline(timeline) {
  const frames = [];

  for (const item of timeline) {
    // 设置ContentDisplay的props
    const props = {
      content: item.content,
      annotation: item.annotation,
      language: item.language,
      textStyle: getTextStyleForItem(item)
    };

    // 如果是封面，设置相应的props
    if (item.isCover) {
      props.content = '';
      props.coverImageUrl = item.coverImageUrl;
      props.showCover = true;
    }

    // 渲染ContentDisplay并转换为Canvas
    const canvas = await renderComponentToCanvas(ContentDisplay, props);

    // 保存帧
    frames.push({
      canvas,
      duration: item.duration,
      startTime: item.startTime
    });
  }

  return frames;
}
```

#### 视频合成阶段

1. 使用生成的帧图片序列
2. 结合已有的音频文件
3. 使用WebAssembly版的FFmpeg或类似工具在浏览器中合成
4. 或者使用简单的帧序列+音频方式生成视频

#### 字幕生成阶段

1. 从时间线数据中提取文本内容和时间信息
2. 根据用户选择的语言，获取相应的文本（原文或翻译）
3. 如选择双语字幕，则组合两种语言的文本
4. 转换为SRT格式的字幕文件

## 4. 技术要点

### 4.1 组件渲染到Canvas

可以使用以下方法将Vue组件渲染到Canvas：

1. **使用html2canvas库**：
   - 优点：使用简单，支持大部分CSS样式
   - 缺点：某些复杂效果可能不支持

2. **自定义DOM-to-Canvas转换**：
   - 使用Canvas API直接绘制文本和图像
   - 更精确控制渲染效果

### 4.2 视频编码选项

1. **WebM格式**（VP8/VP9编码）：
   - 浏览器原生支持良好
   - 文件大小较小

2. **MP4格式**（H.264编码）：
   - 兼容性最佳
   - 需要使用WebAssembly版FFmpeg

### 4.3 性能优化

1. **并行处理**：
   - 使用Web Workers进行帧渲染
   - 分批处理长视频的帧

2. **内存管理**：
   - 及时释放不再需要的Canvas资源
   - 使用对象池减少GC压力

3. **渲染优化**：
   - 缓存相同内容的帧
   - 使用requestAnimationFrame调度渲染任务

## 5. 字幕格式

### 5.1 SRT格式

```
1
00:00:05,000 --> 00:00:08,000
こんにちは、皆さん

2
00:00:10,000 --> 00:00:15,000
今日は日本語の勉強をしましょう
```

### 5.2 双语字幕格式

```
1
00:00:05,000 --> 00:00:08,000
こんにちは、皆さん
你好，大家好

2
00:00:10,000 --> 00:00:15,000
今日は日本語の勉強をしましょう
今天我们来学习日语
```

## 6. 未来扩展

1. **添加简单转场效果**：
   - 淡入淡出
   - 交叉溶解
   - 滑动过渡

2. **高级视频选项**：
   - 自定义水印
   - 片头片尾
   - 背景音乐

3. **平台集成**：
   - 一键上传到视频平台（如Bilibili）
   - 自动填充视频信息和标签

## 7. 注意事项

1. **样式处理**：
   - 移除ContentDisplay组件中的CSS过渡效果（如`transition: color 0.3s ease;`），这些效果在静态帧渲染中没有意义
   - 确保在静态渲染时应用正确的最终样式

2. **简化实现**：
   - 不需要特别处理字体加载，使用系统默认渲染即可
   - 不考虑浏览器兼容性问题，专注于主流现代浏览器的实现
