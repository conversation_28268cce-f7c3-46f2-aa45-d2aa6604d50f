# Echo Lab 视频播放器

本文档详细说明了 Echo Lab 视频播放器的功能、实现和使用方法。

## 概述

Echo Lab 视频播放器是一个专为日语学习设计的播放器，支持多种播放模式、定时关闭功能和自定义播放设置。播放器分为桌面版和移动版两种布局，以适应不同的设备和使用场景。

## 主要功能

- **基本播放控制**：播放、暂停、跳转、音量控制
- **环节导航**：在不同环节之间跳转
- **播放设置**：调整播放速度、循环播放、自动播放
- **显示设置**：显示/隐藏翻译、显示/隐藏标注
- **定时关闭**：设置定时关闭功能，自动停止播放
- **响应式设计**：适应桌面和移动设备

## 播放器布局

### 桌面版布局

桌面版播放器采用传统的视频播放器布局，包括以下组件：

1. **内容显示区域**：显示当前播放的文本内容
2. **控制栏**：包含播放/暂停按钮、进度条、音量控制等
3. **设置面板**：包含播放设置、显示设置、定时关闭等选项
4. **环节导航**：显示所有环节，可以点击跳转

```
+------------------------------------------+
|                                          |
|           内容显示区域                    |
|                                          |
+------------------------------------------+
| << |  >  | >> |  进度条  | 00:00 | 音量 |
+------------------------------------------+
|                                          |
|              设置面板                     |
|                                          |
+------------------------------------------+
|                                          |
|              环节导航                     |
|                                          |
+------------------------------------------+
```

### 移动版布局

移动版播放器采用简化的布局，优化触摸操作，包括以下组件：

1. **内容显示区域**：显示当前播放的文本内容
2. **控制栏**：包含播放/暂停按钮、进度条、时间显示
3. **设置按钮**：点击显示设置面板
4. **环节按钮**：点击显示环节导航

```
+------------------+
|                  |
|   内容显示区域    |
|                  |
+------------------+
| << | > | >> | 设置 |
+------------------+
|     进度条       |
| 00:00 / 00:00    |
+------------------+
```

## 播放控制

### 基本控制

播放器提供以下基本控制功能：

1. **播放/暂停**：点击播放按钮或内容区域切换播放状态
2. **跳转**：点击进度条跳转到指定位置
3. **上一项/下一项**：点击上一项/下一项按钮跳转到上一个/下一个内容项
4. **音量控制**：调整音量大小

### 导航逻辑

播放器的导航逻辑如下：

1. **后退按钮**：
   - 如果当前时间接近内容开头（小于 0.3 秒），则跳转到上一项
   - 否则回到当前内容开头

2. **前进按钮**：
   - 直接跳转到下一项

3. **环节导航**：
   - 点击环节名称跳转到该环节的第一项

### 播放设置

播放器提供以下播放设置：

1. **播放速度**：调整播放速度（0.5x - 2.0x）
2. **循环播放**：启用/禁用循环播放
3. **自动播放**：启用/禁用自动播放

### 显示设置

播放器提供以下显示设置：

1. **显示翻译**：显示/隐藏翻译内容
2. **显示标注**：显示/隐藏标注内容（如振り仮名）

### 定时关闭功能

播放器提供定时关闭功能，可以设置一定时间后自动停止播放：

1. **启用定时关闭**：启用/禁用定时关闭功能
2. **关闭时长**：设置关闭时长（5分钟、10分钟、15分钟、30分钟、60分钟）
3. **剩余时间显示**：显示定时关闭的剩余时间

## 技术实现

### 播放器状态管理

播放器使用 Pinia store 管理状态：

```javascript
// 播放器状态管理
const usePlayerStore = defineStore('player', {
  state: () => ({
    timeline: [],
    currentIndex: 0,
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    speed: 1,
    loop: false,
    autoPlay: true,
    showTranslation: true,
    showAnnotation: true,
    timerEnabled: false,
    timerDuration: 0,
    timerEndTime: null
  }),
  
  getters: {
    currentItem: (state) => {
      return state.timeline[state.currentIndex] || null;
    },
    
    progress: (state) => {
      return state.duration > 0 ? state.currentTime / state.duration : 0;
    },
    
    formattedCurrentTime: (state) => {
      return formatTime(state.currentTime);
    },
    
    formattedDuration: (state) => {
      return formatTime(state.duration);
    },
    
    timerRemaining: (state) => {
      if (!state.timerEnabled || !state.timerEndTime) {
        return 0;
      }
      
      const remaining = Math.max(0, state.timerEndTime - Date.now()) / 1000;
      return Math.floor(remaining);
    }
  },
  
  actions: {
    // 播放控制
    play() {
      this.isPlaying = true;
    },
    
    pause() {
      this.isPlaying = false;
    },
    
    togglePlay() {
      this.isPlaying = !this.isPlaying;
    },
    
    // 导航控制
    next() {
      if (this.currentIndex < this.timeline.length - 1) {
        this.setCurrentIndex(this.currentIndex + 1);
      } else if (this.loop) {
        this.setCurrentIndex(0);
      }
    },
    
    prev() {
      // 如果当前时间接近内容开头（小于0.3秒），则跳转到上一项
      const currentItem = this.currentItem;
      const timeFromStart = this.currentTime - (currentItem?.startTime || 0);
      
      if (timeFromStart <= 0.3 && this.currentIndex > 0) {
        this.setCurrentIndex(this.currentIndex - 1);
      } else {
        // 否则回到当前内容开头
        this.seek(currentItem?.startTime || 0);
      }
    },
    
    // 定时关闭
    enableTimer(duration) {
      this.timerEnabled = true;
      this.timerDuration = duration;
      this.timerEndTime = Date.now() + duration * 60 * 1000;
    },
    
    disableTimer() {
      this.timerEnabled = false;
      this.timerEndTime = null;
    }
  }
});
```

### 播放器组件

播放器组件使用 Vue 3 的 Composition API 实现：

```vue
<!-- 播放器组件 -->
<template>
  <div class="video-player" :class="{ 'is-playing': isPlaying, 'is-mobile': isMobile }">
    <div class="player-content">
      <div class="content-display" @click="togglePlay">
        <div v-if="currentItem && currentItem.speaker" class="speaker-name">
          {{ currentItem.speaker }}
        </div>
        <div class="content-text" v-if="currentItem && !showAnnotation">
          {{ currentItem.content }}
        </div>
        <div class="content-text annotation" v-if="currentItem && showAnnotation">
          {{ getAnnotation(currentItem.id) }}
        </div>
        <div class="translation-text" v-if="currentItem && showTranslation && getTranslation(currentItem.id)">
          {{ getTranslation(currentItem.id) }}
        </div>
      </div>
    </div>
    
    <div class="player-controls">
      <div class="progress-bar">
        <div class="progress-track" @click="onProgressClick">
          <div class="progress-fill" :style="{ width: `${progress * 100}%` }"></div>
        </div>
        <div class="time-display">
          <span class="current-time">{{ formattedCurrentTime }}</span>
          <span class="duration">{{ formattedDuration }}</span>
        </div>
      </div>
      
      <div class="control-buttons">
        <button class="prev-button" @click="prev">
          <i class="icon-prev"></i>
        </button>
        <button class="play-button" @click="togglePlay">
          <i :class="isPlaying ? 'icon-pause' : 'icon-play'"></i>
        </button>
        <button class="next-button" @click="next">
          <i class="icon-next"></i>
        </button>
        <div class="volume-control" v-if="!isMobile">
          <button class="volume-button" @click="toggleMute">
            <i :class="isMuted ? 'icon-volume-mute' : 'icon-volume'"></i>
          </button>
          <div class="volume-slider">
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              v-model.number="volume"
              @input="setVolume"
            />
          </div>
        </div>
        <button class="settings-button" @click="toggleSettings">
          <i class="icon-settings"></i>
        </button>
      </div>
    </div>
    
    <!-- 设置面板 -->
    <div class="player-settings" v-if="showSettings">
      <!-- 设置内容 -->
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { usePlayerStore } from '@/stores/player';
import { useResourceStore } from '@/stores/resource';
import { useWindowSize } from '@/composables/useWindowSize';

// 状态
const playerStore = usePlayerStore();
const resourceStore = useResourceStore();
const { width } = useWindowSize();

const showSettings = ref(false);
const audioElement = ref(null);

// 计算属性
const isMobile = computed(() => width.value < 768);
const isPlaying = computed(() => playerStore.isPlaying);
const currentItem = computed(() => playerStore.currentItem);
const progress = computed(() => playerStore.progress);
const formattedCurrentTime = computed(() => playerStore.formattedCurrentTime);
const formattedDuration = computed(() => playerStore.formattedDuration);
const volume = computed(() => playerStore.volume);
const isMuted = computed(() => playerStore.volume === 0);
const showTranslation = computed(() => playerStore.showTranslation);
const showAnnotation = computed(() => playerStore.showAnnotation);

// 方法
function togglePlay() {
  playerStore.togglePlay();
}

function prev() {
  playerStore.prev();
}

function next() {
  playerStore.next();
}

function toggleMute() {
  if (playerStore.volume > 0) {
    playerStore.setVolume(0);
  } else {
    playerStore.setVolume(1);
  }
}

function setVolume(event) {
  playerStore.setVolume(parseFloat(event.target.value));
}

function toggleSettings() {
  showSettings.value = !showSettings.value;
}

function onProgressClick(event) {
  const rect = event.currentTarget.getBoundingClientRect();
  const clickPosition = (event.clientX - rect.left) / rect.width;
  const seekTime = playerStore.duration * clickPosition;
  playerStore.seek(seekTime);
}

function getAnnotation(itemId) {
  return resourceStore.annotations[itemId] || currentItem.value?.content || '';
}

function getTranslation(itemId) {
  const translationLanguage = 'zh-CN'; // 可以从设置中获取
  return resourceStore.translations[translationLanguage]?.[itemId] || '';
}

// 生命周期
onMounted(() => {
  // 初始化音频元素
  audioElement.value = new Audio();
  
  // 监听播放状态变化
  watch(isPlaying, (newValue) => {
    if (newValue) {
      audioElement.value.play();
    } else {
      audioElement.value.pause();
    }
  });
  
  // 监听当前项变化
  watch(currentItem, (newValue) => {
    if (newValue) {
      audioElement.value.src = newValue.audioUrl;
      audioElement.value.playbackRate = newValue.speed;
      
      if (isPlaying.value) {
        audioElement.value.play();
      }
    }
  });
  
  // 监听音量变化
  watch(volume, (newValue) => {
    audioElement.value.volume = newValue;
  });
  
  // 监听音频事件
  audioElement.value.addEventListener('timeupdate', onTimeUpdate);
  audioElement.value.addEventListener('ended', onEnded);
});

onUnmounted(() => {
  // 移除音频事件监听
  audioElement.value.removeEventListener('timeupdate', onTimeUpdate);
  audioElement.value.removeEventListener('ended', onEnded);
});

// 音频事件处理
function onTimeUpdate() {
  playerStore.updateCurrentTime(audioElement.value.currentTime);
}

function onEnded() {
  playerStore.next();
}
</script>
```

## 响应式设计

播放器采用响应式设计，根据屏幕尺寸自动切换桌面版和移动版布局：

```javascript
// 响应式设计
const { width } = useWindowSize();
const isMobile = computed(() => width.value < 768);
```

移动版布局的特点：

1. **简化控制栏**：移除音量控制，保留基本播放控制
2. **底部进度条**：进度条位于底部，便于触摸操作
3. **时间显示**：时间显示和控制按钮在同一行
4. **弹出式设置面板**：设置面板以弹出式显示，不占用主界面空间

## 定时关闭功能

定时关闭功能允许用户设置一定时间后自动停止播放，实现如下：

```javascript
// 定时关闭功能
function enableTimer(duration) {
  playerStore.timerEnabled = true;
  playerStore.timerDuration = duration;
  playerStore.timerEndTime = Date.now() + duration * 60 * 1000;
}

function disableTimer() {
  playerStore.timerEnabled = false;
  playerStore.timerEndTime = null;
}

// 定时检查
const timerInterval = setInterval(() => {
  if (playerStore.timerEnabled && playerStore.timerEndTime && Date.now() >= playerStore.timerEndTime) {
    playerStore.pause();
    playerStore.timerEnabled = false;
    playerStore.timerEndTime = null;
  }
}, 1000);

// 清理定时器
onUnmounted(() => {
  clearInterval(timerInterval);
});
```

## 使用方法

### 基本使用

1. 点击播放按钮或内容区域开始播放
2. 使用控制栏调整播放进度、音量等
3. 点击上一项/下一项按钮导航到上一个/下一个内容项

### 设置播放选项

1. 点击设置按钮打开设置面板
2. 调整播放速度、循环播放、自动播放等选项
3. 设置显示翻译、显示标注等选项

### 使用定时关闭功能

1. 在设置面板中启用定时关闭功能
2. 选择关闭时长（5分钟、10分钟、15分钟、30分钟、60分钟）
3. 播放器会在设定时间后自动停止播放

## 最佳实践

1. **播放设置**：
   - 初学者：使用较慢的播放速度（0.8x）
   - 中级学习者：使用正常播放速度（1.0x）
   - 高级学习者：使用较快的播放速度（1.2x）

2. **显示设置**：
   - 初学者：显示翻译和标注
   - 中级学习者：显示标注，隐藏翻译
   - 高级学习者：隐藏翻译和标注

3. **定时关闭**：
   - 睡前学习：设置 15-30 分钟的定时关闭
   - 短时间学习：设置 5-10 分钟的定时关闭
