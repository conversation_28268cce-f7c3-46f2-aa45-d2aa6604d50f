# 布局最佳实践 - 避免边距/边框被截断问题

## 常见问题及解决方案

### 1. 统一 box-sizing
```css
/* 全局设置，确保所有元素使用相同的盒模型 */
* {
  box-sizing: border-box;
}
```

### 2. 容器padding规划
```css
/* 为边框和绝对定位元素预留空间 */
.container {
  padding: 20rpx 30rpx; /* 右边多留一些空间 */
}

.item {
  border: 2rpx solid transparent; /* 即使透明也要占位 */
  margin-right: 4rpx; /* 额外的安全边距 */
}
```

### 3. 绝对定位元素处理
```css
.parent {
  position: relative;
  padding-right: 60rpx; /* 为绝对定位的子元素预留空间 */
}

.absolute-child {
  position: absolute;
  right: 16rpx; /* 确保不超出父容器 */
  width: 40rpx;
}
```

### 4. 滚动容器特殊处理
```css
.scroll-container {
  padding: 20rpx 30rpx; /* 内边距 */
  box-sizing: border-box;
}

.scroll-item {
  margin-right: 4rpx; /* 防止边框被截断 */
  border: 2rpx solid transparent;
}
```

## 调试技巧

### 1. 临时边框调试
```css
/* 临时添加明显边框查看布局 */
.debug {
  border: 2rpx solid red !important;
}
```

### 2. 背景色调试
```css
/* 用不同背景色区分容器层级 */
.container { background: rgba(255,0,0,0.1); }
.item { background: rgba(0,255,0,0.1); }
```

### 3. 检查计算值
- 使用开发者工具查看元素的实际宽度、padding、margin
- 注意 `box-sizing` 的影响

## 预防性编码习惯

1. **设计阶段就考虑边距**：UI设计时就要考虑边框、阴影等元素的空间需求
2. **统一spacing系统**：使用设计token统一管理间距
3. **组件边界清晰**：每个组件明确自己的边界和对外接口
4. **测试多种状态**：测试选中、悬停、禁用等各种状态下的布局

## 小程序特殊注意事项

1. **rpx单位**：注意rpx在不同设备上的实际像素值
2. **scroll-view**：滚动容器有特殊的内容区域计算
3. **flex布局**：小程序的flex实现可能与标准有细微差异
4. **绝对定位**：在小程序中要特别注意层级和边界
