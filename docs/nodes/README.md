# Echo Lab 节点系统

本文档概述了 Echo Lab 的节点系统，包括节点类型、节点工厂和节点关系。

## 节点系统概述

Echo Lab 使用基于节点的可视化编辑器，用户可以通过创建和连接不同类型的节点来构建内容。每个节点都有特定的功能和参数，节点之间通过连接传递数据。

### 节点工厂模式

Echo Lab 使用节点工厂模式创建和管理不同类型的节点：

```javascript
// 节点工厂
class NodeFactory {
  constructor() {
    this.nodeTypes = {};
  }
  
  // 注册节点类型
  registerNodeType(type, nodeClass) {
    this.nodeTypes[type] = nodeClass;
  }
  
  // 创建节点
  createNode(type, params = {}) {
    if (!this.nodeTypes[type]) {
      throw new Error(`未知节点类型: ${type}`);
    }
    
    const NodeClass = this.nodeTypes[type];
    const node = new NodeClass(params);
    node.id = `${type}_${nanoid()}`;
    
    return node;
  }
}
```

### 节点基类

所有节点都继承自节点基类，节点基类定义了节点的通用属性和方法：

```javascript
// 节点基类
class BaseNode {
  constructor(params = {}) {
    this.id = params.id || null;
    this.type = this.constructor.type;
    this.customName = params.customName || '';
    this.sourceIds = params.sourceIds || [];
    this.position = params.position || { x: 0, y: 0 };
    this.params = params.params || {};
    this._timestamp = Date.now();
  }
  
  // 获取节点名称
  getName() {
    return this.customName || this.getDefaultName();
  }
  
  // 获取默认名称
  getDefaultName() {
    return `${this.type} #${this.id.split('_')[1].substring(0, 6)}`;
  }
  
  // 更新参数
  updateParams(params) {
    this.params = { ...this.params, ...params };
    this._timestamp = Date.now();
  }
  
  // 添加源节点
  addSource(sourceId) {
    if (!this.sourceIds.includes(sourceId)) {
      this.sourceIds.push(sourceId);
      this._timestamp = Date.now();
    }
  }
  
  // 移除源节点
  removeSource(sourceId) {
    const index = this.sourceIds.indexOf(sourceId);
    if (index !== -1) {
      this.sourceIds.splice(index, 1);
      this._timestamp = Date.now();
    }
  }
}
```

## 节点类型

Echo Lab 支持以下几种节点类型：

1. **文本内容节点 (textContent)**：用于输入和管理文本内容
2. **文本序列节点 (textSequence)**：用于处理文本序列和环节设置
3. **资源管理节点 (resource)**：用于管理音频、翻译等资源
4. **视频配置节点 (videoConfig)**：用于配置视频参数

### 节点类型注册

节点类型通过节点工厂注册：

```javascript
// 注册节点类型
nodeFactory.registerNodeType('textContent', TextContentNode);
nodeFactory.registerNodeType('textSequence', TextSequenceNode);
nodeFactory.registerNodeType('resource', ResourceNode);
nodeFactory.registerNodeType('videoConfig', VideoConfigNode);
```

## 节点关系

节点之间通过连接建立关系，连接表示数据流向。每个节点可以有多个输入源节点，但通常只有一个输出。

### 连接规则

- **文本内容节点**：可以作为文本序列节点的输入
- **文本序列节点**：可以作为资源管理节点的输入
- **资源管理节点**：可以作为视频配置节点的输入
- **视频配置节点**：通常是最终节点，没有输出

### 数据流向

数据在节点之间的流向如下：

```
文本内容节点 -> 文本序列节点 -> 资源管理节点 -> 视频配置节点
```

## 节点状态管理

节点状态通过 Pinia store 进行管理：

```javascript
// 节点状态管理
const useNodeStore = defineStore('node', {
  state: () => ({
    nodes: {},
    selectedNodeId: null,
    nodeTypeCounters: {}
  }),
  
  getters: {
    getNodeById: (state) => (id) => state.nodes[id],
    
    getSelectedNode: (state) => {
      return state.selectedNodeId ? state.nodes[state.selectedNodeId] : null;
    },
    
    getNodesByType: (state) => (type) => {
      return Object.values(state.nodes).filter(node => node.type === type);
    },
    
    getSourceNodes: (state) => (nodeId) => {
      const node = state.nodes[nodeId];
      if (!node) return [];
      
      return node.sourceIds.map(id => state.nodes[id]).filter(Boolean);
    },
    
    getTargetNodes: (state) => (nodeId) => {
      return Object.values(state.nodes).filter(node => 
        node.sourceIds.includes(nodeId)
      );
    }
  },
  
  actions: {
    addNode(node) {
      this.nodes[node.id] = node;
      
      // 更新节点类型计数器
      const type = node.type;
      this.nodeTypeCounters[type] = (this.nodeTypeCounters[type] || 0) + 1;
    },
    
    removeNode(nodeId) {
      const node = this.nodes[nodeId];
      if (!node) return;
      
      // 移除所有引用此节点的连接
      Object.values(this.nodes).forEach(n => {
        if (n.sourceIds.includes(nodeId)) {
          n.sourceIds = n.sourceIds.filter(id => id !== nodeId);
        }
      });
      
      // 删除节点
      delete this.nodes[nodeId];
      
      // 如果删除的是选中的节点，清除选中状态
      if (this.selectedNodeId === nodeId) {
        this.selectedNodeId = null;
      }
    },
    
    updateNode(nodeId, updates) {
      const node = this.nodes[nodeId];
      if (!node) return;
      
      // 更新节点
      this.nodes[nodeId] = { ...node, ...updates };
    },
    
    selectNode(nodeId) {
      this.selectedNodeId = nodeId;
    },
    
    connectNodes(sourceId, targetId) {
      const targetNode = this.nodes[targetId];
      if (!targetNode) return;
      
      // 添加连接
      if (!targetNode.sourceIds.includes(sourceId)) {
        targetNode.sourceIds.push(sourceId);
      }
    },
    
    disconnectNodes(sourceId, targetId) {
      const targetNode = this.nodes[targetId];
      if (!targetNode) return;
      
      // 移除连接
      targetNode.sourceIds = targetNode.sourceIds.filter(id => id !== sourceId);
    }
  }
});
```

## 节点创建和管理

节点的创建和管理通过节点工厂和节点状态管理进行：

```javascript
// 创建节点
function createNode(type, params = {}) {
  const nodeStore = useNodeStore();
  
  // 使用节点工厂创建节点
  const node = nodeFactory.createNode(type, params);
  
  // 添加到节点状态管理
  nodeStore.addNode(node);
  
  return node;
}

// 删除节点
function removeNode(nodeId) {
  const nodeStore = useNodeStore();
  
  // 从节点状态管理中删除节点
  nodeStore.removeNode(nodeId);
}

// 更新节点
function updateNode(nodeId, updates) {
  const nodeStore = useNodeStore();
  
  // 更新节点
  nodeStore.updateNode(nodeId, updates);
}

// 连接节点
function connectNodes(sourceId, targetId) {
  const nodeStore = useNodeStore();
  
  // 连接节点
  nodeStore.connectNodes(sourceId, targetId);
}

// 断开连接
function disconnectNodes(sourceId, targetId) {
  const nodeStore = useNodeStore();
  
  // 断开连接
  nodeStore.disconnectNodes(sourceId, targetId);
}
```

## 节点序列化和反序列化

节点的序列化和反序列化用于保存和加载节点数据：

```javascript
// 序列化节点
function serializeNodes() {
  const nodeStore = useNodeStore();
  
  // 获取所有节点
  const nodes = nodeStore.nodes;
  
  // 序列化节点
  return {
    nodes,
    nodeTypeCounters: nodeStore.nodeTypeCounters
  };
}

// 反序列化节点
function deserializeNodes(data) {
  const nodeStore = useNodeStore();
  
  // 清空现有节点
  Object.keys(nodeStore.nodes).forEach(id => {
    nodeStore.removeNode(id);
  });
  
  // 加载节点
  Object.values(data.nodes).forEach(node => {
    nodeStore.addNode(node);
  });
  
  // 加载节点类型计数器
  nodeStore.nodeTypeCounters = data.nodeTypeCounters;
}
```

## 节点可视化

节点在画布上的可视化通过 Vue 组件实现：

```vue
<!-- 节点组件 -->
<template>
  <div
    class="node"
    :class="[`node-${node.type}`, { 'selected': isSelected }]"
    :style="nodeStyle"
    @click="selectNode"
  >
    <div class="node-header">
      <div class="node-title">{{ node.getName() }}</div>
      <div class="node-actions">
        <button @click.stop="removeNode">×</button>
      </div>
    </div>
    <div class="node-content">
      <component
        :is="getNodeComponent(node.type)"
        :node="node"
        @update="updateNode"
      />
    </div>
    <div class="node-ports">
      <div
        v-for="port in getPorts(node)"
        :key="port.id"
        class="node-port"
        :class="[`port-${port.type}`]"
        @mousedown.stop="startConnection(port)"
        @mouseup.stop="endConnection(port)"
      />
    </div>
  </div>
</template>
```

## 节点交互

节点的交互包括选择、拖动、连接等操作：

```javascript
// 选择节点
function selectNode(nodeId) {
  const nodeStore = useNodeStore();
  
  // 选择节点
  nodeStore.selectNode(nodeId);
}

// 拖动节点
function dragNode(nodeId, dx, dy) {
  const nodeStore = useNodeStore();
  const node = nodeStore.getNodeById(nodeId);
  
  if (!node) return;
  
  // 更新节点位置
  nodeStore.updateNode(nodeId, {
    position: {
      x: node.position.x + dx,
      y: node.position.y + dy
    }
  });
}

// 开始连接
function startConnection(nodeId) {
  const editorStore = useEditorStore();
  
  // 开始连接
  editorStore.startConnecting(nodeId);
}

// 结束连接
function endConnection(nodeId) {
  const editorStore = useEditorStore();
  
  // 结束连接
  editorStore.finishConnecting(nodeId);
}
```
