# 资源管理节点

本文档详细说明了 Echo Lab 中资源管理节点的功能、参数和使用方法。

## 概述

资源管理节点是 Echo Lab 中的重要节点类型，用于管理音频、翻译和标注等资源。它接收来自文本序列节点的输入，生成和管理各种资源。

## 节点参数

资源管理节点具有以下参数：

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `annotation` | 标注设置 | `{ enabled: false, method: 'furigana' }` |
| `translation` | 翻译设置 | `{ enabled: false, targets: [] }` |
| `audio` | 音频设置 | `{ enabled: true, service: 'auto', voice: 'auto' }` |

### 标注设置结构

标注设置包含以下属性：

```javascript
{
  enabled: true,        // 是否启用标注
  method: 'furigana'    // 标注方法: furigana(振り仮名)
}
```

### 翻译设置结构

翻译设置包含以下属性：

```javascript
{
  enabled: true,                // 是否启用翻译
  targets: ['zh-CN', 'en-US']   // 目标语言数组
}
```

### 音频设置结构

音频设置包含以下属性：

```javascript
{
  enabled: true,    // 是否启用音频
  service: 'auto',  // 语音服务: auto(自动), google, baidu
  voice: 'auto'     // 语音声音: auto(自动)
}
```

## 资源类型

### 标注资源

标注资源用于为文本添加辅助标注，如日语的振り仮名。

#### 标注方法

- **振り仮名 (furigana)**: 为日语汉字添加假名标注

```javascript
// 振り仮名标注示例
const text = "今日は良い天気ですね。";
const annotation = "今日（きょう）は良（よ）い天気（てんき）ですね。";
```

### 翻译资源

翻译资源用于将文本翻译为其他语言。

#### 支持的语言

- **中文 (zh-CN)**: 简体中文
- **英语 (en-US)**: 美式英语
- **韩语 (ko-KR)**: 韩语

```javascript
// 翻译资源示例
const translations = {
  "zh-CN": {
    "seg_abc123": "今天天气真好。"
  },
  "en-US": {
    "seg_abc123": "The weather is nice today."
  }
};
```

### 音频资源

音频资源用于为文本生成语音。

#### 支持的服务

- **Google TTS**: 用于日语和英语
- **百度 TTS**: 用于中文

```javascript
// 音频资源示例
const audioItems = [
  {
    id: "audio_abc123",
    text: "今日は良い天気ですね。",
    language: "ja",
    audioUrl: "https://example.com/audio/abc123.mp3",
    duration: 2.5
  }
];
```

## 节点方法

### 生成资源

生成所有启用的资源。

```javascript
// 生成资源方法
async function generateResources() {
  // 获取序列数据
  const sequence = this.getSequenceData();
  
  // 如果没有序列数据，返回
  if (!sequence || sequence.length === 0) {
    return;
  }
  
  // 初始化资源对象
  const resources = {
    annotations: {},
    translations: {},
    audioItems: []
  };
  
  // 生成资源
  await Promise.all([
    this.generateAnnotations(sequence, resources),
    this.generateTranslations(sequence, resources),
    this.generateAudio(sequence, resources)
  ]);
  
  // 返回资源对象
  return resources;
}
```

### 生成标注

为序列中的内容生成标注。

```javascript
// 生成标注方法
async function generateAnnotations(sequence, resources) {
  // 如果未启用标注，返回
  if (!this.params.annotation.enabled) {
    return;
  }
  
  // 获取标注方法
  const method = this.params.annotation.method;
  
  // 为每个内容项生成标注
  for (const item of sequence) {
    // 只处理日语内容
    if (item.language !== 'ja') {
      continue;
    }
    
    try {
      // 调用标注服务
      const annotation = await this.callAnnotationService(item.content, method);
      
      // 保存标注
      resources.annotations[item.id] = annotation;
    } catch (error) {
      console.error(`生成标注失败: ${error.message}`);
    }
  }
}
```

### 生成翻译

为序列中的内容生成翻译。

```javascript
// 生成翻译方法
async function generateTranslations(sequence, resources) {
  // 如果未启用翻译，返回
  if (!this.params.translation.enabled || this.params.translation.targets.length === 0) {
    return;
  }
  
  // 获取目标语言
  const targets = this.params.translation.targets;
  
  // 初始化翻译对象
  resources.translations = {};
  targets.forEach(target => {
    resources.translations[target] = {};
  });
  
  // 为每个内容项生成翻译
  for (const item of sequence) {
    // 为每个目标语言生成翻译
    for (const target of targets) {
      // 跳过源语言与目标语言相同的情况
      if (item.language === target) {
        continue;
      }
      
      try {
        // 调用翻译服务
        const translation = await this.callTranslationService(
          item.content,
          item.language,
          target
        );
        
        // 保存翻译
        resources.translations[target][item.id] = translation;
      } catch (error) {
        console.error(`生成翻译失败: ${error.message}`);
      }
    }
  }
}
```

### 生成音频

为序列中的内容生成音频。

```javascript
// 生成音频方法
async function generateAudio(sequence, resources) {
  // 如果未启用音频，返回
  if (!this.params.audio.enabled) {
    return;
  }
  
  // 获取服务和声音
  const service = this.params.audio.service;
  const voice = this.params.audio.voice;
  
  // 初始化音频项数组
  resources.audioItems = [];
  
  // 为每个内容项生成音频
  for (const item of sequence) {
    try {
      // 调用音频服务
      const { url, duration } = await this.callAudioService(
        item.content,
        item.language,
        service,
        voice
      );
      
      // 保存音频项
      resources.audioItems.push({
        id: `audio_${nanoid()}`,
        text: item.content,
        language: item.language,
        audioUrl: url,
        duration
      });
    } catch (error) {
      console.error(`生成音频失败: ${error.message}`);
    }
  }
}
```

### 调用标注服务

调用标注服务生成标注。

```javascript
// 调用标注服务方法
async function callAnnotationService(text, method) {
  // 构建请求参数
  const params = {
    text,
    language: 'ja',
    method,
    ignoreCache: false
  };
  
  // 调用API
  const response = await apiService.post('/resource/annotation', params);
  
  // 返回标注
  return response.annotation;
}
```

### 调用翻译服务

调用翻译服务生成翻译。

```javascript
// 调用翻译服务方法
async function callTranslationService(text, sourceLanguage, targetLanguage) {
  // 构建请求参数
  const params = {
    text,
    sourceLanguage,
    targetLanguage,
    ignoreCache: false
  };
  
  // 调用API
  const response = await apiService.post('/resource/translation', params);
  
  // 返回翻译
  return response.translation;
}
```

### 调用音频服务

调用音频服务生成音频。

```javascript
// 调用音频服务方法
async function callAudioService(text, language, service, voice) {
  // 构建请求参数
  const params = {
    text,
    language,
    service,
    voice,
    ignoreCache: false
  };
  
  // 调用API
  const response = await apiService.post('/resource/audio', params);
  
  // 返回音频URL和时长
  return {
    url: response.url,
    duration: response.duration
  };
}
```

## 节点 UI

资源管理节点的 UI 包括标注设置、翻译设置和音频设置。

```vue
<!-- 资源管理节点 UI -->
<template>
  <div class="resource-node">
    <div class="resource-section">
      <h3>标注设置</h3>
      <div class="resource-option">
        <label>
          <input
            type="checkbox"
            v-model="annotation.enabled"
            @change="updateAnnotation"
          />
          启用标注
        </label>
      </div>
      <div v-if="annotation.enabled" class="resource-option">
        <label>标注方法:</label>
        <select
          v-model="annotation.method"
          @change="updateAnnotation"
        >
          <option value="furigana">振り仮名</option>
        </select>
      </div>
    </div>
    
    <div class="resource-section">
      <h3>翻译设置</h3>
      <div class="resource-option">
        <label>
          <input
            type="checkbox"
            v-model="translation.enabled"
            @change="updateTranslation"
          />
          启用翻译
        </label>
      </div>
      <div v-if="translation.enabled" class="resource-option">
        <label>目标语言:</label>
        <div class="language-options">
          <label>
            <input
              type="checkbox"
              :value="'zh-CN'"
              v-model="translation.targets"
              @change="updateTranslation"
            />
            中文
          </label>
          <label>
            <input
              type="checkbox"
              :value="'en-US'"
              v-model="translation.targets"
              @change="updateTranslation"
            />
            英语
          </label>
          <label>
            <input
              type="checkbox"
              :value="'ko-KR'"
              v-model="translation.targets"
              @change="updateTranslation"
            />
            韩语
          </label>
        </div>
      </div>
    </div>
    
    <div class="resource-section">
      <h3>音频设置</h3>
      <div class="resource-option">
        <label>
          <input
            type="checkbox"
            v-model="audio.enabled"
            @change="updateAudio"
          />
          启用音频
        </label>
      </div>
      <div v-if="audio.enabled" class="resource-option">
        <label>语音服务:</label>
        <select
          v-model="audio.service"
          @change="updateAudio"
        >
          <option value="auto">自动选择</option>
          <option value="google">Google TTS</option>
          <option value="baidu">百度 TTS</option>
        </select>
      </div>
      <div v-if="audio.enabled" class="resource-option">
        <label>语音声音:</label>
        <select
          v-model="audio.voice"
          @change="updateAudio"
        >
          <option value="auto">自动选择</option>
        </select>
      </div>
    </div>
    
    <div class="resource-actions">
      <button @click="generateAllResources">生成所有资源</button>
    </div>
  </div>
</template>
```

## 节点示例

以下是一个资源管理节点的示例：

```javascript
// 资源管理节点示例
const resourceNode = {
  id: "resource_789ghi",
  type: "resource",
  customName: "资源管理",
  params: {
    annotation: {
      enabled: true,
      method: "furigana"
    },
    translation: {
      enabled: true,
      targets: ["zh-CN", "en-US"]
    },
    audio: {
      enabled: true,
      service: "auto",
      voice: "auto"
    }
  },
  sourceIds: ["textSequence_456def"],
  position: { x: 500, y: 100 }
};
```

## 最佳实践

1. **资源生成**:
   - 先配置好所有设置，再一次性生成所有资源
   - 对于大量内容，分批生成资源以避免超时
   - 使用缓存减少重复生成

2. **标注设置**:
   - 对于日语内容，启用振り仮名标注
   - 确保标注正确，必要时手动修正

3. **翻译设置**:
   - 只选择必要的目标语言
   - 检查翻译质量，必要时手动修正

4. **音频设置**:
   - 使用自动选择服务，系统会根据语言选择最合适的服务
   - 日语和英语使用 Google TTS
   - 中文使用百度 TTS
