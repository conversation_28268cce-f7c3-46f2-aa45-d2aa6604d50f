# 视频配置节点

本文档详细说明了 Echo Lab 中视频配置节点的功能、参数和使用方法。

## 概述

视频配置节点是 Echo Lab 中的最终节点类型，用于配置视频参数和元数据。它接收来自资源管理节点的输入，配置视频的背景、尺寸、元数据等参数。

## 节点参数

视频配置节点具有以下参数：

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `background` | 背景设置 | `{ type: 'color', color: '#000000' }` |
| `size` | 视频尺寸 | `{ width: 1280, height: 720 }` |
| `metadata` | 视频元数据 | `{ title: '', description: '', tags: [] }` |
| `font` | 字体设置 | `{ family: 'sans-serif', size: 32, color: '#FFFFFF' }` |
| `layout` | 布局设置 | `{ position: 'center', maxWidth: 800 }` |

### 背景设置结构

背景设置包含以下属性：

```javascript
{
  type: 'color',     // 背景类型: color(颜色), image(图片)
  color: '#000000',  // 背景颜色(当type为color时)
  imageUrl: '',      // 背景图片URL(当type为image时)
  opacity: 1.0       // 背景不透明度
}
```

### 视频尺寸结构

视频尺寸包含以下属性：

```javascript
{
  width: 1280,   // 视频宽度(像素)
  height: 720    // 视频高度(像素)
}
```

### 视频元数据结构

视频元数据包含以下属性：

```javascript
{
  title: '日语学习视频',                // 视频标题
  description: '基础日语对话练习',      // 视频描述
  tags: ['日语', '学习', '对话']       // 视频标签
}
```

### 字体设置结构

字体设置包含以下属性：

```javascript
{
  family: 'sans-serif',  // 字体系列
  size: 32,              // 字体大小(像素)
  color: '#FFFFFF',      // 字体颜色
  weight: 'normal',      // 字体粗细: normal(正常), bold(粗体)
  shadow: true,          // 是否启用阴影
  shadowColor: '#000000' // 阴影颜色
}
```

### 布局设置结构

布局设置包含以下属性：

```javascript
{
  position: 'center',  // 内容位置: center(居中), top(顶部), bottom(底部)
  maxWidth: 800,       // 内容最大宽度(像素)
  padding: 40          // 内容内边距(像素)
}
```

## 背景类型

### 颜色背景

使用纯色作为视频背景。

```javascript
// 颜色背景示例
const background = {
  type: 'color',
  color: '#000000',
  opacity: 1.0
};
```

### 图片背景

使用图片作为视频背景。

```javascript
// 图片背景示例
const background = {
  type: 'image',
  imageUrl: 'https://example.com/background.jpg',
  opacity: 0.8
};
```

## 视频尺寸

Echo Lab 支持以下常用视频尺寸：

- **720p**: 1280 x 720 像素
- **1080p**: 1920 x 1080 像素
- **方形**: 1080 x 1080 像素
- **竖屏**: 1080 x 1920 像素

```javascript
// 视频尺寸示例
const size = {
  width: 1280,
  height: 720
};
```

## 节点方法

### 更新背景

更新背景设置。

```javascript
// 更新背景方法
function updateBackground(background) {
  this.params.background = {
    ...this.params.background,
    ...background
  };
}
```

### 更新尺寸

更新视频尺寸。

```javascript
// 更新尺寸方法
function updateSize(size) {
  this.params.size = {
    ...this.params.size,
    ...size
  };
}
```

### 更新元数据

更新视频元数据。

```javascript
// 更新元数据方法
function updateMetadata(metadata) {
  this.params.metadata = {
    ...this.params.metadata,
    ...metadata
  };
}
```

### 更新字体

更新字体设置。

```javascript
// 更新字体方法
function updateFont(font) {
  this.params.font = {
    ...this.params.font,
    ...font
  };
}
```

### 更新布局

更新布局设置。

```javascript
// 更新布局方法
function updateLayout(layout) {
  this.params.layout = {
    ...this.params.layout,
    ...layout
  };
}
```

### 上传背景图片

上传并设置背景图片。

```javascript
// 上传背景图片方法
async function uploadBackgroundImage(file) {
  try {
    // 上传图片
    const response = await apiService.uploadFile(file, 'image');
    
    // 更新背景设置
    this.updateBackground({
      type: 'image',
      imageUrl: response.url
    });
    
    return response.url;
  } catch (error) {
    console.error(`上传背景图片失败: ${error.message}`);
    throw error;
  }
}
```

### 预览视频

生成视频预览。

```javascript
// 预览视频方法
async function previewVideo() {
  try {
    // 获取资源数据
    const resources = await this.getResourceData();
    
    // 获取环节设置
    const sections = await this.getSectionSettings();
    
    // 生成时间线
    const timeline = generateTimeline(
      { nodes: {}, resources },
      sections
    );
    
    // 返回预览数据
    return {
      timeline,
      config: {
        background: this.params.background,
        size: this.params.size,
        font: this.params.font,
        layout: this.params.layout
      }
    };
  } catch (error) {
    console.error(`预览视频失败: ${error.message}`);
    throw error;
  }
}
```

### 生成视频

生成最终视频。

```javascript
// 生成视频方法
async function generateVideo() {
  try {
    // 获取资源数据
    const resources = await this.getResourceData();
    
    // 获取环节设置
    const sections = await this.getSectionSettings();
    
    // 获取元数据
    const metadata = this.params.metadata;
    
    // 构建请求参数
    const params = {
      resources,
      sections,
      config: {
        background: this.params.background,
        size: this.params.size,
        font: this.params.font,
        layout: this.params.layout
      },
      metadata
    };
    
    // 调用API
    const response = await apiService.post('/video/generate', params);
    
    // 返回视频URL
    return response.url;
  } catch (error) {
    console.error(`生成视频失败: ${error.message}`);
    throw error;
  }
}
```

## 节点 UI

视频配置节点的 UI 包括背景设置、尺寸设置、元数据设置、字体设置和布局设置。

```vue
<!-- 视频配置节点 UI -->
<template>
  <div class="video-config-node">
    <div class="config-section">
      <h3>背景设置</h3>
      <div class="config-option">
        <label>背景类型:</label>
        <select
          v-model="background.type"
          @change="updateBackground"
        >
          <option value="color">纯色</option>
          <option value="image">图片</option>
        </select>
      </div>
      <div v-if="background.type === 'color'" class="config-option">
        <label>背景颜色:</label>
        <input
          type="color"
          v-model="background.color"
          @change="updateBackground"
        />
      </div>
      <div v-if="background.type === 'image'" class="config-option">
        <label>背景图片:</label>
        <div v-if="background.imageUrl" class="image-preview">
          <img :src="background.imageUrl" alt="背景图片" />
        </div>
        <input
          type="file"
          accept="image/*"
          @change="uploadImage"
        />
      </div>
      <div class="config-option">
        <label>不透明度:</label>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          v-model.number="background.opacity"
          @input="updateBackground"
        />
        <span>{{ background.opacity.toFixed(1) }}</span>
      </div>
    </div>
    
    <div class="config-section">
      <h3>视频尺寸</h3>
      <div class="config-option">
        <label>预设尺寸:</label>
        <select @change="selectPresetSize">
          <option value="720p">720p (1280x720)</option>
          <option value="1080p">1080p (1920x1080)</option>
          <option value="square">方形 (1080x1080)</option>
          <option value="vertical">竖屏 (1080x1920)</option>
          <option value="custom">自定义</option>
        </select>
      </div>
      <div class="config-option">
        <label>宽度:</label>
        <input
          type="number"
          min="320"
          max="3840"
          v-model.number="size.width"
          @input="updateSize"
        />
      </div>
      <div class="config-option">
        <label>高度:</label>
        <input
          type="number"
          min="240"
          max="2160"
          v-model.number="size.height"
          @input="updateSize"
        />
      </div>
    </div>
    
    <div class="config-section">
      <h3>视频元数据</h3>
      <div class="config-option">
        <label>标题:</label>
        <input
          type="text"
          v-model="metadata.title"
          @input="updateMetadata"
          placeholder="视频标题"
        />
      </div>
      <div class="config-option">
        <label>描述:</label>
        <textarea
          v-model="metadata.description"
          @input="updateMetadata"
          placeholder="视频描述"
        ></textarea>
      </div>
      <div class="config-option">
        <label>标签:</label>
        <input
          type="text"
          v-model="tagsInput"
          @keydown.enter="addTag"
          placeholder="输入标签并按回车"
        />
        <div class="tags-list">
          <span
            v-for="(tag, index) in metadata.tags"
            :key="index"
            class="tag"
          >
            {{ tag }}
            <button @click="removeTag(index)">×</button>
          </span>
        </div>
      </div>
    </div>
    
    <div class="config-section">
      <h3>字体设置</h3>
      <div class="config-option">
        <label>字体系列:</label>
        <select
          v-model="font.family"
          @change="updateFont"
        >
          <option value="sans-serif">Sans-serif</option>
          <option value="serif">Serif</option>
          <option value="monospace">Monospace</option>
        </select>
      </div>
      <div class="config-option">
        <label>字体大小:</label>
        <input
          type="number"
          min="12"
          max="72"
          v-model.number="font.size"
          @input="updateFont"
        />
      </div>
      <div class="config-option">
        <label>字体颜色:</label>
        <input
          type="color"
          v-model="font.color"
          @change="updateFont"
        />
      </div>
      <div class="config-option">
        <label>字体粗细:</label>
        <select
          v-model="font.weight"
          @change="updateFont"
        >
          <option value="normal">正常</option>
          <option value="bold">粗体</option>
        </select>
      </div>
      <div class="config-option">
        <label>
          <input
            type="checkbox"
            v-model="font.shadow"
            @change="updateFont"
          />
          启用阴影
        </label>
      </div>
      <div v-if="font.shadow" class="config-option">
        <label>阴影颜色:</label>
        <input
          type="color"
          v-model="font.shadowColor"
          @change="updateFont"
        />
      </div>
    </div>
    
    <div class="config-section">
      <h3>布局设置</h3>
      <div class="config-option">
        <label>内容位置:</label>
        <select
          v-model="layout.position"
          @change="updateLayout"
        >
          <option value="center">居中</option>
          <option value="top">顶部</option>
          <option value="bottom">底部</option>
        </select>
      </div>
      <div class="config-option">
        <label>最大宽度:</label>
        <input
          type="number"
          min="320"
          max="3840"
          v-model.number="layout.maxWidth"
          @input="updateLayout"
        />
      </div>
      <div class="config-option">
        <label>内边距:</label>
        <input
          type="number"
          min="0"
          max="100"
          v-model.number="layout.padding"
          @input="updateLayout"
        />
      </div>
    </div>
    
    <div class="config-actions">
      <button @click="previewVideo">预览视频</button>
      <button @click="generateVideo">生成视频</button>
    </div>
  </div>
</template>
```

## 节点示例

以下是一个视频配置节点的示例：

```javascript
// 视频配置节点示例
const videoConfigNode = {
  id: "videoConfig_012jkl",
  type: "videoConfig",
  customName: "视频配置",
  params: {
    background: {
      type: "color",
      color: "#000000",
      opacity: 1.0
    },
    size: {
      width: 1280,
      height: 720
    },
    metadata: {
      title: "日语学习视频",
      description: "基础日语对话练习",
      tags: ["日语", "学习", "对话"]
    },
    font: {
      family: "sans-serif",
      size: 32,
      color: "#FFFFFF",
      weight: "normal",
      shadow: true,
      shadowColor: "#000000"
    },
    layout: {
      position: "center",
      maxWidth: 800,
      padding: 40
    }
  },
  sourceIds: ["resource_789ghi"],
  position: { x: 700, y: 100 }
};
```

## 最佳实践

1. **背景设置**:
   - 使用深色背景和浅色文字提高可读性
   - 使用图片背景时，降低不透明度以提高文字可见度
   - 避免使用过于复杂的背景图片

2. **视频尺寸**:
   - 使用标准尺寸(720p, 1080p)以获得最佳兼容性
   - 根据目标平台选择合适的尺寸(如移动设备使用竖屏尺寸)

3. **元数据设置**:
   - 使用描述性的标题和详细的描述
   - 添加相关标签以提高可发现性

4. **字体设置**:
   - 使用清晰易读的字体
   - 字体大小应足够大以确保可读性
   - 启用阴影以提高在各种背景上的可见度

5. **布局设置**:
   - 限制内容最大宽度以提高可读性
   - 使用适当的内边距避免内容过于靠近屏幕边缘
