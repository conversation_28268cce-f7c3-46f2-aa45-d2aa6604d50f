# 分页标准文档

## 字段规范

### 请求参数
- `page`: 页码（从1开始）
- `pageSize`: 每页数量

### 响应格式
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "pageSize": 10,
    "total": 100
  }
}
```

## 前端规范

### Store状态
```javascript
pagination: {
  page: 1,
  pageSize: 10,
  total: 0
}
```

### 组件使用
```vue
<el-pagination 
  v-model:current-page="pagination.page"
  v-model:page-size="pagination.pageSize"
  :total="pagination.total"
  @size-change="handleSizeChange"
  @current-change="handleCurrentChange"
/>
```

## 后端规范

### 参数处理
```javascript
const page = parseInt(req.query.page) || 1;
const pageSize = parseInt(req.query.pageSize) || 10;
```

### 数据库查询
```javascript
const { count, rows } = await Model.findAndCountAll({
  limit: pageSize,
  offset: (page - 1) * pageSize
});
```

### 响应返回
```javascript
res.json({
  success: true,
  data: rows,
  pagination: { page, pageSize, total: count }
});
```

## 禁止使用
- `limit`, `per_page`, `current_page`, `totalPages` 等其他字段名
- 前端分页（获取全部数据后前端处理）

---

**就按这个标准执行，不要再搞其他花样。**
