# 播放策略模板系统重构文档

## 重构目标

**核心原则：**
- 不新增功能，只修复现有问题
- 保持功能和交互不变
- 重构实现逻辑，提高代码可维护性
- 重点修复模板策略使用的问题

## 当前问题分析

### 1. 模板状态持久化问题
**问题描述：** 应用模板后重新打开显示默认设置，而不是模板名称

**根本原因：**
- 模板应用时只在内存中临时设置，保存逻辑不完整
- 配置和模板ID的关联保存/加载逻辑有缺陷
- 模板状态恢复时没有验证配置匹配性

### 2. 配置字段混合存储问题
**问题描述：** 服务器控制字段和用户可编辑字段混合保存到模板

**字段分类：**

#### 用户可编辑字段（需要保存到模板）
```javascript
{
  // 播放设置
  "pauseDuration": 3000,         // 停顿时长 (0-5000ms)
  
  // 重复设置
  "repeatCount": 4,              // 重复次数 (1-10)
  "repeatSpeeds": [1,1,1,1],     // 每次重复的播放速度数组
  "repeatPauses": [3000,3000,3000,3000], // 每次重复的停顿时长数组
  
  // 翻译设置
  "enableTranslation": false,    // 是否启用翻译
  "translationLanguage": "",     // 翻译语言
  "translationPosition": 2,      // 插入位置 (1-重复次数)
  
  // 关键词设置
  "enableKeywords": false,       // 是否启用关键词
  "keywordPosition": 2,          // 插入位置 (1-重复次数)
  "keywordRepeatCount": 2        // 重复次数 (1-5)
}
```

#### 服务器控制字段（不保存到模板）
```javascript
{
  "id": "section_xxx",           // 系统生成的ID
  "title": "标题",               // 环节标题（内容固定）
  "description": "描述",         // 环节描述（内容固定）
  "processingMode": "sequence",  // 处理模式（内容固定）
  "userEditable": true,          // 权限控制（系统控制）
  "sourceIndex": 0,              // 序列索引（内容固定）
  "sourceNodeIds": ["xxx"]       // 源节点ID（内容固定）
}
```

### 3. 状态管理混乱
**问题描述：** 模板状态分散在多个地方，职责不清晰

**当前状态分布：**
- `Player.vue` 中有 `currentTemplate`
- `usePlayerConfig.js` 中也有 `currentTemplate`
- `PlaybackSettingsPanel.vue` 中通过 props 接收

### 4. 保存/加载时机不一致
**问题描述：** 临时保存和持久保存的边界不清晰

**正确的时机划分：**
- **临时保存（内存中）：** 选择模板时、修改配置时
- **持久保存（localStorage）：** 点击"应用设置"时

## 重构方案

### 1. 统一模板状态管理

**重构 usePlayerConfig.js：**
```javascript
// 明确模板应用流程
const applyTemplate = async (template) => {
  // 1. 只应用用户可编辑字段到内存
  const appliedConfig = applyTemplateToServerConfig(serverConfig.value, template);
  settings.value = appliedConfig;
  
  // 2. 设置模板状态（临时）
  currentTemplate.value = template;
  
  // 3. 不保存到localStorage
  return true;
}

// 统一的保存逻辑
const saveConfigAndTemplate = () => {
  const keys = storageKeys.value;
  
  // 同时保存配置和模板关联
  localStorage.setItem(keys.settings, JSON.stringify(settings.value));
  
  if (currentTemplate.value) {
    // 验证配置是否匹配模板
    if (isSettingsMatchTemplate(currentTemplate.value, settings.value)) {
      localStorage.setItem(keys.template, currentTemplate.value.id);
    } else {
      localStorage.removeItem(keys.template);
    }
  } else {
    localStorage.removeItem(keys.template);
  }
}

// 统一的加载逻辑
const loadConfigAndTemplate = async () => {
  const savedData = loadFromStorage();
  
  if (savedData.settings) {
    settings.value = savedData.settings;
    
    // 验证模板状态
    if (savedData.templateId) {
      const template = templateStore.templateById(savedData.templateId);
      if (template && isSettingsMatchTemplate(template, savedData.settings)) {
        currentTemplate.value = template;
      } else {
        currentTemplate.value = null;
        localStorage.removeItem(storageKeys.value.template);
      }
    }
  } else {
    applyDefaultConfig();
  }
}
```

### 2. 修复模板应用逻辑

**重构 templateService.js：**
```javascript
// 正确的模板应用逻辑
applyTemplateToContent(serverConfig, template) {
  return {
    sections: serverConfig.sections.map((serverSection, index) => {
      const templateSection = template.config.sections[index];
      if (!templateSection) return serverSection;
      
      return {
        // 保留服务器控制字段
        id: serverSection.id,
        title: serverSection.title,
        description: serverSection.description,
        processingMode: serverSection.processingMode,
        userEditable: serverSection.userEditable,
        sourceIndex: serverSection.sourceIndex,
        sourceNodeIds: serverSection.sourceNodeIds,
        
        // 只应用用户可编辑字段
        pauseDuration: templateSection.pauseDuration,
        repeatCount: templateSection.repeatCount,
        repeatSpeeds: templateSection.repeatSpeeds,
        repeatPauses: templateSection.repeatPauses,
        enableTranslation: templateSection.enableTranslation,
        translationLanguage: templateSection.translationLanguage,
        translationPosition: templateSection.translationPosition,
        enableKeywords: templateSection.enableKeywords,
        keywordPosition: templateSection.keywordPosition,
        keywordRepeatCount: templateSection.keywordRepeatCount
      };
    })
  };
}

// 修复字段验证（去除已删除的speed字段）
validateTemplateConfig(config) {
  const requiredFields = [
    "pauseDuration",
    "repeatCount",
    "enableTranslation",
    "translationLanguage", 
    "translationPosition",
    "enableKeywords",
    "keywordPosition",
    "keywordRepeatCount",
    "repeatSpeeds",
    "repeatPauses"
  ];
  // ... 验证逻辑
}

// 修复默认配置创建（去除speed字段）
createDefaultConfig(sectionCount = 1) {
  const sections = [];
  for (let i = 0; i < sectionCount; i++) {
    sections.push({
      pauseDuration: 3000,
      repeatCount: 4,
      enableTranslation: false,
      translationLanguage: "",
      translationPosition: 2,
      enableKeywords: false,
      keywordRepeatCount: 2,
      keywordPosition: 2,
      repeatSpeeds: [1.0, 1.0, 1.0, 1.0],
      repeatPauses: [3000, 3000, 3000, 3000]
    });
  }
  return { sections };
}
```

### 3. 简化 Player.vue 逻辑

**统一使用 configManager：**
```javascript
// 移除重复的模板状态管理
const { settings, currentTemplate, saveConfigAndTemplate } = configManager;

const handleSettingsSave = async () => {
  await settingsPanelRef.value.saveSettings();
  
  // 统一保存逻辑
  configManager.saveConfigAndTemplate();
  
  generateTimelineFromSettings();
  await processAudioFiles();
  showSettings.value = false;
  ElMessage.success('设置已保存');
}
```

## 重构检查清单

### 文件修改清单
- [x] `usePlayerConfig.js` - 彻底重构状态管理和保存/加载逻辑
- [x] `templateService.js` - 修复模板应用逻辑和字段验证
- [x] `Player.vue` - 简化模板状态管理
- [ ] `PlaybackSettingsPanel.vue` - 确保使用统一的状态管理

### 功能验证清单
- [ ] 选择模板后点击应用设置，重新打开能正确显示模板名称
- [ ] 基于模板修改配置后，模板状态能正确清除
- [ ] 模板只保存用户可编辑字段，不包含服务器控制字段
- [ ] 临时状态和持久状态的边界清晰
- [ ] 配置验证不包含已删除的speed字段

### 测试场景
1. **模板应用流程：** 选择模板 → 应用设置 → 关闭页面 → 重新打开 → 验证模板状态
2. **配置修改流程：** 应用模板 → 修改配置 → 验证模板状态清除
3. **配置保存流程：** 修改配置 → 应用设置 → 验证配置持久化
4. **默认配置流程：** 清空localStorage → 重新打开 → 验证默认配置

## 注意事项

1. **字段清理：** 确保所有地方都移除了已删除的speed字段
2. **状态同步：** 模板状态和配置状态必须保持同步
3. **边界清晰：** 临时状态和持久状态的操作边界要明确
4. **向后兼容：** 考虑已有localStorage数据的兼容性处理

## 重构后的优势

1. **状态管理统一：** 模板状态只在一个地方管理
2. **保存/加载一致：** 配置和模板状态同步保存和加载  
3. **逻辑清晰：** 每个函数职责单一，易于理解和维护
4. **问题修复：** 解决模板状态丢失的根本问题
5. **字段分离：** 用户可编辑字段和服务器控制字段完全分离