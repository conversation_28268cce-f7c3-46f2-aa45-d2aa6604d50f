# Echo Lab 安全防护指南

本文档提供了 Echo Lab 项目的安全防护措施和最佳实践，帮助开发者和管理员保护系统免受常见的安全威胁。

## 目录

1. [安全架构概述](#安全架构概述)
2. [防护措施](#防护措施)
3. [安全配置](#安全配置)
4. [监控与审计](#监控与审计)
5. [安全最佳实践](#安全最佳实践)
6. [安全事件响应](#安全事件响应)

## 安全架构概述

Echo Lab 采用多层次的安全防护架构，包括：

- **网络层防护**：使用 Nginx 提供的请求频率限制、IP 黑名单等功能
- **应用层防护**：使用 Express 中间件实现的请求频率限制、输入验证、CSRF 保护等
- **认证与授权**：基于 JWT 的认证系统，带有会话管理和权限控制
- **数据安全**：敏感数据加密存储，防止数据泄露
- **监控与审计**：请求日志记录、安全警报系统、失败登录监控等

## 防护措施

### 请求频率限制 (Rate Limiting)

系统实现了多层次的请求频率限制，防止暴力破解和 DoS 攻击：

1. **Nginx 层**：
   - 普通 API 请求：每秒最多 10 个请求，突发最多 20 个
   - 认证 API 请求：每分钟最多 5 个请求，突发最多 5 个
   - 每个 IP 最多同时 20 个连接

2. **应用层**：
   - 全局 API 限制：每分钟最多 100 个请求
   - 认证 API 限制：每 5 分钟最多 20 个请求
   - 验证码 API 限制：每小时最多 5 个请求
   - TTS API 限制：每 5 分钟最多 30 个请求
   - 视频导出 API 限制：每小时最多 5 个请求

### IP 黑名单

系统支持静态和动态 IP 黑名单：

1. **静态黑名单**：在 `backend/config/ip-blacklist.json` 中配置，永久生效
2. **动态黑名单**：系统自动检测可疑 IP 并临时加入黑名单，有过期时间

触发动态黑名单的条件：
- 短时间内多次验证码验证失败
- 短时间内多次请求频率超限
- 安全监控脚本检测到的可疑行为

### 输入验证

所有 API 请求都经过严格的输入验证：

1. **验证中间件**：使用 Joi 验证请求参数，防止恶意输入和注入攻击
2. **内容类型检查**：防止 MIME 类型欺骗
3. **参数长度限制**：防止缓冲区溢出攻击

### CSRF 保护

系统实现了完整的 CSRF 保护机制：

1. **CSRF 令牌**：所有修改数据的请求都需要有效的 CSRF 令牌
2. **同站 Cookie**：使用 SameSite=Strict 策略的 Cookie
3. **来源检查**：验证请求的 Referer 和 Origin 头

### 安全头部

系统设置了多种安全相关的 HTTP 头部：

1. **Content-Security-Policy**：限制资源加载来源，防止 XSS 攻击
2. **X-Content-Type-Options**：防止 MIME 类型嗅探
3. **X-Frame-Options**：防止点击劫持
4. **Strict-Transport-Security**：强制使用 HTTPS
5. **X-XSS-Protection**：启用浏览器 XSS 过滤器

## 安全配置

### 环境变量配置

安全相关的环境变量在 `.env` 文件中配置：

```
# JWT配置
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=30d

# 验证码配置
CODE_EXPIRES_IN=600
CODE_LENGTH=6
MAX_ATTEMPTS=5
IP_LOCK_TIME=3600

# 其他配置可以根据需要添加
```

### Nginx 配置

Nginx 安全配置位于 `echolab.club.conf` 文件中，包括：

- 请求频率限制
- IP 黑名单
- 安全头部
- 敏感路径保护
- HTTPS 配置

### 应用安全配置

应用安全配置位于 `backend/config/security.js` 文件中，包括：

- JWT 配置
- 验证码配置
- 请求频率限制配置
- CORS 配置
- 内容安全策略
- 失败登录配置

## 监控与审计

### 请求日志

系统记录所有 API 请求的详细信息：

- 请求 ID
- 客户端 IP
- 用户 ID（如果已登录）
- 请求路径和方法
- 响应状态码
- 响应时间
- 请求体（敏感字段已脱敏）

日志文件位于 `backend/logs/access.log`，每天自动轮转。

### 安全警报

系统会自动检测并记录可疑的安全事件：

1. **失败登录**：记录所有失败的登录尝试
2. **请求频率超限**：记录触发频率限制的请求
3. **可疑 IP 活动**：记录来自可疑 IP 的异常请求模式

安全警报存储在数据库的 `security_alerts` 表中，可通过管理后台查看。

### 安全管理

系统提供了安全管理界面，可以通过管理后台访问：

- 管理IP黑名单和白名单
- 查看和处理安全警报
- 检查特定IP的安全状态

管理员可以通过这个界面方便地管理系统安全，无需复杂的脚本和定时任务。

## 安全最佳实践

### 密码和密钥管理

- 使用强密码和密钥
- 定期轮换密钥
- 不要在代码中硬编码密钥
- 使用环境变量或安全的密钥管理服务

### 依赖管理

- 定期更新依赖包
- 使用 `npm audit` 检查依赖的安全漏洞
- 考虑使用 Dependabot 等工具自动更新依赖

### 服务器加固

- 只开放必要的端口
- 使用防火墙限制访问
- 定期更新操作系统和软件
- 禁用不必要的服务
- 使用最小权限原则

### 数据备份

- 定期备份数据库和重要文件
- 测试备份的恢复过程
- 存储备份在安全的位置

## 安全事件响应

### 检测安全事件

可能的安全事件包括：

- 异常的请求模式
- 多次失败的登录尝试
- 未授权的访问尝试
- 系统性能突然下降

### 响应步骤

1. **隔离**：限制受影响系统的访问
2. **调查**：分析日志和警报，确定攻击范围和方法
3. **修复**：解决漏洞，清除恶意代码
4. **恢复**：恢复正常服务，可能需要从备份恢复
5. **总结**：记录事件，分析原因，改进防护措施

### 联系方式

如发现安全问题，请联系：

- 管理员邮箱：<EMAIL>
- 紧急联系电话：[电话号码]
