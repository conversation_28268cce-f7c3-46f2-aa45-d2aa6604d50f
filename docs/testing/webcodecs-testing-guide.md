# WebCodecs前端视频导出测试指南

本文档提供了测试WebCodecs前端视频导出功能的详细指南。

## 🚀 快速开始

### 1. 环境要求

- **浏览器**: Chrome 94+ 或 Edge 94+
- **开发服务器**: 已启动 (`npm run dev`)
- **依赖**: mp4-muxer 已安装

### 2. 基础兼容性检查

在浏览器控制台中运行：

```javascript
// 快速兼容性检查
webCodecsQuickTest.runCompatibilityCheck()

// 详细兼容性测试
webCodecsTest.runFullCompatibilityTest()
```

### 3. 快速功能测试

```javascript
// 运行完整的导出测试（会生成并下载一个测试视频）
webCodecsQuickTest.runQuickTest()
```

## 🔧 测试步骤

### 步骤1: 检查浏览器支持

```javascript
// 基础API检查
console.log('VideoEncoder:', 'VideoEncoder' in window);
console.log('VideoFrame:', 'VideoFrame' in window);
console.log('AudioEncoder:', 'AudioEncoder' in window);
console.log('AudioData:', 'AudioData' in window);
```

预期结果：所有API都应该返回 `true`

### 步骤2: 检查依赖加载

```javascript
// 检查mp4-muxer是否正确加载
import('mp4-muxer').then(module => {
  console.log('mp4-muxer加载成功:', Object.keys(module));
}).catch(error => {
  console.error('mp4-muxer加载失败:', error);
});
```

预期结果：应该看到 `['Muxer', 'ArrayBufferTarget', ...]` 等导出

### 步骤3: 运行快速测试

```javascript
// 运行完整测试
webCodecsQuickTest.runQuickTest().then(result => {
  if (result.success) {
    console.log('✅ 测试成功!');
    console.log('视频大小:', (result.videoSize / 1024 / 1024).toFixed(2), 'MB');
  } else {
    console.error('❌ 测试失败:', result.error);
  }
});
```

预期结果：
- 控制台显示进度信息
- 生成约5秒的测试视频
- 自动下载 `webcodecs_test.mp4` 文件

### 步骤4: 在实际播放页面测试

1. 打开播放页面
2. 加载任意音频文件
3. 点击"导出视频"按钮
4. 检查是否显示绿色的"前端视频合成"提示
5. 选择导出选项并开始导出
6. 观察进度显示和最终结果

## 🐛 常见问题排查

### 问题1: mp4-muxer加载失败

**症状**: 
```
Error: MP4Muxer库加载失败: TypeError: Failed to fetch dynamically imported module
```

**解决方案**:
1. 清除Vite缓存: `rm -rf node_modules/.vite`
2. 重启开发服务器: `npm run dev`
3. 检查依赖安装: `npm list mp4-muxer`

### 问题2: WebCodecs API不支持

**症状**:
```
Error: 当前浏览器不支持WebCodecs API，请使用Chrome 94+
```

**解决方案**:
1. 升级到Chrome 94+或Edge 94+
2. 检查浏览器版本: `navigator.userAgent`
3. 确保没有禁用实验性功能

### 问题3: 视频编码失败

**症状**:
```
Error: 视频编码配置不被支持
```

**解决方案**:
1. 检查硬件加速设置
2. 尝试软件编码模式
3. 降低视频质量设置

### 问题4: 内存不足

**症状**:
- 浏览器卡顿或崩溃
- 长时间无响应

**解决方案**:
1. 减少视频时长
2. 降低视频质量
3. 关闭其他标签页释放内存

## 📊 性能基准

### 典型性能指标

| 视频时长 | 质量 | 处理时间 | 内存占用 | 输出大小 |
|---------|------|----------|----------|----------|
| 30秒 | 中等(720p) | ~15秒 | ~200MB | ~5MB |
| 1分钟 | 中等(720p) | ~30秒 | ~400MB | ~10MB |
| 2分钟 | 中等(720p) | ~60秒 | ~800MB | ~20MB |

### 优化建议

1. **短视频优先**: 建议单次导出不超过5分钟
2. **质量选择**: 对于测试使用低质量，正式导出使用中等质量
3. **内存管理**: 导出完成后刷新页面释放内存

## 🔍 调试技巧

### 启用详细日志

```javascript
// 在控制台中设置调试模式
localStorage.setItem('debug-webcodecs', 'true');

// 重新加载页面后会显示详细日志
location.reload();
```

### 监控内存使用

```javascript
// 监控内存使用情况
setInterval(() => {
  if (performance.memory) {
    console.log('内存使用:', {
      used: (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2) + 'MB',
      total: (performance.memory.totalJSHeapSize / 1024 / 1024).toFixed(2) + 'MB',
      limit: (performance.memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2) + 'MB'
    });
  }
}, 5000);
```

### 检查编码器配置

```javascript
// 测试视频编码器配置
VideoEncoder.isConfigSupported({
  codec: 'avc1.42E01E',
  width: 1280,
  height: 720,
  bitrate: 2500000,
  framerate: 30
}).then(result => {
  console.log('视频编码器支持:', result);
});

// 测试音频编码器配置
AudioEncoder.isConfigSupported({
  codec: 'mp4a.40.2',
  sampleRate: 44100,
  numberOfChannels: 1,
  bitrate: 128000
}).then(result => {
  console.log('音频编码器支持:', result);
});
```

## 📝 测试报告模板

```
# WebCodecs测试报告

**测试时间**: [日期时间]
**浏览器**: [浏览器版本]
**测试环境**: [开发/生产]

## 兼容性测试
- [ ] VideoEncoder API
- [ ] VideoFrame API  
- [ ] AudioEncoder API
- [ ] AudioData API
- [ ] mp4-muxer库加载

## 功能测试
- [ ] 快速测试通过
- [ ] 实际播放页面导出
- [ ] 字幕生成功能
- [ ] 多质量选项

## 性能测试
- 测试视频时长: [时长]
- 处理时间: [时间]
- 内存峰值: [内存]
- 输出文件大小: [大小]

## 问题记录
[记录遇到的问题和解决方案]

## 总体评价
[测试结论和建议]
```

## 🎯 下一步

测试通过后，可以：

1. 在生产环境中部署
2. 收集用户反馈
3. 监控性能指标
4. 优化用户体验
