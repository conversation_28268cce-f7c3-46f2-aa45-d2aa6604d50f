# 多维度标签系统需求文档

## 1. 项目概述

### 1.1 背景
当前系统已有基于语言学习等级的标签系统（如日语N1-N5，英语A1-C2等），为了提供更丰富的内容分类和检索能力，需要新增一个独立的多维度标签系统。

### 1.2 目标
- 保持现有语言等级标签系统不变
- 新增通用的多维度标签系统
- 提供更精准的内容分类和检索功能
- 支持标签的层级管理和扩展

### 1.3 设计原则
- **向后兼容**：不影响现有语言等级标签功能
- **独立性**：新标签系统作为独立功能模块
- **可扩展性**：支持未来新增标签维度
- **用户友好**：提供直观的标签选择和管理界面

## 2. 功能需求

### 2.1 标签维度分类

#### 2.1.1 内容类型标签
- **对话**：两人或多人对话
- **独白**：单人叙述或演讲
- **新闻**：新闻播报内容
- **故事**：故事叙述
- **访谈**：采访或访谈节目
- **讲座**：教学或学术讲座
- **动漫**：动画片段
- **电视剧**：日剧片段

#### 2.1.2 主题场景标签
- **日常生活**：购物、餐饮、交通等日常场景
- **工作职场**：办公室、会议、商务等工作场景
- **学校教育**：课堂、考试、校园生活等教育场景
- **旅游出行**：旅游、酒店、机场等出行场景
- **娱乐休闲**：电影、音乐、运动等娱乐场景
- **医疗健康**：医院、诊所、健康话题
- **文化社会**：文化、历史、社会话题

#### 2.1.3 内容特色标签
- **编辑推荐**：编辑精心挑选的优质内容
- **免费内容**：免费开放的听力材料
- **会员专享**：需要会员权限的内容
- **有字幕**：提供字幕辅助的内容
- **无字幕**：纯听力练习，无字幕
- **慢速**：语速较慢，适合初学者
- **标准语速**：正常语速
- **快速**：语速较快，挑战性内容

### 2.2 核心功能

#### 2.2.1 标签管理功能

**标签分类管理**：
- **创建分类**：管理员可创建新的标签分类
- **编辑分类**：修改分类名称、描述、排序等
- **删除限制**：系统预设分类(is_system=true)不可删除，自定义分类可删除
- **分类配置**：设置分类是否允许添加新标签

**标签创建和管理**：
- **标签创建**：在允许的分类下创建具体标签
- **标签编辑**：修改标签名称、描述、排序、颜色等
- **删除保护**：系统预设标签(is_system=true)不可删除
- **标签状态管理**：启用/禁用标签

#### 2.2.2 内容标签功能
- **多选标签**：支持为内容选择多个不同维度的标签
- **标签搜索**：在标签选择时支持搜索功能
- **标签推荐**：基于内容特征推荐相关标签
- **标签预览**：显示已选标签的分类和数量
- **标签限制**：设置每个内容最多可选标签数量

#### 2.2.3 检索和筛选功能
- **多维度筛选**：支持按不同标签维度组合筛选
- **标签云展示**：以标签云形式展示标签
- **相关内容推荐**：基于标签相似度推荐相关内容

## 3. 技术需求

### 3.1 数据库设计

#### 3.1.1 新增数据表

**标签分类表 (tag_categories)**
```sql
- id: 主键
- name: 分类名称 (如"内容主题"、"技能类型"等)
- key: 分类标识符 (如"content_theme"、"skill_type"等，用于程序识别)
- description: 分类描述
- sort_order: 排序顺序
- is_active: 是否启用
- is_system: 是否为系统预设分类 (true=不可删除，false=可删除)
- can_add_tags: 是否允许添加新标签 (true=可添加，false=固定标签)
- created_at: 创建时间
- updated_at: 更新时间
```

**通用标签表 (general_tags)**
```sql
- id: 主键
- category_id: 分类ID (外键)
- name: 标签名称
- key: 标签标识符 (用于程序识别，可选)
- description: 标签描述
- color: 标签颜色 (可选)
- sort_order: 排序顺序
- is_active: 是否启用
- is_system: 是否为系统预设标签 (true=不可删除，false=可删除)
- usage_count: 使用次数
- created_at: 创建时间
- updated_at: 更新时间
```

**内容标签关联表 (content_general_tags)**
```sql
- id: 主键
- content_id: 内容ID (外键，关联contents表)
- tag_id: 标签ID (外键，关联general_tags表)
- created_at: 创建时间
- 联合唯一索引: (content_id, tag_id)
```

#### 3.1.2 现有表修改
**Contents表无需修改**
- 保持现有的tags字段用于语言等级标签
- 通过content_general_tags关联表管理新的多维度标签
- 两套标签系统完全独立，互不影响

#### 3.1.3 数据关联说明
```
Contents (内容表)
├── tags (现有字段) → 语言等级标签 (如"N1,N2")
└── content_general_tags (关联表) → 多维度标签
    └── general_tags (新标签表)
        └── tag_categories (标签分类表)
```

### 3.2 API接口设计

#### 3.2.1 标签分类管理
```
GET /api/tag-categories - 获取所有标签分类
GET /api/tag-categories/:id - 获取指定标签分类详情
POST /api/tag-categories - 创建标签分类 (管理员)
PUT /api/tag-categories/:id - 更新标签分类 (管理员)
DELETE /api/tag-categories/:id - 删除标签分类 (管理员，仅非系统分类)
```

#### 3.2.2 通用标签管理
```
GET /api/general-tags - 获取标签列表 (支持分类筛选)
GET /api/general-tags/popular - 获取热门标签
GET /api/general-tags/by-category/:categoryId - 获取指定分类下的标签
POST /api/general-tags - 创建标签 (管理员)
PUT /api/general-tags/:id - 更新标签 (管理员)
DELETE /api/general-tags/:id - 删除标签 (管理员，仅非系统标签)
```

#### 3.2.3 内容标签关联
```
GET /api/contents/:id/general-tags - 获取内容的通用标签 (按分类分组返回)
POST /api/contents/:id/general-tags - 为内容批量设置通用标签
PUT /api/contents/:id/general-tags - 更新内容的通用标签
DELETE /api/contents/:id/general-tags/:tagId - 移除内容的指定通用标签
```

#### 3.2.4 标签数据查询
```
GET /api/contents?generalTags=tag1,tag2 - 根据通用标签筛选内容
GET /api/contents?includeGeneralTags=true - 获取内容列表时包含标签信息
GET /api/general-tags/stats - 获取标签使用统计
GET /api/tag-categories/with-tags - 获取分类及其下属标签的树形结构
```

#### 3.2.5 内容查询优化
**单个内容查询**：
```javascript
// 返回格式示例
{
  "id": "content_123",
  "name": "日语N3对话练习",
  "description": "...",
  "tags": "N3,N2", // 现有语言等级标签
  "generalTags": {
    "content_theme": [
      {"id": 1, "name": "日常生活", "color": "#blue"}
    ],
    "skill_type": [
      {"id": 5, "name": "听力训练", "color": "#green"},
      {"id": 6, "name": "口语练习", "color": "#orange"}
    ],
    "grammar_focus": [
      {"id": 10, "name": "时态语态", "color": "#purple"}
    ],
    "usage_scenario": [
      {"id": 15, "name": "日常交流", "color": "#red"}
    ]
  }
}
```

**批量内容查询**：
```javascript
// 列表查询时可选择是否包含标签
GET /api/contents?includeGeneralTags=true&page=1&limit=20

// 返回格式
{
  "contents": [
    {
      "id": "content_123",
      "name": "...",
      "tags": "N3,N2",
      "generalTags": {...} // 按分类组织的标签
    }
  ],
  "pagination": {...}
}
```

### 3.3 前端组件设计

#### 3.3.1 标签选择组件 (TagSelector)
- 分类展示标签
- 支持多选和搜索
- 显示已选标签数量
- 标签数量限制提示

#### 3.3.2 标签显示组件 (TagDisplay)
- 按分类分组显示标签
- 支持不同颜色和样式
- 点击标签进行筛选

#### 3.3.3 标签管理组件 (TagManager)
- 管理员专用组件
- 支持拖拽排序
- 批量操作功能

### 3.4 查询便利性设计

#### 3.4.1 Service层封装
```javascript
// 内容服务示例
class ContentService {
  // 获取单个内容及其标签
  async getContentWithTags(contentId) {
    const content = await this.getContent(contentId);
    const generalTags = await this.getContentGeneralTags(contentId);
    return {
      ...content,
      generalTags: this.groupTagsByCategory(generalTags)
    };
  }

  // 获取内容列表（可选包含标签）
  async getContents(params = {}) {
    const { includeGeneralTags = false, ...otherParams } = params;
    const contents = await this.queryContents(otherParams);

    if (includeGeneralTags && contents.length > 0) {
      const contentIds = contents.map(c => c.id);
      const tagsMap = await this.batchGetContentTags(contentIds);

      return contents.map(content => ({
        ...content,
        generalTags: tagsMap[content.id] || {}
      }));
    }

    return contents;
  }
}
```

#### 3.4.2 查询模式选择
1. **轻量查询**：只获取内容基本信息，不包含标签
2. **完整查询**：包含所有标签信息，适用于详情页
3. **按需查询**：根据前端需要选择是否包含标签

## 4. 用户界面需求

### 4.1 内容创建/编辑页面
- 在现有语言等级标签下方新增"内容标签"区域
- 按分类展示可选标签，支持折叠/展开
- 显示已选标签的分类统计
- 提供标签搜索和快速选择功能

### 4.2 内容浏览页面
- 在内容卡片上显示主要标签
- 支持点击标签进行筛选
- 提供标签筛选面板，按分类组织

### 4.3 管理后台页面
- 标签分类管理界面
- 标签创建和编辑界面
- 标签使用统计和分析界面

## 5. 实施计划

### 5.1 第一阶段：基础架构
- 创建数据库表结构
- 实现基础API接口
- 开发核心标签管理功能

### 5.2 第二阶段：用户界面
- 开发标签选择组件
- 集成到内容创建/编辑流程
- 实现标签显示和筛选功能

### 5.3 第三阶段：高级功能
- 标签推荐算法
- 使用统计和分析
- 性能优化和用户体验改进

## 6. 验收标准

### 6.1 功能验收
- [ ] 管理员可以创建和管理标签分类（非系统分类可删除）
- [ ] 管理员可以在各分类下创建标签（系统标签不可删除）
- [ ] 用户可以为内容选择多个维度的标签
- [ ] 用户可以基于标签筛选和搜索内容
- [ ] 现有语言等级标签功能不受影响
- [ ] 内容与标签通过关联表正确关联，无需修改contents表
- [ ] 系统预设的分类和标签受到删除保护

### 6.2 数据完整性验收
- [ ] 标签分类的is_system字段正确控制删除权限
- [ ] 标签的is_system字段正确控制删除权限
- [ ] content_general_tags关联表正确维护内容与标签的关系
- [ ] 删除内容时自动清理相关的标签关联记录
- [ ] 标签使用统计(usage_count)准确更新

### 6.3 性能验收
- [ ] 标签选择界面响应时间 < 500ms
- [ ] 标签筛选查询响应时间 < 1s
- [ ] 支持至少1000个标签的管理
- [ ] 关联表查询性能优化，支持大量内容标签关联

### 6.4 用户体验验收
- [ ] 标签选择界面直观易用
- [ ] 标签显示美观且信息清晰
- [ ] 移动端适配良好
- [ ] 系统标签有明确的视觉标识

## 7. 风险评估

### 7.1 技术风险
- **数据迁移风险**：新旧标签系统并存可能导致数据不一致
- **性能风险**：大量标签可能影响查询性能
- **兼容性风险**：新功能可能与现有功能冲突

### 7.2 缓解措施
- 充分测试新旧系统的兼容性
- 实施数据库索引优化
- 分阶段发布，逐步验证功能稳定性

## 8. 数据初始化

### 8.1 系统预设标签分类
```sql
-- 系统预设的4个标签分类，不可删除
INSERT INTO tag_categories (name, key, description, sort_order, is_system, can_add_tags) VALUES
('内容主题', 'content_theme', '内容的主题分类', 1, true, true),
('技能类型', 'skill_type', '训练的技能类型', 2, true, true),
('语法重点', 'grammar_focus', '重点语法内容', 3, true, true),
('使用场景', 'usage_scenario', '适用的使用场景', 4, true, true);
```

### 8.2 系统预设标签
每个分类下预设一些基础标签，标记为系统标签(is_system=true)，管理员可以添加更多标签。

## 9. 后续扩展

### 9.1 智能标签
- 基于AI的自动标签推荐
- 内容分析自动打标签

### 9.2 个性化功能
- 用户自定义标签分类（is_system=false）
- 基于标签的个性化推荐

### 9.3 社交功能
- 标签热度排行
- 用户标签偏好分析

## 10. 关键设计说明

### 10.1 为什么不修改Contents表？
1. **向后兼容**：保持现有语言等级标签系统完全不变
2. **数据安全**：避免修改核心业务表结构的风险
3. **灵活性**：关联表设计更灵活，支持复杂的多对多关系
4. **性能优化**：可以针对关联表单独优化索引和查询

### 10.1.1 业界标签系统设计模式对比

**WordPress模式**（我们采用的方案）：
- ✅ 成熟稳定，被广泛验证
- ✅ 支持多维度分类，扩展性强
- ✅ 查询灵活，支持复杂的标签组合
- ✅ 有完整的管理界面和API

**简单关联表模式**（如Stack Overflow）：
- ✅ 实现简单，性能好
- ❌ 扩展性有限，难以支持多维度
- ❌ 缺乏分类管理功能

**Drupal Taxonomy模式**：
- ✅ 功能最强大，支持复杂层级
- ❌ 复杂度高，学习成本大
- ❌ 对于我们的需求过于复杂

**结论**：我们的方案在功能性、复杂度和扩展性之间取得了最佳平衡

### 10.2 系统标签保护机制
1. **分类保护**：is_system=true的分类不可删除，确保核心分类稳定
2. **标签保护**：is_system=true的标签不可删除，保护基础标签
3. **扩展性**：允许管理员在系统分类下添加新标签
4. **自定义**：支持创建自定义分类和标签，满足特殊需求

### 10.3 查询性能优化策略

#### 10.3.1 数据库层面优化
```sql
-- 关联表索引优化
CREATE INDEX idx_content_general_tags_content_id ON content_general_tags(content_id);
CREATE INDEX idx_content_general_tags_tag_id ON content_general_tags(tag_id);
CREATE UNIQUE INDEX idx_content_general_tags_unique ON content_general_tags(content_id, tag_id);

-- 标签表索引优化
CREATE INDEX idx_general_tags_category_id ON general_tags(category_id);
CREATE INDEX idx_general_tags_active ON general_tags(is_active);
```

#### 10.3.2 查询便利性评估
**与其他方案对比**：

**WordPress查询方式**：
```php
// 获取文章的所有标签（按分类分组）
$terms = wp_get_object_terms($post_id, ['category', 'post_tag', 'custom_taxonomy']);
```

**我们的查询方式**：
```javascript
// 获取内容的所有标签（按分类分组）
GET /api/contents/123?includeGeneralTags=true
```

**优势对比**：
- ✅ API设计更RESTful，前后端分离友好
- ✅ 返回结构化数据，前端处理更方便
- ✅ 支持批量查询优化
- ✅ 缓存策略更灵活

#### 10.3.2 查询优化方案
**方案1：JOIN查询（推荐用于单个内容）**
```sql
-- 获取单个内容的所有标签，按分类分组
SELECT
  c.id as content_id,
  c.name as content_name,
  c.tags as language_tags,
  tc.key as category_key,
  tc.name as category_name,
  gt.id as tag_id,
  gt.name as tag_name,
  gt.color as tag_color
FROM contents c
LEFT JOIN content_general_tags cgt ON c.id = cgt.content_id
LEFT JOIN general_tags gt ON cgt.tag_id = gt.id AND gt.is_active = true
LEFT JOIN tag_categories tc ON gt.category_id = tc.id AND tc.is_active = true
WHERE c.id = 'content_123'
ORDER BY tc.sort_order, gt.sort_order;
```

**方案2：分步查询（推荐用于列表查询）**
```sql
-- 第一步：获取内容列表
SELECT id, name, tags FROM contents WHERE ... LIMIT 20;

-- 第二步：批量获取这些内容的标签
SELECT
  cgt.content_id,
  tc.key as category_key,
  gt.id, gt.name, gt.color
FROM content_general_tags cgt
JOIN general_tags gt ON cgt.tag_id = gt.id AND gt.is_active = true
JOIN tag_categories tc ON gt.category_id = tc.id AND tc.is_active = true
WHERE cgt.content_id IN ('content_123', 'content_124', ...)
ORDER BY tc.sort_order, gt.sort_order;
```

#### 10.3.3 缓存策略
1. **Redis缓存**：缓存热门内容的标签信息
2. **应用层缓存**：缓存标签分类和标签的基础信息
3. **CDN缓存**：缓存标签的静态配置数据

#### 10.3.4 前端优化
1. **懒加载**：列表页面可选择是否加载标签信息
2. **批量请求**：一次请求获取多个内容的标签
3. **本地缓存**：缓存标签分类和标签的基础配置

### 10.4 业界最佳实践总结

#### 10.4.1 成功案例分析
**WordPress**：
- 全球40%+网站使用，标签系统经过大规模验证
- 支持categories、tags、custom taxonomies多种分类法
- 查询API成熟，性能优化充分

**Medium**：
- 简化的标签系统，用户体验优秀
- 标签推荐算法先进
- 社交化标签功能

**GitHub Topics**：
- 扁平化标签结构，简单有效
- 标签发现和推荐机制完善
- 与搜索系统深度集成

#### 10.4.2 我们方案的竞争优势
1. **技术成熟度**：基于WordPress模式，经过大规模验证
2. **功能完整性**：支持多维度分类，满足复杂需求
3. **扩展性**：预留足够的扩展空间，支持未来需求
4. **用户体验**：API设计现代化，前端集成友好
5. **维护成本**：设计简洁，维护成本可控
