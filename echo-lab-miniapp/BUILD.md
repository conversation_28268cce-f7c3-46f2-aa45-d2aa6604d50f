# 构建说明

## ⚠️ 重要提醒

**此项目使用 uniCloud 云服务，必须通过 HBuilderX 开发工具运行，不能使用 CLI 命令！**

## 环境配置

### 开发环境

#### 小程序开发（必须使用 HBuilderX）
1. 用 HBuilderX 打开项目
2. 确保 uniCloud 服务空间已关联（echo-lab）
3. 右键项目 → 运行 → 运行到小程序模拟器 → 微信开发者工具

#### H5 开发（可使用 CLI）
```bash
# 启动 H5 开发（支持 CLI）
npm run dev:h5           # H5
npm run dev:h5:ssr       # H5 SSR
```

### 生产环境构建

#### 小程序构建（必须使用 HBuilderX）
1. 用 HBuilderX 打开项目
2. 右键项目 → 发行 → 小程序-微信
3. 在微信开发者工具中上传代码

#### H5 构建（可使用 CLI）
```bash
# 构建 H5（支持 CLI）
npm run build:h5         # H5
npm run build:h5:ssr     # H5 SSR
```

## 构建优化

### 开发环境 vs 生产环境

#### 开发环境（HBuilderX 运行）
- ❌ **保留console日志**：所有 `console.log`、`console.error` 等都会保留
- ❌ **保留debugger**：所有 `debugger` 语句都会保留
- ❌ **代码未压缩**：便于调试
- ❌ **保留注释**：便于开发
- ✅ **启用sourcemap**：便于调试

#### 生产环境（HBuilderX 发行）
- ✅ **移除console日志**：自动移除所有 `console.log`、`console.error` 等
- ✅ **移除debugger**：自动移除所有 `debugger` 语句
- ✅ **代码压缩**：使用内置压缩工具
- ✅ **移除注释**：生产版本移除所有注释
- ✅ **关闭sourcemap**：减少包体积

### 重要提醒
⚠️ **开发时console日志会保留**：HBuilderX运行时不会删除console，只有发行时才会删除

## 构建检查

### 构建前检查
```bash
# 检查代码中的console语句（可选）
grep -r "console\." src/ --include="*.js" --include="*.vue" --include="*.ts"

# 检查代码中的debugger语句（可选）
grep -r "debugger" src/ --include="*.js" --include="*.vue" --include="*.ts"
```

### 构建后验证
1. 检查 `dist/build/mp-weixin` 目录
2. 确认文件已压缩
3. 确认无console日志输出
4. 在微信开发者工具中测试

## 发布流程

1. **代码检查**：确保所有功能正常
2. **清理console日志**：检查并清理不必要的console输出（可选，发行时会自动删除）
3. **云函数部署**：在HBuilderX中上传最新的云函数
4. **生产构建**：在HBuilderX中右键项目 → 发行 → 小程序-微信
5. **测试验证**：在微信开发者工具中测试发行版本
6. **上传审核**：通过微信开发者工具上传代码
7. **提交审核**：在微信公众平台提交审核

## 注意事项

### 关于console日志
- **开发时**：HBuilderX运行时console日志会保留，便于调试
- **发行时**：HBuilderX发行时会自动移除所有console日志
- **建议**：重要信息使用 `uni.showToast` 等小程序API，而不是console

### 其他注意事项
- **依赖安装**：构建前确保所有依赖已安装：`npm install`
- **API请求**：所有接口请求通过云函数代理，无需配置API地址
- **云函数部署**：需要单独在HBuilderX中部署云函数，不包含在前端构建中
- **不要使用CLI**：小程序必须使用HBuilderX，CLI命令已被禁用
