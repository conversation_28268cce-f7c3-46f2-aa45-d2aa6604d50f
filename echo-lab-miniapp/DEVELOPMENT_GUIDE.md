# 开发指南

## ⚠️ 重要说明

**此项目使用 uniCloud 云服务，小程序开发必须使用 HBuilderX，不能使用 CLI 命令！**

## 开发环境对比

### HBuilderX vs CLI 命令

| 功能 | HBuilderX | CLI 命令 |
|------|-----------|----------|
| 小程序开发 | ✅ 支持 | ❌ 不支持 |
| uniCloud 集成 | ✅ 完整支持 | ❌ 不支持 |
| 云函数调试 | ✅ 支持 | ❌ 不支持 |
| H5 开发 | ✅ 支持 | ✅ 支持 |
| console 日志 | 开发时保留，发行时删除 | 根据配置决定 |
| 热重载 | ✅ 支持 | ✅ 支持 |

## 正确的开发流程

### 1. 小程序开发（推荐）

#### 环境准备
1. 安装 HBuilderX
2. 安装微信开发者工具
3. 配置 uniCloud 服务空间

#### 开发步骤
1. 用 HBuilderX 打开项目
2. 确保 uniCloud 服务空间已关联（echo-lab）
3. 右键项目 → 运行 → 运行到小程序模拟器 → 微信开发者工具
4. 在微信开发者工具中查看效果

#### 云函数开发
1. 在 HBuilderX 中编辑云函数代码
2. 右键云函数 → 上传并运行
3. 在小程序中测试云函数调用

### 2. H5 开发（备选）

如果只需要开发 H5 版本，可以使用 CLI：

```bash
# 开发环境
npm run dev:h5

# 生产构建
npm run build:h5
```

## 常见问题

### Q: 为什么不能使用 `npm run dev:mp-weixin`？
A: 因为项目使用了 uniCloud，CLI 无法处理云服务配置，会导致：
- uniCloud 初始化失败
- 云函数调用失败
- 项目无法正常运行

### Q: 开发时 console 日志会显示吗？
A: 是的，HBuilderX 开发时会保留所有 console 日志，便于调试。只有在发行时才会自动删除。

### Q: 如何部署云函数？
A: 在 HBuilderX 中：
1. 展开 uniCloud-aliyun/cloudfunctions
2. 右键要部署的云函数
3. 选择"上传并运行"

### Q: 如何发布小程序？
A: 在 HBuilderX 中：
1. 右键项目 → 发行 → 小程序-微信
2. 在微信开发者工具中上传代码
3. 在微信公众平台提交审核

## 调试技巧

### 1. 云函数调试
- 在 HBuilderX 中查看云函数日志
- 使用 `console.log` 输出调试信息
- 在微信开发者工具的控制台查看错误

### 2. 小程序调试
- 使用微信开发者工具的调试器
- 查看 Network 面板的请求
- 使用 Storage 面板查看本地存储

### 3. 性能优化
- 开发时保留 console 便于调试
- 发行前检查代码质量
- 测试云函数响应时间

## 项目结构说明

```
echo-lab-miniapp/
├── src/                    # 源码目录
│   ├── pages/             # 页面文件
│   ├── components/        # 组件
│   ├── utils/             # 工具函数
│   │   └── api.js         # 云函数调用封装
│   └── services/          # 业务服务
├── uniCloud-aliyun/       # 云服务目录
│   ├── cloudfunctions/    # 云函数
│   │   └── api-proxy/     # API代理云函数
│   └── database/          # 数据库
├── static/                # 静态资源
└── dist/                  # 构建输出（HBuilderX生成）
```

## 最佳实践

1. **始终使用 HBuilderX** 进行小程序开发
2. **定期备份云函数** 代码
3. **测试云函数** 在上传前本地测试
4. **合理使用 console** 开发时可以使用，发行时会自动删除
5. **关注性能** 云函数调用有延迟，合理设计API
