# Echo Lab 小程序项目

## ⚠️ 重要说明

### 必须使用云函数代理
**域名 `echolab.club` 没有备案，小程序无法直接访问境外服务器，必须通过 UniCloud 云函数代理转发请求。请勿尝试直接调用HTTP接口。**

### 必须使用 HBuilderX 运行
**使用 uniCloud 的项目必须通过 HBuilderX 运行，不能使用 CLI 命令（npm run dev:mp-weixin）运行，否则云函数无法正常工作。**

## 🚀 开发流程

### 1. 环境要求
- HBuilderX 开发工具
- 微信开发者工具
- UniCloud 账号（已配置阿里云服务空间）

### 2. 启动项目
1. 用 HBuilderX 打开项目
2. 确保 uniCloud 服务空间已关联（echo-lab）
3. 右键项目 → 运行 → 运行到小程序模拟器 → 微信开发者工具

### 3. 云函数配置
- 云函数名称：`api-proxy`
- 服务空间：`echo-lab` (阿里云)
- 作用：代理转发API请求到 `https://echolab.club`

## 📁 项目结构

```
echo-lab-miniapp/
├── src/
│   ├── pages/              # 页面文件
│   ├── components/         # 组件
│   ├── services/          # API服务
│   ├── utils/             # 工具函数
│   ├── config/            # 配置文件
│   └── styles/            # 样式文件
├── uniCloud-aliyun/       # 云函数目录
│   └── cloudfunctions/
│       └── api-proxy/     # API代理云函数
├── static/                # 静态资源
└── dist/                  # 构建输出
```

## 🔧 开发注意事项

1. **API调用**：所有API请求都通过 `src/utils/api.js` 中的云函数代理
2. **静态资源**：图标文件放在 `src/static/` 目录下
3. **页面配置**：在 `src/pages.json` 中配置页面和tabBar
4. **云函数**：修改云函数后需要重新上传到服务空间

## 🐛 常见问题

### Q: 出现 "uni-app cli项目内使用uniCloud需要使用HBuilderX的运行菜单运行项目" 错误
A: 这是正常的，必须使用 HBuilderX 运行项目，不能使用 npm 命令。

### Q: API请求失败
A: 检查云函数是否正常运行，确保服务空间已关联。

### Q: tabBar图标不显示
A: 确保图标文件在 `src/static/` 目录下，构建时会自动复制。

## 📞 技术支持

如有问题请查看：
- UniApp 官方文档
- UniCloud 开发指南
- 微信小程序开发文档
