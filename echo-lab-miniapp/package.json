{"name": "echo-lab-miniapp", "version": "1.0.0", "description": "Echo Lab 听力练习小程序", "main": "main.js", "scripts": {"⚠️-warning": "echo '⚠️ 此项目使用uniCloud，必须通过HBuilderX运行，CLI命令无法正常工作！'", "dev:h5": "uni -p h5", "dev:h5:ssr": "uni -p h5:ssr", "build:h5": "uni build -p h5 --mode production", "build:h5:ssr": "uni build -p h5:ssr --mode production", "dev:mp-weixin": "echo '⚠️ 错误：uniCloud项目必须使用HBuilderX运行！请用HBuilderX打开项目并运行到微信开发者工具' && exit 1", "build:mp-weixin": "echo '⚠️ 错误：uniCloud项目必须使用HBuilderX构建！请用HBuilderX打开项目并发行到微信小程序' && exit 1", "dev:mp-alipay": "echo '⚠️ 错误：uniCloud项目必须使用HBuilderX运行！' && exit 1", "dev:mp-baidu": "echo '⚠️ 错误：uniCloud项目必须使用HBuilderX运行！' && exit 1", "dev:mp-qq": "echo '⚠️ 错误：uniCloud项目必须使用HBuilderX运行！' && exit 1", "dev:mp-toutiao": "echo '⚠️ 错误：uniCloud项目必须使用HBuilderX运行！' && exit 1", "build:mp-alipay": "echo '⚠️ 错误：uniCloud项目必须使用HBuilderX构建！' && exit 1", "build:mp-baidu": "echo '⚠️ 错误：uniCloud项目必须使用HBuilderX构建！' && exit 1", "build:mp-qq": "echo '⚠️ 错误：uniCloud项目必须使用HBuilderX构建！' && exit 1", "build:mp-toutiao": "echo '⚠️ 错误：uniCloud项目必须使用HBuilderX构建！' && exit 1"}, "keywords": ["uniapp", "miniprogram", "japanese", "learning"], "author": "", "license": "MIT", "dependencies": {"@dcloudio/uni-app": "3.0.0-4020920240930001", "@dcloudio/uni-app-plus": "3.0.0-4020920240930001", "@dcloudio/uni-components": "3.0.0-4020920240930001", "@dcloudio/uni-h5": "3.0.0-4020920240930001", "@dcloudio/uni-mp-alipay": "3.0.0-4020920240930001", "@dcloudio/uni-mp-baidu": "3.0.0-4020920240930001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4020920240930001", "@dcloudio/uni-mp-lark": "3.0.0-4020920240930001", "@dcloudio/uni-mp-qq": "3.0.0-4020920240930001", "@dcloudio/uni-mp-toutiao": "3.0.0-4020920240930001", "@dcloudio/uni-mp-weixin": "3.0.0-4020920240930001", "@dcloudio/uni-mp-xhs": "3.0.0-4020920240930001", "@dcloudio/uni-quickapp-webview": "3.0.0-4020920240930001", "@vitejs/plugin-vue": "^5.2.4", "crypto-js": "^4.2.0", "pinia": "^2.0.36"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4020920240930001", "@dcloudio/uni-cli-shared": "3.0.0-4020920240930001", "@dcloudio/vite-plugin-uni": "3.0.0-4020920240930001", "@vue/runtime-core": "^3.4.21", "typescript": "^5.4.5", "vite": "5.2.8", "vue": "^3.4.21", "vue-tsc": "^2.0.6"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}