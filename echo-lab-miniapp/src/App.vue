<template>
  <view></view>
</template>

<script>
import cacheManager from './utils/cacheManager.js'

export default {
  onLaunch() {
    console.log('🚀 小程序启动')

    // 延迟执行缓存清理，避免影响启动速度
    setTimeout(() => {
      cacheManager.startupCleanup()
    }, 3000) // 延长到3秒，确保启动流畅
  }
}
</script>

<style>
/* 引入全局CSS变量 */
@import './styles/variables.css';

/* 重置page默认样式 - 解决导航栏下方空隙问题 */
page {
  background-color: #f5f5f5;
  padding: 0 !important;
  margin: 0 !important;
  box-sizing: border-box;
}

/* 重置view默认样式 */
view {
  box-sizing: border-box;
}
</style>
