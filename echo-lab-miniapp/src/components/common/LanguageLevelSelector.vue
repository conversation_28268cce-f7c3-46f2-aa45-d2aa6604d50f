<template>
  <view class="language-level-selector">
    <view class="selector-title" v-if="title">{{ title }}</view>
    
    <view class="columns-container">
      <!-- 左列：语言选择 -->
      <view class="column language-column">
        <view class="column-header">语言</view>
        <view class="column-content">
          <view
            v-for="lang in availableLanguages"
            :key="lang.value"
            class="option-item"
            :class="{ selected: selectedLanguage === lang.value }"
            @click="selectLanguage(lang.value)"
          >
            <view class="radio-icon">{{ selectedLanguage === lang.value ? '●' : '○' }}</view>
            <text class="option-text">{{ lang.label }}</text>
          </view>
        </view>
      </view>

      <!-- 右列：等级选择 -->
      <view class="column level-column">
        <view class="column-header">等级</view>
        <view class="column-content">
          <view v-if="!selectedLanguage" class="empty-hint">
            <text class="hint-text">请先选择语言</text>
          </view>
          <view v-else>
            <view
              v-for="level in availableLevels"
              :key="level.key"
              class="option-item"
              :class="{ selected: selectedLevels.includes(level.key) }"
              @click="toggleLevel(level.key)"
            >
              <view class="checkbox-icon">{{ selectedLevels.includes(level.key) ? '☑' : '☐' }}</view>
              <text class="option-text">{{ level.name }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { SUPPORTED_LANGUAGES } from '../../config/languages.js'
import { getLanguageLevels } from '../../config/languageLevels.js'

// Props
const props = defineProps({
  // 当前选中的语言
  language: {
    type: String,
    default: null
  },
  // 当前选中的等级数组
  levels: {
    type: Array,
    default: () => []
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 是否允许清空语言选择
  allowClearLanguage: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:language', 'update:levels', 'change'])

// 内部状态
const selectedLanguage = ref(props.language)
const selectedLevels = ref([...props.levels])

// 可用选项
const availableLanguages = computed(() => SUPPORTED_LANGUAGES.filter(lang => lang.status === 'available'))
const availableLevels = computed(() => {
  if (!selectedLanguage.value) return []
  return getLanguageLevels(selectedLanguage.value)
})

// 监听props变化
watch(() => props.language, (newVal) => {
  selectedLanguage.value = newVal
})

watch(() => props.levels, (newVal) => {
  selectedLevels.value = [...newVal]
})

// 选择语言
const selectLanguage = (langValue) => {
  // 如果点击已选中的语言且允许清空，则清空选择
  if (selectedLanguage.value === langValue && props.allowClearLanguage) {
    selectedLanguage.value = null
    selectedLevels.value = []
  } else {
    selectedLanguage.value = langValue
    // 切换语言时清空等级选择
    selectedLevels.value = []
  }
  
  // 发出事件
  emit('update:language', selectedLanguage.value)
  emit('update:levels', selectedLevels.value)
  emit('change', {
    language: selectedLanguage.value,
    levels: selectedLevels.value
  })
}

// 切换等级
const toggleLevel = (levelKey) => {
  const index = selectedLevels.value.indexOf(levelKey)
  if (index > -1) {
    selectedLevels.value.splice(index, 1)
  } else {
    selectedLevels.value.push(levelKey)
  }
  
  // 发出事件
  emit('update:levels', selectedLevels.value)
  emit('change', {
    language: selectedLanguage.value,
    levels: selectedLevels.value
  })
}

// 重置选择
const reset = () => {
  selectedLanguage.value = null
  selectedLevels.value = []
  emit('update:language', null)
  emit('update:levels', [])
  emit('change', {
    language: null,
    levels: []
  })
}

// 暴露方法给父组件
defineExpose({
  reset
})
</script>

<style scoped>
.language-level-selector {
  width: 100%;
}

.selector-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.columns-container {
  display: flex;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  overflow: hidden;
  height: 400rpx;
}

.column {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.language-column {
  border-right: 1rpx solid #e9ecef;
}

.column-header {
  padding: 20rpx;
  background: #f8f9fa;
  font-size: 26rpx;
  font-weight: 500;
  color: #495057;
  text-align: center;
  border-bottom: 1rpx solid #e9ecef;
}

.column-content {
  flex: 1;
  overflow-y: auto;
  background: white;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 20rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.option-item:last-child {
  border-bottom: none;
}

.option-item.selected {
  background: #f0f8ff;
}

.radio-icon,
.checkbox-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  color: #667eea;
  width: 32rpx;
  text-align: center;
}

.option-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.empty-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40rpx 20rpx;
}

.hint-text {
  font-size: 26rpx;
  color: #999;
}
</style>
