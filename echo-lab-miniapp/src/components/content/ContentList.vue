<template>
  <view class="content-list">
    <!-- 搜索和筛选区域 -->
    <view v-if="showSearch || showFilter" class="content-header">
      <!-- 搜索框和筛选按钮在同一行 -->
      <view class="search-and-filter">
        <!-- 搜索框 -->
        <view v-if="showSearch" class="search-control">
          <view class="search-input-wrapper">
            <input
              v-model="searchQuery"
              :placeholder="searchPlaceholder"
              class="search-input"
              @confirm="handleSearch"
            />
            <view v-if="searchQuery" class="search-clear-btn" @tap="clearSearch">✕</view>
          </view>
          <button class="search-btn" @click="handleSearch" :disabled="searchLoading">
            {{ searchLoading ? '搜索中...' : '搜索' }}
          </button>
        </view>

        <!-- 筛选按钮 -->
        <button v-if="showFilter" class="filter-btn" @click="showFilterModal = true">
          按难度筛选
          <text v-if="selectedTags.length > 0" class="filter-count">({{ selectedTags.length }})</text>
        </button>
      </view>
    </view>

    <!-- 内容网格 -->
    <view class="content-grid">
      <!-- 错误状态 -->
      <view v-if="error" class="error">
        <text class="error-text">{{ error }}</text>
        <button class="retry-btn" @click="handleRetry">重试</button>
      </view>

      <!-- 空状态 -->
      <view v-else-if="contentList.length === 0" class="empty">
        <text>{{ emptyText }}</text>
      </view>

      <!-- 内容网格 -->
      <view v-else-if="contentList.length > 0">
        <view class="grid-container">
          <ContentCard
            v-for="item in contentList"
            :key="item.id"
            :content="item"
            :show-author="true"
            :show-view-count="true"
            :show-date="true"
            @click="playContent"
          />
        </view>
        
        <!-- 加载更多 -->
        <view v-if="hasMore" class="load-more">
          <button v-if="!loadingMore" class="load-more-btn" @click="handleLoadMore">加载更多</button>
          <text v-else>加载更多...</text>
        </view>
        
        <!-- 没有更多数据 -->
        <view v-else-if="contentList.length > 0" class="no-more">
          <text>没有更多内容了</text>
        </view>
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <view v-if="showFilterModal" class="filter-modal" @touchmove.prevent>
      <view class="modal-overlay" @click="showFilterModal = false"></view>
      <view class="modal-content" @touchmove.stop>
        <view class="modal-header">
          <text class="modal-title">按难度筛选</text>
          <button class="modal-close" @click="showFilterModal = false">×</button>
        </view>
        <view class="modal-body">
          <view class="filter-options">
            <view
              v-for="tag in availableTags"
              :key="tag.key"
              class="filter-option"
              @click="toggleTag(tag.key)"
            >
              <view class="option-content">
                <text class="option-text">{{ tag.name }}</text>
                <view class="option-checkbox" :class="{ checked: selectedTags.includes(tag.key) }">
                  <text v-if="selectedTags.includes(tag.key)" class="check-icon">✓</text>
                </view>
              </view>
            </view>
            
            <view v-if="availableTags.length === 0" class="no-options">
              <text>暂无可用筛选选项</text>
            </view>
          </view>
        </view>
        <view class="modal-footer">
          <button class="clear-btn" @click="clearFilters">清除</button>
          <button class="apply-btn" @click="applyFilters">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue'
import ContentCard from '../ui/ContentCard.vue'

// Props
const props = defineProps({
  // 数据
  contentList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  loadingMore: {
    type: Boolean,
    default: false
  },
  hasMore: {
    type: Boolean,
    default: true
  },
  error: {
    type: String,
    default: null
  },
  
  // 功能开关
  showSearch: {
    type: Boolean,
    default: true
  },
  showFilter: {
    type: Boolean,
    default: true
  },
  
  // 文本配置
  searchPlaceholder: {
    type: String,
    default: '搜索内容...'
  },
  loadingText: {
    type: String,
    default: '正在加载内容...'
  },
  emptyText: {
    type: String,
    default: '暂无内容'
  },
  
  // 筛选选项
  availableTags: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits([
  'search',
  'load-more',
  'retry',
  'play-content',
  'filter-change'
])

// 响应式数据
const searchQuery = ref('')
const searchLoading = ref(false)
const showFilterModal = ref(false)
const selectedTags = ref([])

// 搜索
const handleSearch = async () => {
  if (searchLoading.value) return
  searchLoading.value = true
  try {
    emit('search', searchQuery.value)
  } finally {
    searchLoading.value = false
  }
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  emit('search', '')
}

// 加载更多
const handleLoadMore = () => {
  emit('load-more')
}

// 重试
const handleRetry = () => {
  emit('retry')
}

// 播放内容
const playContent = (item) => {
  emit('play-content', item)
}

// 标签筛选
const toggleTag = (tagKey) => {
  const index = selectedTags.value.indexOf(tagKey)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tagKey)
  }
}

const clearFilters = () => {
  selectedTags.value = []
  searchQuery.value = ''
}

const applyFilters = () => {
  showFilterModal.value = false
  emit('filter-change', {
    tags: selectedTags.value,
    search: searchQuery.value
  })
}

// 监听筛选变化
watch(selectedTags, () => {
  emit('filter-change', {
    tags: selectedTags.value,
    search: searchQuery.value
  })
}, { deep: true })


</script>

<style scoped>
/* 引入全局CSS变量 */
@import '../../styles/variables.css';

.content-list {
  width: 100%;
}

.content-header {
  background: white;
  padding: 24rpx;
  margin-bottom: 16rpx;
  border-radius: 12rpx;
}

.search-and-filter {
  display: flex;
  gap: 12rpx;
  align-items: center;
}

.search-control {
  flex: 1;
  display: flex;
  gap: 12rpx;
  align-items: center;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  height: 72rpx;
  padding: 0 40rpx 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 36rpx;
  font-size: var(--font-size-input-text);
  background: #f8f9fa;
  line-height: 72rpx;
  box-sizing: border-box;
}

.search-clear-btn {
  position: absolute;
  right: 20rpx;
  font-size: 24rpx;
  color: #999;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f0f0f0;
  cursor: pointer;
  z-index: 10;
}

.search-btn {
  height: 72rpx;
  padding: 0 24rpx;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 36rpx;
  font-size: var(--font-size-button-medium);
  white-space: nowrap;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  min-width: 120rpx;
}

.filter-btn {
  height: 72rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border: 2rpx solid #e0e0e0;
  border-radius: 36rpx;
  font-size: var(--font-size-button-medium);
  color: var(--color-text-regular);
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  flex-shrink: 0;
  box-sizing: border-box;
  line-height: 1;
}

.filter-count {
  color: var(--color-primary);
  margin-left: 8rpx;
}

/* 内容网格布局 */
.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.loading, .empty, .no-more {
  text-align: center;
  padding: 80rpx 0;
  color: var(--color-text-placeholder);
  font-size: var(--font-size-sm);
}

.error {
  text-align: center;
  padding: 80rpx 0;
}

.error-text {
  color: var(--color-danger);
  font-size: var(--font-size-sm);
  margin-bottom: 24rpx;
  display: block;
}

.retry-btn {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 24rpx 80rpx;
  font-size: 32rpx;
  font-weight: normal;
  width: 300rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.retry-btn:active {
  background: #0056cc;
}

.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.load-more-btn {
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: var(--font-size-sm);
}

/* 筛选弹窗样式 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  background: rgba(0, 0, 0, 0.5);
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.modal-content {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 80vh;
  position: relative;
}

.modal-header {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.modal-close {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 48rpx;
  height: 48rpx;
  border: none;
  background: #f5f5f5;
  border-radius: 50%;
  font-size: 32rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.modal-body {
  padding: 24rpx;
  max-height: 50vh;
  overflow-y: auto;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.filter-option {
  padding: 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  background: #fafafa;
}

.option-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
}

.option-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-checkbox.checked {
  background: var(--color-primary);
  border-color: var(--color-primary);
}

.check-icon {
  color: white;
  font-size: 24rpx;
}

.no-options {
  text-align: center;
  padding: 40rpx 0;
  color: var(--color-text-placeholder);
  font-size: var(--font-size-sm);
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-footer .clear-btn,
.modal-footer .apply-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  border-radius: 44rpx;
  font-size: var(--font-size-button-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  box-sizing: border-box;
}

.modal-footer .clear-btn {
  background: #f5f5f5;
  color: var(--color-text-regular);
}

.modal-footer .apply-btn {
  background: var(--color-primary);
  color: white;
}
</style>
