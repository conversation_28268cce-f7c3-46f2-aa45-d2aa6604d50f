<template>
  <view class="config-manager">
    <!-- 管理器头部 -->
    <view class="manager-header">
      <view class="header-title">配置管理</view>
      <view class="close-btn" @click="closeManager">×</view>
    </view>

    <!-- 功能选项卡 -->
    <view class="manager-tabs">
      <view 
        v-for="tab in tabs" 
        :key="tab.key"
        class="tab-item"
        :class="{ 'active': activeTab === tab.key }"
        @click="switchTab(tab.key)"
      >
        <text class="tab-text">{{ tab.label }}</text>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="manager-content" scroll-y>
      <!-- 导入导出 -->
      <view v-if="activeTab === 'import-export'" class="content-section">
        <view class="section-title">配置导入导出</view>
        
        <!-- 导出配置 -->
        <view class="action-card">
          <view class="card-header">
            <text class="card-title">导出配置</text>
            <text class="card-desc">将当前播放策略配置导出为文件</text>
          </view>
          <button class="action-btn export-btn" @click="handleExportConfig">
            <text class="btn-icon">📤</text>
            <text class="btn-text">导出配置</text>
          </button>
        </view>

        <!-- 导入配置 -->
        <view class="action-card">
          <view class="card-header">
            <text class="card-title">导入配置</text>
            <text class="card-desc">从文件导入播放策略配置</text>
          </view>
          <button class="action-btn import-btn" @click="handleImportConfig">
            <text class="btn-icon">📥</text>
            <text class="btn-text">选择文件导入</text>
          </button>
        </view>

        <!-- 配置信息 -->
        <view class="info-card">
          <view class="info-title">当前配置信息</view>
          <view class="info-item">
            <text class="info-label">配置状态：</text>
            <text class="info-value">{{ configStateText }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">环节数量：</text>
            <text class="info-value">{{ sectionsCount }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">最后修改：</text>
            <text class="info-value">{{ lastModified }}</text>
          </view>
        </view>
      </view>

      <!-- 备份管理 -->
      <view v-if="activeTab === 'backup'" class="content-section">
        <view class="section-title">配置备份</view>
        
        <!-- 创建备份 -->
        <view class="action-card">
          <view class="card-header">
            <text class="card-title">创建备份</text>
            <text class="card-desc">为当前配置创建备份点</text>
          </view>
          <button class="action-btn backup-btn" @click="handleCreateBackup">
            <text class="btn-icon">💾</text>
            <text class="btn-text">创建备份</text>
          </button>
        </view>

        <!-- 备份列表 -->
        <view class="backup-list">
          <view class="list-title">备份历史</view>
          
          <view v-if="backupList.length === 0" class="empty-state">
            <text class="empty-text">暂无备份记录</text>
          </view>
          
          <view 
            v-for="backup in backupList" 
            :key="backup.key"
            class="backup-item"
          >
            <view class="backup-info">
              <view class="backup-date">{{ backup.date }}</view>
              <view class="backup-details">
                <text class="detail-item">版本: {{ backup.version }}</text>
                <text class="detail-item">环节: {{ backup.sectionsCount }}个</text>
              </view>
            </view>
            
            <view class="backup-actions">
              <button 
                class="mini-btn restore-btn" 
                @click="handleRestoreBackup(backup.key)"
              >
                恢复
              </button>
              <button 
                class="mini-btn delete-btn" 
                @click="handleDeleteBackup(backup.key)"
              >
                删除
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- 存储信息 -->
      <view v-if="activeTab === 'storage'" class="content-section">
        <view class="section-title">存储信息</view>
        
        <view class="storage-stats">
          <view class="stat-card">
            <view class="stat-value">{{ storageUsage.playerConfigKeys }}</view>
            <view class="stat-label">配置项数量</view>
          </view>
          
          <view class="stat-card">
            <view class="stat-value">{{ formatSize(storageUsage.playerConfigSize) }}</view>
            <view class="stat-label">配置占用空间</view>
          </view>
          
          <view class="stat-card">
            <view class="stat-value">{{ formatSize(storageUsage.currentSize) }}</view>
            <view class="stat-label">总占用空间</view>
          </view>
        </view>

        <!-- 清理操作 -->
        <view class="action-card">
          <view class="card-header">
            <text class="card-title">存储清理</text>
            <text class="card-desc">清理旧的配置数据和备份</text>
          </view>
          <view class="cleanup-actions">
            <button class="action-btn cleanup-btn" @click="handleCleanupBackups">
              <text class="btn-text">清理旧备份</text>
            </button>
            <button class="action-btn danger-btn" @click="handleClearAllConfig">
              <text class="btn-text">清空所有配置</text>
            </button>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作 -->
    <view class="manager-footer">
      <button class="close-btn-large" @click="closeManager">关闭</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 接收属性
const props = defineProps({
  playerConfig: {
    type: Object,
    required: true
  }
})

// 事件
const emit = defineEmits(['close', 'config-changed'])

// 状态
const activeTab = ref('import-export')
const backupList = ref([])
const storageUsage = ref({
  totalKeys: 0,
  currentSize: 0,
  limitSize: 0,
  playerConfigKeys: 0,
  playerConfigSize: 0
})

// 标签配置
const tabs = [
  { key: 'import-export', label: '导入导出' },
  { key: 'backup', label: '备份管理' },
  { key: 'storage', label: '存储信息' }
]

// 计算属性
const configStateText = computed(() => {
  const { type, name } = props.playerConfig.configState.value
  switch (type) {
    case 'custom': return '自定义配置'
    case 'template': return `模板配置：${name}`
    case 'server': return '默认配置'
    default: return '未知状态'
  }
})

const sectionsCount = computed(() => {
  return props.playerConfig.settings.value?.sections?.length || 0
})

const lastModified = computed(() => {
  // 这里可以从存储的元数据中获取最后修改时间
  return '刚刚'
})

// 方法
const closeManager = () => {
  emit('close')
}

const switchTab = (tabKey) => {
  activeTab.value = tabKey
  if (tabKey === 'backup') {
    loadBackupList()
  } else if (tabKey === 'storage') {
    loadStorageUsage()
  }
}

const handleExportConfig = async () => {
  try {
    const configJson = props.playerConfig.exportConfig()
    
    // 在小程序环境中，我们可以将配置复制到剪贴板
    uni.setClipboardData({
      data: configJson,
      success: () => {
        uni.showToast({
          title: '配置已复制到剪贴板',
          icon: 'success'
        })
      },
      fail: () => {
        // 如果复制失败，显示配置内容让用户手动复制
        uni.showModal({
          title: '导出配置',
          content: '配置已生成，请手动复制以下内容：\n' + configJson.substring(0, 100) + '...',
          showCancel: false
        })
      }
    })
  } catch (error) {
    uni.showToast({
      title: '导出失败：' + error.message,
      icon: 'error'
    })
  }
}

const handleImportConfig = () => {
  // 在小程序环境中，通过输入框让用户粘贴配置
  uni.showModal({
    title: '导入配置',
    content: '请将配置JSON粘贴到下方输入框',
    editable: true,
    placeholderText: '请粘贴配置JSON...',
    success: (res) => {
      if (res.confirm && res.content) {
        try {
          props.playerConfig.importConfig(res.content)
          uni.showToast({
            title: '配置导入成功',
            icon: 'success'
          })
          emit('config-changed')
        } catch (error) {
          uni.showToast({
            title: '导入失败：' + error.message,
            icon: 'error'
          })
        }
      }
    }
  })
}

const handleCreateBackup = async () => {
  try {
    const backupKey = props.playerConfig.createBackup()
    uni.showToast({
      title: '备份创建成功',
      icon: 'success'
    })
    loadBackupList()
  } catch (error) {
    uni.showToast({
      title: '创建备份失败：' + error.message,
      icon: 'error'
    })
  }
}

const handleRestoreBackup = (backupKey) => {
  uni.showModal({
    title: '确认恢复',
    content: '恢复备份将覆盖当前配置，是否继续？',
    success: (res) => {
      if (res.confirm) {
        try {
          props.playerConfig.restoreBackup(backupKey)
          uni.showToast({
            title: '配置恢复成功',
            icon: 'success'
          })
          emit('config-changed')
        } catch (error) {
          uni.showToast({
            title: '恢复失败：' + error.message,
            icon: 'error'
          })
        }
      }
    }
  })
}

const handleDeleteBackup = (backupKey) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除此备份吗？',
    success: (res) => {
      if (res.confirm) {
        try {
          uni.removeStorageSync(backupKey)
          uni.showToast({
            title: '备份删除成功',
            icon: 'success'
          })
          loadBackupList()
        } catch (error) {
          uni.showToast({
            title: '删除失败：' + error.message,
            icon: 'error'
          })
        }
      }
    }
  })
}

const handleCleanupBackups = () => {
  try {
    props.playerConfig.cleanupOldBackups()
    uni.showToast({
      title: '清理完成',
      icon: 'success'
    })
    loadBackupList()
  } catch (error) {
    uni.showToast({
      title: '清理失败：' + error.message,
      icon: 'error'
    })
  }
}

const handleClearAllConfig = () => {
  uni.showModal({
    title: '危险操作',
    content: '此操作将清空所有播放策略配置，无法恢复，确定继续吗？',
    success: (res) => {
      if (res.confirm) {
        try {
          props.playerConfig.clearStorage()
          // 清理所有备份
          const allBackups = props.playerConfig.getBackupList()
          allBackups.forEach(backup => {
            try {
              uni.removeStorageSync(backup.key)
            } catch (error) {
              // 删除备份失败，继续执行
            }
          })
          
          uni.showToast({
            title: '所有配置已清空',
            icon: 'success'
          })
          emit('config-changed')
          loadBackupList()
          loadStorageUsage()
        } catch (error) {
          uni.showToast({
            title: '清空失败：' + error.message,
            icon: 'error'
          })
        }
      }
    }
  })
}

const loadBackupList = () => {
  try {
    backupList.value = props.playerConfig.getBackupList()
  } catch (error) {
    backupList.value = []
  }
}

const loadStorageUsage = () => {
  try {
    storageUsage.value = props.playerConfig.getStorageUsage()
  } catch (error) {
    console.error('加载存储使用情况失败:', error)
  }
}

const formatSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生命周期
onMounted(() => {
  loadStorageUsage()
})
</script>

<style scoped>
.config-manager {
  display: flex;
  flex-direction: column;
  height: 80vh;
  max-height: 1000rpx;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #ebeef5;
}

.header-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #303133;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #909399;
  border-radius: 50%;
  background-color: #f5f7fa;
}

.manager-tabs {
  display: flex;
  padding: 0 40rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #ebeef5;
}

.tab-item {
  padding: 24rpx 0;
  margin-right: 60rpx;
  font-size: 28rpx;
  color: #606266;
  border-bottom: 4rpx solid transparent;
}

.tab-item.active {
  color: #409eff;
  border-bottom-color: #409eff;
}

.manager-content {
  flex: 1;
  padding: 30rpx 40rpx;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #303133;
}

.action-card {
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e4e7ed;
}

.card-header {
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #303133;
  display: block;
  margin-bottom: 8rpx;
}

.card-desc {
  font-size: 26rpx;
  color: #606266;
  line-height: 1.4;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  width: 100%;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.export-btn {
  background-color: #409eff;
  color: #fff;
}

.import-btn {
  background-color: #67c23a;
  color: #fff;
}

.backup-btn {
  background-color: #e6a23c;
  color: #fff;
}

.cleanup-btn {
  background-color: #909399;
  color: #fff;
}

.danger-btn {
  background-color: #f56c6c;
  color: #fff;
}

.info-card {
  padding: 30rpx;
  background-color: #f0f8ff;
  border-radius: 12rpx;
  border: 1rpx solid #d4edda;
}

.info-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #303133;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12rpx;
  font-size: 26rpx;
}

.info-label {
  color: #606266;
}

.info-value {
  color: #303133;
  font-weight: 500;
}

.backup-list {
  margin-top: 20rpx;
}

.list-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #303133;
  margin-bottom: 20rpx;
}

.empty-state {
  text-align: center;
  padding: 60rpx 0;
  color: #909399;
}

.backup-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e4e7ed;
}

.backup-info {
  flex: 1;
}

.backup-date {
  font-size: 28rpx;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8rpx;
}

.backup-details {
  display: flex;
  gap: 20rpx;
}

.detail-item {
  font-size: 24rpx;
  color: #909399;
}

.backup-actions {
  display: flex;
  gap: 12rpx;
}

.mini-btn {
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
  border: none;
}

.restore-btn {
  background-color: #67c23a;
  color: #fff;
}

.delete-btn {
  background-color: #f56c6c;
  color: #fff;
}

.storage-stats {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  flex: 1;
  text-align: center;
  padding: 30rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e4e7ed;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #606266;
}

.cleanup-actions {
  display: flex;
  gap: 20rpx;
}

.cleanup-actions .action-btn {
  flex: 1;
}

.manager-footer {
  padding: 30rpx 40rpx;
  border-top: 1rpx solid #ebeef5;
}

.close-btn-large {
  width: 100%;
  height: 80rpx;
  background-color: #409eff;
  color: #fff;
  border-radius: 12rpx;
  font-size: 30rpx;
  border: none;
}
</style>
