<template>
  <view class="playback-settings">
    <!-- 设置头部 -->
    <view class="settings-header">
      <view class="header-left">
        <view class="header-title">播放策略设置</view>
        <view class="config-status">
          <view class="status-tag" :class="configStateClass">
            <text class="status-icon">{{ configStateIcon }}</text>
            <text class="status-text">{{ configStatusText }}</text>
          </view>
        </view>
      </view>

      <view class="header-right">
        <button class="template-btn" @click="showTemplateSelector">
          <text class="btn-icon">📋</text>
          <text class="btn-text">选择策略</text>
        </button>
        <view class="close-btn" @click="closeSettings">×</view>
      </view>
    </view>

    <!-- 环节配置区域 -->
    <view class="sections-area">
      <view class="area-header">
        <view class="header-left">
          <text class="area-title">环节配置</text>
          <text class="area-subtitle">共{{ localSettings.sections.length }}个环节</text>
        </view>
        <button class="add-section-button" @click="addSection">
          <text class="add-icon">+</text>
          <text class="add-text">新增</text>
        </button>
      </view>

      <scroll-view class="sections-scroll" scroll-y="true">
        <view class="sections-list">
          <view
            v-for="(section, index) in localSettings.sections"
            :key="index"
            class="section-item"
            :class="{ 'selected': index === activeSectionIndex }"
          >
            <!-- 环节信息 -->
            <view class="section-main" @click="selectSection(index)">
              <view class="section-number">{{ index + 1 }}</view>
              <view class="section-content">
                <view class="section-title">{{ section.title }}</view>

                <!-- 基础设置 -->
                <view class="section-basic">
                  <text class="basic-item">重复{{ section.repeatCount }}次</text>
                  <text class="basic-item">停顿{{ section.pauseDuration }}ms</text>
                </view>

                <!-- 详细设置 -->
                <view class="section-details">
                  <!-- 翻译设置 -->
                  <view class="detail-item">
                    <text class="detail-label">翻译:</text>
                    <text class="detail-value">{{ section.enableTranslation ? `${getLanguageLabel(section.translationLanguage)}，第${section.translationPosition || 1}次后` : '未启用' }}</text>
                  </view>

                  <!-- 关键词设置 -->
                  <view class="detail-item">
                    <text class="detail-label">关键词:</text>
                    <text class="detail-value">{{ section.enableKeywords ? `${section.keywordRepeatCount || 1}次重复，第${section.keywordPosition || 1}次后` : '未启用' }}</text>
                  </view>

                  <!-- 重复速度设置 -->
                  <view class="detail-item">
                    <text class="detail-label">语速:</text>
                    <text class="detail-value">{{ getSpeedInfo(section) }}</text>
                  </view>

                  <!-- 重复停顿设置 -->
                  <view class="detail-item">
                    <text class="detail-label">停顿:</text>
                    <text class="detail-value">{{ getPauseInfo(section) }}</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 操作按钮 -->
            <view class="section-operations">
              <button class="op-btn edit-btn" @click.stop="editSection(index)">
                <text class="op-icon">✏️</text>
              </button>
              <button class="op-btn copy-btn" @click.stop="duplicateSection(index)">
                <text class="op-icon">📄</text>
              </button>
              <button
                v-if="localSettings.sections.length > 1"
                class="op-btn delete-btn"
                @click.stop="removeSection(index)"
              >
                <text class="op-icon">🗑️</text>
              </button>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="settings-footer">
      <view class="footer-buttons">
        <button class="reset-btn" @click="resetToOriginalConfig">
          <text class="btn-text">重置为原始配置</text>
        </button>
        <button class="apply-settings-btn" @click="saveSettings">
          <text class="btn-text">应用设置</text>
        </button>
      </view>
    </view>

    <!-- 模板选择器弹窗 -->
    <view v-if="showTemplatePopup" class="popup-overlay" @click="closeTemplateSelector">
      <view class="popup-content template-popup" @click.stop>
        <TemplateSelector
          :current-template="localCurrentTemplate"
          @select="selectTemplate"
          @close="closeTemplateSelector"
        />
      </view>
    </view>

    <!-- 环节编辑器弹窗 -->
    <view v-if="showSectionPopup" class="popup-overlay" @click="closeSectionEditor">
      <view class="popup-content" @click.stop>
        <SectionEditor
          v-if="activeSection"
          :section="activeSection"
          :content="content"
          @update:section="handleSectionUpdate"
          @close="closeSectionEditor"
        />
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { getLanguageLabel } from '../../config/languages.js'
import TemplateSelector from './TemplateSelector.vue'
import SectionEditor from './SectionEditor.vue'



// 接收属性
const props = defineProps({
  settings: {
    type: Object,
    required: true
  },
  serverConfig: {
    type: Object,
    required: true
  },
  currentTemplate: {
    type: Object,
    default: null
  },
  configState: {
    type: Object,
    default: () => ({ type: 'server', source: null, name: '' })
  },
  content: {
    type: Object,
    required: true
  }
})

// 事件
const emit = defineEmits(['save', 'close', 'reset-to-original', 'section-updated'])

// 本地状态 - 初始化时深拷贝props，之后不再同步
const localSettings = ref(JSON.parse(JSON.stringify(props.settings)))
const localCurrentTemplate = ref(props.currentTemplate ? JSON.parse(JSON.stringify(props.currentTemplate)) : null)
const localConfigState = ref(JSON.parse(JSON.stringify(props.configState)))
const activeSectionIndex = ref(0)
const showTemplatePopup = ref(false)
const showSectionPopup = ref(false)
const isApplyingTemplate = ref(false) // 标记是否正在应用模板

// 监听用户主动修改localSettings，检测模板状态
watch(() => localSettings.value, (newSettings) => {
  // 如果正在应用模板，跳过检测
  if (isApplyingTemplate.value) {
    return
  }

  // 检查是否需要切换到自定义状态
  let shouldSwitchToCustom = false

  if (localCurrentTemplate.value) {
    // 如果当前有模板，检查配置是否还匹配
    const isMatch = checkSettingsMatchTemplate(localCurrentTemplate.value, newSettings)
    if (!isMatch) {
      shouldSwitchToCustom = true
    }
  } else if (localConfigState.value.type === 'server') {
    // 如果是"内容默认"状态，用户修改了就应该切换为自定义
    shouldSwitchToCustom = true
  }

  if (shouldSwitchToCustom) {
    // 只更新本地显示状态，不立即保存
    localCurrentTemplate.value = null
    localConfigState.value = {
      type: 'custom',
      source: null,
      name: ''
    }

    // 显示提示但不立即保存状态
    uni.showToast({
      title: '配置已修改，请保存生效',
      icon: 'none'
    })
  }
}, { deep: true })

// 检查设置是否与模板匹配（深度比较）
const checkSettingsMatchTemplate = (template, settingsToCheck) => {
  if (!template?.config?.sections || !settingsToCheck?.sections) {
    return false
  }

  const templateSections = template.config.sections
  const currentSections = settingsToCheck.sections

  // 环节数量不同，肯定不匹配
  if (templateSections.length !== currentSections.length) {
    return false
  }

  // 深度比较每个环节的用户可修改字段
  for (let i = 0; i < templateSections.length; i++) {
    const templateSection = templateSections[i]
    const currentSection = currentSections[i]

    if (!currentSection) return false

    // 比较关键配置字段（与后端模板字段一致）
    const fieldsToCompare = [
      'speed', 'pauseDuration', 'repeatCount', 'enableTranslation',
      'translationLanguage', 'translationPosition', 'enableKeywords',
      'keywordPosition', 'keywordRepeatCount', 'repeatSpeeds', 'repeatPauses'
    ]

    for (const field of fieldsToCompare) {
      if (!deepEqual(templateSection[field], currentSection[field])) {
        return false
      }
    }
  }

  return true
}

// 深度比较函数
const deepEqual = (a, b) => {
  if (a === b) return true
  if (a == null || b == null) return false
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false
    for (let i = 0; i < a.length; i++) {
      if (!deepEqual(a[i], b[i])) return false
    }
    return true
  }
  return a === b
}

// 计算属性
const activeSection = computed(() => {
  if (localSettings.value.sections && localSettings.value.sections.length > 0 && activeSectionIndex.value >= 0) {
    return localSettings.value.sections[activeSectionIndex.value]
  }
  return null
})



const configStateClass = computed(() => {
  const type = localConfigState.value.type
  return {
    'status-custom': type === 'custom',
    'status-template': type === 'template',
    'status-server': type === 'server'
  }
})

const configStateIcon = computed(() => {
  const type = localConfigState.value.type
  switch (type) {
    case 'custom': return '👤'
    case 'template': return '📋'
    case 'server': return '📄'
    default: return '📄'
  }
})

const configStatusText = computed(() => {
  if (localConfigState.value.type === 'template' && localConfigState.value.name) {
    return `使用模板：${localConfigState.value.name}`
  } else if (localConfigState.value.type === 'server') {
    return '内容默认'
  } else {
    return '自定义设置'
  }
})

// 方法
const closeSettings = () => {
  emit('close')
}

const selectSection = (index) => {
  activeSectionIndex.value = index
}

const addSection = () => {
  // 始终使用sequence类型和基于序列的处理模式
  const sectionType = 'sequence'
  const processingMode = 'sequence'

  // 创建新环节，使用简单的标题
  const newSection = {
    title: `环节 ${localSettings.value.sections.length + 1}`,
    type: sectionType,
    processingMode: processingMode,
    repeatCount: 4, // 默认重复4次
    pauseDuration: 2500,
    userEditable: true
  }

  // 翻译设置
  newSection.enableTranslation = false // 默认不启用翻译
  newSection.translationLanguage = ''
  newSection.translationPosition = Math.min(2, newSection.repeatCount || 4) // 默认在第2次重复后插入翻译

  // 关键词设置
  newSection.enableKeywords = false // 默认不启用关键词
  newSection.keywordRepeatCount = 2 // 默认关键词重复2次
  newSection.keywordPosition = Math.min(2, newSection.repeatCount || 4) // 默认在第2次重复后插入关键词

  // 每次重复的参数
  const defaultSpeed = 1.0
  const defaultPause = newSection.pauseDuration || 2500

  newSection.repeatSpeeds = Array(newSection.repeatCount).fill(defaultSpeed)
  newSection.repeatPauses = Array(newSection.repeatCount).fill(defaultPause)

  localSettings.value.sections.push(newSection)
  activeSectionIndex.value = localSettings.value.sections.length - 1
}

const duplicateSection = (index) => {
  const sectionToDuplicate = localSettings.value.sections[index]
  if (!sectionToDuplicate) return

  const newSection = JSON.parse(JSON.stringify(sectionToDuplicate))
  // 删除原有ID，让服务器重新分配
  delete newSection.id

  // 确保名称不为空，并正确处理副本命名
  const originalName = newSection.title || `环节${index + 1}`
  if (originalName.includes('副本')) {
    // 如果已经包含副本，增加编号
    const match = originalName.match(/副本(\d+)?$/)
    if (match) {
      const num = match[1] ? parseInt(match[1]) + 1 : 2
      newSection.title = originalName.replace(/副本(\d+)?$/, `副本${num}`)
    } else {
      newSection.title = `${originalName}副本`
    }
  } else {
    newSection.title = `${originalName}副本`
  }

  localSettings.value.sections.splice(index + 1, 0, newSection)
  activeSectionIndex.value = index + 1

  uni.showToast({
    title: '环节复制成功',
    icon: 'success'
  })
}

const removeSection = (index) => {
  if (localSettings.value.sections.length <= 1) {
    uni.showToast({
      title: '至少需要保留一个环节',
      icon: 'none'
    })
    return
  }

  uni.showModal({
    title: '提示',
    content: '确定要删除此环节吗？',
    success: (res) => {
      if (res.confirm) {
        // 删除环节
        localSettings.value.sections.splice(index, 1)

        // 更新选中的环节索引
        if (index === activeSectionIndex.value) {
          activeSectionIndex.value = 0
        } else if (index < activeSectionIndex.value) {
          activeSectionIndex.value--
        }

        // 强制触发响应式更新
        localSettings.value = { ...localSettings.value }

        uni.showToast({
          title: '环节删除成功',
          icon: 'success'
        })
      }
    },
    fail: (err) => {
      // 如果弹窗失败，直接删除
      if (confirm('确定要删除此环节吗？')) {
        localSettings.value.sections.splice(index, 1)
        if (index === activeSectionIndex.value) {
          activeSectionIndex.value = 0
        } else if (index < activeSectionIndex.value) {
          activeSectionIndex.value--
        }
      }
    }
  })
}

// 模板选择功能
const showTemplateSelector = () => {
  showTemplatePopup.value = true
}

const closeTemplateSelector = () => {
  showTemplatePopup.value = false
}

const selectTemplate = async (template) => {
  try {
    // 设置应用模板标记，避免触发watch检测
    isApplyingTemplate.value = true

    // 应用模板到本地设置
    const appliedConfig = applyTemplateToConfig(localSettings.value, template)

    // 应用模板配置
    localSettings.value = appliedConfig

    // 更新模板状态
    localCurrentTemplate.value = template
    localConfigState.value = {
      type: 'template',
      source: template.id,
      name: template.name
    }

    // 关闭模板选择器
    closeTemplateSelector()

    uni.showToast({
      title: '已应用策略',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '应用模板失败',
      icon: 'error'
    })
  } finally {
    // 清除应用模板标记
    setTimeout(() => {
      isApplyingTemplate.value = false
    }, 100)
  }
}

// 应用模板到配置的辅助函数
const applyTemplateToConfig = (currentConfig, template) => {
  if (!template || !template.config || !template.config.sections) {
    throw new Error('无效的模板配置')
  }

  if (!currentConfig || !currentConfig.sections) {
    throw new Error('无效的当前配置')
  }

  // 创建新的配置对象，完全基于模板的环节结构
  const newConfig = {
    sections: template.config.sections.map((templateSection, index) => {
      // 尝试从当前配置中找到对应的环节（用于保留服务器字段）
      const currentSection = currentConfig.sections[index]

      if (currentSection) {
        // 如果当前配置中有对应环节，保留服务器字段并应用模板参数
        return {
          // 保留服务器控制字段
          id: currentSection.id,
          title: templateSection.title || currentSection.title, // 优先使用模板的title
          description: currentSection.description,
          processingMode: currentSection.processingMode,
          userEditable: currentSection.userEditable,
          sourceIndex: currentSection.sourceIndex,
          sourceNodeIds: currentSection.sourceNodeIds,

          // 应用模板的播放参数
          pauseDuration: templateSection.pauseDuration,
          repeatCount: templateSection.repeatCount,
          repeatSpeeds: templateSection.repeatSpeeds || [1.0, 1.0, 1.0, 1.0],
          repeatPauses: templateSection.repeatPauses || [2500, 2500, 2500, 2500],
          enableTranslation: templateSection.enableTranslation || false,
          translationLanguage: templateSection.translationLanguage || 'zh',
          translationPosition: templateSection.translationPosition || 'after',
          enableKeywords: templateSection.enableKeywords || false,
          keywordPosition: templateSection.keywordPosition || 'after',
          keywordRepeatCount: templateSection.keywordRepeatCount || 1
        }
      } else {
        // 如果当前配置中没有对应环节，创建新环节（使用默认服务器字段）
        return {
          // 默认服务器控制字段
          id: `section_${index + 1}`,
          title: templateSection.title || `环节 ${index + 1}`, // 优先使用模板的title
          description: `第${index + 1}个播放环节`,
          processingMode: 'sequence',
          userEditable: true,
          sourceIndex: index,
          sourceNodeIds: [],

          // 应用模板的播放参数
          pauseDuration: templateSection.pauseDuration,
          repeatCount: templateSection.repeatCount,
          repeatSpeeds: templateSection.repeatSpeeds || [1.0, 1.0, 1.0, 1.0],
          repeatPauses: templateSection.repeatPauses || [2500, 2500, 2500, 2500],
          enableTranslation: templateSection.enableTranslation || false,
          translationLanguage: templateSection.translationLanguage || 'zh',
          translationPosition: templateSection.translationPosition || 'after',
          enableKeywords: templateSection.enableKeywords || false,
          keywordPosition: templateSection.keywordPosition || 'after',
          keywordRepeatCount: templateSection.keywordRepeatCount || 1
        }
      }
    })
  }

  return newConfig
}

const editSection = (index) => {
  activeSectionIndex.value = index
  showSectionPopup.value = true
}

const closeSectionEditor = () => {
  showSectionPopup.value = false
}

// 处理环节更新 - 简化版本，直接更新引用
const handleSectionUpdate = (newSection) => {
  if (activeSection.value) {
    // 直接更新activeSection，因为它是localSettings.sections[index]的引用
    Object.assign(activeSection.value, newSection)
  }
}



// 获取语速信息
const getSpeedInfo = (section) => {
  if (!section.repeatSpeeds || section.repeatSpeeds.length === 0) return '1.0x'
  return section.repeatSpeeds.map(speed => `${speed}x`).join(', ')
}

// 获取停顿信息
const getPauseInfo = (section) => {
  if (!section.repeatPauses || section.repeatPauses.length === 0) return `${section.pauseDuration}ms`
  return section.repeatPauses.map(pause => `${pause}ms`).join(', ')
}

const saveSettings = () => {
  // 在保存时才真正通知父组件更新状态
  emit('check-template-state', JSON.parse(JSON.stringify(localSettings.value)))

  // 传递完整的状态数据
  const settingsData = {
    settings: JSON.parse(JSON.stringify(localSettings.value)),
    template: localCurrentTemplate.value ? JSON.parse(JSON.stringify(localCurrentTemplate.value)) : null,
    configState: JSON.parse(JSON.stringify(localConfigState.value))
  }

  emit('save', settingsData)

  uni.showToast({
    title: '设置已保存',
    icon: 'success'
  })
}



// 重置为原始配置（底部按钮调用）
const resetToOriginalConfig = () => {
  uni.showModal({
    title: '重置确认',
    content: '确定要重置为原始配置吗？这将清除所有自定义设置和模板状态',
    success: (res) => {
      if (res.confirm) {
        // 直接使用服务器配置重置，而不是发出事件
        if (props.serverConfig && props.serverConfig.sections) {
          // 设置应用模板标记，避免触发watch检测
          isApplyingTemplate.value = true

          // 重置为服务器原始配置
          localSettings.value = JSON.parse(JSON.stringify(props.serverConfig))
          localCurrentTemplate.value = null
          localConfigState.value = {
            type: 'server',
            source: null,
            name: ''
          }

          if (localSettings.value.sections && localSettings.value.sections.length > 0) {
            activeSectionIndex.value = 0
          }

          // 通知父组件重置
          emit('reset-to-original')

          uni.showToast({
            title: '已重置为原始配置',
            icon: 'success'
          })

          // 清除应用模板标记
          setTimeout(() => {
            isApplyingTemplate.value = false
          }, 100)
        } else {
          uni.showToast({
            title: '无法获取原始配置信息',
            icon: 'error'
          })
        }
      }
    }
  })
}
</script>

<style scoped>
/* 播放设置组件样式 */

.playback-settings {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 85vh;
  background-color: #f5f7fa;
  border-radius: 20rpx 20rpx 0 0;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #ebeef5;
}

.header-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #303133;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #909399;
  border-radius: 50%;
  background-color: #f5f7fa;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.close-btn:active {
  transform: scale(0.9);
  background-color: #e8eaed;
}

/* 头部布局 */
.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.template-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  background-color: #409eff;
  color: #fff;
  border-radius: 10rpx;
  border: none;
  font-size: 22rpx;
  height: 44rpx;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 6rpx rgba(64, 158, 255, 0.2);
}

.template-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 3rpx rgba(64, 158, 255, 0.3);
}

.btn-icon {
  font-size: 18rpx;
}

.btn-text {
  font-size: 24rpx;
}

.config-status {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
}

.status-tag.status-custom {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-tag.status-template {
  background-color: #e8f5e8;
  color: #388e3c;
}

.status-tag.status-server {
  background-color: #fff3e0;
  color: #f57c00;
}

.status-icon {
  font-size: 20rpx;
}

.status-text {
  font-size: 22rpx;
}

/* 模板选择区域 */
.template-section {
  padding: 0 40rpx 30rpx;
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #303133;
  display: block;
  margin-bottom: 4rpx;
}

.section-subtitle {
  font-size: 22rpx;
  color: #909399;
}

.template-options {
  display: flex;
  flex-direction: column;
}

.template-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
  border: 1rpx solid #e4e7ed;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.option-content {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.option-icon {
  font-size: 32rpx;
}

.option-text {
  flex: 1;
}

.option-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #303133;
  display: block;
  margin-bottom: 4rpx;
}

.option-desc {
  font-size: 22rpx;
  color: #909399;
}

.option-arrow {
  font-size: 24rpx;
  color: #c0c4cc;
}

/* 环节配置区域 */
.sections-area {
  flex: 1;
  padding: 0 40rpx;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex子项收缩 */
}

.area-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #ebeef5;
}

.header-left {
  flex: 1;
}

.area-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
  display: block;
  margin-bottom: 4rpx;
}

.area-subtitle {
  font-size: 24rpx;
  color: #909399;
}

.add-section-button {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 10rpx 16rpx;
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: #fff;
  border-radius: 16rpx;
  border: none;
  font-size: 22rpx;
  box-shadow: 0 2rpx 8rpx rgba(103, 194, 58, 0.25);
  transition: all 0.2s ease;
  height: 48rpx;
  min-width: 80rpx;
}

.add-section-button:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(103, 194, 58, 0.3);
}

.add-icon {
  font-size: 20rpx;
  font-weight: bold;
}

.add-text {
  font-size: 22rpx;
  font-weight: 500;
}

/* 环节滚动容器 */
.sections-scroll {
  flex: 1;
  height: 0; /* 配合flex: 1 实现自适应高度 */
}

/* 环节列表 */
.sections-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding-bottom: 20rpx; /* 底部留白，确保最后一项可见 */
}

.section-item {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.section-item.selected {
  border-color: #409eff;
  background-color: #f0f8ff;
  box-shadow: 0 4rpx 16rpx rgba(64, 158, 255, 0.2);
}

.section-main {
  flex: 1;
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.section-number {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #409eff, #66b1ff);
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 4rpx;
  flex-shrink: 0;
}

.section-content {
  flex: 1;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12rpx;
}

.section-basic {
  display: flex;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.basic-item {
  font-size: 22rpx;
  color: #606266;
  padding: 4rpx 12rpx;
  background-color: #f0f2f5;
  border-radius: 8rpx;
}

.section-details {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 20rpx;
}

.detail-label {
  color: #909399;
  font-weight: 500;
  min-width: 80rpx;
}

.detail-value {
  color: #606266;
  flex: 1;
}

.summary-tag {
  padding: 4rpx 12rpx;
  font-size: 22rpx;
  border-radius: 12rpx;
  background-color: #f0f2f5;
  color: #606266;
}

.summary-tag.translation {
  background-color: #e8f4fd;
  color: #409eff;
}

.summary-tag.keywords {
  background-color: #f0f9ff;
  color: #67c23a;
}

/* 操作按钮 */
.section-operations {
  display: flex;
  gap: 12rpx;
}

.op-btn {
  width: 44rpx;
  height: 44rpx;
  border-radius: 10rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.op-btn:active {
  transform: scale(0.9);
}

.edit-btn {
  background-color: #409eff;
}

.copy-btn {
  background-color: #67c23a;
}

/* 模板选择器弹窗样式 */
.template-popup {
  width: 100vw;
  height: 85vh;
  border-radius: 20rpx;
  overflow: hidden;
}

.delete-btn {
  background-color: #f56c6c;
}

.op-icon {
  font-size: 20rpx;
}

/* 底部操作区域 */
.settings-footer {
  padding: 30rpx 40rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  background-color: #fff;
  border-top: 1rpx solid #ebeef5;
}

.footer-buttons {
  display: flex;
  gap: 20rpx;
}

.reset-btn, .apply-settings-btn {
  flex: 1;
  height: 68rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 26rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.reset-btn {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(245, 108, 108, 0.3);
}

.reset-btn:active {
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(245, 108, 108, 0.4);
}

.apply-settings-btn {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(103, 194, 58, 0.3);
}

.apply-settings-btn:active {
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(103, 194, 58, 0.4);
}

.btn-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #fff;
}

/* 弹窗样式 */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 900;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.popup-content {
  width: 100%;
  max-height: 85vh;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: visible;
  display: flex;
  flex-direction: column;
  margin-top: auto;
  margin-bottom: 0;
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
