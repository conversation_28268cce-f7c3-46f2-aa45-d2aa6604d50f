<template>
  <view class="section-editor">
    <!-- 编辑器头部 -->
    <view class="editor-header">
      <view class="back-btn" @click="closeEditor">
        <text class="back-icon">‹</text>
        <text class="back-text">返回</text>
      </view>
      <view class="header-title">{{ localSection.title || '环节详情' }}</view>
      <view class="header-spacer"></view>
    </view>

    <!-- 编辑器内容 -->
    <scroll-view class="editor-content" scroll-y>
      <!-- 基本设置 -->
      <view class="settings-section">
        <view class="section-title">基本设置</view>
        
        <view class="form-item">
          <view class="form-label">环节名称</view>
          <input
            class="form-input"
            v-model="localSection.title"
            placeholder="请输入环节名称"
          />
        </view>

        <view class="form-item">
          <view class="form-label">默认停顿时长 (毫秒)</view>
          <NumberInput
            v-model="localSection.pauseDuration"
            :min="0"
            :max="5000"
            :step="100"
            :precision="0"
            placeholder="请输入停顿时长"
          />
        </view>
      </view>

      <!-- 重复设置 -->
      <view class="settings-section">
        <view class="section-title">重复设置</view>
        
        <view class="form-item">
          <view class="form-label">重复次数</view>
          <view class="number-input">
            <view class="number-btn" @click="adjustRepeatCount(-1)">-</view>
            <input
              class="number-value"
              type="number"
              v-model.number="localSection.repeatCount"
              @input="handleRepeatCountChange"
            />
            <view class="number-btn" @click="adjustRepeatCount(1)">+</view>
          </view>
        </view>

        <!-- 每次重复的参数 -->
        <view class="repeat-params">
          <view class="params-title">每次重复的参数</view>

          <!-- 语速提示 -->
          <view class="speed-notice">
            <text class="notice-icon">ℹ️</text>
            <text class="notice-text">小程序暂时不支持调整语速，固定为1.0倍速</text>
          </view>

          <view class="params-header">
            <text class="param-col">次数</text>
            <text class="param-col">速度</text>
            <text class="param-col">实际停顿(ms)</text>
          </view>

          <view
            v-for="(_, index) in Array(localSection.repeatCount)"
            :key="index"
            class="param-row"
          >
            <text class="param-index">第{{ index + 1 }}次</text>
            <view class="param-speed">
              <view class="speed-disabled">
                <text class="speed-value">1.0x</text>
                <text class="speed-hint">固定</text>
              </view>
            </view>
            <view class="param-pause">
              <NumberInput
                v-model="localSection.repeatPauses[index]"
                :min="0"
                :max="5000"
                :step="100"
                :precision="0"
                placeholder="停顿时长"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 翻译设置 -->
      <view class="settings-section">
        <view class="section-header">
          <view class="section-title">翻译设置</view>
          <switch 
            :checked="localSection.enableTranslation" 
            @change="handleTranslationToggle"
            color="#409eff"
          />
        </view>

        <view v-if="localSection.enableTranslation" class="translation-options">
          <view class="form-item">
            <view class="form-label">翻译语言</view>
            <view class="picker-display" @click="showLanguagePicker">
              {{ selectedLanguageLabel || '请选择翻译语言' }}
              <text class="picker-arrow">></text>
            </view>
          </view>

          <!-- 语言选择弹窗 -->
          <view v-if="showLanguageModal" class="language-modal" @click="hideLanguagePicker">
            <view class="language-modal-content" @click.stop>
              <view class="modal-header">
                <text class="modal-title">选择翻译语言</text>
                <text class="modal-close" @click="hideLanguagePicker">×</text>
              </view>
              <view class="language-options">
                <view
                  v-for="(lang, index) in availableLanguages"
                  :key="lang.value"
                  class="language-option"
                  :class="{ selected: lang.value === localSection.translationLanguage }"
                  @click="selectLanguage(lang)"
                >
                  <text class="language-label">{{ lang.label }}</text>
                  <text v-if="lang.value === localSection.translationLanguage" class="check-icon">✓</text>
                </view>
              </view>
            </view>
          </view>

          <view class="form-item">
            <view class="form-label">插入位置</view>
            <view class="number-input">
              <view
                class="number-btn"
                :class="{ 'disabled': localSection.translationPosition <= 1 }"
                @click="localSection.translationPosition > 1 && adjustTranslationPosition(-1)"
              >-</view>
              <input
                class="number-value"
                type="number"
                v-model.number="localSection.translationPosition"
                @input="validateTranslationPosition"
              />
              <view
                class="number-btn"
                :class="{ 'disabled': localSection.translationPosition >= localSection.repeatCount }"
                @click="localSection.translationPosition < localSection.repeatCount && adjustTranslationPosition(1)"
              >+</view>
            </view>
            <view class="form-help">在第几次重复后插入翻译（1-{{ localSection.repeatCount }}）</view>
          </view>
        </view>
      </view>

      <!-- 关键词设置 -->
      <view class="settings-section">
        <view class="section-header">
          <view class="section-title">关键词设置</view>
          <switch 
            :checked="localSection.enableKeywords" 
            @change="handleKeywordToggle"
            color="#409eff"
          />
        </view>

        <view v-if="localSection.enableKeywords" class="keyword-options">
          <view class="form-item">
            <view class="form-label">插入位置</view>
            <view class="number-input">
              <view class="number-btn" @click="adjustKeywordPosition(-1)">-</view>
              <input 
                class="number-value" 
                type="number" 
                v-model.number="localSection.keywordPosition"
                @input="validateKeywordPosition"
              />
              <view class="number-btn" @click="adjustKeywordPosition(1)">+</view>
            </view>
            <view class="form-help">在第几次重复后插入关键词（1-{{ localSection.repeatCount }}）</view>
          </view>

          <view class="form-item">
            <view class="form-label">关键词重复次数</view>
            <view class="number-input">
              <view class="number-btn" @click="adjustKeywordRepeat(-1)">-</view>
              <input 
                class="number-value" 
                type="number" 
                v-model.number="localSection.keywordRepeatCount"
                @input="validateKeywordRepeat"
              />
              <view class="number-btn" @click="adjustKeywordRepeat(1)">+</view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 编辑器底部 -->
    <view class="editor-footer">
      <view class="footer-buttons">
        <button class="cancel-btn" @click="cancelChanges">取消</button>
        <button class="save-btn" @click="saveChanges">保存</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { getLanguageLabel, SUPPORTED_LANGUAGES } from '../../config/languages.js'
import NumberInput from '../ui/NumberInput.vue'

// 接收属性
const props = defineProps({
  section: {
    type: Object,
    required: true
  },
  content: {
    type: Object,
    required: false,
    default: () => ({})
  }
})

// 事件
const emit = defineEmits(['save', 'close'])

// 本地编辑状态
const localSection = ref({})

// 语言选择弹窗状态
const showLanguageModal = ref(false)

// 初始化本地状态
const initLocalSection = () => {
  // 直接使用已经预处理好的环节数据，无需重复处理
  localSection.value = JSON.parse(JSON.stringify(props.section))

  // 确保重复参数数组长度正确（这是唯一需要的处理）
  initCustomRepeatSettings()
}

// 初始化每次重复的参数（按照H5版本的实现）
const initCustomRepeatSettings = () => {
  if (!localSection.value) return

  // 获取重复次数
  const repeatCount = localSection.value.repeatCount || 4
  const baseSpeed = 1.0
  const basePause = localSection.value.pauseDuration || 2500

  // 创建或调整重复速度数组 - 小程序固定为1.0
  // 在小程序中，语速固定为1.0，不允许用户修改
  localSection.value.repeatSpeeds = Array(repeatCount).fill(1.0)

  // 创建或调整重复停顿时长数组
  if (!localSection.value.repeatPauses || !Array.isArray(localSection.value.repeatPauses)) {
    localSection.value.repeatPauses = Array(repeatCount).fill(basePause)
  } else {
    // 调整数组长度为重复次数
    while (localSection.value.repeatPauses.length < repeatCount) {
      localSection.value.repeatPauses.push(basePause)
    }
    // 如果数组过长，截断
    if (localSection.value.repeatPauses.length > repeatCount) {
      localSection.value.repeatPauses = localSection.value.repeatPauses.slice(0, repeatCount)
    }
  }
}

// 监听section变化，更新本地编辑状态
watch(() => props.section, (newSection) => {
  if (newSection) {
    initLocalSection()
  }
}, { immediate: true, deep: true })



// 计算属性：可用的翻译语言
const availableLanguages = computed(() => {
  if (!props.content || !props.content.configJson || !props.content.configJson.resources || !props.content.configJson.resources.translations) {
    // 使用配置文件中的支持语言列表
    return SUPPORTED_LANGUAGES.map(lang => ({
      value: lang.value,
      label: lang.label
    }))
  }

  const translations = props.content.configJson.resources.translations
  const languages = Object.keys(translations)

  return languages.map(code => ({
    value: code,
    label: getLanguageLabel(code)
  }))
})

const selectedLanguageIndex = computed(() => {
  return availableLanguages.value.findIndex(lang => lang.value === localSection.value.translationLanguage)
})

const selectedLanguageLabel = computed(() => {
  const lang = availableLanguages.value.find(lang => lang.value === localSection.value.translationLanguage)
  return lang ? lang.label : ''
})



// 使用统一的语言配置（从config/languages.js导入）

// 方法
const closeEditor = () => {
  emit('close')
}

const cancelChanges = () => {
  closeEditor()
}

const saveChanges = () => {
  // 验证重复次数
  if (localSection.value.repeatCount < 1 || localSection.value.repeatCount > 10) {
    uni.showToast({
      title: '重复次数必须在1-10之间',
      icon: 'none'
    })
    return
  }



  // 按照H5版本的逻辑，发送update:section事件
  emit('update:section', localSection.value)

  uni.showToast({
    title: '环节设置已保存',
    icon: 'success'
  })

  closeEditor()
}



const adjustPauseDuration = (delta) => {
  localSection.value.pauseDuration = Math.max(0, Math.min(10000, (localSection.value.pauseDuration || 2500) + delta))
}

const adjustRepeatCount = (delta) => {
  const newCount = Math.max(1, Math.min(10, (localSection.value.repeatCount || 4) + delta))
  localSection.value.repeatCount = newCount
  handleRepeatCountChange()
}

// 重复次数变化处理（按照H5版本的实现）
const handleRepeatCountChange = () => {
  const value = localSection.value.repeatCount

  // 确保翻译插入位置不超过重复次数
  if (localSection.value.enableTranslation) {
    if (localSection.value.translationPosition > value) {
      localSection.value.translationPosition = value
    }
    if (value === 1) {
      localSection.value.translationPosition = 1
    }
  }

  // 确保关键词插入位置不超过重复次数
  if (localSection.value.enableKeywords) {
    if (localSection.value.keywordPosition > value) {
      localSection.value.keywordPosition = value
    }
    if (value === 1) {
      localSection.value.keywordPosition = 1
    }
  }

  // 始终更新重复速度和停顿时长数组
  initCustomRepeatSettings()
}



const adjustTranslationPosition = (delta) => {
  localSection.value.translationPosition = Math.max(1, Math.min(localSection.value.repeatCount, (localSection.value.translationPosition || 2) + delta))
}

const adjustKeywordPosition = (delta) => {
  localSection.value.keywordPosition = Math.max(1, Math.min(localSection.value.repeatCount, (localSection.value.keywordPosition || 2) + delta))
}

const adjustKeywordRepeat = (delta) => {
  localSection.value.keywordRepeatCount = Math.max(1, Math.min(5, (localSection.value.keywordRepeatCount || 2) + delta))
}

// 验证方法
const validatePauseDuration = () => {
  localSection.value.pauseDuration = Math.max(0, Math.min(10000, localSection.value.pauseDuration || 2500))
}



const validateTranslationPosition = () => {
  localSection.value.translationPosition = Math.max(1, Math.min(localSection.value.repeatCount, localSection.value.translationPosition || 2))
}

const validateKeywordPosition = () => {
  localSection.value.keywordPosition = Math.max(1, Math.min(localSection.value.repeatCount, localSection.value.keywordPosition || 2))
}

const validateKeywordRepeat = () => {
  localSection.value.keywordRepeatCount = Math.max(1, Math.min(5, localSection.value.keywordRepeatCount || 2))
}





// 处理翻译启用状态变化
const handleTranslationToggle = (e) => {
  localSection.value.enableTranslation = e.detail.value
  if (localSection.value.enableTranslation && !localSection.value.translationLanguage) {
    localSection.value.translationLanguage = 'zh-CN'
  }
}

// 处理关键词启用状态变化
const handleKeywordToggle = (e) => {
  localSection.value.enableKeywords = e.detail.value
}

// 显示语言选择器
const showLanguagePicker = () => {
  showLanguageModal.value = true
}

// 隐藏语言选择器
const hideLanguagePicker = () => {
  showLanguageModal.value = false
}

// 选择语言
const selectLanguage = (lang) => {
  localSection.value.translationLanguage = lang.value
  hideLanguagePicker()
}

// 处理语言选择变化（保留备用）
const handleLanguageChange = (e) => {
  const index = e.detail.value
  if (availableLanguages.value[index]) {
    localSection.value.translationLanguage = availableLanguages.value[index].value
  }
}
</script>

<style scoped>
.section-editor {
  display: flex;
  flex-direction: column;
  height: 85vh;
  max-height: 85vh;
  background-color: #f5f7fa;
  box-sizing: border-box;
}

.editor-header {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #ebeef5;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #409eff;
  font-size: 28rpx;
  min-width: 120rpx; /* 改为最小宽度，允许自动扩展 */
  white-space: nowrap; /* 防止文本换行 */
}

.back-icon {
  font-size: 36rpx;
  font-weight: bold;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #303133;
}

.header-spacer {
  width: 120rpx;
}

.editor-content {
  flex: 1;
  height: 0; /* 配合flex: 1 实现自适应高度 */
  padding: 30rpx 40rpx; /* 恢复左右对称的padding */
  padding-bottom: 100rpx; /* 增加底部padding，确保最后内容可见 */
  box-sizing: border-box;
}

.settings-section {
  margin-bottom: 40rpx;
  margin-left: 0;
  margin-right: 0;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #303133;
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #606266;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 1rpx solid #dcdfe6;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.number-input {
  display: flex;
  align-items: center;
  border: 1rpx solid #dcdfe6;
  border-radius: 8rpx;
  background-color: #fff;
}

.number-btn {
  width: 90rpx; /* 增大宽度 */
  height: 90rpx; /* 增大高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx; /* 增大字体 */
  color: #409eff;
  border-right: 1rpx solid #dcdfe6;
  font-weight: bold; /* 加粗字体 */
}

.number-btn:last-child {
  border-right: none;
  border-left: 1rpx solid #dcdfe6;
}

.number-value {
  flex: 1;
  height: 90rpx; /* 与按钮高度保持一致 */
  text-align: center;
  font-size: 30rpx; /* 增大字体 */
  border: none;
}



.repeat-params {
  margin-top: 30rpx;
}

.params-title {
  font-size: 28rpx;
  color: #606266;
  margin-bottom: 20rpx;
}

/* 语速提示样式 */
.speed-notice {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f0f9ff;
  border: 1rpx solid #bfdbfe;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.notice-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.notice-text {
  font-size: 24rpx;
  color: #1e40af;
  line-height: 1.4;
}

.params-header {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #ebeef5;
  font-size: 26rpx;
  color: #909399;
  font-weight: 500;
  gap: 20rpx; /* 头部列之间的间隙 */
}

.param-col {
  flex: 1.3; /* 速度和停顿列更宽 */
  text-align: center;
}

.param-col:first-child {
  flex: 0.4; /* 次数列更窄 */
}

.param-row {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f7fa;
  gap: 20rpx; /* 列之间的间隙 */
}

.param-index {
  flex: 0.4; /* 与头部次数列保持一致 */
  text-align: center;
  font-size: 26rpx;
  color: #606266;
}

.param-speed, .param-pause {
  flex: 1.3; /* 增大语速和停顿列的宽度 */
  display: flex;
  justify-content: center;
}

/* 禁用的语速输入框样式 */
.speed-disabled {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  border: 1rpx solid #dcdfe6;
  border-radius: 4rpx;
  height: 60rpx;
  padding: 0 12rpx;
  width: 100%;
}

.speed-value {
  font-size: 26rpx;
  color: #606266;
}

.speed-hint {
  font-size: 22rpx;
  color: #909399;
  margin-left: 8rpx;
  background: #e9ecef;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}



.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  padding: 0 24rpx;
  border: 1rpx solid #dcdfe6;
  border-radius: 8rpx;
  background-color: #fff;
  font-size: 28rpx;
  color: #606266;
}

.picker-arrow {
  font-size: 24rpx;
  color: #c0c4cc;
}

.form-help {
  font-size: 24rpx;
  color: #909399;
  margin-top: 12rpx;
  line-height: 1.4;
}

.translation-options, .keyword-options {
  margin-top: 20rpx;
}

.editor-footer {
  padding: 30rpx 40rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  background-color: #fff; /* 恢复白色背景 */
  border-top: 1rpx solid #ebeef5; /* 恢复正常边框 */
  position: relative;
  z-index: 100;
  flex-shrink: 0;
  min-height: 120rpx; /* 保留最小高度确保按钮可见 */
}

.footer-buttons {
  display: flex;
  gap: 30rpx;
}

.cancel-btn, .save-btn {
  flex: 1;
  height: 80rpx; /* 恢复正常高度 */
  border-radius: 12rpx;
  font-size: 30rpx; /* 恢复正常字体大小 */
  border: none; /* 恢复无边框 */
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  background-color: #f56c6c;
  color: #fff;
}

.cancel-btn:hover {
  background-color: #f45454;
}

.save-btn {
  background-color: #67c23a;
  color: #fff;
}

.save-btn:hover {
  background-color: #5daf34;
}

/* 语言选择弹窗样式 */
.language-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 950; /* 弹框层级 */
}

.language-modal-content {
  background-color: #fff;
  border-radius: 16rpx;
  width: 80%;
  max-width: 600rpx;
  max-height: 70vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #ebeef5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #303133;
}

.modal-close {
  font-size: 40rpx;
  color: #909399;
  cursor: pointer;
}

.language-options {
  max-height: 400rpx;
  overflow-y: auto;
}

.language-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f5f7fa;
  cursor: pointer;
}

.language-option:hover {
  background-color: #f5f7fa;
}

.language-option.selected {
  background-color: #f0f8ff;
  color: #409eff;
}

.language-label {
  font-size: 30rpx;
}

.check-icon {
  font-size: 32rpx;
  color: #409eff;
  font-weight: bold;
}
</style>
