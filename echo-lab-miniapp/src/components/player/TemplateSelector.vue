<template>
  <view class="template-selector">
    <!-- 选择器头部 -->
    <view class="selector-header">
      <view class="header-title">选择播放策略</view>
      <view class="close-btn" @click="closeSelector">×</view>
    </view>

    <!-- 模板分类标签 -->
    <view class="template-tabs">
      <view
        v-for="tab in tabs"
        :key="tab.key"
        class="tab-item"
        :class="{ 'active': activeTab === tab.key }"
        @click="switchTab(tab.key)"
      >
        <text class="tab-text">{{ tab.label }}</text>
        <text class="tab-count">({{ tab.count }})</text>
      </view>
    </view>

    <!-- 模板列表 -->
    <scroll-view class="template-list" scroll-y>
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <view class="loading-icon">⏳</view>
        <view class="loading-text">加载中...</view>
      </view>

      <!-- 错误状态 -->
      <view v-else-if="error" class="error-state">
        <view class="error-icon">❌</view>
        <view class="error-text">{{ error }}</view>
        <button class="retry-btn" @click="loadTemplates">重试</button>
      </view>

      <!-- 无模板状态 -->
      <view v-else-if="currentTemplates.length === 0" class="empty-state">
        <view class="empty-icon">📋</view>
        <view class="empty-text">暂无可用的播放策略</view>
      </view>

      <!-- 模板项 -->
      <view
        v-for="template in currentTemplates"
        :key="template.id"
        class="template-item"
        :class="{ 'selected': selectedTemplate && selectedTemplate.id === template.id }"
        @click="selectTemplate(template)"
      >
        <view class="template-info">
          <view class="template-name">{{ template.name }}</view>
          <view class="template-description">{{ template.description || '暂无描述' }}</view>
          <view class="template-stats">
            <text class="stats-item">{{ getSectionCount(template) }}个环节</text>
            <text class="stats-item">{{ template.type === 'system' ? '系统模板' : '用户模板' }}</text>
          </view>
          <view class="template-preview" v-if="template.config && template.config.sections">
            <text class="preview-label">配置预览：</text>
            <text class="preview-text">{{ getTemplatePreview(template) }}</text>
          </view>
        </view>

        <!-- 选中标记 -->
        <view v-if="selectedTemplate && selectedTemplate.id === template.id" class="selected-mark">
          <text>✓</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作按钮 -->
    <view class="selector-footer">
      <view class="footer-buttons">
        <button class="cancel-btn" @click="closeSelector">取消</button>
        <button
          class="apply-btn"
          :disabled="!selectedTemplate"
          @click="applyTemplate"
        >
          应用策略
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import templateService from '../../services/templateService.js'
import { getLearningLanguage } from '../../utils/userSettings.js'

const props = defineProps({
  currentTemplate: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['select', 'close'])

// 状态
const activeTab = ref('system')
const selectedTemplate = ref(props.currentTemplate)
const systemTemplates = ref([])
const userTemplates = ref([])
const loading = ref(false)
const error = ref(null)

// 标签配置
const tabs = computed(() => [
  {
    key: 'system',
    label: '系统模板',
    count: systemTemplates.value.length
  },
  {
    key: 'user',
    label: '我的模板',
    count: userTemplates.value.length
  }
])

// 当前显示的模板列表
const currentTemplates = computed(() => {
  return activeTab.value === 'system' ? systemTemplates.value : userTemplates.value
})

// 加载模板数据
const loadTemplates = async () => {
  if (loading.value) return

  loading.value = true
  error.value = null

  try {
    // 获取当前学习语言
    const currentLanguage = getLearningLanguage()

    // 根据当前语言过滤模板
    const response = await templateService.getTemplates({ language: currentLanguage })
    if (response && response.success) {
      systemTemplates.value = response.data.system || []
      userTemplates.value = response.data.user || []
    } else {
      throw new Error((response && response.error) || '获取模板列表失败')
    }
  } catch (err) {
    error.value = err.message || '加载模板失败'
    uni.showToast({
      title: '加载模板失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const closeSelector = () => {
  emit('close')
}

const switchTab = (tabKey) => {
  activeTab.value = tabKey
}

const selectTemplate = (template) => {
  selectedTemplate.value = template
}

const applyTemplate = async () => {
  if (!selectedTemplate.value) return

  try {
    await templateService.useTemplate(selectedTemplate.value.id)
    emit('select', selectedTemplate.value)
    emit('close')
    uni.showToast({
      title: '已应用策略',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '应用模板失败',
      icon: 'none'
    })
  }
}

// 获取模板环节数量
const getSectionCount = (template) => {
  return (template.config && template.config.sections && template.config.sections.length) || 0
}

// 获取模板配置预览
const getTemplatePreview = (template) => {
  if (!template.config || !template.config.sections || !template.config.sections.length) {
    return '无配置信息'
  }

  const section = template.config.sections[0]
  const parts = []

  if (section.repeatCount) parts.push(`重复${section.repeatCount}次`)
  if (section.pauseDuration) parts.push(`停顿${section.pauseDuration}ms`)
  if (section.enableTranslation) parts.push('启用翻译')
  if (section.enableKeywords) parts.push('启用关键词')

  return parts.length > 0 ? parts.join('，') : '基础配置'
}

onMounted(() => {
  loadTemplates()
  if (props.currentTemplate) {
    selectedTemplate.value = props.currentTemplate
  }
})
</script>

<style scoped>
.template-selector {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 85vh;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #ebeef5;
}

.header-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #303133;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #909399;
  border-radius: 50%;
  background-color: #f5f7fa;
}

.template-tabs {
  display: flex;
  padding: 0 40rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #ebeef5;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 24rpx 0;
  margin-right: 60rpx;
  font-size: 28rpx;
  color: #606266;
  border-bottom: 4rpx solid transparent;
}

.tab-item.active {
  color: #409eff;
  border-bottom-color: #409eff;
}

.tab-text {
  font-weight: 500;
}

.tab-count {
  font-size: 24rpx;
  opacity: 0.7;
}

.template-list {
  flex: 1;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}

.loading-state, .error-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.loading-icon, .error-icon, .empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.loading-text, .error-text, .empty-text {
  font-size: 28rpx;
  color: #909399;
  margin-bottom: 20rpx;
}

.retry-btn {
  padding: 16rpx 32rpx;
  background-color: #409eff;
  color: #fff;
  border-radius: 8rpx;
  border: none;
  font-size: 26rpx;
}

.template-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  box-sizing: border-box;
}

.template-item.selected {
  border-color: #409eff;
  background-color: #f0f8ff;
}

.template-info {
  flex: 1;
}

.template-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8rpx;
}

.template-description {
  font-size: 26rpx;
  color: #606266;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.template-stats {
  display: flex;
  gap: 16rpx;
  margin-top: 12rpx;
  flex-wrap: wrap;
}

.stats-item {
  font-size: 22rpx;
  color: #909399;
  padding: 4rpx 12rpx;
  background-color: #f0f2f5;
  border-radius: 8rpx;
}

.template-preview {
  margin-top: 12rpx;
  padding: 12rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #409eff;
}

.preview-label {
  font-size: 20rpx;
  color: #606266;
  font-weight: 500;
}

.preview-text {
  font-size: 22rpx;
  color: #303133;
  margin-left: 8rpx;
}

.selected-mark {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #409eff;
  color: #fff;
  border-radius: 50%;
  font-size: 24rpx;
}

.selector-footer {
  padding: 30rpx 40rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #ebeef5;
  background-color: #fff;
}

.footer-buttons {
  display: flex;
  gap: 30rpx;
}

.cancel-btn, .apply-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  border: none;
}

.cancel-btn {
  background-color: #f56c6c;
  color: #fff;
}

.apply-btn {
  background-color: #67c23a;
  color: #fff;
}

.apply-btn:disabled {
  background-color: #c0c4cc;
  color: #fff;
}

</style>
