<template>
  <view class="content-card" @click="$emit('click', content)">
    <!-- 卡片缩略图 -->
    <view class="card-thumbnail">
      <ResponsiveImage
        v-if="content.thumbnailUrl"
        :src="content.thumbnailUrl"
        type="thumbnail"
        :width="320"
        :height="180"
        mode="aspectFill"
        container-class="thumbnail-container"
        image-class="thumbnail-image"
      />
      <view v-else class="thumbnail-placeholder">
        <text class="placeholder-icon">📹</text>
      </view>

      <!-- 缩略图底部信息 -->
      <view class="thumbnail-overlay" v-if="showOverlay">
        <view class="overlay-left" v-if="content.creator && showAuthor">
          <text class="overlay-author">{{ getAuthorDisplayName(content.creator) }}</text>
        </view>
        <view class="overlay-right">
          <text v-if="content.viewCount && showViewCount" class="overlay-views">👁️ {{ content.viewCount }}</text>
          <slot name="overlay-actions"></slot>
        </view>
      </view>
    </view>

    <!-- 卡片内容 -->
    <view class="card-content">
      <text class="card-title">{{ content.name }}</text>
      <view class="card-footer">
        <view class="card-tags">
          <text
            v-for="tag in parseTags(content.tags)"
            :key="tag"
            class="card-tag"
          >
            {{ tag }}
          </text>
          <text v-if="!content.tags || parseTags(content.tags).length === 0" class="card-tag no-tag">未分类</text>
        </view>
        <text v-if="showDate && content.updatedAt" class="card-date">{{ formatTime(content.updatedAt) }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import ResponsiveImage from './ResponsiveImage.vue'

const props = defineProps({
  content: {
    type: Object,
    required: true
  },
  showAuthor: {
    type: Boolean,
    default: true
  },
  showViewCount: {
    type: Boolean,
    default: true
  },
  showDate: {
    type: Boolean,
    default: true
  },
  showOverlay: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['click'])

const parseTags = (tags) => {
  if (!tags) return []
  return tags.split(',').map(tag => tag.trim()).filter(tag => tag)
}

const getAuthorDisplayName = (creator) => {
  if (!creator) return ''
  return creator.username || creator.name || '未知作者'
}

const formatTime = (dateString) => {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  const now = new Date()
  const diff = now - date
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return minutes < 1 ? '刚刚' : `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 30) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }
}
</script>

<style scoped>
@import '../../styles/variables.css';

.content-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.content-card:active {
  transform: scale(0.98);
}

.card-thumbnail {
  position: relative;
  width: 100%;
  /* 16:9 比例，使用padding-top方案兼容小程序 */
  padding-top: 56.25%; /* 9/16 = 0.5625 */
  overflow: hidden;
}

.thumbnail-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-icon {
  font-size: 48rpx;
  color: white;
}

.thumbnail-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.overlay-left {
  flex: 1;
  min-width: 0;
}

.overlay-author {
  font-size: var(--font-size-xs);
  color: white;
  opacity: 0.9;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.overlay-right {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.overlay-views {
  font-size: var(--font-size-xs);
  color: white;
  opacity: 0.9;
}

.card-content {
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  height: 140rpx;
}

.card-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  flex: 1;
  margin-right: 12rpx;
}

.card-tag {
  font-size: var(--font-size-xs);
  color: var(--color-primary);
  background: rgba(64, 158, 255, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  line-height: 1;
}

.card-tag.no-tag {
  color: var(--color-text-placeholder);
  background: rgba(0, 0, 0, 0.05);
}

.card-date {
  font-size: var(--font-size-xs);
  color: var(--color-text-placeholder);
  white-space: nowrap;
  flex-shrink: 0;
}
</style>
