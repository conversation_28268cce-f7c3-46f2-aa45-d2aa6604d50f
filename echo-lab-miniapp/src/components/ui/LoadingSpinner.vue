<template>
  <view
    class="loading-spinner"
    :class="{
      'loading-spinner--small': size === 'small',
      'loading-spinner--large': size === 'large',
      'loading-spinner--overlay': overlay
    }"
  >
    <view v-if="overlay" class="loading-mask"></view>
    <view class="loading-content">
      <view class="spinner" :style="{ borderColor: color }"></view>
      <text v-if="text" class="loading-text" :style="{ color: textColor }">{{ text }}</text>
    </view>
  </view>
</template>

<script setup>
defineProps({
  size: {
    type: String,
    default: 'medium', // small, medium, large
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  color: {
    type: String,
    default: '#007AFF'
  },
  textColor: {
    type: String,
    default: '#666'
  },
  text: {
    type: String,
    default: ''
  },
  overlay: {
    type: Boolean,
    default: false // 是否显示全屏遮罩
  }
})
</script>

<style scoped>
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  position: relative;
  z-index: 99999; /* loading应该在所有元素之上，没有例外 */
}

/* 全屏遮罩模式 */
.loading-spinner--overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999999; /* loading就是最高优先级，没有任何东西应该比它更高 */
  padding: 0;
}

.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.loading-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.loading-spinner--small {
  padding: 20rpx;
}

.loading-spinner--large {
  padding: 60rpx;
}

.spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid var(--border-color, #007AFF);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner--small .spinner {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
}

.loading-spinner--large .spinner {
  width: 80rpx;
  height: 80rpx;
  border-width: 6rpx;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.loading-spinner--small .loading-text {
  font-size: 24rpx;
  margin-top: 16rpx;
}

.loading-spinner--large .loading-text {
  font-size: 32rpx;
  margin-top: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
