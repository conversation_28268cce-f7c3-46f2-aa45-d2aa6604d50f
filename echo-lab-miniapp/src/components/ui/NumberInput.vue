<template>
  <view class="number-input" :class="{ 'disabled': disabled }">
    <view
      class="number-btn"
      :class="{ 'disabled': isMinusDisabled }"
      @click="!isMinusDisabled && handleMinus()"
    >-</view>
    <input
      class="number-value"
      type="number"
      :value="displayValue"
      @input="handleInput"
      @blur="handleBlur"
      :placeholder="placeholder"
      :disabled="disabled"
    />
    <view
      class="number-btn"
      :class="{ 'disabled': isPlusDisabled }"
      @click="!isPlusDisabled && handlePlus()"
    >+</view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Number,
    required: true
  },
  min: {
    type: Number,
    default: -Infinity
  },
  max: {
    type: Number,
    default: Infinity
  },
  step: {
    type: Number,
    default: 1
  },
  precision: {
    type: Number,
    default: 0
  },
  placeholder: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 显示值（根据精度格式化）
const displayValue = computed(() => {
  if (props.modelValue === undefined || props.modelValue === null) {
    return ''
  }

  if (props.precision === 0) {
    return props.modelValue.toString()
  }

  return props.modelValue.toFixed(props.precision)
})

// 计算按钮禁用状态
const isMinusDisabled = computed(() => {
  return props.disabled || props.modelValue <= props.min
})

const isPlusDisabled = computed(() => {
  return props.disabled || props.modelValue >= props.max
})

// 精度处理函数
const formatValue = (value) => {
  if (props.precision === 0) {
    return Math.round(value)
  }
  return parseFloat((Math.round(value * Math.pow(10, props.precision)) / Math.pow(10, props.precision)).toFixed(props.precision))
}

// 范围限制函数
const clampValue = (value) => {
  return Math.max(props.min, Math.min(props.max, value))
}

// 处理减少
const handleMinus = () => {
  if (props.disabled) return
  const newValue = formatValue(clampValue(props.modelValue - props.step))
  emit('update:modelValue', newValue)
  emit('change', newValue)
}

// 处理增加
const handlePlus = () => {
  if (props.disabled) return
  const newValue = formatValue(clampValue(props.modelValue + props.step))
  emit('update:modelValue', newValue)
  emit('change', newValue)
}

// 处理输入
const handleInput = (e) => {
  const value = e.detail.value

  // 允许空值、负号和小数点
  if (value === '' || value === '-' || value === '.') {
    return
  }

  const numValue = props.precision === 0 ? parseInt(value) : parseFloat(value)

  // 检查是否为有效数字
  if (isNaN(numValue)) {
    return
  }
}

// 处理失焦
const handleBlur = (e) => {
  const value = e.detail.value

  // 如果为空，设置为最小值
  if (value === '' || value === '-' || value === '.') {
    const newValue = props.min
    emit('update:modelValue', newValue)
    emit('change', newValue)
    return
  }

  let numValue = props.precision === 0 ? parseInt(value) : parseFloat(value)

  if (isNaN(numValue)) {
    return
  }

  // 限制在最小值和最大值之间
  numValue = clampValue(numValue)

  // 根据精度格式化
  numValue = formatValue(numValue)

  emit('update:modelValue', numValue)
  emit('change', numValue)
}
</script>

<style scoped>
.number-input {
  display: flex;
  align-items: center;
  border: 1rpx solid #dcdfe6;
  border-radius: 8rpx;
  background-color: #fff;
}

.number-input.disabled {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
}

.number-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #409eff;
  border-right: 1rpx solid #dcdfe6;
  transition: all 0.3s;
}

.number-btn:last-child {
  border-right: none;
  border-left: 1rpx solid #dcdfe6;
}

.number-btn.disabled {
  color: #c0c4cc;
  background-color: #f5f7fa;
  cursor: not-allowed;
}

.number-btn:not(.disabled):active {
  background-color: #f0f9ff;
}

.number-value {
  flex: 1;
  height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border: none;
  background-color: transparent;
}

.number-value:disabled {
  color: #c0c4cc;
  background-color: transparent;
}
</style>
