<template>
  <view class="responsive-image-container" :class="[containerClass, `layout-${layout}`]">
    <image 
      :src="processedImageUrl" 
      :mode="mode"
      :lazy-load="lazyLoad"
      :fade-show="fadeShow"
      :webp="webp"
      :show-menu-by-longpress="showMenuByLongpress"
      :class="['responsive-image', imageClass]"
      @load="handleLoad"
      @error="handleError"
      @tap="handleTap"
    />
    
    <!-- 加载状态 -->
    <view v-if="loading" class="image-loading" :class="loadingClass">
      <view class="loading-spinner"></view>
    </view>
    
    <!-- 错误状态 -->
    <view v-if="error" class="image-error" :class="errorClass">
      <text class="error-icon">📷</text>
      <text class="error-text">{{ errorText }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { getResponsiveImageUrl, getThumbnailUrl, getAvatarUrl } from '../../utils/imageUtils.js'

const props = defineProps({
  // 图片源URL
  src: {
    type: String,
    default: ''
  },
  
  // 图片类型：responsive(响应式), thumbnail(缩略图), avatar(头像)
  type: {
    type: String,
    default: 'responsive',
    validator: (value) => ['responsive', 'thumbnail', 'avatar'].includes(value)
  },
  
  // 目标宽度（rpx）
  width: {
    type: Number,
    default: 480
  },
  
  // 目标高度（rpx，仅用于缩略图）
  height: {
    type: Number,
    default: 360
  },
  
  // 头像尺寸（rpx，仅用于头像）
  size: {
    type: Number,
    default: 120
  },
  
  // 图片裁剪、缩放的模式
  mode: {
    type: String,
    default: 'aspectFill'
  },
  
  // 是否懒加载
  lazyLoad: {
    type: Boolean,
    default: true
  },
  
  // 是否渐显
  fadeShow: {
    type: Boolean,
    default: true
  },
  
  // 是否解析webp格式
  webp: {
    type: Boolean,
    default: true
  },
  
  // 是否开启长按图片显示识别小程序码菜单
  showMenuByLongpress: {
    type: Boolean,
    default: false
  },
  
  // 布局模式：absolute(绝对定位), relative(相对定位)
  layout: {
    type: String,
    default: 'absolute',
    validator: (value) => ['absolute', 'relative'].includes(value)
  },

  // 自定义样式类
  containerClass: {
    type: String,
    default: ''
  },
  
  imageClass: {
    type: String,
    default: ''
  },
  
  loadingClass: {
    type: String,
    default: ''
  },
  
  errorClass: {
    type: String,
    default: ''
  },
  
  // 错误提示文本
  errorText: {
    type: String,
    default: '图片加载失败'
  }
})

const emit = defineEmits(['load', 'error', 'tap'])

// 响应式数据
const loading = ref(true)
const error = ref(false)

// 处理后的图片URL
const processedImageUrl = computed(() => {
  if (!props.src) return ''
  
  switch (props.type) {
    case 'thumbnail':
      return getThumbnailUrl(props.src, props.width, props.height)
    case 'avatar':
      return getAvatarUrl(props.src, props.size)
    case 'responsive':
    default:
      return getResponsiveImageUrl(props.src, props.width)
  }
})

// 监听src变化，重置状态
watch(() => props.src, () => {
  if (props.src) {
    loading.value = true
    error.value = false
  }
}, { immediate: true })

// 处理图片加载成功
const handleLoad = (event) => {
  loading.value = false
  error.value = false
  emit('load', event)
}

// 处理图片加载失败
const handleError = (event) => {
  loading.value = false
  error.value = true
  emit('error', event)
}

// 处理图片点击
const handleTap = (event) => {
  emit('tap', event)
}
</script>

<style scoped>
/* 绝对定位布局 - 用于缩略图等需要填充父容器的场景 */
.responsive-image-container.layout-absolute {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 相对定位布局 - 用于头像等有固定尺寸的场景 */
.responsive-image-container.layout-relative {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.responsive-image {
  width: 100%;
  height: 100%;
  display: block;
}

.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.image-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #999;
}

.error-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.error-text {
  font-size: 24rpx;
  text-align: center;
}
</style>
