import { ref, reactive, computed, readonly } from 'vue'
import { showNetworkError, showLoading, hideLoading, withNetworkRetry } from '../utils/errorHandler.js'
import { isNetworkConnected } from '../utils/networkStatus.js'

/**
 * 统一的加载状态管理组合式函数
 * 提供标准化的加载、错误、空状态管理
 */
export function useLoading(options = {}) {
  const {
    initialLoading = false,
    showGlobalLoading = false, // 是否显示全局loading（uni.showLoading）
    loadingText = '加载中...',
    retryLimit = 3,
    retryDelay = 1000
  } = options

  // 基础状态
  const loading = ref(initialLoading)
  const error = ref(null)
  const isEmpty = ref(false)
  const retryCount = ref(0)

  // 计算属性
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)
  const canRetry = computed(() => hasError.value && retryCount.value < retryLimit)
  const isRetrying = computed(() => loading.value && retryCount.value > 0)

  // 状态管理方法
  const startLoading = (text = loadingText) => {
    loading.value = true
    error.value = null
    isEmpty.value = false
    
    if (showGlobalLoading) {
      showLoading(text)
    }
  }

  const stopLoading = () => {
    loading.value = false
    
    if (showGlobalLoading) {
      hideLoading()
    }
  }

  const setError = (err, showToast = true) => {
    loading.value = false
    error.value = err
    
    if (showGlobalLoading) {
      hideLoading()
    }
    
    if (showToast) {
      showNetworkError(err)
    }
  }

  const setEmpty = (empty = true) => {
    isEmpty.value = empty
  }

  const clearError = () => {
    error.value = null
    retryCount.value = 0
  }

  const reset = () => {
    loading.value = false
    error.value = null
    isEmpty.value = false
    retryCount.value = 0
    
    if (showGlobalLoading) {
      hideLoading()
    }
  }

  // 重试机制
  const retry = async (retryFn) => {
    if (!canRetry.value || !retryFn) return false

    try {
      retryCount.value++
      startLoading(`重试中... (${retryCount.value}/${retryLimit})`)
      
      // 添加延迟
      if (retryDelay > 0) {
        await new Promise(resolve => setTimeout(resolve, retryDelay))
      }
      
      const result = await retryFn()
      
      // 重试成功，重置状态
      clearError()
      stopLoading()
      
      return result
    } catch (err) {
      if (retryCount.value >= retryLimit) {
        setError(err)
      } else {
        // 还可以继续重试，不显示错误提示
        setError(err, false)
      }
      throw err
    }
  }

  // 执行异步操作的包装器
  const execute = async (asyncFn, options = {}) => {
    const {
      loadingText: customLoadingText,
      showError = true,
      resetBefore = true
    } = options

    if (resetBefore) {
      reset()
    }

    try {
      startLoading(customLoadingText)
      const result = await asyncFn()
      
      // 检查结果是否为空
      if (Array.isArray(result)) {
        setEmpty(result.length === 0)
      } else if (result && typeof result === 'object') {
        // 检查对象是否有数据
        const hasData = Object.keys(result).length > 0
        setEmpty(!hasData)
      }
      
      stopLoading()
      return result
    } catch (err) {
      setError(err, showError)
      throw err
    }
  }

  // 带重试的执行器（集成网络重试）
  const executeWithRetry = async (asyncFn, options = {}) => {
    const maxRetries = options.maxRetries || retryLimit
    const useNetworkRetry = options.useNetworkRetry !== false

    try {
      if (useNetworkRetry) {
        return await execute(async () => {
          return await withNetworkRetry(asyncFn, maxRetries, retryDelay)
        }, options)
      } else {
        // 原有的重试逻辑
        let lastError = null

        for (let i = 0; i <= maxRetries; i++) {
          try {
            retryCount.value = i
            const loadingText = i === 0 ?
              (options.loadingText || loadingText) :
              `重试中... (${i}/${maxRetries})`

            return await execute(asyncFn, {
              ...options,
              loadingText,
              resetBefore: i === 0
            })
          } catch (err) {
            lastError = err
            if (i < maxRetries && retryDelay > 0) {
              await new Promise(resolve => setTimeout(resolve, retryDelay))
            }
          }
        }

        // 所有重试都失败了
        setError(lastError, options.showError !== false)
        throw lastError
      }
    } catch (error) {
      setError(error, options.showError !== false)
      throw error
    }
  }

  return {
    // 状态
    loading: readonly(loading),
    error: readonly(error),
    isEmpty: readonly(isEmpty),
    retryCount: readonly(retryCount),
    
    // 计算属性
    isLoading,
    hasError,
    canRetry,
    isRetrying,
    
    // 方法
    startLoading,
    stopLoading,
    setError,
    setEmpty,
    clearError,
    reset,
    retry,
    execute,
    executeWithRetry
  }
}

/**
 * 列表加载状态管理
 * 专门用于处理分页列表的加载状态
 */
export function useListLoading(options = {}) {
  const baseLoading = useLoading(options)
  
  // 列表特有状态
  const loadingMore = ref(false)
  const hasMore = ref(true)
  const page = ref(1)
  const pageSize = ref(options.pageSize || 10)
  const total = ref(0)
  const list = ref([])

  // 计算属性
  const isLoadingMore = computed(() => loadingMore.value)
  const canLoadMore = computed(() => hasMore.value && !baseLoading.isLoading.value && !loadingMore.value)

  // 加载首页数据
  const loadFirstPage = async (loadFn, options = {}) => {
    page.value = 1
    hasMore.value = true
    list.value = []
    
    return await baseLoading.execute(async () => {
      const result = await loadFn({ page: page.value, pageSize: pageSize.value })
      
      if (result && result.data) {
        list.value = result.data
        total.value = result.total || 0
        hasMore.value = result.data.length === pageSize.value
      }
      
      return result
    }, options)
  }

  // 加载更多数据
  const loadMore = async (loadFn, options = {}) => {
    if (!canLoadMore.value) return

    try {
      loadingMore.value = true
      page.value++
      
      const result = await loadFn({ page: page.value, pageSize: pageSize.value })
      
      if (result && result.data) {
        list.value.push(...result.data)
        total.value = result.total || 0
        hasMore.value = result.data.length === pageSize.value
      }
      
      return result
    } catch (err) {
      page.value-- // 回滚页码
      baseLoading.setError(err)
      throw err
    } finally {
      loadingMore.value = false
    }
  }

  // 刷新列表
  const refresh = async (loadFn, options = {}) => {
    return await loadFirstPage(loadFn, { ...options, resetBefore: true })
  }

  // 重置列表状态
  const resetList = () => {
    baseLoading.reset()
    loadingMore.value = false
    hasMore.value = true
    page.value = 1
    total.value = 0
    list.value = []
  }

  return {
    // 继承基础loading的所有属性和方法
    ...baseLoading,
    
    // 列表特有状态
    loadingMore: readonly(loadingMore),
    hasMore: readonly(hasMore),
    page: readonly(page),
    pageSize: readonly(pageSize),
    total: readonly(total),
    list: readonly(list),
    
    // 计算属性
    isLoadingMore,
    canLoadMore,
    
    // 方法
    loadFirstPage,
    loadMore,
    refresh,
    resetList
  }
}
