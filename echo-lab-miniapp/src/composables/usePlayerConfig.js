/**
 * 播放策略配置管理 Composable
 * 适配uni-app小程序环境
 */
import { ref, computed, watch } from 'vue'

/**
 * 播放策略配置管理
 * @param {string} contentId 内容ID
 * @returns {Object} 配置管理对象
 */
export function usePlayerConfig(contentId) {
  // 配置状态
  const settings = ref({ sections: [] })
  const currentTemplate = ref(null)
  const serverConfig = ref({ sections: [] })
  
  // 配置状态跟踪
  const configState = ref({
    type: 'server', // 'custom' | 'template' | 'server' | 'global'
    source: null,   // 具体的模板对象或null
    name: ''        // 显示名称
  })

  // 存储键名
  const storageKeys = computed(() => {
    const id = contentId
    return {
      settings: `ps_${id}`,
      template: `pt_${id}`,
      source: `src_${id}`
    }
  })

  /**
   * 全局模板管理
   */
  const getGlobalTemplateId = () => {
    try {
      return uni.getStorageSync('globalPlaybackTemplate')
    } catch (error) {
      console.error('获取全局模板ID失败:', error)
      return null
    }
  }

  const setGlobalTemplateId = (templateId) => {
    try {
      if (templateId) {
        uni.setStorageSync('globalPlaybackTemplate', templateId)
      } else {
        uni.removeStorageSync('globalPlaybackTemplate')
      }
    } catch (error) {
      console.error('设置全局模板ID失败:', error)
    }
  }

  /**
   * 统一的存储操作（增强版）
   */
  const setStorageItem = (key, value) => {
    try {
      if (value) {
        const dataToStore = {
          data: value,
          timestamp: Date.now(),
          version: '1.0.0'
        }
        uni.setStorageSync(key, JSON.stringify(dataToStore))
      } else {
        uni.removeStorageSync(key)
      }
    } catch (error) {
      console.error('存储操作失败:', error)
      // 如果存储失败，尝试清理旧数据后重试
      try {
        uni.removeStorageSync(key)
        if (value) {
          const dataToStore = {
            data: value,
            timestamp: Date.now(),
            version: '1.0.0'
          }
          uni.setStorageSync(key, JSON.stringify(dataToStore))
        }
      } catch (retryError) {
        console.error('重试存储操作也失败:', retryError)
      }
    }
  }

  /**
   * 统一的读取操作（增强版）
   */
  const getStorageItem = (key) => {
    try {
      const storedData = uni.getStorageSync(key)
      if (!storedData) return null

      // 尝试解析为新格式
      try {
        const parsedData = JSON.parse(storedData)
        if (parsedData && typeof parsedData === 'object' && parsedData.data !== undefined) {
          // 新格式：包含版本和时间戳
          return {
            data: parsedData.data,
            timestamp: parsedData.timestamp || 0,
            version: parsedData.version || '1.0.0'
          }
        } else {
          // 旧格式：直接返回数据，添加默认元信息
          return {
            data: parsedData,
            timestamp: 0,
            version: '0.9.0'
          }
        }
      } catch (parseError) {
        // 如果解析失败，可能是字符串格式
        return {
          data: storedData,
          timestamp: 0,
          version: '0.9.0'
        }
      }
    } catch (error) {
      console.error('读取存储失败:', error)
      return null
    }
  }

  /**
   * 保存配置到本地存储
   */
  const saveToStorage = () => {
    try {
      const keys = storageKeys.value
      setStorageItem(keys.settings, settings.value)
      setStorageItem(keys.template, currentTemplate.value?.id)
      console.log("已保存配置到本地存储")
    } catch (error) {
      console.error("保存配置失败:", error)
    }
  }

  /**
   * 从本地存储加载配置（增强版）
   */
  const loadFromStorage = () => {
    try {
      const keys = storageKeys.value
      const settingsData = getStorageItem(keys.settings)
      const templateData = getStorageItem(keys.template)
      const sourceData = getStorageItem(keys.source)

      console.log("从本地存储加载配置:", {
        settingsKey: keys.settings,
        templateKey: keys.template,
        sourceKey: keys.source,
        settingsFound: settingsData ? "found" : "not found",
        templateFound: templateData ? "found" : "not found",
        sourceFound: sourceData ? "found" : "not found",
        settingsVersion: settingsData?.version || "unknown",
        settingsTimestamp: settingsData?.timestamp || 0
      })

      return {
        settings: settingsData?.data || null,
        templateId: templateData?.data || null,
        source: sourceData?.data || null,
        metadata: {
          settingsVersion: settingsData?.version || null,
          settingsTimestamp: settingsData?.timestamp || 0,
          templateVersion: templateData?.version || null,
          templateTimestamp: templateData?.timestamp || 0
        }
      }
    } catch (error) {
      console.error("从本地存储加载配置失败:", error)
      return {
        settings: null,
        templateId: null,
        source: null,
        metadata: {}
      }
    }
  }

  /**
   * 清除存储的配置
   */
  const clearStorage = () => {
    try {
      const keys = storageKeys.value
      uni.removeStorageSync(keys.settings)
      uni.removeStorageSync(keys.template)
      uni.removeStorageSync(keys.source)
      console.log("已清除存储的配置")
    } catch (error) {
      console.error("清除配置失败:", error)
    }
  }

  /**
   * 验证和优化配置
   */
  const validateAndOptimizeConfig = (config) => {
    if (!config || !config.sections || !Array.isArray(config.sections)) {
      console.error("无效的配置格式")
      return null
    }

    // 深拷贝配置
    const optimizedConfig = JSON.parse(JSON.stringify(config))

    // 验证和修复每个环节
    optimizedConfig.sections.forEach(section => {
      // 确保必需字段存在
      section.id = section.id || `section_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      section.name = section.name || section.title || '未命名环节'
      section.type = section.type || 'sequence'
      section.processingMode = section.processingMode || 'sequence'
      section.repeatCount = Math.max(1, Math.min(10, section.repeatCount || 4))
      section.pauseDuration = Math.max(0, Math.min(10000, section.pauseDuration || 2500))

      // 确保重复参数数组存在且长度正确
      const repeatCount = section.repeatCount
      if (!section.repeatSpeeds || !Array.isArray(section.repeatSpeeds)) {
        section.repeatSpeeds = Array(repeatCount).fill(1.0)
      } else {
        // 调整数组长度
        while (section.repeatSpeeds.length < repeatCount) {
          section.repeatSpeeds.push(1.0)
        }
        if (section.repeatSpeeds.length > repeatCount) {
          section.repeatSpeeds = section.repeatSpeeds.slice(0, repeatCount)
        }
      }

      if (!section.repeatPauses || !Array.isArray(section.repeatPauses)) {
        section.repeatPauses = Array(repeatCount).fill(section.pauseDuration)
      } else {
        // 调整数组长度
        while (section.repeatPauses.length < repeatCount) {
          section.repeatPauses.push(section.pauseDuration)
        }
        if (section.repeatPauses.length > repeatCount) {
          section.repeatPauses = section.repeatPauses.slice(0, repeatCount)
        }
      }

      // 翻译设置
      if (section.enableTranslation === undefined) section.enableTranslation = false
      if (section.translationLanguage === undefined) section.translationLanguage = ''
      if (section.translationPosition === undefined) {
        section.translationPosition = Math.min(2, section.repeatCount)
      }

      // 关键词设置
      if (section.enableKeywords === undefined) section.enableKeywords = false
      if (section.keywordRepeatCount === undefined) section.keywordRepeatCount = 2
      if (section.keywordPosition === undefined) {
        section.keywordPosition = Math.min(2, section.repeatCount)
      }
    })

    return optimizedConfig
  }

  /**
   * 智能配置加载
   */
  const smartLoadConfig = async () => {
    try {
      console.log("开始智能配置加载...")

      // 1. 检查服务器配置是否准备好
      if (!serverConfig.value || !serverConfig.value.sections || serverConfig.value.sections.length === 0) {
        console.log("服务器配置未准备好，跳过配置加载")
        return false
      }

      // 2. 加载保存的数据
      const savedData = loadFromStorage()

      // 3. 如果有保存的用户设置，使用用户设置
      if (savedData.settings) {
        console.log("找到保存的用户设置，正在应用...")
        settings.value = savedData.settings

        // 4. 根据配置来源设置状态
        if (savedData.source === 'server') {
          console.log('检测到 server 来源标记，设置为内容默认')
          currentTemplate.value = null
          updateConfigState('server')
        } else if (savedData.templateId) {
          console.log('检测到模板ID，但模板功能已移除，设置为自定义')
          currentTemplate.value = null
          updateConfigState('custom')
        } else {
          console.log('没有模板和来源标记，设置为自定义')
          updateConfigState('custom')
        }

        return true
      }

      // 5. 检查全局默认模板（新增的优先级处理）
      const globalTemplateId = getGlobalTemplateId()
      if (globalTemplateId) {
        console.log('检查全局默认模板:', globalTemplateId)
        try {
          // 小程序环境下简化全局模板应用
          // 这里可以根据实际需求扩展模板应用逻辑
          const globalTemplate = { id: globalTemplateId, name: '全局学习模式' }
          console.log('应用全局默认模板:', globalTemplate.name)

          // 应用全局模板到服务器配置
          // 注意：这里需要实际的模板应用逻辑，暂时使用服务器配置
          settings.value = JSON.parse(JSON.stringify(serverConfig.value))
          updateConfigState('global', globalTemplate)
          return true
        } catch (error) {
          console.error('应用全局模板失败:', error)
        }
      }

      // 6. 最后使用服务器原始配置
      console.log("没有全局模板，使用服务器原始配置")
      settings.value = JSON.parse(JSON.stringify(serverConfig.value))
      updateConfigState('server')

      return true
    } catch (error) {
      console.error("智能加载配置失败:", error)
      return false
    }
  }

  /**
   * 更新配置状态
   */
  const updateConfigState = (type, template = null) => {
    configState.value = {
      type,
      source: template,
      name: template ? template.name : ''
    }
    console.log('配置状态已更新:', configState.value)
  }

  /**
   * 检查设置是否与模板匹配（简化版）
   */
  const isSettingsMatchTemplate = (template, settingsToCheck) => {
    if (!template || !template.config || !template.config.sections) return false
    if (!settingsToCheck || !settingsToCheck.sections) return false

    const templateSections = template.config.sections
    const currentSections = settingsToCheck.sections

    // 环节数量不匹配
    if (templateSections.length !== currentSections.length) return false

    // 检查每个环节的关键配置
    for (let i = 0; i < templateSections.length; i++) {
      const templateSection = templateSections[i]
      const currentSection = currentSections[i]

      // 检查关键配置项
      if (templateSection.repeatCount !== currentSection.repeatCount) return false
      if (templateSection.pauseDuration !== currentSection.pauseDuration) return false
      if (templateSection.enableTranslation !== currentSection.enableTranslation) return false
      if (templateSection.enableKeywords !== currentSection.enableKeywords) return false

      // 检查翻译配置
      if (templateSection.enableTranslation) {
        if (templateSection.translationLanguage !== currentSection.translationLanguage) return false
        if (templateSection.translationPosition !== currentSection.translationPosition) return false
      }

      // 检查关键词配置
      if (templateSection.enableKeywords) {
        if (templateSection.keywordPosition !== currentSection.keywordPosition) return false
        if (templateSection.keywordRepeatCount !== currentSection.keywordRepeatCount) return false
      }
    }

    return true
  }



  /**
   * 重置为服务器配置
   */
  const resetToServerConfig = () => {
    if (!serverConfig.value || !serverConfig.value.sections || serverConfig.value.sections.length === 0) {
      console.error("服务器配置不可用")
      return false
    }

    settings.value = JSON.parse(JSON.stringify(serverConfig.value))
    currentTemplate.value = null
    updateConfigState('server')

    // 清除本地存储
    clearStorage()

    console.log("已重置为服务器配置")
    return true
  }

  /**
   * 重置为已保存的配置
   */
  const resetToSavedConfig = async () => {
    try {
      // 重新加载保存的配置和模板状态
      const success = await smartLoadConfig()
      if (success) {
        console.log('已重置为保存的配置')
        return true
      } else {
        console.error('无法加载保存的配置')
        return false
      }
    } catch (error) {
      console.error('重置配置失败:', error)
      return false
    }
  }

  /**
   * 统一的保存逻辑（配置和模板状态同步保存）
   */
  const saveConfigAndTemplate = () => {
    const keys = storageKeys.value
    const { type } = configState.value

    // 保存配置
    setStorageItem(keys.settings, settings.value)

    // 根据状态类型保存对应标记
    if (currentTemplate.value) {
      setStorageItem(keys.template, currentTemplate.value.id)
      setStorageItem(keys.source, null)
    } else if (type === 'server') {
      setStorageItem(keys.source, 'server')
      setStorageItem(keys.template, null)
    } else {
      setStorageItem(keys.template, null)
      setStorageItem(keys.source, null)
    }

    console.log('配置和模板状态已保存')
  }

  /**
   * 统一的加载逻辑（配置和模板状态同步加载）
   */
  const loadConfigAndTemplate = async () => {
    const savedData = loadFromStorage()

    if (savedData.settings) {
      settings.value = savedData.settings

      // 验证模板状态
      if (savedData.templateId) {
        console.log('loadConfigAndTemplate: 检查模板ID:', savedData.templateId)
        // 小程序环境下简化模板处理
        currentTemplate.value = { id: savedData.templateId, name: '已保存的模板' }
        console.log('loadConfigAndTemplate: 恢复模板状态')
      }
    } else {
      settings.value = JSON.parse(JSON.stringify(serverConfig.value))
    }
  }

  /**
   * 导出配置为JSON字符串
   */
  const exportConfig = () => {
    try {
      const exportData = {
        version: '1.0.0',
        timestamp: Date.now(),
        contentId: contentId,
        settings: settings.value,
        currentTemplate: currentTemplate.value,
        configState: configState.value,
        metadata: {
          exportedAt: new Date().toISOString(),
          platform: 'miniapp'
        }
      }

      return JSON.stringify(exportData, null, 2)
    } catch (error) {
      console.error('导出配置失败:', error)
      throw new Error('导出配置失败')
    }
  }

  /**
   * 从JSON字符串导入配置
   */
  const importConfig = (jsonString) => {
    try {
      const importData = JSON.parse(jsonString)

      // 验证导入数据格式
      if (!importData.version || !importData.settings) {
        throw new Error('无效的配置文件格式')
      }

      // 版本兼容性检查
      if (importData.version !== '1.0.0') {
        console.warn('配置文件版本不匹配，尝试兼容性导入')
      }

      // 验证和优化配置
      const validatedSettings = validateAndOptimizeConfig(importData.settings)
      if (!validatedSettings) {
        throw new Error('配置文件内容无效')
      }

      // 应用导入的配置
      settings.value = validatedSettings

      if (importData.currentTemplate) {
        currentTemplate.value = importData.currentTemplate
        updateConfigState('template', importData.currentTemplate)
      } else {
        currentTemplate.value = null
        updateConfigState('custom')
      }

      // 保存导入的配置
      saveConfigAndTemplate()

      console.log('配置导入成功:', {
        version: importData.version,
        timestamp: importData.timestamp,
        settingsCount: validatedSettings.sections?.length || 0
      })

      return true
    } catch (error) {
      console.error('导入配置失败:', error)
      throw error
    }
  }

  /**
   * 创建配置备份
   */
  const createBackup = () => {
    try {
      const backupKey = `${storageKeys.value.settings}_backup_${Date.now()}`
      const backupData = {
        originalKey: storageKeys.value.settings,
        settings: settings.value,
        currentTemplate: currentTemplate.value,
        configState: configState.value,
        timestamp: Date.now(),
        version: '1.0.0'
      }

      setStorageItem(backupKey, backupData)

      // 清理旧备份（保留最近5个）
      cleanupOldBackups()

      console.log('配置备份创建成功:', backupKey)
      return backupKey
    } catch (error) {
      console.error('创建配置备份失败:', error)
      throw error
    }
  }

  /**
   * 恢复配置备份
   */
  const restoreBackup = (backupKey) => {
    try {
      const backupData = getStorageItem(backupKey)
      if (!backupData || !backupData.data) {
        throw new Error('备份数据不存在或已损坏')
      }

      const backup = backupData.data

      // 验证备份数据
      if (!backup.settings) {
        throw new Error('备份数据格式无效')
      }

      // 恢复配置
      settings.value = backup.settings
      currentTemplate.value = backup.currentTemplate || null
      configState.value = backup.configState || { type: 'custom', source: null, name: '' }

      // 保存恢复的配置
      saveConfigAndTemplate()

      console.log('配置恢复成功:', {
        backupKey,
        timestamp: backup.timestamp,
        version: backup.version
      })

      return true
    } catch (error) {
      console.error('恢复配置备份失败:', error)
      throw error
    }
  }

  /**
   * 获取可用的备份列表
   */
  const getBackupList = () => {
    try {
      const allKeys = uni.getStorageInfoSync().keys || []
      const backupKeys = allKeys.filter(key =>
        key.startsWith(`${storageKeys.value.settings}_backup_`)
      )

      const backups = backupKeys.map(key => {
        const backupData = getStorageItem(key)
        if (backupData && backupData.data) {
          return {
            key,
            timestamp: backupData.data.timestamp || 0,
            version: backupData.data.version || 'unknown',
            date: new Date(backupData.data.timestamp || 0).toLocaleString(),
            sectionsCount: backupData.data.settings?.sections?.length || 0
          }
        }
        return null
      }).filter(Boolean)

      // 按时间戳降序排列
      backups.sort((a, b) => b.timestamp - a.timestamp)

      return backups
    } catch (error) {
      console.error('获取备份列表失败:', error)
      return []
    }
  }

  /**
   * 清理旧备份
   */
  const cleanupOldBackups = () => {
    try {
      const backups = getBackupList()
      const maxBackups = 5

      if (backups.length > maxBackups) {
        const backupsToDelete = backups.slice(maxBackups)
        backupsToDelete.forEach(backup => {
          try {
            uni.removeStorageSync(backup.key)
            console.log('删除旧备份:', backup.key)
          } catch (error) {
            console.error('删除备份失败:', backup.key, error)
          }
        })
      }
    } catch (error) {
      console.error('清理旧备份失败:', error)
    }
  }

  /**
   * 获取存储使用情况
   */
  const getStorageUsage = () => {
    try {
      const storageInfo = uni.getStorageInfoSync()
      const keys = storageKeys.value

      const usage = {
        totalKeys: storageInfo.keys?.length || 0,
        currentSize: storageInfo.currentSize || 0,
        limitSize: storageInfo.limitSize || 0,
        playerConfigKeys: 0,
        playerConfigSize: 0
      }

      // 计算播放器配置相关的存储使用
      if (storageInfo.keys) {
        const playerKeys = storageInfo.keys.filter(key =>
          key.startsWith('ps_') || key.startsWith('pt_') || key.startsWith('src_')
        )
        usage.playerConfigKeys = playerKeys.length

        // 估算大小（小程序环境下无法精确获取单个key的大小）
        playerKeys.forEach(key => {
          try {
            const data = uni.getStorageSync(key)
            if (data) {
              usage.playerConfigSize += JSON.stringify(data).length
            }
          } catch (error) {
            // 忽略读取错误
          }
        })
      }

      return usage
    } catch (error) {
      console.error('获取存储使用情况失败:', error)
      return {
        totalKeys: 0,
        currentSize: 0,
        limitSize: 0,
        playerConfigKeys: 0,
        playerConfigSize: 0
      }
    }
  }

  /**
   * 智能更新模板状态（仅在用户主动修改时调用）
   */
  const updateTemplateState = () => {
    currentTemplate.value = null
    updateConfigState('custom')

    // 清除所有关联标记
    const keys = storageKeys.value
    setStorageItem(keys.template, null)
    setStorageItem(keys.source, null)
  }

  return {
    // 状态
    settings,
    currentTemplate,
    serverConfig,
    configState,
    storageKeys,

    // 基础方法
    saveToStorage,
    loadFromStorage,
    clearStorage,
    validateAndOptimizeConfig,
    smartLoadConfig,
    updateConfigState,
    resetToServerConfig,
    resetToSavedConfig,
    saveConfigAndTemplate,
    loadConfigAndTemplate,

    // 模板状态管理
    isSettingsMatchTemplate,
    updateTemplateState,

    // 全局模板管理
    getGlobalTemplateId,
    setGlobalTemplateId,

    // 增强功能
    exportConfig,
    importConfig,
    createBackup,
    restoreBackup,
    getBackupList,
    cleanupOldBackups,
    getStorageUsage
  }
}
