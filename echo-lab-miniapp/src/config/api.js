/**
 * API配置 - 基于H5项目的API端点配置
 * 适配小程序环境
 */

// API端点配置
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    BASE: "/api/auth",
    SEND_CODE: "/api/auth/send-code",
    VERIFY_CODE: "/api/auth/verify-code",
    CURRENT_USER: "/api/auth/me",
    UPDATE_USER: "/api/auth/me",
    LOGOUT: "/api/auth/logout",
  },

  // 内容管理
  CONTENTS: {
    BASE: "/api/contents",
    PUBLIC: "/api/public/contents",
    BY_ID: (id) => `/api/contents/${id}`,
  },

  // 合集管理
  COLLECTIONS: {
    BASE: "/api/collections",
    PUBLIC: "/api/collections/public",
    FAVORITES: "/api/collections/favorites",
    BY_ID: (id) => `/api/collections/${id}`,
    ITEMS: (id) => `/api/collections/${id}/items`,
    FAVORITE: (id) => `/api/collections/${id}/favorite`,
  },

  // 用户空间
  USERS: {
    BASE: "/api/users",
    BY_ID: (id) => `/api/users/${id}`,
    CONTENTS: (id) => `/api/users/${id}/contents`,
    COLLECTIONS: (id) => `/api/users/${id}/collections`,
  },

  // 收藏管理
  FAVORITES: {
    BASE: "/api/favorites",
    CHECK: (id) => `/api/favorites/check/${id}`,
  },

  // 用户等级
  USER_LEVELS: {
    BASE: "/api/user-levels",
    ME: "/api/user-levels/me",
  },

  // 播放策略模板
  TEMPLATES: {
    BASE: "/api/templates",
    BY_ID: (id) => `/api/templates/${id}`,
    USE: (id) => `/api/templates/${id}/use`,
    DUPLICATE: (id) => `/api/templates/${id}/duplicate`,
  },
};

// 语言配置
export const LANGUAGES = {
  'ja': '日语',
  'en': '英语',
  'zh-CN': '中文'
};



// 播放速度配置
export const SPEED_OPTIONS = [
  { value: 0.5, label: '0.5x' },
  { value: 0.75, label: '0.75x' },
  { value: 1.0, label: '1.0x' },
  { value: 1.25, label: '1.25x' },
  { value: 1.5, label: '1.5x' },
  { value: 2.0, label: '2.0x' }
];

export default {
  API_ENDPOINTS,
  LANGUAGES,
  SPEED_OPTIONS,
};
