/**
 * 语言等级配置
 * 定义不同语言的学习等级体系
 */

// 不同语言的等级配置
export const LANGUAGE_LEVELS = {
  ja: [
    { key: 'N5', name: 'N5', description: '初级 - 基础词汇和语法' },
    { key: 'N4', name: 'N4', description: '初中级 - 日常对话' },
    { key: 'N3', name: 'N3', description: '中级 - 复杂表达' },
    { key: 'N2', name: 'N2', description: '中高级 - 流利交流' },
    { key: 'N1', name: 'N1', description: '高级 - 接近母语' }
  ],
  en: [
    { key: 'A1', name: 'A1', description: '初级 - 基础词汇' },
    { key: 'A2', name: 'A2', description: '初中级 - 简单交流' },
    { key: 'B1', name: 'B1', description: '中级 - 日常对话' },
    { key: 'B2', name: 'B2', description: '中高级 - 流利表达' },
    { key: 'C1', name: 'C1', description: '高级 - 专业交流' },
    { key: 'C2', name: 'C2', description: '精通 - 接近母语' }
  ],
  'zh-CN': [
    { key: 'HSK1', name: 'HSK1', description: '初级 - 基础汉字' },
    { key: 'HSK2', name: 'HSK2', description: '初中级 - 简单对话' },
    { key: 'HSK3', name: 'HSK3', description: '中级 - 日常交流' },
    { key: 'HSK4', name: 'HSK4', description: '中高级 - 复杂表达' },
    { key: 'HSK5', name: 'HSK5', description: '高级 - 流利交流' },
    { key: 'HSK6', name: 'HSK6', description: '精通 - 专业水平' }
  ],
  'zh-TW': [
    { key: 'HSK1', name: 'HSK1', description: '初级 - 基础繁體字' },
    { key: 'HSK2', name: 'HSK2', description: '初中級 - 簡單對話' },
    { key: 'HSK3', name: 'HSK3', description: '中級 - 日常交流' },
    { key: 'HSK4', name: 'HSK4', description: '中高級 - 複雜表達' },
    { key: 'HSK5', name: 'HSK5', description: '高級 - 流利交流' },
    { key: 'HSK6', name: 'HSK6', description: '精通 - 專業水平' }
  ]
};

/**
 * 获取指定语言的等级列表
 * @param {string} languageCode 语言代码
 * @returns {Array} 等级列表
 */
export function getLanguageLevels(languageCode) {
  return LANGUAGE_LEVELS[languageCode] || [];
}

/**
 * 获取等级信息
 * @param {string} languageCode 语言代码
 * @param {string} levelKey 等级键
 * @returns {Object|null} 等级信息
 */
export function getLevelInfo(languageCode, levelKey) {
  const levels = getLanguageLevels(languageCode);
  return levels.find(level => level.key === levelKey) || null;
}

/**
 * 验证等级是否有效
 * @param {string} languageCode 语言代码
 * @param {string} levelKey 等级键
 * @returns {boolean} 是否有效
 */
export function isValidLevel(languageCode, levelKey) {
  if (!languageCode || !levelKey) return false;
  return !!getLevelInfo(languageCode, levelKey);
}

/**
 * 获取默认等级（每种语言的第一个等级）
 * @param {string} languageCode 语言代码
 * @returns {string|null} 默认等级键
 */
export function getDefaultLevel(languageCode) {
  const levels = getLanguageLevels(languageCode);
  return levels.length > 0 ? levels[0].key : null;
}

export default {
  LANGUAGE_LEVELS,
  getLanguageLevels,
  getLevelInfo,
  isValidLevel,
  getDefaultLevel
};
