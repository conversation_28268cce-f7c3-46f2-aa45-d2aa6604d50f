<template>
  <view class="about-page">
    <!-- 应用信息 -->
    <view class="app-info-section">
      <view class="app-logo">
        <image src="/static/logo.jpg" class="logo-image" mode="aspectFit" />
      </view>
      <view class="app-details">
        <text class="app-name">磨耳朵听力</text>
        <text class="app-subtitle">Echo Lab</text>
        <text class="app-version">版本 1.0.0</text>
      </view>
    </view>

    <!-- 应用描述 -->
    <view class="description-section">
      <text class="description-text">
        专业的听力练习小程序，帮助您提升语言听力水平。支持多种语言学习，个性化练习设置，让语言学习更高效。
      </text>
    </view>

    <!-- 功能特色 -->
    <view class="features-section">
      <view class="section-title">
        <text>功能特色</text>
      </view>
      <view class="feature-list">
        <view class="feature-item">
          <text class="feature-icon">🎧</text>
          <text class="feature-text">高质量音频内容</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">⚙️</text>
          <text class="feature-text">个性化播放设置</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">📱</text>
          <text class="feature-text">离线缓存支持</text>
        </view>
      </view>
    </view>

    <!-- H5版本信息 -->
    <view class="web-version-section">
      <view class="section-title">
        <text>H5版本</text>
      </view>
      <view class="web-info">
        <text class="web-description">
          访问完整的H5版本，体验更多功能
        </text>
        <view class="web-url-container">
          <text class="web-url">https://echolab.club</text>
          <button class="copy-btn" @click="copyWebUrl">复制地址</button>
        </view>
      </view>
    </view>

    <!-- 联系信息 -->
    <view class="contact-section">
      <view class="section-title">
        <text>联系我们</text>
      </view>
      <view class="contact-info">
        <text class="contact-text">
          如有问题或建议，欢迎通过H5版本联系我们
        </text>
      </view>
    </view>

    <!-- 版权信息 -->
    <view class="copyright-section">
      <text class="copyright-text">© 2025 Echo Lab. All rights reserved.</text>
    </view>
  </view>
</template>

<script setup>
// 复制H5地址
const copyWebUrl = () => {
  uni.setClipboardData({
    data: 'https://echolab.club',
    success: () => {
      uni.showToast({
        title: '地址已复制',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'error'
      })
    }
  })
}
</script>

<style scoped>
.about-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0 0 40rpx 0;
}

/* 应用信息区域 */
.app-info-section {
  background: white;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.app-logo {
  margin-bottom: 30rpx;
}

.logo-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
}

.app-details {
  text-align: center;
}

.app-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.app-subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.app-version {
  font-size: 24rpx;
  color: #999;
  display: block;
}

/* 描述区域 */
.description-section {
  background: white;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.description-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
}

/* 功能特色区域 */
.features-section {
  background: white;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.section-title {
  margin-bottom: 30rpx;
}

.section-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}

.feature-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  width: 40rpx;
  text-align: center;
}

.feature-text {
  font-size: 28rpx;
  color: #666;
}

/* H5版本区域 */
.web-version-section {
  background: white;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.web-info {
  margin-top: 20rpx;
}

.web-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  display: block;
  margin-bottom: 20rpx;
}

.web-url-container {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.web-url {
  font-size: 26rpx;
  color: #007AFF;
  font-family: monospace;
  flex: 1;
}

.copy-btn {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
}

.copy-btn:active {
  background: #0056CC;
}

/* 联系信息区域 */
.contact-section {
  background: white;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.contact-info {
  margin-top: 20rpx;
}

.contact-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 版权信息区域 */
.copyright-section {
  padding: 40rpx;
  text-align: center;
}

.copyright-text {
  font-size: 24rpx;
  color: #999;
}
</style>
