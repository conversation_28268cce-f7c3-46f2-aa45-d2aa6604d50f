<template>
  <view class="login-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">Echo Lab</text>
      <text class="page-subtitle">使用邮箱验证码登录</text>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <!-- 邮箱输入 -->
      <view class="form-item">
        <text class="form-label">邮箱地址</text>
        <input
          v-model="loginForm.email"
          type="text"
          placeholder="请输入邮箱地址"
          class="form-input"
          :class="{ 'input-error': emailError }"
          :disabled="codeSent || verifying"
          @blur="validateEmail"
          @input="clearEmailError"
        />
        <text v-if="emailError" class="error-text">{{ emailError }}</text>
      </view>

      <!-- 验证码输入 -->
      <view class="form-item" v-if="codeSent">
        <text class="form-label">验证码</text>
        <view class="code-input-group">
          <input
            v-model="loginForm.code"
            type="number"
            placeholder="请输入验证码"
            class="form-input code-input"
            :disabled="verifying"
            maxlength="6"
          />
          <button
            class="resend-btn"
            :disabled="countdown > 0"
            @click="sendVerificationCode"
          >
            {{ countdown > 0 ? `${countdown}s` : '重新发送' }}
          </button>
        </view>
      </view>

      <!-- 登录按钮 -->
      <button
        class="login-btn"
        :class="{
          disabled: isButtonDisabled,
          'not-agreed': !agreedToTerms && !codeSent
        }"
        :disabled="isButtonDisabled"
        @click="handleSubmit"
      >
        {{ buttonText }}
      </button>

      <!-- 提示信息 -->
      <view class="tips" v-if="codeSent">
        <text class="tip-text">验证码已发送到您的邮箱，请查收</text>
      </view>

      <!-- 用户协议 -->
      <view class="agreement">
        <view class="agreement-checkbox">
          <checkbox-group @change="handleAgreementChange">
            <checkbox value="agree" color="#409eff" class="custom-checkbox" />
          </checkbox-group>
          <view class="agreement-text-container">
            <text class="agreement-text">
              我已阅读并同意
              <text class="agreement-link" @click="showUserAgreement">《用户协议》</text>
              和
              <text class="agreement-link" @click="showPrivacyPolicy">《隐私政策》</text>
            </text>
          </view>
        </view>

        <!-- 协议警告提示 - 占位形式 -->
        <view class="agreement-warning">
          <text class="warning-text" :class="{ 'visible': !agreedToTerms && !codeSent }">
            请先阅读并同意用户协议和隐私政策
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onBeforeUnmount } from 'vue'
import { sendCode, verifyCode } from '../../services/authService.js'

// 表单数据
const loginForm = reactive({
  email: '',
  code: ''
})

// 状态
const codeSent = ref(false)
const sending = ref(false)
const verifying = ref(false)
const countdown = ref(0)
const agreedToTerms = ref(false)
const emailError = ref('')
let countdownTimer = null

// 计算属性
const isButtonDisabled = computed(() => {
  if (!agreedToTerms.value) {
    return true
  }
  if (!codeSent.value) {
    return !loginForm.email || sending.value
  } else {
    return !loginForm.code || verifying.value
  }
})

const buttonText = computed(() => {
  if (!codeSent.value) {
    return sending.value ? '发送中...' : '发送验证码'
  } else {
    return verifying.value ? '登录中...' : '登录'
  }
})

// 邮箱校验函数
const validateEmail = () => {
  if (!loginForm.email) {
    emailError.value = ''
    return false
  }

  // 严格的邮箱格式验证
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/

  if (!emailRegex.test(loginForm.email)) {
    emailError.value = '请输入有效的邮箱地址'
    return false
  }

  if (loginForm.email.length > 254) {
    emailError.value = '邮箱地址过长'
    return false
  }

  // 检查常见的无效邮箱格式
  const invalidPatterns = [
    /\.{2,}/, // 连续的点
    /^\./, // 以点开头
    /\.$/, // 以点结尾
    /@\./, // @后直接跟点
    /\.@/, // 点后直接跟@
  ]

  if (invalidPatterns.some(pattern => pattern.test(loginForm.email))) {
    emailError.value = '邮箱格式不正确'
    return false
  }

  emailError.value = ''
  return true
}

// 清除邮箱错误
const clearEmailError = () => {
  if (emailError.value) {
    emailError.value = ''
  }
}

// 处理协议变化
const handleAgreementChange = (e) => {
  agreedToTerms.value = e.detail.value.includes('agree')
}

// 显示用户协议
const showUserAgreement = () => {
  uni.navigateTo({
    url: '/pages/legal/user-agreement'
  })
}

// 显示隐私政策
const showPrivacyPolicy = () => {
  uni.navigateTo({
    url: '/pages/legal/privacy-policy'
  })
}

// 发送验证码
const sendVerificationCode = async () => {
  if (!agreedToTerms.value) {
    uni.showModal({
      title: '温馨提示',
      content: '请先阅读并同意《用户协议》和《隐私政策》后再继续',
      showCancel: false,
      confirmText: '我知道了'
    })
    return
  }

  if (!loginForm.email) {
    uni.showToast({
      title: '请输入邮箱地址',
      icon: 'none'
    })
    return
  }

  // 使用统一的邮箱校验函数
  if (!validateEmail()) {
    uni.showToast({
      title: emailError.value || '请输入有效的邮箱地址',
      icon: 'none'
    })
    return
  }

  try {
    sending.value = true
    const result = await sendCode(loginForm.email, 'login')

    if (result.success) {
      uni.showToast({
        title: '验证码已发送',
        icon: 'success'
      })
      codeSent.value = true
      startCountdown()
    } else {
      uni.showToast({
        title: result.error || '发送失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    uni.showToast({
      title: error.error || '发送失败，请稍后重试',
      icon: 'none'
    })
  } finally {
    sending.value = false
  }
}

// 验证码登录
const verifyCodeLogin = async () => {
  if (!loginForm.code) {
    uni.showToast({
      title: '请输入验证码',
      icon: 'none'
    })
    return
  }

  // 验证码格式校验
  const codeRegex = /^\d{6}$/
  if (!codeRegex.test(loginForm.code)) {
    uni.showToast({
      title: '验证码必须是6位数字',
      icon: 'none'
    })
    return
  }

  try {
    verifying.value = true
    const result = await verifyCode(loginForm.email, loginForm.code, 'login')

    if (result.success) {
      // 立即跳转，在profile页面显示成功提示
      uni.switchTab({
        url: '/pages/profile/profile',
        success: () => {
          // 跳转成功后显示提示
          setTimeout(() => {
            uni.showToast({
              title: '登录成功',
              icon: 'success'
            })
          }, 100)
        }
      })
    } else {
      uni.showToast({
        title: result.error || '验证码错误',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('验证码验证失败:', error)
    uni.showToast({
      title: error.error || '登录失败',
      icon: 'none'
    })
  } finally {
    verifying.value = false
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer)
    }
  }, 1000)
}

// 处理表单提交
const handleSubmit = () => {
  if (codeSent.value) {
    verifyCodeLogin()
  } else {
    sendVerificationCode()
  }
}

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style scoped>
/* 引入全局CSS变量 */
@import '../../styles/variables.css';

.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 80rpx 40rpx;
}

.page-header {
  text-align: center;
  margin-bottom: 80rpx;
  color: white;
}

.page-title {
  display: block;
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  margin-bottom: 20rpx;
}

.page-subtitle {
  display: block;
  font-size: var(--font-size-lg);
  opacity: 0.9;
}

.login-form {
  background: white;
  border-radius: 16rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
  margin-bottom: 16rpx;
  font-weight: var(--font-weight-medium);
}

.form-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e4e7ed;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: var(--font-size-md);
  box-sizing: border-box;
}

.form-input:focus {
  border-color: var(--color-primary);
}

.form-input:disabled {
  background-color: #f5f7fa;
  color: var(--color-text-placeholder);
}

.form-input.input-error {
  border-color: var(--color-danger);
}

.error-text {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--color-danger);
  margin-top: 8rpx;
  line-height: 1.4;
}

.code-input-group {
  display: flex;
  gap: 16rpx;
}

.code-input {
  flex: 1;
}

.resend-btn {
  width: 160rpx;
  height: 88rpx;
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  text-align: center;
}

.resend-btn:disabled {
  background-color: var(--color-text-placeholder);
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  text-align: center;
}

.login-btn.disabled {
  background-color: var(--color-text-placeholder);
}

.login-btn.not-agreed {
  background-color: #f0f0f0;
  color: var(--color-text-secondary);
  border: 2rpx dashed var(--color-text-placeholder);
}

.tips {
  margin-top: 30rpx;
  text-align: center;
}

.tip-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.agreement {
  margin-top: 30rpx;
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.agreement-text-container {
  flex: 1;
  margin-top: 2rpx;
}

.agreement-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: 1.6;
}

.agreement-link {
  color: var(--color-primary);
  text-decoration: underline;
}

.agreement-warning {
  margin-top: 16rpx;
  min-height: 32rpx; /* 保持固定高度，避免布局跳动 */
}

.warning-text {
  font-size: var(--font-size-sm);
  color: transparent; /* 默认透明 */
  line-height: 1.4;
  transition: color 0.2s ease; /* 平滑过渡效果 */
}

.warning-text.visible {
  color: #ff4757; /* 显示时为红色 */
}

.custom-checkbox {
  transform: scale(0.8);
  margin-top: 2rpx;
}
</style>
