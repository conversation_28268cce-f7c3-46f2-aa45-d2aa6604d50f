<template>
  <!-- 全屏加载遮罩 -->
  <LoadingSpinner
    v-if="contentLoading.isLoading.value"
    :overlay="true"
    text="正在加载收藏内容..."
  />

  <view class="container">
    <!-- 收藏内容网格 -->
    <view class="content-grid">
      <!-- 错误状态 -->
      <view v-if="contentLoading.error.value" class="error">
        <text class="error-text">{{ contentLoading.error.value.message }}</text>
        <button class="retry-btn" @click="handleRetry">重试</button>
      </view>

      <!-- 空状态 -->
      <view v-else-if="favorites.length === 0" class="empty">
        <text>暂无收藏内容</text>
      </view>

      <!-- 内容网格 -->
      <view v-else class="grid-container">
        <ContentCard
          v-for="favorite in favorites"
          :key="favorite.id"
          :content="favorite"
          :show-author="true"
          :show-view-count="true"
          :show-date="true"
          @click="playContent"
        >
          <template #overlay-actions>
            <view class="favorite-control" @click.stop="removeFavorite(favorite)">
              <text class="favorite-icon">❤️</text>
            </view>
          </template>
        </ContentCard>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import favoriteService from '../../services/favoriteService.js'
import ContentCard from '../../components/ui/ContentCard.vue'
import { LoadingSpinner } from '../../components/ui/index.js'
import { useLoading } from '../../composables/useLoading.js'

// 加载状态管理
const contentLoading = useLoading({
  showGlobalLoading: true,
  loadingText: '正在加载收藏内容...'
})

const favorites = ref([])

const loadFavorites = async () => {
  return await contentLoading.execute(async () => {
    // 后端收藏接口不支持分页，直接获取所有收藏
    const result = await favoriteService.getFavorites()

    if (result.success) {
      favorites.value = result.favorites || []
      return favorites.value
    } else {
      throw new Error(result.error || '获取收藏列表失败')
    }
  })
}

const removeFavorite = async (favorite) => {
  try {
    const result = await favoriteService.removeFavorite(favorite.id)
    if (result.success) {
      const index = favorites.value.findIndex(item => item.id === favorite.id)
      if (index > -1) favorites.value.splice(index, 1)
      uni.showToast({ title: '已取消收藏', icon: 'success' })
    }
  } catch (error) {
    console.error('取消收藏失败:', error)
    uni.showToast({ title: '操作失败，请稍后重试', icon: 'none' })
  }
}

const playContent = (content) => {
  if (!content?.id) {
    uni.showToast({ title: '内容信息不完整', icon: 'none' })
    return
  }
  uni.navigateTo({ url: `/pages/player/player?id=${content.id}` })
}

const handleRetry = async () => {
  contentLoading.clearError()
  await loadFavorites()
}

onMounted(() => {
  loadFavorites()
})
</script>

<script>
// 使用统一的分享配置
import { createShareConfig } from '../../utils/shareConfig.js'

export default {
  ...createShareConfig('pages/favorites/favorites')
}
</script>

<style scoped>
/* 引入全局CSS变量 - 与首页完全一致 */
@import '../../styles/variables.css';

.container {
  background-color: #f5f7fa;
  padding: 0 24rpx;
}

/* 内容网格布局 */
.content-grid {
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.empty, .error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  font-size: var(--font-size-md);
  color: var(--color-text-secondary);
}

.error-text {
  color: var(--color-danger);
  margin-bottom: 24rpx;
}

.retry-btn {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 24rpx 80rpx;
  font-size: 32rpx;
  font-weight: normal;
  width: 300rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.retry-btn:active {
  background: #0056cc;
}

/* 收藏标识样式 - 唯一的差异化部分 */
.favorite-control {
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.favorite-control:active {
  transform: scale(0.9);
  background: rgba(0, 0, 0, 0.8);
}

.favorite-icon {
  font-size: 32rpx;
  line-height: 1;
}
</style>