<template>
  <view class="container">
    <!-- 全屏加载遮罩 - 只在非静默更新时显示 -->
    <LoadingSpinner
      v-if="contentLoading.isLoading.value && !isSilentUpdate"
      :overlay="true"
      text="正在加载内容..."
    />
    <!-- 内容页面 -->
    <view class="content-wrapper">
      <!-- 搜索栏 -->
      <view class="search-bar">
        <view class="search-input-wrapper">
          <text class="search-icon">🔍</text>
          <input
            v-model="searchQuery"
            class="search-input"
            placeholder="搜索内容..."
            @confirm="handleSearch"
          />
          <button
            v-if="searchQuery"
            class="clear-btn"
            @click="clearSearch"
          >
            <text class="clear-icon">×</text>
          </button>
        </view>
        <button class="search-btn" @click="handleSearch">
          <text class="search-btn-text">搜索</text>
        </button>
      </view>

      <!-- 内容列表 -->
      <view class="content-list-container">
        <ContentList
          :content-list="contentList"
          :loading="false"
          :loading-more="loadingMore"
          :has-more="hasMore"
          :error="contentLoading.error.value?.message"
          :show-search="false"
          :show-filter="false"
          loading-text="正在加载内容..."
          empty-text="暂无内容"
          @load-more="loadMoreContent"
          @retry="handleRetry"
          @play-content="playContent"
        />
      </view>

      <!-- 初次访问设置引导弹窗 -->
      <view v-if="showFirstVisitModal" class="first-visit-modal" @click="closeFirstVisitModal" @touchmove.prevent>
        <view class="modal-content" @click.stop @touchmove.stop>
          <view class="modal-header">
            <text class="modal-title">欢迎使用</text>
            <button class="close-btn" @click="closeFirstVisitModal">×</button>
          </view>

          <view class="modal-body">
            <view class="welcome-message">
              <text class="welcome-text">请设置您的学习语言和水平等级，以获得更好的学习体验</text>
            </view>

            <!-- 使用语言等级选择组件 -->
            <view class="quick-setup">
              <LanguageLevelSelector
                v-model:language="firstVisitLanguage"
                v-model:levels="firstVisitLevels"
                title="选择语言和等级"
                @change="handleFirstVisitChange"
              />
            </view>

            <view class="action-buttons">
              <button class="later-btn" @click="closeFirstVisitModal">稍后设置</button>
              <button
                class="setup-btn"
                :class="{ disabled: !firstVisitLanguage }"
                @click="saveFirstVisitSettings"
              >
                完成设置
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import { onPullDownRefresh, onShow } from '@dcloudio/uni-app'
import ContentList from '../../components/content/ContentList.vue'
import LanguageLevelSelector from '../../components/common/LanguageLevelSelector.vue'
import contentService from '../../services/contentService'
import languageStore from '../../stores/languageStore.js'
import { LoadingSpinner } from '../../components/ui/index.js'
import { useLoading } from '../../composables/useLoading.js'

// 加载状态管理
const contentLoading = useLoading({
  showGlobalLoading: true,
  loadingText: '正在加载内容...'
})

// 是否是后台静默更新（语言切换时）
const isSilentUpdate = ref(false)

// 常量配置
const PAGE_SIZE = 20

// 响应式数据
const contentList = ref([])
const loadingMore = ref(false)
const hasMore = ref(true)
const searchQuery = ref('')

// 初次访问引导
const showFirstVisitModal = ref(false)
const firstVisitLanguage = ref(null)
const firstVisitLevels = ref([])

// 从store获取状态
const currentLanguage = computed(() => languageStore.state.currentLearningLanguage)
const currentSelectedLevels = computed(() => languageStore.state.selectedLevels)

// 页面加载
onMounted(async () => {
  await initializeData()
})

// 页面显示时只重新加载store状态，不重新加载内容
onShow(() => {
  // 重新加载store状态
  languageStore.reloadFromStorage()
})

// 初始化数据
const initializeData = async () => {
  // 确保语言store状态是最新的
  languageStore.reloadFromStorage()

  // 检查是否是首次访问
  checkFirstVisit()

  // 加载内容
  await loadContent(true)
}

// 检查是否是首次访问
const checkFirstVisit = () => {
  const hasVisited = uni.getStorageSync('hasVisitedApp')
  if (!hasVisited) {
    showFirstVisitModal.value = true
  }
}

// 关闭首次访问弹窗
const closeFirstVisitModal = () => {
  showFirstVisitModal.value = false
  uni.setStorageSync('hasVisitedApp', 'true')
}

// 首次访问设置相关函数
const handleFirstVisitChange = (data) => {
  // 组件内部已经处理了语言和等级的联动，这里只需要更新状态
  firstVisitLanguage.value = data.language
  firstVisitLevels.value = data.levels
}

const saveFirstVisitSettings = async () => {
  if (!firstVisitLanguage.value) {
    uni.showToast({
      title: '请选择语言',
      icon: 'none'
    })
    return
  }

  try {
    // 保存语言设置
    languageStore.setLearningLanguage(firstVisitLanguage.value)

    // 保存等级设置
    languageStore.setSelectedLevels(firstVisitLevels.value)

    // 关闭弹窗
    closeFirstVisitModal()

    // 重新加载内容
    await loadContent(true)

    uni.showToast({
      title: '设置完成',
      icon: 'success'
    })
  } catch (error) {
    console.error('保存设置失败:', error)
    uni.showToast({
      title: '设置失败，请重试',
      icon: 'none'
    })
  }
}

// 加载内容
const loadContent = async (refresh = false, silent = false) => {
  if (silent) {
    isSilentUpdate.value = true
  }

  try {
    return await contentLoading.execute(async () => {
    const params = {
      page: 1,
      pageSize: PAGE_SIZE
    }

    // 有语言就传，没语言不传 - 使用全局设置
    if (currentLanguage.value) {
      params.language = currentLanguage.value
    }

    // 有等级就传，没等级不传 - 使用全局设置
    if (currentSelectedLevels.value && currentSelectedLevels.value.length > 0) {
      params.tags = currentSelectedLevels.value.join(',')
    }

    // 如果有搜索关键词，添加到参数中
    if (searchQuery.value && searchQuery.value.trim()) {
      params.search = searchQuery.value.trim()
    }

    const response = await contentService.getPublicContents(params)

    // 按照后端API返回的数据结构处理
    if (response && response.success) {
      const newContent = response.contents || []
      contentList.value = newContent
      hasMore.value = newContent.length === PAGE_SIZE
      return newContent
    } else {
      throw new Error(response?.error || '获取内容失败')
    }
    })
  } finally {
    if (silent) {
      isSilentUpdate.value = false
    }
  }
}

// 加载更多内容
const loadMoreContent = async () => {
  if (!hasMore.value || contentLoading.isLoading.value || loadingMore.value) return

  try {
    loadingMore.value = true

    const params = {
      page: Math.floor(contentList.value.length / PAGE_SIZE) + 1,
      pageSize: PAGE_SIZE
    }

    // 有语言就传，没语言不传 - 使用全局设置
    if (currentLanguage.value) {
      params.language = currentLanguage.value
    }

    // 有等级就传，没等级不传 - 使用全局设置
    if (currentSelectedLevels.value && currentSelectedLevels.value.length > 0) {
      params.tags = currentSelectedLevels.value.join(',')
    }

    if (searchQuery.value && searchQuery.value.trim()) {
      params.search = searchQuery.value.trim()
    }

    const response = await contentService.getPublicContents(params)

    if (response && response.success) {
      const newContent = response.contents || []
      contentList.value = [...contentList.value, ...newContent]
      hasMore.value = newContent.length === PAGE_SIZE
    }
  } catch (error) {
    console.error('加载更多失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })
  } finally {
    loadingMore.value = false
  }
}

// 处理搜索按钮点击
const handleSearch = () => {
  loadContent(true)
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  loadContent(true)
}



// 播放内容
const playContent = (item) => {
  uni.navigateTo({
    url: `/pages/player/player?id=${item.id}`
  })
}

// 错误处理函数
const handleRetry = async () => {
  contentLoading.clearError()
  await loadContent(true)
}

// 事件监听器
let unsubscribeLanguageChange = null
let unsubscribeLevelsChange = null

// 组件挂载时设置监听
onMounted(() => {
  // 监听语言切换事件
  unsubscribeLanguageChange = languageStore.onLanguageChanged(async (newLanguage) => {
    console.log('收到语言切换事件，重新加载内容:', newLanguage)
    await loadContent(true, true) // 静默更新
  })

  // 监听等级变化事件
  unsubscribeLevelsChange = languageStore.onLevelsChanged(async (newLevels) => {
    console.log('收到等级变化事件，重新加载内容:', newLevels)
    await loadContent(true, true) // 静默更新
  })
})

// 组件卸载时清理监听器
onUnmounted(() => {
  if (unsubscribeLanguageChange) {
    unsubscribeLanguageChange()
  }
  if (unsubscribeLevelsChange) {
    unsubscribeLevelsChange()
  }
})

// 下拉刷新处理
onPullDownRefresh(async () => {
  console.log('=== 下拉刷新触发 ===')

  try {
    // 先显示一个立即的反馈
    uni.showToast({
      title: '正在刷新...',
      icon: 'loading',
      duration: 1000
    })

    // 重新加载内容
    await loadContent(true)

    // 显示成功提示
    setTimeout(() => {
      uni.showToast({
        title: '刷新完成',
        icon: 'success',
        duration: 1000
      })
    }, 100)

  } catch (error) {
    console.error('下拉刷新失败:', error)
    uni.showToast({
      title: '刷新失败',
      icon: 'error',
      duration: 1500
    })
  } finally {
    // 确保停止下拉刷新动画
    setTimeout(() => {
      uni.stopPullDownRefresh()
      console.log('=== 下拉刷新结束 ===')
    }, 500)
  }
})


</script>

<script>
// 使用统一的分享配置
import { createShareConfig } from '../../utils/shareConfig.js'

export default {
  ...createShareConfig('pages/index/index')
}
</script>

<style scoped>
/* 引入全局CSS变量 */
@import '../../styles/variables.css';

.container {
  background-color: #f5f7fa;
  height: 100vh;
  width: 100%;
  position: relative;
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: white;
  gap: 16rpx;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 24rpx;
  padding: 0 20rpx;
  height: 72rpx;
  position: relative;
}

.search-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 12rpx;
}

.search-input {
  flex: 1;
  height: 100%;
  border: none;
  background: transparent;
  font-size: 28rpx;
  color: #333;
  padding-right: 40rpx;
}

.clear-btn {
  position: absolute;
  right: 20rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #ccc;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.clear-icon {
  font-size: 24rpx;
  color: white;
  line-height: 1;
}

.search-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24rpx;
  height: 72rpx;
  background: #667eea;
  border: none;
  border-radius: 24rpx;
  min-width: 100rpx;
}

.search-btn-text {
  font-size: 28rpx;
  color: white;
}

/* 筛选标签栏样式 */
.filter-tags-bar {
  display: flex;
  align-items: center;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
  padding: 16rpx 24rpx;
  gap: 16rpx;
  position: relative;
}

.filter-tags-scroll {
  flex: 1;
  white-space: nowrap;
  margin-right: 70rpx; /* 为右侧清空按钮留出空间 */
}

.filter-tags-content {
  display: flex;
  gap: 12rpx;
}

.filter-tag {
  display: flex;
  align-items: center;
  background: #667eea;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  white-space: nowrap;
  flex-shrink: 0;
}

.tag-text {
  margin-right: 8rpx;
}

.tag-close {
  font-size: 24rpx;
  font-weight: bold;
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
}

.clear-all-container {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  background: white;
  padding-left: 8rpx;
}

.clear-all-btn {
  background: #6c757d;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  white-space: nowrap;
  flex-shrink: 0;
  height: 48rpx;
  line-height: 32rpx;
}

/* 内容列表容器 */
.content-list-container {
  padding: 0 24rpx;
}

/* 初次访问弹窗样式 */
.first-visit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.modal-content {
  width: 100%;
  max-width: 600rpx;
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  position: relative;
  width: 100%;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  text-align: center;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  background: none;
  border: none;
  padding: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.modal-body {
  text-align: center;
}

.welcome-message {
  margin-bottom: 40rpx;
}

.welcome-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.later-btn, .setup-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.later-btn {
  background: #f5f5f5;
  color: #666;
}

.setup-btn {
  background: #667eea;
  color: white;
}

.setup-btn.disabled {
  background: #ccc;
  color: #999;
}

/* 快速设置样式 */
.quick-setup {
  margin: 32rpx 0;
}
</style>
