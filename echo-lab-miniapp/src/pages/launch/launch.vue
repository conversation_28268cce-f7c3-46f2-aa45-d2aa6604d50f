<template>
  <view class="launch-container">
    <view class="logo-container">
      <image src="/static/logo.jpg" class="logo" mode="aspectFit" />
      <text class="app-name">磨耳朵听力</text>
    </view>
  </view>
</template>

<script setup>
import { onMounted } from 'vue'
import languageStore from '../../stores/languageStore.js'

onMounted(() => {
  // 直接跳转到首页，不检查语言设置
  setTimeout(() => {
    uni.switchTab({
      url: '/pages/index/index'
    })
  }, 1000) // 显示1秒启动页
})
</script>

<style scoped>
.launch-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  width: 200rpx;
  height: 200rpx;
  border-radius: 100rpx;
  margin-bottom: 40rpx;
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}
</style>
