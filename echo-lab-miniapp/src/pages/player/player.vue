<template>
  <view class="mobile-player-layout" :class="{ 'player-only': !showPlaylist }">

    <!-- 全屏加载遮罩 - 放在最外层 -->
    <LoadingSpinner
      v-if="contentLoading.isLoading.value"
      :overlay="true"
      text="正在加载内容..."
      text-color="#fff"
    />
    <!-- 播放器区域（16:9比例） -->
    <view class="video-player-wrapper">
      <view class="aspect-ratio-container">
        <!-- 播放器内容区域 -->
        <view class="player-content" :style="{ backgroundColor: '#000' }">

          <!-- 内容显示区域 -->
          <view class="content-display-area">
            <!-- 音频缓冲loading状态 -->
            <view v-if="isBuffering" class="audio-buffering-overlay">
              <view class="buffering-spinner">
                <view class="simple-spinner"></view>
                <text class="buffering-text">音频加载中...</text>
              </view>
            </view>

            <!-- 图片内容（封面） -->
            <ResponsiveImage
              v-if="currentItem && currentItem.imageUrl"
              :src="currentItem.imageUrl"
              type="responsive"
              :width="750"
              layout="absolute"
              container-class="content-image-container"
              image-class="content-image"
              mode="aspectFill"
            />
            <!-- 文本内容（带振假名） -->
            <view v-else-if="currentItem && currentItem.content" class="text-content-container">
              <!-- 关键词标记显示 -->
              <view v-if="currentItem.isKeyword" class="keyword-indicator">
                <text class="keyword-label">关键词</text>
              </view>

              <!-- 使用rich-text组件支持标注显示 -->
              <rich-text
                v-if="textStyle.showRuby && currentItem.annotation"
                class="content-text ruby-text"
                :class="{ 'keyword-highlight': currentItem.isKeyword }"
                :style="{
                  '--text-size': textStyle.fontSize + 'rpx',
                  '--text-color': textStyle.color,
                  fontSize: 'var(--text-size)',
                  color: 'var(--text-color)'
                }"
                :nodes="currentItemRubyNodes"
              />
              <!-- 普通文本显示 -->
              <text
                v-else
                class="content-text"
                :class="{ 'keyword-highlight': currentItem.isKeyword }"
                :style="{
                  '--text-size': textStyle.fontSize + 'rpx',
                  '--text-color': textStyle.color,
                  fontSize: 'var(--text-size)',
                  color: 'var(--text-color)'
                }"
              >
                {{ currentItem.content }}
              </text>
            </view>
            <!-- 错误和空状态 -->
            <view v-if="contentLoading.error.value" class="error-state">
              <text>加载失败，请重试</text>
              <button class="retry-btn" @click="handleContentRetry">重试</button>
            </view>
            <view v-else-if="!content && !contentLoading.isLoading.value" class="empty-state">
              <text>暂无内容</text>
            </view>
          </view>





          <!-- 交互蒙层：点击切换控制区 -->
          <view
            v-if="!contentLoading.isLoading.value && !isBuffering && content"
            class="interaction-overlay"
            :class="{ 'paused': !isPlaying }"
            @click="toggleControlsVisibility()"
          ></view>

          <!-- 移动端控制按钮组 - 缓冲时隐藏 -->
          <view v-if="!contentLoading.isLoading.value && !isBuffering && showControls" class="mobile-controls-group">
            <!-- 后退按钮 -->
            <button
              class="mobile-nav-button"
              @click.stop="jumpToPreviousSentence()"
            >
              <text class="nav-icon">‹</text>
            </button>

            <!-- 播放/暂停按钮 -->
            <button class="unified-play-button" @click.stop="togglePlay()">
              <text class="play-icon">{{ isPlaying ? '❚❚' : '▶' }}</text>
            </button>

            <!-- 前进按钮 -->
            <button
              class="mobile-nav-button"
              @click.stop="jumpToNextSentence()"
            >
              <text class="nav-icon">›</text>
            </button>
          </view>

          <!-- 顶部控制栏 -->
          <view class="mobile-top-controls" v-show="!isBuffering && showControls">
            <!-- 收藏按钮 -->
            <view class="favorite-control icon-wrapper" :class="{ 'favorited': isFavorited }" @click="toggleFavorite">
              <text class="favorite-icon">{{ isFavorited ? '♥' : '♡' }}</text>
            </view>



            <!-- 播放策略设置按钮 -->
            <view class="strategy-control icon-wrapper" @click="showPlaybackSettingsPanel(); dismissGuide()">
              <text class="strategy-icon">◐</text>
              <!-- 初次访问提示 -->
              <view v-if="showFirstTimeTooltip" class="tooltip-overlay" @click.stop="dismissGuide">
                <view class="tooltip-content">
                  <view class="tooltip-text">设置播放步骤、重复次数、语速、间隔及翻译语音</view>
                  <view class="tooltip-close" @click.stop="dismissGuide">×</view>
                </view>
                <view class="tooltip-arrow"></view>
              </view>
            </view>

            <!-- 更多功能按钮 -->
            <view class="more-control icon-wrapper" @click="toggleMobileMenu">
              <text class="settings-icon">⚙</text>
            </view>
          </view>

          <!-- 底部视频控制栏 - 缓冲时隐藏 -->
          <view class="video-controls" v-show="!isBuffering && showControls">
            <view class="controls-bottom">
              <view class="mobile-controls-row">
                <!-- 时间显示 -->
                <text class="mobile-time-display">
                  {{ formattedCurrentTime }} / {{ formattedTotalDuration }}
                </text>

                <!-- 进度条 -->
                <view class="mobile-progress-slider-container">
                  <slider
                    class="video-progress-slider"
                    :value="progress"
                    :max="100"
                    @change="onProgressChange"
                    @changing="onProgressChanging"
                    activeColor="#ff0000"
                    backgroundColor="rgba(255, 255, 255, 0.2)"
                    block-color="#ffffff"
                    :block-size="20"
                  />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 播放列表区域 -->
    <view class="context-panel" :class="{ 'content-collapsed': !showPlaylist }">
      <view class="panel-header">
        <text class="panel-title">当前视频内容</text>
        <switch
          :checked="showPlaylist"
          @change="onPlaylistSwitchChange"
          color="#ff0000"
        />
      </view>
      <scroll-view v-if="showPlaylist" class="scroll-wrapper" scroll-y :scroll-top="scrollTop">
        <view
          v-for="(item, index) in filteredTimeline"
          :key="`${item.id}_${item.timelineIndex}`"
          class="playlist-item"
          :class="{ active: item.timelineIndex === currentIndex }"
          @click="jumpToItem(item.timelineIndex)"
        >
          <view class="item-time">{{ formatTime(item.startTime) }}</view>
          <view class="item-content">
            <text class="item-text">{{ item.displayContent }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 移动端设置菜单 -->
    <view v-if="showMobileMenu" class="mobile-menu-overlay" @click="toggleMobileMenu">
      <view class="mobile-menu" @click.stop>
        <view class="menu-header">
          <text class="menu-title">播放设置</text>
          <view class="close-btn" @click="toggleMobileMenu">×</view>
        </view>

        <!-- 播放速度设置 -->
        <view class="menu-item" @click="toggleSpeedMenu">
          <text class="item-label">播放速度</text>
          <view class="item-value">
            <text>{{ getPlaybackRate() }}x</text>
            <text class="arrow">></text>
          </view>
        </view>

        <!-- 速度子菜单 -->
        <view v-if="showSpeedMenu" class="submenu">
          <view
            v-for="(speed, index) in speedOptions"
            :key="speed"
            class="submenu-item"
            :class="{ active: index === speedIndex }"
            @click="selectSpeed(index)"
          >
            <text>{{ speed }}</text>
            <text v-if="index === speedIndex" class="check">✓</text>
          </view>
        </view>

        <!-- 字体大小设置 -->
        <view class="menu-item" @click="toggleFontSizeMenu">
          <text class="item-label">字体大小</text>
          <view class="item-value">
            <text>{{ getCurrentFontSizeLabel() }}</text>
            <text class="arrow">></text>
          </view>
        </view>

        <!-- 字体大小子菜单 -->
        <view v-if="showFontSizeMenu" class="submenu">
          <view
            v-for="size in fontSizeOptions"
            :key="size.value"
            class="submenu-item"
            :class="{ active: textStyle.fontSize === size.value }"
            @click="setFontSize(size.value)"
          >
            <text>{{ size.label }}</text>
            <text v-if="textStyle.fontSize === size.value" class="check">✓</text>
          </view>
        </view>

        <!-- 文字颜色设置 -->
        <view class="menu-item" @click="toggleColorMenu">
          <text class="item-label">文字颜色</text>
          <view class="item-value">
            <view class="color-preview" :style="{ backgroundColor: textStyle.color }"></view>
            <text class="arrow">></text>
          </view>
        </view>

        <!-- 文字颜色子菜单 -->
        <view v-if="showColorMenu" class="submenu color-submenu">
          <view
            v-for="color in colorOptions"
            :key="color.value"
            class="submenu-item color-item"
            :class="{ active: textStyle.color === color.value }"
            @click="setTextColor(color.value)"
          >
            <view class="color-preview" :style="{ backgroundColor: color.value }"></view>
            <text>{{ color.label }}</text>
            <text v-if="textStyle.color === color.value" class="check">✓</text>
          </view>
        </view>



        <!-- 标注显示设置 -->
        <view class="menu-item">
          <text class="item-label">显示标注</text>
          <switch
            :checked="textStyle.showRuby"
            @change="toggleRuby"
            color="#f56c6c"
          />
        </view>



        <!-- 循环播放开关 -->
        <view class="menu-item">
          <text class="item-label">循环播放</text>
          <switch
            :checked="loopEnabled"
            @change="toggleLoop"
            color="#f56c6c"
          />
        </view>
      </view>
    </view>

    <!-- 播放策略设置弹窗 -->
    <view v-if="showPlaybackSettings" class="playback-settings-overlay">
      <PlaybackSettings
        v-if="content"
        :settings="playerConfig.settings.value"
        :server-config="playerConfig.serverConfig.value"
        :current-template="playerConfig.currentTemplate.value"
        :config-state="playerConfig.configState.value"
        :content="content"
        @save="savePlaybackSettings"
        @reset-to-original="handleResetToOriginal"
        @close="hidePlaybackSettingsPanel"
        @check-template-state="handleTemplateStateCheck"
      />
    </view>

  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import { onShow, onHide, onUnload } from '@dcloudio/uni-app'
import { showNetworkError, showLoadingError, showPlaybackError, showLoading, hideLoading, withRetry } from '../../utils/errorHandler.js'
import contentService from '../../services/contentService.js'
import { extractSequencesFromNodes, generateTimeline } from '../../utils/timelineGenerator.js'
import { BackgroundAudioPlayer } from '../../utils/backgroundAudioPlayer.js'
import ResponsiveImage from '../../components/ui/ResponsiveImage.vue'
import { generateRubyHTML, formatRubyForRichText } from '../../utils/rubyGenerator.js'
import { decryptJSON } from '../../utils/cryptoService.js'
import { usePlayerConfig } from '../../composables/usePlayerConfig.js'
import { useLoading } from '../../composables/useLoading.js'
import favoriteService from '../../services/favoriteService.js'
import PlaybackSettings from '../../components/player/PlaybackSettings.vue'
import { LoadingSpinner } from '../../components/ui/index.js'
import { getLanguageShareText } from '../../config/languages.js'
import { getLearningLanguage } from '../../utils/userSettings.js'

// 加载状态管理
const contentLoading = useLoading({
  showGlobalLoading: true,
  loadingText: '正在加载内容...',
  retryLimit: 3,
  retryDelay: 1000
})

// 响应式数据
const content = ref(null) // 内容数据
const timeline = ref([]) // 播放时间线
const currentItem = ref(null) // 当前播放项
const isPlaying = ref(false) // 播放状态（响应式）
const currentTime = ref(0) // 当前播放时间（秒）
const totalDuration = ref(0) // 总时长（秒）
const progress = ref(0) // 播放进度（0-100）
const speedOptions = ref(['0.5x', '0.75x', '1.0x', '1.25x', '1.5x', '2.0x'])
const speedIndex = ref(2) // 默认1.0x
// loading状态统一使用contentLoading.isLoading


const isBuffering = ref(false) // 缓冲状态
const showPlaylist = ref(true) // 播放列表显示状态
const showMobileMenu = ref(false) // 移动端菜单显示状态
const showControls = ref(true) // 控制按钮显示状态
let controlsTimeout = null // 控制区隐藏定时器

const showSpeedMenu = ref(false) // 速度子菜单
const showFontSizeMenu = ref(false) // 字号子菜单
const showColorMenu = ref(false) // 颜色子菜单
const currentIndex = ref(0) // 当前播放索引
const scrollTop = ref(0) // 播放列表滚动位置
const loopEnabled = ref(false) // 循环播放状态
const showPlaybackSettings = ref(false) // 播放策略设置显示状态
const isFavorited = ref(false)
const favoriteLoading = ref(false)

// 首次访问提示状态
const showFirstTimeTooltip = ref(false)

// 字体大小选项
const fontSizeOptions = ref([
  { label: '小', value: 28 },
  { label: '中', value: 36 },
  { label: '大', value: 48 },
  { label: '特大', value: 56 }
])

// 颜色选项
const colorOptions = ref([
  { label: '白', value: '#FFFFFF' },
  { label: '金', value: '#FFD700' },
  { label: '红', value: '#FF6B6B' },
  { label: '青', value: '#4ECDC4' },
  { label: '绿', value: '#9ACD32' }
])

// 音频播放器
let audioPlayer = null

// 播放策略配置管理
const contentId = ref('default')

// 在页面加载时设置contentId
onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  contentId.value = currentPage?.options?.id || 'default'
})

const playerConfig = usePlayerConfig(contentId.value)

// 文本样式设置
const textStyle = ref({
  fontSize: 36, // rpx单位，默认中等大小
  color: '#ffffff', // 默认白色，适合黑色背景
  backgroundColor: '#000000',
  showRuby: true, // 启用Ruby标签显示
})

// 计算属性
const currentItemContent = computed(() => {
  // 优先使用currentItem，如果没有则使用第一个项目
  const item = currentItem.value || (timeline.value.length > 0 ? timeline.value[0] : null)
  if (!item || !item.content) return '加载中...'

  // 否则返回普通文本
  return item.content
})

// 生成rich-text组件需要的nodes数据
const currentItemRubyNodes = computed(() => {
  const item = currentItem.value
  if (!item || !item.content || !item.annotation) {
    return [{
      name: 'span',
      attrs: {
        style: `font-weight: bold; font-size: ${textStyle.value.fontSize}rpx; color: ${textStyle.value.color};`
      },
      children: [{ type: 'text', text: item?.content || '加载中...' }]
    }]
  }

  try {
    return formatRubyForRichText(item.content, item.annotation)
  } catch (error) {
    console.error('生成Ruby节点失败:', error)
    return [{
      name: 'span',
      attrs: {
        style: `font-weight: bold; font-size: ${textStyle.value.fontSize}rpx; color: ${textStyle.value.color};`
      },
      children: [{ type: 'text', text: item.content }]
    }]
  }
})

const formattedCurrentTime = computed(() => {
  return formatTime(currentTime.value)
})

const formattedTotalDuration = computed(() => {
  return formatTime(totalDuration.value)
})

// 检查是否可以跳转到上一句
const canJumpToPrevious = computed(() => {
  if (!timeline.value || timeline.value.length === 0) return false

  for (let i = currentIndex.value - 1; i >= 0; i--) {
    const item = timeline.value[i]
    if (item && !item.isPause && !item.isCover && item.content?.trim()) {
      return true
    }
  }
  return false
})

// 检查是否可以跳转到下一句
const canJumpToNext = computed(() => {
  if (!timeline.value || timeline.value.length === 0) return false

  for (let i = currentIndex.value + 1; i < timeline.value.length; i++) {
    const item = timeline.value[i]
    if (item && !item.isPause && !item.isCover && item.content?.trim()) {
      return true
    }
  }
  return false
})

// 过滤播放列表，按照H5版本逻辑
const filteredTimeline = computed(() => {
  if (!timeline.value) return []

  const filtered = []

  timeline.value.forEach((item, timelineIndex) => {
    // 过滤条件：不是停顿项目，且有内容或是封面
    if (!item.isPause && (item.content?.trim() || item.isCover)) {
      // 为封面项目添加显示内容
      let displayContent = item.content
      if (item.isCover || item.type === 'cover') {
        displayContent = '封面'
      }

      filtered.push({
        ...item,
        displayContent,
        timelineIndex // 使用实际的时间线索引
      })
    }
  })

  return filtered
})

// 是否显示Ruby标签
const isRubyContent = computed(() => {
  return Array.isArray(currentItemContent.value)
})



// 页面加载
onMounted(async () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const contentId = currentPage.options.id

  if (contentId) {
    try {
      await loadContent(contentId)
    } catch (error) {
      // 错误已经在loadContent中处理，这里不需要额外处理
    }
  } else {
    contentLoading.setError(new Error('内容ID不能为空'))
    setTimeout(() => {
      uni.navigateBack()
    }, 3000)
  }

  // 添加键盘事件监听
  if (typeof window !== 'undefined') {
    window.addEventListener('keydown', handleKeyDown)
  }

  // 检查是否需要显示首次访问提示
  const hasSeenGuide = uni.getStorageSync('hasSeenPlayerGuide')
  if (!hasSeenGuide) {
    // 延迟显示提示，确保页面已完全加载
    setTimeout(() => {
      showFirstTimeTooltip.value = true
    }, 1000)
  }
})

// 页面卸载时清理
onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('keydown', handleKeyDown)
  }

  // 清理音频播放器
  if (audioPlayer) {
    audioPlayer.destroy()
    audioPlayer = null
  }
})

// 小程序页面生命周期 - 页面显示
onShow(() => {
  // 页面显示时不需要特殊处理，后台音频会自动恢复
})

// 小程序页面生命周期 - 页面隐藏
onHide(() => {
  // 页面隐藏时不暂停音频，让后台音频管理器处理
  // 只清理UI相关的定时器
  if (controlsTimeout) {
    clearTimeout(controlsTimeout)
    controlsTimeout = null
  }
})

// 微信分享功能将在页面选项中定义

// 加载内容
const loadContent = async (id) => {
  return await contentLoading.execute(async () => {
    // 使用重试机制加载内容
    const response = await withRetry(
      () => contentService.getContent(id),
      3, // 最多重试3次
      1000 // 延迟1秒
    )

    if (response && response.success && response.content) {
      const contentData = response.content



      // 解析配置JSON
      if (contentData.configJson) {
        let configJson = contentData.configJson

        // 如果是字符串，尝试解密或解析
        if (typeof configJson === 'string') {
          try {
            configJson = decryptJSON(configJson)
          } catch (parseError) {
            showLoadingError('内容配置')
            return
          }
        }

        // 更新content中的configJson为解密后的数据
        contentData.configJson = configJson



        // 所有数据处理完成后，设置到响应式变量
        content.value = contentData

        // 初始化播放策略配置
        await initPlayerConfig()

        // 生成时间线
        const sequences = extractSequencesFromNodes(configJson)

        if (sequences.length > 0) {
          // 生成播放时间线
          await generateTimelineWithConfig(sequences, configJson)



          // 初始化音频播放器
          await initAudioPlayer()

          // 设置第一个项目
          if (timeline.value.length > 0) {
            currentItem.value = timeline.value[0]
            currentIndex.value = 0
          }
        } else {
          showLoadingError('可播放内容')
        }
      } else {
        showLoadingError('内容配置')
      }
    } else {
      throw new Error('内容数据格式不正确')
    }

    // 内容加载完成后检查收藏状态
    await checkFavoriteStatus()

    return content.value
  }, {
    loadingText: '正在加载内容...',
    showError: true
  })
}

// 更新当前播放项 - 完全按照H5版本的updateCurrentIndex实现
const updateCurrentItem = (timeSeconds) => {
  if (!timeline.value || timeline.value.length === 0) return

  // 查找当前时间对应的索引 - 使用displayDuration来确定内容显示时长
  for (let i = 0; i < timeline.value.length; i++) {
    const item = timeline.value[i]

    // 跳过停顿和封面项目，只处理有内容的项目
    if (item?.isPause || item?.isCover) {
      continue
    }

    const startTime = item.startTime
    const displayDuration = item.displayDuration || item.duration
    const endTime = startTime + displayDuration

    // 精确的时间匹配 - 内容显示时长 = 音频时长 + 后面的间隔时长
    if (timeSeconds >= startTime && timeSeconds < endTime) {
      if (i !== currentIndex.value) {
        currentIndex.value = i
        currentItem.value = item
      }
      return
    }
  }

  // 如果没找到匹配的索引，查找最后一个有内容的项目
  for (let i = timeline.value.length - 1; i >= 0; i--) {
    const item = timeline.value[i]
    if (!item?.isPause && !item?.isCover) {
      const displayDuration = item.displayDuration || item.duration
      if (timeSeconds >= (item.startTime + displayDuration)) {
        if (i !== currentIndex.value) {
          currentIndex.value = i
          currentItem.value = item
        }
      }
      break
    }
  }
}

// 播放策略相关函数

/**
 * 根据配置生成时间线
 */
const generateTimelineWithConfig = async (sequences, configJson) => {
  try {
    // 检查是否有播放策略配置
    if (playerConfig.settings.value &&
        playerConfig.settings.value.sections &&
        playerConfig.settings.value.sections.length > 0) {



      // 使用H5版本的时间线生成逻辑
      // 直接传入configJson和settings，与H5版本保持一致
      timeline.value = generateTimeline(configJson, playerConfig.settings.value)



    } else {

      // 使用默认配置生成时间线
      const playbackConfig = {
        repeatCount: 1,
        pauseBetweenRepeats: 1000,
        pauseBetweenItems: 500,
        playbackSpeed: getPlaybackRate()
      }

      timeline.value = generateTimeline(sequences, playbackConfig, configJson)
    }

    // 计算总时长
    totalDuration.value = timeline.value.length > 0
      ? timeline.value[timeline.value.length - 1].startTime + timeline.value[timeline.value.length - 1].duration
      : 0





  } catch (error) {
    showPlaybackError('生成播放时间线失败')
  }
}

/**
 * 初始化播放策略配置
 */
const initPlayerConfig = async () => {
  try {
    if (!content.value) return

    // 设置服务器配置（内容的默认配置）
    const defaultConfig = createDefaultConfig()
    playerConfig.serverConfig.value = defaultConfig

    // 智能加载配置
    await playerConfig.smartLoadConfig()

  } catch (error) {
    // 初始化失败，使用默认配置
  }
}

/**
 * 创建默认配置（与H5版本完全一致）
 */
const createDefaultConfig = () => {
  if (!content.value || !content.value.configJson) {
    return { sections: [] }
  }

  const sections = []

  // 从节点中提取序列数据（与H5版本一致）
  const sequences = extractSequencesFromNodes(content.value.configJson)

  // 检查是否有文本序列节点
  const textSequenceNodes = Object.values(content.value.configJson.nodes || {})
    .filter(node => node.type === 'textSequence')

  // 如果有文本序列节点，检查它们的环节设置
  if (textSequenceNodes.length > 0) {
    const firstSequenceNode = textSequenceNodes[0]

    // 检查节点是否有环节设置
    if (firstSequenceNode.params && firstSequenceNode.params.sections && firstSequenceNode.params.sections.length > 0) {

      // 直接使用完整的环节配置，保留所有原始字段包括播放参数
      firstSequenceNode.params.sections.forEach((section, index) => {
        const newSection = {
          ...section, // 保留所有原有字段，包括repeatCount等播放参数
          id: section.id || `section_default_${Date.now() + index}`,
          title: section.title || section.name || `环节 ${index + 1}`,
          description: section.description || '',
          processingMode: section.processingMode || (section.sourceNodeId ? 'source' : 'sequence'),
          userEditable: section.userEditable !== false
        }

        // 根据处理模式设置正确的属性
        if (newSection.processingMode === 'sequence') {
          newSection.sourceIndex = newSection.sourceIndex ?? 0 // 默认使用第一个序列
        } else if (newSection.processingMode === 'source') {
          // 如果是基于源节点，设置源节点ID
          if (section.sourceNodeIds && section.sourceNodeIds.length > 0) {
            newSection.sourceNodeIds = [...section.sourceNodeIds]
          } else if (section.sourceNodeId) {
            newSection.sourceNodeIds = [section.sourceNodeId]
          }
        }

        sections.push(newSection)
      })
    } else {

      // 只创建一个默认的通读环节（基于序列）
      if (sequences.length > 0) {
        sections.push({
          id: `section_seq_${Date.now()}`,
          title: `环节: 基于序列`,
          description: '默认环节',
          processingMode: 'sequence',
          sourceIndex: 0, // 使用第一个序列
          userEditable: true
        })
      } else {
        // 如果没有序列，尝试创建基于源节点的通读环节
        const textContentNodes = Object.values(content.value.configJson.nodes || {})
          .filter(node => node.type === 'textContent')

        if (textContentNodes.length > 0) {
          sections.push({
            id: `section_src_${Date.now()}`,
            title: `环节: 基于源节点`,
            description: '默认环节',
            processingMode: 'source',
            sourceNodeIds: [textContentNodes[0].id],
            userEditable: true
          })
        }
      }
    }
  } else {

    // 创建通读环节（基于序列）
    if (sequences.length > 0) {
      sections.push({
        id: `section_seq_${Date.now()}`,
        title: `环节: 基于序列`,
        description: '默认环节',
        processingMode: 'sequence',
        sourceIndex: 0,
        userEditable: true
      })
    }
  }

  // 确保至少有一个环节
  if (sections.length === 0) {
    sections.push({
      id: `section_fallback_${Date.now()}`,
      title: '默认播放',
      description: '基础播放环节',
      processingMode: 'sequence',
      sourceIndex: 0,
      userEditable: true
    })
  }

  return { sections }
}

/**
 * 显示播放策略设置
 */
const showPlaybackSettingsPanel = () => {
  showPlaybackSettings.value = true
}

/**
 * 关闭首次访问提示
 */
const dismissGuide = () => {
  showFirstTimeTooltip.value = false
  uni.setStorageSync('hasSeenPlayerGuide', 'true')
}

/**
 * 隐藏播放策略设置
 */
const hidePlaybackSettingsPanel = () => {
  showPlaybackSettings.value = false
}

/**
 * 保存播放策略设置
 */
const savePlaybackSettings = async (settingsData) => {
  try {
    // settingsData 现在包含 { settings, template, configState }
    const { settings: newSettings, template, configState } = settingsData

    // 更新全局配置
    playerConfig.settings.value = newSettings

    // 更新模板状态
    if (template) {
      playerConfig.currentTemplate.value = template
      playerConfig.updateConfigState(configState.type, template)
    } else {
      playerConfig.currentTemplate.value = null
      playerConfig.updateConfigState(configState.type)
    }

    // 保存到本地存储
    playerConfig.saveConfigAndTemplate()

    // 重新生成时间线
    if (content.value && content.value.configJson) {
      const sequences = extractSequencesFromNodes(content.value.configJson)
      if (sequences.length > 0) {
        await generateTimelineWithConfig(sequences, content.value.configJson)

        // 重新初始化音频播放器
        await initAudioPlayer()

        // 重置播放状态
        currentTime.value = 0
        currentIndex.value = 0
        if (timeline.value.length > 0) {
          currentItem.value = timeline.value[0]
        }
      }
    }

    // 隐藏设置面板
    hidePlaybackSettingsPanel()

    uni.showToast({
      title: '策略已应用',
      icon: 'success'
    })

  } catch (error) {
    uni.showToast({
      title: '保存失败',
      icon: 'error'
    })
  }
}





/**
 * 处理重置为原始配置
 */
const handleResetToOriginal = async () => {
  try {
    // 使用配置管理器的重置方法
    const success = playerConfig.resetToServerConfig()
    if (!success) {
      uni.showToast({
        title: '无法获取原始配置信息',
        icon: 'error'
      })
      return
    }

    // 重新生成时间线
    if (content.value && content.value.configJson) {
      const sequences = extractSequencesFromNodes(content.value.configJson)
      if (sequences.length > 0) {
        await generateTimelineWithConfig(sequences, content.value.configJson)

        // 重新初始化音频播放器
        await initAudioPlayer()

        // 重置播放状态
        currentTime.value = 0
        currentIndex.value = 0
        if (timeline.value.length > 0) {
          currentItem.value = timeline.value[0]
        }
      }
    }

    uni.showToast({
      title: '已重置配置',
      icon: 'success'
    })

  } catch (error) {
    uni.showToast({
      title: '重置失败',
      icon: 'error'
    })
  }
}

/**
 * 处理模板状态检测（实时检测，不保存设置）
 */
const handleTemplateStateCheck = (newSettings) => {
  // 直接调用 configManager 的更新方法
  playerConfig.updateTemplateState()
}

/**
 * 应用模板到服务器配置
 */
const applyTemplateToServerConfig = (serverConfig, template) => {
  if (!template || !template.config || !template.config.sections) {
    throw new Error('无效的模板配置')
  }

  // 直接使用模板配置，为每个环节生成必要的字段
  const appliedSections = template.config.sections.map((templateSection, index) => ({
    id: `section_${Date.now()}_${index}`,
    title: templateSection.title || `环节 ${index + 1}`,
    name: templateSection.title || `环节 ${index + 1}`,
    description: templateSection.description || '',
    type: 'sequence',
    processingMode: 'sequence',
    userEditable: true,
    sourceIndex: index,

    // 应用模板配置
    pauseDuration: templateSection.pauseDuration || 2500,
    repeatCount: templateSection.repeatCount || 4,
    repeatSpeeds: templateSection.repeatSpeeds || Array(templateSection.repeatCount || 4).fill(1.0),
    repeatPauses: templateSection.repeatPauses || Array(templateSection.repeatCount || 4).fill(0),
    enableTranslation: templateSection.enableTranslation || false,
    translationLanguage: templateSection.translationLanguage || 'zh-CN',
    translationPosition: templateSection.translationPosition || 1,
    enableKeywords: templateSection.enableKeywords || false,
    keywordPosition: templateSection.keywordPosition || 1,
    keywordRepeatCount: templateSection.keywordRepeatCount || 1
  }))

  return {
    sections: appliedSections
  }
}





// 初始化音频播放器
const initAudioPlayer = async () => {
  if (audioPlayer) {
    audioPlayer.destroy()
  }

  audioPlayer = new BackgroundAudioPlayer()

  // 设置事件回调
  audioPlayer.onTimeUpdate = (time) => {
    // 只在用户没有交互时更新UI
    if (!isUserInteracting.value && !isDraggingProgress.value) {
      currentTime.value = time

      // 更新进度条
      if (totalDuration.value > 0) {
        progress.value = (time / totalDuration.value) * 100
      }

      // 检查是否需要切换到下一个项目
      updateCurrentItem(time)
    }
  }

  audioPlayer.onEnded = () => {
    // 播放完成，重置到初始状态
    isPlaying.value = false
    showControls.value = true

    // 重置进度和时间显示
    currentTime.value = 0
    progress.value = 0
    currentIndex.value = 0

    // 重置到第一个项目
    if (timeline.value && timeline.value.length > 0) {
      currentItem.value = timeline.value[0]
    }

    // 清除控制区自动隐藏定时器
    if (controlsTimeout) {
      clearTimeout(controlsTimeout)
    }
  }

  audioPlayer.onLoadingChange = (loading) => {
    isBuffering.value = loading
  }

  // 监听播放状态变化
  audioPlayer.onPlayStateChange = (playing, options = {}) => {
    isPlaying.value = playing

    // 非用户操作的状态变化时的控制区逻辑
    if (!options?.userAction) {
      if (playing) {
        // 自动开始播放时，显示控制区并设置自动隐藏
        showControlsForUser()
      } else {
        // 自动暂停时，只有在控制区隐藏时才显示
        if (!showControls.value) {
          showControlsForPause()
        }
      }
    }
  }







  // 加载时间线数据
  audioPlayer.setTimeline(timeline.value)

  // 初始化完成后显示控制区
  if (!isPlaying.value) {
    // 暂停状态，控制区保持显示
    showControlsForPause()
  } else {
    // 播放状态，显示控制区并设置自动隐藏
    showControlsForUser()
  }
}

// 播放下一个项目
const playNextItem = () => {
  if (!audioPlayer) return
  audioPlayer.jumpToNext()
}

// 播放上一个项目
const playPreviousItem = () => {
  if (!audioPlayer) return
  audioPlayer.jumpToPrevious()
}

// 跳转到指定时间
const seekToTime = (timeSeconds) => {
  if (!audioPlayer) return
  audioPlayer.seekTo(timeSeconds)
}

// 用户操作时显示控制区
const showControlsForUser = () => {
  showControls.value = true

  // 清除之前的倒计时
  if (controlsTimeout) {
    clearTimeout(controlsTimeout)
  }

  // 设置自动隐藏（4秒），播放时才隐藏
  controlsTimeout = setTimeout(() => {
    if (isPlaying.value) {
      showControls.value = false
    }
  }, 4000)
}

// 暂停时显示控制区（不设置自动隐藏）
const showControlsForPause = () => {
  showControls.value = true

  // 清除自动隐藏倒计时
  if (controlsTimeout) {
    clearTimeout(controlsTimeout)
  }
}



// 点击屏幕切换控制区显示状态
const toggleControlsVisibility = () => {
  if (showControls.value) {
    // 当前显示，点击隐藏
    showControls.value = false
    if (controlsTimeout) {
      clearTimeout(controlsTimeout)
    }
  } else {
    // 当前隐藏，点击显示
    showControlsForUser()
  }
}



// 切换播放状态
const togglePlay = () => {
  if (!audioPlayer) return

  if (isPlaying.value) {
    // 当前正在播放，执行暂停
    audioPlayer.pause()
    showControlsForPause()
  } else {
    // 当前已暂停，执行播放
    audioPlayer.play(true)
    showControlsForUser()
  }
}

// 用户交互状态
const isUserInteracting = ref(false)
const isDraggingProgress = ref(false)

// 开始拖动进度条
const startProgressDrag = () => {
  isDraggingProgress.value = true
  isUserInteracting.value = true

  // 用户拖拽进度条，显示控制区
  showControlsForUser()
}

// 结束拖动进度条
const endProgressDrag = () => {
  isDraggingProgress.value = false

  // 延迟恢复自动更新，确保跳转操作完成
  setTimeout(() => {
    isUserInteracting.value = false
  }, 500)
}

// 进度改变（拖动结束时触发）
const onProgressChange = (e) => {
  const value = e.detail.value
  const seekTime = (value / 100) * totalDuration.value

  // 立即更新进度条值
  progress.value = value

  // 跳转到新位置
  seekToTime(seekTime)

  // 立即更新当前项目
  updateCurrentItem(seekTime)

  // 结束拖动
  endProgressDrag()
}

// 进度拖动中（实时更新进度条显示）
const onProgressChanging = (e) => {
  const value = e.detail.value
  const seekTime = (value / 100) * totalDuration.value

  // 标记用户正在交互
  if (!isUserInteracting.value) {
    startProgressDrag()
  }

  // 立即更新进度条值
  progress.value = value

  // 实时更新内容显示
  updateCurrentItem(seekTime)
}

// 倍速改变
const onSpeedChange = (e) => {
  speedIndex.value = e.detail.value
  if (audioPlayer) {
    audioPlayer.setGlobalPlaybackRate(getPlaybackRate())
  }
}

// 获取播放倍率
const getPlaybackRate = () => {
  const speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]
  return speeds[speedIndex.value] || 1.0
}

// 调整字体大小
const adjustFontSize = (delta) => {
  const newSize = textStyle.value.fontSize + delta
  if (newSize >= 32 && newSize <= 72) {
    textStyle.value.fontSize = newSize
  }
}

// 切换Ruby标签显示
const toggleRuby = (e) => {
  textStyle.value.showRuby = e.detail.value
}

// 切换播放列表显示
const togglePlaylist = () => {
  showPlaylist.value = !showPlaylist.value
}

// 播放列表开关改变
const onPlaylistSwitchChange = (e) => {
  showPlaylist.value = e.detail.value
}

// 键盘事件处理
const handleKeyDown = (e) => {
  // 检查是否在输入框中
  if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
    return
  }

  showControlsForUser()

  switch (e.code) {
    case 'Space':
      e.preventDefault()
      togglePlay()
      break
    case 'ArrowLeft':
      e.preventDefault()
      jumpToCurrentSentenceStart()
      break
    case 'ArrowRight':
      e.preventDefault()
      jumpToNextSentence()
      break
    case 'ArrowUp':
      e.preventDefault()
      jumpToPreviousSentence()
      break
    case 'ArrowDown':
      e.preventDefault()
      jumpToNextSentence()
      break
  }
}

// 跳转到当前句子开始
const jumpToCurrentSentenceStart = () => {
  if (!audioPlayer || currentIndex.value < 0) return

  showControlsForUser()

  const currentItem = timeline.value[currentIndex.value]
  if (currentItem) {
    seekToTime(currentItem.startTime || 0)
  }
}

// 跳转到上一句
const jumpToPreviousSentence = () => {
  if (!timeline.value || timeline.value.length === 0) return

  const currentIdx = currentIndex.value
  const currentTimeSeconds = currentTime.value
  const currentItem = timeline.value[currentIdx]

  showControlsForUser()

  // 如果不在当前句开头附近，先跳到开头
  if (currentItem && currentItem.content && currentItem.content.trim()) {
    const currentStartTime = currentItem.startTime || 0
    const isNearStart = Math.abs(currentTimeSeconds - currentStartTime) < 0.3 // 0.3秒

    if (!isNearStart) {
      seekToTime(currentStartTime)
      return
    }
  }

  // 查找上一个有内容的项目
  for (let i = currentIdx - 1; i >= 0; i--) {
    const item = timeline.value[i]
    if (item && !item.isPause && !item.isCover && item.content?.trim()) {
      currentIndex.value = i
      seekToTime(item.startTime || 0)
      return
    }
  }
}

// 跳转到下一句
const jumpToNextSentence = () => {
  if (!timeline.value || timeline.value.length === 0) return

  const currentIdx = currentIndex.value

  showControlsForUser()

  for (let i = currentIdx + 1; i < timeline.value.length; i++) {
    const item = timeline.value[i]
    if (item && !item.isPause && !item.isCover && item.content?.trim()) {
      currentIndex.value = i
      seekToTime(item.startTime || 0)
      return
    }
  }
}



// 处理内容区域点击
const handleContentClick = () => {
  if (!audioPlayer) return

  // 如果控制区已显示，则切换播放状态
  if (showControls.value) {
    togglePlay()
  } else {
    // 如果控制区隐藏，则显示控制区
    if (isPlaying.value) {
      showControlsForUser() // 播放中，显示控制区并设置自动隐藏
    } else {
      showControlsForPause() // 暂停中，显示控制区不自动隐藏
    }
  }
}

// 跳转到指定项目
const jumpToItem = (index) => {
  if (index >= 0 && index < timeline.value.length) {
    const item = timeline.value[index]
    currentIndex.value = index
    currentItem.value = item // 更新当前项目
    seekToTime(item.startTime || 0)

    // 用户主动选择项目，显示控制区
    showControlsForUser()

    // 如果没有播放，自动开始播放
    if (!isPlaying.value) {
      togglePlay()
    }

    // 不隐藏播放列表，让用户可以继续选择其他项目
  }
}

// 切换移动端菜单
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
  // 关闭子菜单
  showSpeedMenu.value = false
  showFontSizeMenu.value = false
  showColorMenu.value = false
}

// 切换速度菜单
const toggleSpeedMenu = () => {
  showSpeedMenu.value = !showSpeedMenu.value
  showFontSizeMenu.value = false
  showColorMenu.value = false
}

// 切换字体大小菜单
const toggleFontSizeMenu = () => {
  showFontSizeMenu.value = !showFontSizeMenu.value
  showSpeedMenu.value = false
  showColorMenu.value = false
}

// 切换颜色菜单
const toggleColorMenu = () => {
  showColorMenu.value = !showColorMenu.value
  showSpeedMenu.value = false
  showFontSizeMenu.value = false
}

// 选择播放速度
const selectSpeed = (index) => {
  speedIndex.value = index
  if (audioPlayer) {
    audioPlayer.setGlobalPlaybackRate(getPlaybackRate())
  }
  showSpeedMenu.value = false
}

// 设置字体大小
const setFontSize = (size) => {
  textStyle.value.fontSize = size
}

// 获取当前字体大小标签
const getCurrentFontSizeLabel = () => {
  const currentOption = fontSizeOptions.value.find(option => option.value === textStyle.value.fontSize)
  return currentOption ? currentOption.label : '中'
}

// 设置文字颜色
const setTextColor = (color) => {
  textStyle.value.color = color
}

// 切换循环播放
const toggleLoop = (e) => {
  loopEnabled.value = e.detail.value
  if (audioPlayer) {
    audioPlayer.setLoop(loopEnabled.value)
  }
}

const checkFavoriteStatus = async () => {
  if (!content.value?.id) return

  // 检查用户是否已登录，未登录则不检查收藏状态
  const token = uni.getStorageSync('auth_token')
  if (!token) {
    isFavorited.value = false
    return
  }

  try {
    const result = await favoriteService.checkFavorite(content.value.id)
    if (result.success) {
      isFavorited.value = result.isFavorite
    }
  } catch (error) {
    isFavorited.value = false
  }
}

const toggleFavorite = async () => {
  if (!content.value?.id || favoriteLoading.value) return

  // 检查用户是否已登录
  const token = uni.getStorageSync('auth_token')
  if (!token) {
    uni.showToast({
      title: '请先登录',
      icon: 'none',
      duration: 2000
    })
    return
  }

  favoriteLoading.value = true

  try {
    if (isFavorited.value) {
      await favoriteService.removeFavorite(content.value.id)
      isFavorited.value = false
      uni.showToast({ title: '取消收藏成功', icon: 'success' })
    } else {
      await favoriteService.addFavorite(content.value.id)
      isFavorited.value = true
      uni.showToast({ title: '收藏成功', icon: 'success' })
    }
  } catch (error) {
    uni.showToast({ title: '操作失败', icon: 'none' })
  } finally {
    favoriteLoading.value = false
  }
}

// 滚动到当前播放项（使用小程序API获取实际高度）
const scrollToCurrentItem = async () => {
  if (!showPlaylist.value) return

  await nextTick()

  // 找到当前项在过滤后时间线中的索引
  const filteredIndex = filteredTimeline.value.findIndex(item => {
    return item.timelineIndex === currentIndex.value
  })

  if (filteredIndex === -1) return

  // 使用小程序API获取实际的元素位置和高度
  const query = uni.createSelectorQuery()

  // 获取容器信息
  query.select('.scroll-wrapper').boundingClientRect()

  // 获取所有列表项的信息
  query.selectAll('.playlist-item').boundingClientRect()

  query.exec((res) => {
    const containerRect = res[0]
    const itemRects = res[1]

    if (!containerRect || !itemRects || !itemRects[filteredIndex]) {
      return
    }

    const targetItem = itemRects[filteredIndex]

    // 按照H5版本的公式计算滚动位置
    // scrollTop + (itemRect.top - containerRect.top) - (container.clientHeight / 2) + (currentItem.clientHeight / 2)
    const targetScrollTop = Math.max(0,
      scrollTop.value + (targetItem.top - containerRect.top) - (containerRect.height / 2) + (targetItem.height / 2)
    )

    scrollTop.value = targetScrollTop
  })
}

// 监听currentIndex变化，自动滚动到当前项
watch(currentIndex, () => {
  scrollToCurrentItem()
}, { immediate: true })

// 监听播放列表显示状态，显示时也要滚动到当前项
watch(showPlaylist, (newValue) => {
  if (newValue) {
    nextTick(() => {
      scrollToCurrentItem()
    })
  }
})

// 格式化时间 - 与H5版本保持一致
const formatTime = (time) => {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// 重试处理函数
const handleContentRetry = async () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const contentId = currentPage.options.id

  if (contentId) {
    // 清除错误状态并重新加载
    contentLoading.clearError()
    try {
      await loadContent(contentId)
    } catch (error) {
      // 错误已经在 loadContent 中处理
    }
  }
}

</script>

<script>
// 页面分享配置
export default {
  onShareAppMessage() {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const contentId = currentPage?.options?.id || ''

    // 获取当前语言和分享文案
    const currentLanguage = uni.getStorageSync('learning_language') || 'ja'
    const shareText = this.getShareText(currentLanguage)

    // 尝试获取当前内容信息
    const currentContent = this.$refs?.playerComponent?.content || null

    return {
      title: currentContent?.name || shareText,
      path: `/pages/player/player?id=${contentId}`,
      imageUrl: currentContent?.thumbnailUrl || 'https://f6zx3qc.oss-cn-beijing.aliyuncs.com/pengyouquan.png'
    }
  },

  onShareTimeline() {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const contentId = currentPage?.options?.id || ''

    // 获取当前语言和分享文案
    const currentLanguage = uni.getStorageSync('learning_language') || 'ja'
    const shareText = this.getShareText(currentLanguage)

    // 尝试获取当前内容信息
    const currentContent = this.$refs?.playerComponent?.content || null

    return {
      title: currentContent?.name || shareText,
      query: `id=${contentId}&from=timeline`,
      imageUrl: currentContent?.thumbnailUrl || 'https://f6zx3qc.oss-cn-beijing.aliyuncs.com/pengyouquan.png'
    }
  },

  methods: {
    getShareText(langCode) {
      const shareTexts = {
        'ja': '轻松练听力，随时随地学日语',
        'en': '轻松练听力，随时随地学英语',
        'zh-CN': '轻松练听力，随时随地学中文',
        'zh-TW': '輕鬆練聽力，隨時隨地學中文'
      }
      return shareTexts[langCode] || '轻松练听力，随时随地学语言'
    }
  }
}
</script>

<style>
/* 页面级别样式 - 必须是全局样式 */
page {
  height: 100%;
  overflow: hidden;
}
</style>

<style scoped>
:root {
  --font-size-small: 20rpx;
  --font-size-normal: 24rpx;
  --font-size-medium: 28rpx;
  --font-size-large: 32rpx;
}
/* 移动端播放器布局 - 固定播放区域 + 独立滚动列表 */
.mobile-player-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden; /* 防止页面级别滚动 */
  box-sizing: border-box;
}

/* 当播放列表隐藏时，播放器垂直居中显示 */
.mobile-player-layout.player-only {
  justify-content: center;
}

/* 播放器区域 - 固定在顶部，不参与滚动 */
.video-player-wrapper {
  width: 100%;
  position: relative;
  flex-shrink: 0; /* 防止被压缩 */
}

.aspect-ratio-container {
  width: 100%;
  padding-bottom: 56.25%; /* 16:9 */
  position: relative;
}

.aspect-ratio-container > .player-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* 播放器内容区域 */
.player-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  overflow: hidden;
  transition: background-color 0.5s ease;
}

/* 移除未使用的loading样式，统一使用LoadingSpinner组件 */

/* 内容显示区域 */
.content-display-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  padding: 32rpx 48rpx; /* 确保内容不会贴边显示 */
  box-sizing: border-box;
}

/* 文本内容容器 */
.text-content-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.content-text {
  font-size: var(--text-size, 48rpx);
  line-height: 1.6;
  color: var(--text-color, #fff);
  font-weight: bold;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.8);
  text-align: center;
  width: 100%;
  max-width: none;
  word-wrap: break-word;
  box-sizing: border-box;
}

.content-image-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.content-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ruby-text {
  /* Ruby标签特殊样式 */
}

/* Ruby标签样式 - 适配小程序rich-text组件 */
.ruby-text {
  line-height: 2.2; /* 增加行高为Ruby标签留出空间 */
}

/* Rich-text组件中的Ruby标签样式 */
.content-text :deep(ruby) {
  margin: 0 0.2em;
  display: inline-block;
  text-align: center;
  vertical-align: baseline;
}

.content-text :deep(rt) {
  font-size: 0.6em;
  line-height: 1;
  display: block;
  text-align: center;
  margin-bottom: 0.3em; /* 增加与下方汉字的距离 */
  opacity: 0.9;
  font-weight: normal;
}

.empty-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 32rpx;
}

/* 内容点击区域 */
.content-click-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}



/* 交互蒙层 */
.interaction-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 1;
}

.interaction-overlay.paused {
  background-color: rgba(0, 0, 0, 0.3);
}
/* 移动端控制按钮组 */
.mobile-controls-group {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  gap: 56rpx;
  z-index: 2;
}

/* 导航按钮 */
.mobile-nav-button {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.95);
  color: #333;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  font-size: 32rpx;
  backdrop-filter: blur(8rpx);
}

.mobile-nav-button:disabled {
  opacity: 0.4;
  background-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.mobile-nav-button:active {
  transform: scale(0.92);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.nav-icon {
  font-size: 32rpx;
  font-weight: bold;
}

/* 播放按钮 */
.unified-play-button {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #ff0000;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 3rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
  font-size: 40rpx;
}

.unified-play-button:active {
  transform: scale(0.95);
}

.play-icon {
  font-size: 40rpx;
}

/* 顶部控制栏 */
.mobile-top-controls {
  position: absolute;
  top: 16rpx;
  right: 24rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  z-index: 2;
}

/* 收藏按钮样式 */
.favorite-control {
  border-radius: 50%;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.favorite-control:active {
  transform: scale(0.9);
}

.favorite-icon {
  font-size: 56rpx;
  line-height: 1;
  color: #ffffff;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.8);
}

/* 收藏状态的红色 */
.favorite-control.favorited .favorite-icon {
  color: #ff0000;
}





.strategy-control {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.strategy-control:active {
  transform: scale(0.92);
}

.strategy-icon {
  font-size: 52rpx;
  color: #ffffff;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.8);
}

.more-control {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.more-control:active {
  transform: scale(0.92);
}

.settings-icon {
  font-size: 52rpx;
  color: #ffffff;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.8);
}

/* 底部视频控制栏 */
.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.6) 70%, transparent 100%);
  padding: 0;
  box-sizing: border-box;
  opacity: 1;
  transition: all 0.3s ease;
  z-index: 5;
  backdrop-filter: blur(8rpx);
}

.controls-bottom {
  padding: 12rpx 24rpx 8rpx 24rpx;
  box-sizing: border-box;
  width: 100%;
}

.mobile-controls-row {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 24rpx;
}

.mobile-time-display {
  color: rgba(255, 255, 255, 0.95);
  font-size: 20rpx;
  white-space: nowrap;
  min-width: 120rpx;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.8);
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

.mobile-progress-slider-container {
  flex: 1;
  margin: 0;
  position: relative;
  z-index: 7;
  box-sizing: border-box;
  border-radius: 12rpx;
}

/* 移动端菜单样式 */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.mobile-menu {
  background: white;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  width: 100%;
  max-height: 60vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.menu-title {
  font-size: var(--font-size-medium);
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.close-btn {
  position: absolute;
  top: 20rpx;
  right: 30rpx;
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  cursor: pointer;
  border-radius: 50%;
  background: #f5f5f5;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.close-btn:active {
  transform: scale(0.9);
  background: #e8e8e8;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f5f5f5;
  font-size: 28rpx;
  color: #333;
  cursor: pointer;
}

.menu-item:active {
  background: #f5f5f5;
}

.menu-value {
  color: #ff0000;
  font-weight: bold;
}

.submenu {
  background: #f8f8f8;
  padding: 20rpx 40rpx;
}

.submenu-item {
  padding: 20rpx 0;
  font-size: 26rpx;
  color: #666;
  cursor: pointer;
}

.submenu-item.active {
  color: #ff0000;
  font-weight: bold;
}

/* 内容列表区域样式 - 独立滚动区域 */
.context-panel {
  background: white;
  border-top: 1rpx solid #eee;
  flex: 1; /* 占据剩余空间 */
  display: flex;
  flex-direction: column;
  height: 0; /* 配合flex: 1，强制计算高度 */
  overflow: hidden; /* 防止整体滚动，只允许内部scroll-view滚动 */
}

/* 内容折叠状态 - 只显示header */
.context-panel.content-collapsed {
  flex: 0 0 auto; /* 不占据剩余空间，只显示header高度 */
  height: auto;
  width: 100%; /* 确保宽度保持100% */
}

.panel-header {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
  flex-shrink: 0; /* 头部固定不滚动 */
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%; /* 确保header宽度100% */
  box-sizing: border-box; /* 包含padding在内的宽度计算 */
}

.panel-title {
  font-size: var(--font-size-medium);
  font-weight: bold;
  color: #333;
}



.scroll-wrapper {
  flex: 1; /* 占据剩余空间 */
  height: 0; /* 配合flex: 1，让scroll-view正确计算高度 */
  width: 100%;
  box-sizing: border-box;
}

.playlist-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
  position: relative;
}

.playlist-item.active {
  background: #f0f8ff;
  border-left: 4rpx solid #ff0000;
}

.playlist-item:active {
  background: #f8f9fa;
  transform: scale(0.98);
}

.item-time {
  font-size: var(--font-size-small);
  color: #999;
  min-width: 72rpx;
  text-align: left;
  flex-shrink: 0;
  font-weight: 500;
  margin-right: 16rpx;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-text {
  font-size: var(--font-size-normal);
  color: #333;
  display: block;
  margin-bottom: 6rpx;
  font-weight: 500;
  line-height: 1.4;
  word-wrap: break-word;
  white-space: normal;
}

.item-annotation {
  font-size: var(--font-size-small);
  color: #666;
  display: block;
  line-height: 1.3;
  word-wrap: break-word;
  white-space: normal;
}

/* 设置菜单样式 */
.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: white;
  cursor: pointer;
}

/* 信息展示项（不可点击） */
.menu-item.info-item {
  cursor: default;
  background: #f8f9fa;
}

/* 预加载状态样式 */
.preload-stats {
  font-size: 24rpx;
  color: #666;
  font-family: monospace;
}

.menu-item:active {
  background: #f8f8f8;
}

.item-label {
  font-size: var(--font-size-normal);
  color: #333;
  font-weight: 500;
}

.item-value {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: var(--font-size-normal);
  color: #666;
}

.arrow {
  font-size: 24rpx;
  color: #999;
}

.color-indicator {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
}

/* 子菜单样式 */
.submenu {
  background: #f8f8f8;
  border-bottom: 1rpx solid #f0f0f0;
}

.submenu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 50rpx;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
  font-size: var(--font-size-normal);
  color: #333;
}

.submenu-item:last-child {
  border-bottom: none;
}

.submenu-item:active {
  background: #f0f0f0;
}

.submenu-item.active {
  color: var(--color-primary);
  font-weight: 500;
}

.check {
  color: var(--color-primary);
  font-weight: bold;
  font-size: 28rpx;
}

/* 颜色子菜单特殊样式 */
.color-item {
  gap: 20rpx;
}

.color-preview {
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  flex-shrink: 0;
}

/* Switch开关样式 */
switch {
  transform: scale(0.8);
}



.color-buttons {
  display: flex;
  gap: 20rpx;
}

.color-button {
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  font-size: var(--font-size-small);
  color: white;
  text-shadow: 1rpx 1rpx 2rpx rgba(0,0,0,0.5);
  flex: 1;
}

.color-button.active {
  border-color: #007AFF;
  box-shadow: 0 0 0 2rpx #007AFF;
}

/* 播放策略设置覆盖层 */
.playback-settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 900;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
}

/* 错误和空状态样式 */
.error-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200rpx;
  color: #fff;
  text-align: center;
}

/* 首次访问提示样式 */
.tooltip-overlay {
  position: absolute;
  top: 100%;
  right: -20rpx;
  margin-top: 16rpx;
  z-index: 1000;
  animation: tooltipFadeIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.tooltip-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  padding: 24rpx 28rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
  line-height: 1.5;
  max-width: 320rpx;
  min-width: 280rpx;
  position: relative;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15), 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.tooltip-text {
  flex: 1;
  font-weight: 400;
  letter-spacing: 0.5rpx;
}

.tooltip-close {
  font-size: 28rpx;
  font-weight: 500;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  line-height: 1;
  flex-shrink: 0;
}

.tooltip-close:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.9);
}

.tooltip-arrow {
  position: absolute;
  top: -12rpx;
  right: 32rpx;
  width: 0;
  height: 0;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  border-bottom: 12rpx solid #667eea;
  filter: drop-shadow(0 -2rpx 4rpx rgba(0, 0, 0, 0.1));
}

@keyframes tooltipFadeIn {
  0% {
    opacity: 0;
    transform: translateY(-20rpx) scale(0.9);
  }
  60% {
    opacity: 1;
    transform: translateY(4rpx) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 音频缓冲loading样式 */
.audio-buffering-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8); /* 深色背景 */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200; /* 高层级，完全覆盖控制区 */
}

.buffering-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.simple-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.2);
  border-top: 6rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.buffering-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}



/* 重试按钮样式 */
.retry-btn {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 24rpx 80rpx;
  font-size: 32rpx;
  font-weight: normal;
  width: 300rpx;
  height: 88rpx;
  margin-top: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.retry-btn:active {
  background: #0056cc;
}
</style>
