<template>
  <view class="container">
    <!-- 用户信息 -->
    <view class="user-section">
      <view class="avatar-container">
        <image
          src="/static/logo.jpg"
          class="avatar"
          mode="aspectFill"
        />
      </view>
      <view class="user-info">
        <text class="username">{{ userInfo.username || userInfo.email || '未登录' }}</text>
        <text class="email">{{ userInfo.email || '点击登录' }}</text>
      </view>


      <button
        v-if="!isLoggedIn"
        class="login-btn"
        @click="login"
      >
        登录
      </button>
      <button
        v-else
        class="logout-btn"
        @click="logout"
      >
        退出
      </button>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <!-- 语言设置 -->
      <view class="menu-item language-switcher" @click="openLanguageLevelModal">
        <view class="menu-left">
          <text class="menu-icon">🌐</text>
          <text class="menu-text">语言和等级</text>
        </view>
        <view class="language-info">
          <text class="current-language">{{ currentLanguageLabel }}</text>
          <text v-if="selectedLevelsText" class="current-level">{{ selectedLevelsText }}</text>
          <text class="menu-arrow">></text>
        </view>
      </view>

      <view class="menu-item" @click="viewFavorites">
        <text class="menu-icon">❤️</text>
        <text class="menu-text">我的收藏</text>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" @click="viewSettings">
        <text class="menu-icon">⚙️</text>
        <text class="menu-text">设置</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 语言和等级设置弹窗 -->
    <view v-if="showLanguageLevelModal" class="modal-overlay" @click="closeLanguageLevelModal" @touchmove.prevent>
      <view class="modal-content" @click.stop @touchmove.stop>
        <view class="modal-header">
          <text class="modal-title">语言和等级</text>
          <button class="close-btn" @click="closeLanguageLevelModal">×</button>
        </view>

        <!-- 使用语言等级选择组件 -->
        <LanguageLevelSelector
          v-model:language="tempSelectedLanguage"
          v-model:levels="tempSelectedLevels"
          title="选择语言和等级"
          :allow-clear-language="true"
          @change="handleLanguageLevelChange"
        />

        <!-- 操作按钮 -->
        <view class="modal-actions">
          <button class="cancel-btn" @click="closeLanguageLevelModal">取消</button>
          <button class="save-btn" @click="saveLanguageLevel">保存</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import languageStore from '../../stores/languageStore.js'
import { getLanguageLabel } from '../../config/languages.js'
import LanguageLevelSelector from '../../components/common/LanguageLevelSelector.vue'


const userInfo = ref({})
const isLoggedIn = ref(false)
const isLoading = ref(true)

// 语言和等级设置弹窗
const showLanguageLevelModal = ref(false)
const tempSelectedLanguage = ref(null)
const tempSelectedLevels = ref([])

// 语言相关计算属性
const currentLanguageLabel = computed(() => {
  return getLanguageLabel(languageStore.state.currentLearningLanguage)
})

const currentUserLevel = computed(() => {
  return languageStore.state.currentUserLevel
})

const selectedLevelsText = computed(() => {
  const levels = languageStore.state.selectedLevels
  if (!levels || levels.length === 0) return ''

  // 如果只有一个等级，直接显示
  if (levels.length === 1) return levels[0]

  // 如果有多个等级，显示数量
  return `${levels.length}个等级`
})





onMounted(() => {
  checkLoginStatus()
})

// 使用uni-app的onShow钩子
onShow(() => {
  checkLoginStatus()
})

// 监听登录状态变化事件
uni.$on('loginStatusChanged', () => {
  checkLoginStatus()
})

// 页面卸载时移除事件监听
onUnmounted(() => {
  uni.$off('loginStatusChanged')
})



const checkLoginStatus = () => {
  // 检查本地存储的登录状态
  const token = uni.getStorageSync('auth_token')
  const user = uni.getStorageSync('auth_user')



  if (token && user) {
    isLoggedIn.value = true
    try {
      userInfo.value = typeof user === 'string' ? JSON.parse(user) : user

    } catch (error) {
      console.error('解析用户信息失败:', error)
      // 如果解析失败，清除无效数据
      uni.removeStorageSync('auth_token')
      uni.removeStorageSync('auth_user')
      isLoggedIn.value = false
      userInfo.value = {}
    }
  } else {
    isLoggedIn.value = false
    userInfo.value = {}
  }

  // 检查完成，停止加载状态
  isLoading.value = false
}

const login = () => {
  uni.navigateTo({
    url: '/pages/auth/login'
  })
}

const logout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 清除本地存储
        uni.removeStorageSync('auth_token')
        uni.removeStorageSync('auth_user')

        // 更新状态
        isLoggedIn.value = false
        userInfo.value = {}

        // 触发登录状态变化事件
        uni.$emit('loginStatusChanged')

        uni.showToast({
          title: '已退出登录',
          icon: 'success'
        })
      }
    }
  })
}

// 语言和等级设置弹窗操作
const openLanguageLevelModal = () => {
  // 初始化当前设置
  tempSelectedLanguage.value = languageStore.state.currentLearningLanguage
  tempSelectedLevels.value = [...(languageStore.state.selectedLevels || [])]
  showLanguageLevelModal.value = true
}

const closeLanguageLevelModal = () => {
  showLanguageLevelModal.value = false
  // 重置临时选择
  tempSelectedLanguage.value = null
  tempSelectedLevels.value = []
}

// 处理语言和等级变化
const handleLanguageLevelChange = (data) => {
  // 组件内部已经处理了语言和等级的联动，这里只需要更新状态
  tempSelectedLanguage.value = data.language
  tempSelectedLevels.value = data.levels
}

const saveLanguageLevel = async () => {
  try {
    // 必须先选择语言
    if (!tempSelectedLanguage.value) {
      uni.showToast({
        title: '请先选择学习语言',
        icon: 'none'
      })
      return
    }

    // 保存语言设置
    languageStore.setLearningLanguage(tempSelectedLanguage.value)

    // 保存等级设置（可以为空数组）
    languageStore.setSelectedLevels(tempSelectedLevels.value)

    uni.showToast({
      title: '设置已保存',
      icon: 'success'
    })

    closeLanguageLevelModal()
  } catch (error) {
    console.error('保存设置失败:', error)
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'error'
    })
  }
}

const viewFavorites = () => {
  if (!isLoggedIn.value) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  uni.navigateTo({
    url: '/pages/favorites/favorites'
  })
}

const viewSettings = () => {
  uni.navigateTo({
    url: '/pages/settings/settings'
  })
}
</script>

<script>
// 使用统一的分享配置
import { createShareConfig } from '../../utils/shareConfig.js'

export default {
  ...createShareConfig('pages/profile/profile')
}
</script>

<style scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.user-section {
  display: flex;
  align-items: center;
  background: white;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.avatar-container {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  overflow: hidden;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 60rpx;
}

.avatar-placeholder {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  font-size: 50rpx;
  color: white;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.email {
  font-size: 28rpx;
  color: #666;
}

.login-btn {
  background: #409eff;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
}

.logout-btn {
  background: #f56c6c;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
}

.menu-section {
  background: white;
  border-radius: 16rpx;
  margin: 0 20rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 30rpx;
}

.menu-text {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.menu-arrow {
  font-size: 32rpx;
  color: #ccc;
}

/* 语言切换器特殊样式 */
.language-switcher {
  justify-content: space-between;
}

.menu-left {
  display: flex;
  align-items: center;
}

.language-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.current-language {
  font-size: 28rpx;
  color: #409eff;
  font-weight: 500;
}

.current-level {
  font-size: 24rpx;
  color: #409eff;
  background: #f0f9ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 600;
}

/* 语言和等级设置弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.modal-content {
  background: white;
  width: 100%;
  max-height: 80vh;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  position: relative;
  width: 100%;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  text-align: center;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  background: none;
  border: none;
  padding: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.setting-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.options-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.option-btn {
  padding: 16rpx 24rpx;
  background: #f5f5f5;
  border: 2rpx solid transparent;
  border-radius: 24rpx;
  font-size: 28rpx;
  color: #333;
  transition: all 0.2s ease;
}

.option-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.modal-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 32rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn,
.save-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 400;
  border: none;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.save-btn {
  background: #667eea;
  color: white;
}


</style>
