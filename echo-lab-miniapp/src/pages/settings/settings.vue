<template>
  <view class="settings-page">
    <!-- 设置项列表 -->
    <view class="settings-list">
      <!-- 清除缓存 -->
      <view class="setting-item" @click="confirmClearCache">
        <view class="item-content">
          <view class="item-icon">🗑️</view>
          <view class="item-info">
            <text class="item-title">清除缓存</text>
            <text class="item-desc">清除应用缓存数据</text>
          </view>
        </view>
        <view class="item-arrow" v-if="!clearing">></view>
        <view class="item-loading" v-else>
          <text class="loading-text">清除中...</text>
        </view>
      </view>

      <!-- 关于 -->
      <view class="setting-item" @click="showAbout">
        <view class="item-content">
          <view class="item-icon">ℹ️</view>
          <view class="item-info">
            <text class="item-title">关于应用</text>
            <text class="item-desc">查看应用信息</text>
          </view>
        </view>
        <view class="item-arrow">></view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { clearAllUserSettings } from '../../utils/userSettings.js'
import cacheManager from '../../utils/cacheManager.js'

const clearing = ref(false)

// 确认清除缓存
const confirmClearCache = () => {
  uni.showModal({
    title: '清除缓存',
    content: '确定要清除所有缓存数据吗？这将删除所有本地存储的设置和数据。',
    confirmText: '确定清除',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        clearAllCache()
      }
    }
  })
}

// 清除所有缓存
const clearAllCache = async () => {
  clearing.value = true
  
  try {
    // 清除用户设置
    const settingsCleared = clearAllUserSettings()
    
    // 清除文件缓存
    try {
      cacheManager.clearAllCache()
    } catch (error) {
      console.warn('清除文件缓存失败:', error)
    }

    // 清除其他存储数据
    try {
      uni.removeStorageSync('auth_token')
      uni.removeStorageSync('user_info')
      uni.removeStorageSync('auth_user')

      // 清除其他可能的缓存数据
      const storageInfo = uni.getStorageInfoSync()
      storageInfo.keys.forEach(key => {
        if (key.startsWith('cache_') || key.startsWith('temp_')) {
          uni.removeStorageSync(key)
        }
      })
    } catch (error) {
      // 清除存储数据失败，继续执行
    }
    
    if (settingsCleared) {
      uni.showToast({
        title: '缓存已清除',
        icon: 'success',
        duration: 2000
      })
      
      // 延迟重启应用
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/settings/language'
        })
      }, 2000)
    } else {
      throw new Error('清除设置失败')
    }
    
  } catch (error) {
    console.error('清除缓存失败:', error)
    uni.showToast({
      title: '清除失败',
      icon: 'error'
    })
  } finally {
    clearing.value = false
  }
}

// 显示关于信息
const showAbout = () => {
  uni.navigateTo({
    url: '/pages/about/about'
  })
}
</script>

<style scoped>
/* 引入全局CSS变量 */
@import '../../styles/variables.css';

.settings-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.settings-list {
  margin-top: 0;
}

.setting-item {
  background: white;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.setting-item:active {
  background-color: #f8f8f8;
}

.item-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
}

.item-title {
  display: block;
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: 8rpx;
}

.item-desc {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.item-arrow {
  font-size: var(--font-size-lg);
  color: var(--color-text-placeholder);
}

.item-loading {
  display: flex;
  align-items: center;
}

.loading-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}
</style>
