/**
 * 认证服务 - 基于H5项目的authService
 * 适配小程序环境，使用云函数代理
 */
import { apiGet, apiPost } from '../utils/api.js'

// 本地存储键
const TOKEN_KEY = "auth_token"
const USER_KEY = "auth_user"

/**
 * 发送验证码
 * @param {string} email 邮箱地址
 * @param {string} type 验证码类型（login, register, reset_password）
 * @returns {Promise} 请求结果
 */
export async function sendCode(email, type = "login") {
  try {
    const response = await apiPost("/api/auth/send-code", {
      email,
      type,
    })
    return response
  } catch (error) {
    console.error("发送验证码失败:", error)
    throw error
  }
}

/**
 * 验证码登录
 * @param {string} email 邮箱地址
 * @param {string} code 验证码
 * @param {string} type 验证码类型（login, register, reset_password）
 * @returns {Promise} 请求结果
 */
export async function verifyCode(email, code, type = "login") {
  try {
    const response = await apiPost("/api/auth/verify-code", {
      email,
      code,
      type,
    })

    // 云函数直接返回服务器原始数据
    // 如果登录成功，保存令牌和用户信息
    if (response.success) {
      setToken(response.token)
      setUser(response.user)

      // 触发登录状态变化事件
      uni.$emit('loginStatusChanged')
    }

    return response
  } catch (error) {
    console.error("验证码登录失败:", error)
    throw error
  }
}

/**
 * 获取当前用户信息
 * @returns {Promise} 用户信息
 */
export async function getCurrentUser() {
  try {
    const response = await apiGet("/api/auth/me")

    // 云函数直接返回服务器原始数据
    if (response.success) {
      setUser(response.user)
    }

    return response
  } catch (error) {
    console.error("获取用户信息失败:", error)
    throw error
  }
}

/**
 * 更新用户信息
 * @param {Object} userData 用户数据
 * @returns {Promise} 更新结果
 */
export async function updateUser(userData) {
  try {
    const response = await apiPost("/api/auth/me", userData)

    // 云函数直接返回服务器原始数据
    if (response.success) {
      setUser(response.user)
    }

    return response
  } catch (error) {
    console.error("更新用户信息失败:", error)
    throw error
  }
}

/**
 * 退出登录
 * @returns {Promise} 退出结果
 */
export async function logout() {
  try {
    await apiPost("/api/auth/logout")
  } catch (error) {
    console.error("退出登录失败:", error)
  } finally {
    // 无论API调用是否成功，都清除本地存储
    clearAuth()
  }
}

/**
 * 保存令牌到本地存储
 * @param {string} token 认证令牌
 */
export function setToken(token) {
  uni.setStorageSync(TOKEN_KEY, token)
}

/**
 * 从本地存储获取令牌
 * @returns {string|null} 认证令牌
 */
export function getToken() {
  return uni.getStorageSync(TOKEN_KEY) || null
}

/**
 * 保存用户信息到本地存储
 * @param {Object} user 用户信息
 */
export function setUser(user) {
  uni.setStorageSync(USER_KEY, user)
}

/**
 * 从本地存储获取用户信息
 * @returns {Object|null} 用户信息
 */
export function getUser() {
  return uni.getStorageSync(USER_KEY) || null
}

/**
 * 清除认证信息
 */
export function clearAuth() {
  uni.removeStorageSync(TOKEN_KEY)
  uni.removeStorageSync(USER_KEY)
}

/**
 * 检查是否已登录
 * @returns {boolean} 是否已登录
 */
export function isLoggedIn() {
  return !!getToken()
}

export default {
  sendCode,
  verifyCode,
  getCurrentUser,
  updateUser,
  logout,
  setToken,
  getToken,
  setUser,
  getUser,
  clearAuth,
  isLoggedIn,
}
