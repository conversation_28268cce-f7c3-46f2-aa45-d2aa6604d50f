/**
 * 内容服务 - 处理内容相关的 API 请求
 * 使用云函数代理转发请求
 */

import { apiGet, apiPost, apiPut, apiDelete } from '../utils/api.js'

class ContentService {
  /**
   * 获取公开内容列表
   * @param {Object} params 查询参数
   * @returns {Promise} 内容列表
   */
  async getPublicContents(params = {}) {
    try {
      console.log('=== getPublicContents 调用 ===')
      console.log('参数:', params)
      const result = await apiGet('/api/public/contents', params)
      console.log('getPublicContents 结果:', result)
      return result
    } catch (error) {
      console.error('获取内容列表失败:', error)
      throw error
    }
  }

  /**
   * 获取推荐内容（基于用户等级的标签筛选）
   * @param {Object} params 查询参数，应包含 tags 字段（用户等级）
   * @returns {Promise} 推荐内容列表
   */
  async getRecommendedContents(params = {}) {
    try {
      // 推荐内容和普通内容使用相同的端点，通过 tags 参数区分
      // 语言筛选通过请求头 X-User-Language 自动处理
      return await apiGet('/api/public/contents', params)
    } catch (error) {
      console.error('获取推荐内容失败:', error)
      throw error
    }
  }

  /**
   * 获取单个内容详情
   * @param {string|number} id 内容ID
   * @returns {Promise} 内容详情
   */
  async getContent(id) {
    try {
      return await apiGet(`/api/contents/${id}`)
    } catch (error) {
      console.error('获取内容详情失败:', error)
      throw error
    }
  }

  /**
   * 获取用户的内容列表
   * @param {Object} params 查询参数
   * @returns {Promise} 用户内容列表
   */
  async getUserContents(params = {}) {
    try {
      return await apiGet('/api/contents', params)
    } catch (error) {
      console.error('获取用户内容失败:', error)
      throw error
    }
  }

  /**
   * 创建新内容
   * @param {Object} content 内容数据
   * @returns {Promise} 创建结果
   */
  async createContent(content) {
    try {
      const tags = Array.isArray(content.tags)
        ? content.tags.join(",")
        : content.tags || ""

      return await apiPost('/api/contents', {
        name: content.title,
        description: content.description || "",
        tags: tags,
        configJson: content.configJson,
        thumbnailUrl: content.coverImageUrl || "",
        learningLanguage: content.learningLanguage,
        filterIds: content.filterIds || [],
      })
    } catch (error) {
      console.error('创建内容失败:', error)
      throw error
    }
  }

  /**
   * 更新内容
   * @param {string|number} id 内容ID
   * @param {Object} content 内容数据
   * @returns {Promise} 更新结果
   */
  async updateContent(id, content) {
    try {
      const tags = Array.isArray(content.tags)
        ? content.tags.join(",")
        : content.tags || ""

      return await apiPut(`/api/contents/${id}`, {
        name: content.title,
        description: content.description || "",
        tags: tags,
        configJson: content.configJson,
        thumbnailUrl: content.coverImageUrl || "",
        learningLanguage: content.learningLanguage,
        filterIds: content.filterIds || [],
      })
    } catch (error) {
      console.error('更新内容失败:', error)
      throw error
    }
  }

  /**
   * 删除内容
   * @param {string|number} id 内容ID
   * @returns {Promise} 删除结果
   */
  async deleteContent(id) {
    try {
      return await apiDelete(`/api/contents/${id}`)
    } catch (error) {
      console.error('删除内容失败:', error)
      throw error
    }
  }
}

// 创建并导出实例
const contentService = new ContentService()

export default contentService
