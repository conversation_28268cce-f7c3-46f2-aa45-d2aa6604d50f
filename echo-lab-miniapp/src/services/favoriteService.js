import { apiGet, apiPost, apiDelete } from '../utils/api.js'

export default {
  async getFavorites() {
    return await apiGet('/api/favorites')
  },

  async addFavorite(contentId) {
    return await apiPost('/api/favorites', { contentId })
  },

  async removeFavorite(contentId) {
    return await apiDelete(`/api/favorites/${contentId}`)
  },

  async checkFavorite(contentId) {
    return await apiGet(`/api/favorites/check/${contentId}`)
  }
}
