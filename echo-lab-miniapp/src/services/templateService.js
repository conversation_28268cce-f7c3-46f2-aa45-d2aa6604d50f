/**
 * 播放策略模板服务 - 处理模板相关的 API 请求
 * 使用云函数代理转发请求
 */

import { apiGet, apiPost, apiPut, apiDelete } from '../utils/api.js'
import { API_ENDPOINTS } from '../config/api.js'

class TemplateService {
  /**
   * 获取模板列表
   * @param {Object} params 查询参数
   * @returns {Promise} 模板列表
   */
  async getTemplates(params = {}) {
    try {
      const response = await apiGet(API_ENDPOINTS.TEMPLATES.BASE, params)
      return response
    } catch (error) {
      console.error('获取模板列表失败:', error)
      throw error
    }
  }

  /**
   * 根据ID获取模板
   * @param {string} id 模板ID
   * @returns {Promise} 模板对象
   */
  async getTemplateById(id) {
    try {
      const response = await apiGet(API_ENDPOINTS.TEMPLATES.BY_ID(id))
      return response
    } catch (error) {
      console.error('获取模板失败:', error)
      throw error
    }
  }

  /**
   * 创建模板
   * @param {Object} templateData 模板数据
   * @returns {Promise} 创建的模板
   */
  async createTemplate(templateData) {
    try {
      const response = await apiPost(API_ENDPOINTS.TEMPLATES.BASE, templateData)
      return response
    } catch (error) {
      console.error('创建模板失败:', error)
      throw error
    }
  }

  /**
   * 更新模板
   * @param {string} id 模板ID
   * @param {Object} templateData 模板数据
   * @returns {Promise} 更新的模板
   */
  async updateTemplate(id, templateData) {
    try {
      const response = await apiPut(API_ENDPOINTS.TEMPLATES.BY_ID(id), templateData)
      return response
    } catch (error) {
      console.error('更新模板失败:', error)
      throw error
    }
  }

  /**
   * 删除模板
   * @param {string} id 模板ID
   * @returns {Promise} 删除结果
   */
  async deleteTemplate(id) {
    try {
      const response = await apiDelete(API_ENDPOINTS.TEMPLATES.BY_ID(id))
      return response
    } catch (error) {
      console.error('删除模板失败:', error)
      throw error
    }
  }

  /**
   * 使用模板（增加使用次数）
   * @param {string} id 模板ID
   * @returns {Promise} 使用结果
   */
  async useTemplate(id) {
    try {
      const response = await apiPost(API_ENDPOINTS.TEMPLATES.USE(id))
      return response
    } catch (error) {
      console.error('使用模板失败:', error)
      throw error
    }
  }

  /**
   * 验证模板配置格式
   * @param {Object} config 模板配置
   * @returns {Object} 验证结果
   */
  validateTemplateConfig(config) {
    const errors = []

    if (!config) {
      errors.push('配置不能为空')
      return { valid: false, errors }
    }

    if (!config.sections || !Array.isArray(config.sections)) {
      errors.push('sections必须是数组')
      return { valid: false, errors }
    }

    if (config.sections.length === 0) {
      errors.push('至少需要一个环节')
      return { valid: false, errors }
    }

    // 验证每个环节
    config.sections.forEach((section, index) => {
      if (typeof section.repeatCount !== 'number' || section.repeatCount < 1) {
        errors.push(`环节${index + 1}: 重复次数必须是大于0的数字`)
      }

      if (typeof section.pauseDuration !== 'number' || section.pauseDuration < 0) {
        errors.push(`环节${index + 1}: 停顿时长必须是非负数`)
      }

      if (section.enableTranslation && !section.translationLanguage) {
        errors.push(`环节${index + 1}: 启用翻译时必须指定翻译语言`)
      }

      if (section.enableKeywords && typeof section.keywordRepeatCount !== 'number') {
        errors.push(`环节${index + 1}: 启用关键词时必须指定重复次数`)
      }
    })

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 应用模板到内容配置
   * @param {Object} serverConfig 服务器内容配置
   * @param {Object} template 模板对象
   * @returns {Object} 应用模板后的配置
   */
  applyTemplateToContent(serverConfig, template) {
    if (!serverConfig || !serverConfig.sections || !template || !template.config) {
      throw new Error('无效的配置或模板')
    }

    return {
      sections: serverConfig.sections.map((serverSection, index) => {
        const templateSection = template.config.sections[index]
        if (!templateSection) return serverSection

        return {
          // 保留服务器控制字段
          id: serverSection.id,
          title: serverSection.title,
          description: serverSection.description,
          processingMode: serverSection.processingMode,
          userEditable: serverSection.userEditable,
          sourceIndex: serverSection.sourceIndex,
          sourceNodeIds: serverSection.sourceNodeIds,

          // 应用模板字段
          pauseDuration: templateSection.pauseDuration,
          repeatCount: templateSection.repeatCount,
          repeatSpeeds: templateSection.repeatSpeeds || [1.0],
          repeatPauses: templateSection.repeatPauses || [0],
          enableTranslation: templateSection.enableTranslation || false,
          translationLanguage: templateSection.translationLanguage || 'zh-CN',
          translationPosition: templateSection.translationPosition || 1,
          enableKeywords: templateSection.enableKeywords || false,
          keywordPosition: templateSection.keywordPosition || 1,
          keywordRepeatCount: templateSection.keywordRepeatCount || 1
        }
      })
    }
  }

  /**
   * 从当前配置创建模板
   * @param {Object} currentConfig 当前播放配置
   * @returns {Object} 模板配置
   */
  createTemplateFromConfig(currentConfig) {
    if (!currentConfig || !currentConfig.sections) {
      throw new Error('无效的配置')
    }

    const templateConfig = {
      sections: currentConfig.sections.map((section) => {
        // 只提取用户可编辑字段
        return {
          pauseDuration: section.pauseDuration,
          repeatCount: section.repeatCount,
          repeatSpeeds: section.repeatSpeeds,
          repeatPauses: section.repeatPauses,
          enableTranslation: section.enableTranslation,
          translationLanguage: section.translationLanguage,
          translationPosition: section.translationPosition,
          enableKeywords: section.enableKeywords,
          keywordPosition: section.keywordPosition,
          keywordRepeatCount: section.keywordRepeatCount
        }
      })
    }

    return templateConfig
  }
}

// 创建单例实例
const templateService = new TemplateService()

export default templateService
