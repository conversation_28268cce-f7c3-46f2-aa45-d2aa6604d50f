/**
 * 用户等级服务 - 基于H5项目的userLevelService
 * 适配小程序环境，使用云函数代理
 */
import { apiGet, apiPost } from '../utils/api.js'

/**
 * 获取所有用户等级
 * @returns {Promise<Array>} 用户等级列表
 */
export async function getAllLevels() {
  try {
    const response = await apiGet("/api/user-levels")

    if (response && response.success) {
      return response.levels
    }

    return []
  } catch (error) {
    console.error("获取用户等级失败:", error)
    return []
  }
}

/**
 * 获取当前用户的等级信息
 * @returns {Promise<Object>} 用户等级信息
 */
export async function getUserLevelInfo() {
  try {
    const response = await apiGet("/api/user-levels/me")

    if (response && response.success) {
      return {
        level: response.level,
        levelName: response.levelName,
        levelDescription: response.levelDescription,
        subscription: response.subscription,
      }
    }

    throw new Error(response?.error || "获取用户等级信息失败")
  } catch (error) {
    console.error("获取用户等级信息失败:", error)
    throw error
  }
}

/**
 * 获取功能使用情况
 * @param {string} featureKey - 功能标识符
 * @returns {Promise<Object>} 使用情况
 */
export async function getFeatureUsage(featureKey) {
  try {
    const response = await apiGet(`/api/permissions/usage/${featureKey}`)

    if (response && response.success) {
      return {
        hasLimit: response.hasLimit,
        dailyLimit: response.dailyLimit,
        monthlyLimit: response.monthlyLimit,
        dailyUsage: response.dailyUsage,
        monthlyUsage: response.monthlyUsage,
        dailyRemaining: response.dailyRemaining,
        monthlyRemaining: response.monthlyRemaining,
      }
    }

    throw new Error(response?.error || "获取功能使用情况失败")
  } catch (error) {
    console.error(`获取功能使用情况失败 (${featureKey}):`, error)
    throw error
  }
}

/**
 * 获取用户可用的所有功能
 * @returns {Promise<Array<string>>} 功能标识符列表
 */
export async function getUserFeatures() {
  try {
    const response = await apiGet("/api/permissions/me")

    if (response && response.success) {
      return response.features
    }

    return []
  } catch (error) {
    console.error("获取用户功能失败:", error)
    return []
  }
}

export default {
  getAllLevels,
  getUserLevelInfo,
  getFeatureUsage,
  getUserFeatures,
}
