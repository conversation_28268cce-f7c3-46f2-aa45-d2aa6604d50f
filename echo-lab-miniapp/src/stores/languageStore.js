/**
 * 语言状态管理 - 小程序版本
 * 基于H5版本的languageStore，适配小程序环境
 * 管理用户当前学习的语言和等级
 */

import { reactive, computed } from 'vue'
import { SUPPORTED_LANGUAGES } from '../config/languages.js'
import { getLanguageLabel } from '../config/languages.js'
import { getLanguageLevels, getLevelInfo, isValidLevel } from '../config/languageLevels.js'
import {
  getLearningLanguage,
  setLearningLanguage,
  getUserLevel,
  setUserLevel,
  getSelectedLevels,
  setSelectedLevels,
  shouldShowOnboarding
} from '../utils/userSettings.js'

// 事件监听器管理
const eventListeners = {
  languageChanged: [],
  levelsChanged: []
}

// 创建响应式状态
const state = reactive({
  // 当前学习的语言
  currentLearningLanguage: getLearningLanguage(),

  // 当前用户等级（保留兼容性）
  currentUserLevel: getUserLevel(),

  // 用户选择的多个水平等级
  selectedLevels: getSelectedLevels(),

  // 支持的语言列表
  supportedLanguages: SUPPORTED_LANGUAGES,

  // 状态更新时间戳（用于强制响应性更新）
  updateTimestamp: Date.now()
})

// 计算属性
const getters = {
  /**
   * 获取当前学习语言的标签
   */
  currentLanguageLabel: computed(() => {
    return getLanguageLabel(state.currentLearningLanguage)
  }),

  /**
   * 获取当前学习语言的配置对象
   */
  currentLanguageConfig: computed(() => {
    return state.supportedLanguages.find(
      lang => lang.value === state.currentLearningLanguage
    )
  }),

  /**
   * 检查是否已设置学习语言
   */
  hasLanguageSet: computed(() => {
    return !!state.currentLearningLanguage
  }),

  /**
   * 检查是否已设置用户等级
   */
  hasLevelSet: computed(() => {
    return !!state.currentUserLevel
  }),

  /**
   * 获取当前语言的等级选项
   */
  currentLanguageLevels: computed(() => {
    if (!state.currentLearningLanguage) return []
    return getLanguageLevels(state.currentLearningLanguage)
  }),

  /**
   * 获取当前用户等级的详细信息
   */
  currentLevelInfo: computed(() => {
    if (!state.currentUserLevel || !state.currentLearningLanguage) return null
    return getLevelInfo(state.currentLearningLanguage, state.currentUserLevel)
  }),

  /**
   * 检查是否需要显示引导流程
   */
  needsOnboarding: computed(() => {
    return shouldShowOnboarding()
  })
}

// 操作方法
const actions = {
  /**
   * 设置当前学习语言
   * @param {string} languageCode 语言代码
   */
  setLearningLanguage(languageCode) {
    if (!languageCode) return false

    // 验证语言代码是否支持
    const isSupported = state.supportedLanguages.some(
      lang => lang.value === languageCode
    )

    if (!isSupported) {
      console.warn(`不支持的语言代码: ${languageCode}`)
      return false
    }

    // 如果语言发生变化，清除之前的等级设置和相关状态
    const languageChanged = state.currentLearningLanguage !== languageCode

    // 保存到本地存储（内部会自动处理等级清理）
    const success = setLearningLanguage(languageCode)

    if (success) {
      // 更新状态
      state.currentLearningLanguage = languageCode

      // 如果语言变化，同步清除等级状态
      if (languageChanged) {
        state.currentUserLevel = null

        // 触发语言切换事件，通知其他组件重置数据
        this.emitLanguageChanged(languageCode)
      }

      // 更新时间戳，触发响应性更新
      state.updateTimestamp = Date.now()
    }

    return success
  },

  /**
   * 设置用户等级
   * @param {string} level 等级代码
   */
  setUserLevel(level) {
    // 验证等级是否有效
    if (level && state.currentLearningLanguage) {
      if (!isValidLevel(state.currentLearningLanguage, level)) {
        console.warn(`无效的等级: ${level} (语言: ${state.currentLearningLanguage})`)
        return false
      }
    }

    // 保存到本地存储
    const success = setUserLevel(level)

    if (success) {
      // 更新状态
      state.currentUserLevel = level
      
      // 更新时间戳，触发响应性更新
      state.updateTimestamp = Date.now()
    }

    return success
  },

  /**
   * 清除用户等级
   */
  clearUserLevel() {
    return this.setUserLevel(null)
  },

  /**
   * 设置用户选择的多个水平等级
   * @param {Array} levels 水平等级数组
   */
  setSelectedLevels(levels) {
    const validLevels = Array.isArray(levels) ? levels : []

    // 验证等级是否有效
    if (validLevels.length > 0 && state.currentLearningLanguage) {
      const invalidLevels = validLevels.filter(level =>
        !isValidLevel(state.currentLearningLanguage, level)
      )
      if (invalidLevels.length > 0) {
        console.warn(`无效的等级: ${invalidLevels.join(', ')} (语言: ${state.currentLearningLanguage})`)
        return false
      }
    }

    // 保存到本地存储
    const success = setSelectedLevels(validLevels)

    if (success) {
      // 检查等级是否发生变化
      const oldLevels = state.selectedLevels || []
      const levelsChanged = oldLevels.length !== validLevels.length ||
        !oldLevels.every(level => validLevels.includes(level))

      // 更新状态
      state.selectedLevels = validLevels

      // 如果等级发生变化，触发事件
      if (levelsChanged) {
        this.emitLevelsChanged(validLevels)
      }

      // 更新时间戳，触发响应性更新
      state.updateTimestamp = Date.now()
    }

    return success
  },

  /**
   * 清除用户选择的水平等级
   */
  clearSelectedLevels() {
    return this.setSelectedLevels([])
  },

  /**
   * 从本地存储重新加载设置
   */
  reloadFromStorage() {
    const savedLanguage = getLearningLanguage()
    const savedLevel = getUserLevel()
    const savedSelectedLevels = getSelectedLevels()

    // 验证保存的语言是否仍然支持
    if (savedLanguage) {
      const isSupported = state.supportedLanguages.some(
        lang => lang.value === savedLanguage
      )

      if (isSupported) {
        state.currentLearningLanguage = savedLanguage
      } else {
        console.warn(`保存的语言代码不再支持: ${savedLanguage}，使用默认语言`)
        this.setLearningLanguage('ja') // 使用默认日语
      }
    }

    // 加载等级设置（保留兼容性）
    if (savedLevel) {
      state.currentUserLevel = savedLevel
    }

    // 加载多选水平等级设置
    if (savedSelectedLevels && savedSelectedLevels.length > 0) {
      state.selectedLevels = savedSelectedLevels
    }

    // 更新时间戳
    state.updateTimestamp = Date.now()
  },

  /**
   * 重置为默认语言
   */
  resetToDefault() {
    this.setLearningLanguage('ja')
  },

  /**
   * 切换学习语言（带确认）
   * @param {string} languageCode 新的语言代码
   * @returns {boolean} 是否成功切换
   */
  switchLanguage(languageCode) {
    if (languageCode === state.currentLearningLanguage) {
      return true // 相同语言，无需切换
    }

    try {
      return this.setLearningLanguage(languageCode)
    } catch (error) {
      console.error('切换学习语言失败:', error)
      return false
    }
  },

  /**
   * 触发语言切换事件
   * @param {string} newLanguage 新的语言代码
   */
  emitLanguageChanged(newLanguage) {
    console.log('触发语言切换事件:', newLanguage)
    eventListeners.languageChanged.forEach(callback => {
      try {
        callback(newLanguage)
      } catch (error) {
        console.error('语言切换事件回调执行失败:', error)
      }
    })
  },

  /**
   * 监听语言切换事件
   * @param {Function} callback 回调函数
   * @returns {Function} 取消监听的函数
   */
  onLanguageChanged(callback) {
    if (typeof callback !== 'function') {
      console.warn('语言切换监听器必须是函数')
      return () => {}
    }

    eventListeners.languageChanged.push(callback)

    // 返回取消监听的函数
    return () => {
      const index = eventListeners.languageChanged.indexOf(callback)
      if (index > -1) {
        eventListeners.languageChanged.splice(index, 1)
      }
    }
  },

  /**
   * 移除所有语言切换监听器
   */
  removeAllLanguageListeners() {
    eventListeners.languageChanged = []
  },

  /**
   * 触发等级变化事件
   * @param {Array} newLevels 新的等级数组
   */
  emitLevelsChanged(newLevels) {
    console.log('触发等级变化事件:', newLevels)
    eventListeners.levelsChanged.forEach(callback => {
      try {
        callback(newLevels)
      } catch (error) {
        console.error('等级变化事件回调执行失败:', error)
      }
    })
  },

  /**
   * 监听等级变化事件
   * @param {Function} callback 回调函数
   * @returns {Function} 取消监听的函数
   */
  onLevelsChanged(callback) {
    if (typeof callback !== 'function') {
      console.warn('等级变化监听器必须是函数')
      return () => {}
    }

    eventListeners.levelsChanged.push(callback)

    // 返回取消监听的函数
    return () => {
      const index = eventListeners.levelsChanged.indexOf(callback)
      if (index > -1) {
        eventListeners.levelsChanged.splice(index, 1)
      }
    }
  },

  /**
   * 移除所有等级变化监听器
   */
  removeAllLevelsListeners() {
    eventListeners.levelsChanged = []
  }
}

// 创建store实例
const languageStore = {
  // 状态
  state,
  
  // 计算属性
  ...getters,
  
  // 操作方法
  ...actions
}

// 初始化时从存储加载数据
languageStore.reloadFromStorage()

export default languageStore
