/**
 * 全局CSS变量定义
 * 基于微信小程序设计规范的字体大小标准
 */

/* 根元素变量定义 */
:root {
  /* ========== 基础字体大小 ========== */
  --font-size-xxl: 36rpx;    /* 超大字体 */
  --font-size-xl: 32rpx;     /* 大字体 */
  --font-size-lg: 28rpx;     /* 大号字体 */
  --font-size-md: 24rpx;     /* 中等字体（默认） */
  --font-size-sm: 20rpx;     /* 小字体 */
  --font-size-xs: 16rpx;     /* 超小字体 */
  --font-size-xxs: 14rpx;    /* 微小字体 */

  /* ========== 组件专用字体大小 ========== */
  /* 标题相关 */
  --font-size-page-title: var(--font-size-lg);      /* 页面标题 28rpx */
  --font-size-section-title: var(--font-size-md);   /* 区块标题 24rpx */
  --font-size-card-title: var(--font-size-sm);      /* 卡片标题 20rpx */
  
  /* 正文内容 */
  --font-size-body-text: var(--font-size-sm);       /* 正文 20rpx */
  --font-size-description: var(--font-size-xs);     /* 描述文字 16rpx */
  --font-size-caption: var(--font-size-xxs);        /* 说明文字 14rpx */
  
  /* 按钮相关 */
  --font-size-button-large: var(--font-size-md);    /* 大按钮 24rpx */
  --font-size-button-medium: var(--font-size-sm);   /* 中等按钮 20rpx */
  --font-size-button-small: var(--font-size-xs);    /* 小按钮 16rpx */
  
  /* 表单相关 */
  --font-size-input-text: var(--font-size-sm);      /* 输入框文字 20rpx */
  --font-size-input-placeholder: var(--font-size-xs); /* 占位符文字 16rpx */
  --font-size-label: var(--font-size-xs);           /* 标签文字 16rpx */
  
  /* 弹窗相关 */
  --font-size-modal-title: var(--font-size-md);     /* 弹窗标题 24rpx */
  --font-size-modal-content: var(--font-size-sm);   /* 弹窗内容 20rpx */
  --font-size-modal-button: var(--font-size-sm);    /* 弹窗按钮 20rpx */
  
  /* 卡片相关 */
  --font-size-card-meta: var(--font-size-xxs);      /* 卡片元信息 14rpx */
  --font-size-card-tag: var(--font-size-xxs);       /* 卡片标签 14rpx */
  
  /* 导航相关 */
  --font-size-tab-text: var(--font-size-sm);        /* 标签页文字 20rpx */
  --font-size-nav-text: var(--font-size-xs);        /* 导航文字 16rpx */
  
  /* 状态相关 */
  --font-size-status-text: var(--font-size-xs);     /* 状态文字 16rpx */
  --font-size-error-text: var(--font-size-xs);      /* 错误提示 16rpx */
  --font-size-success-text: var(--font-size-xs);    /* 成功提示 16rpx */

  /* ========== 行高配置 ========== */
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  --line-height-loose: 1.8;

  /* ========== 字重配置 ========== */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* ========== 颜色配置 ========== */
  --color-text-primary: #303133;     /* 主要文字颜色 */
  --color-text-regular: #606266;     /* 常规文字颜色 */
  --color-text-secondary: #909399;   /* 次要文字颜色 */
  --color-text-placeholder: #C0C4CC; /* 占位符文字颜色 */
  
  /* ========== 主题色配置 ========== */
  --color-primary: #409eff;          /* 主题色 */
  --color-success: #67c23a;          /* 成功色 */
  --color-warning: #e6a23c;          /* 警告色 */
  --color-danger: #f56c6c;           /* 危险色 */
  --color-info: #909399;             /* 信息色 */
}

/* ========== 通用字体类 ========== */
.font-xxl { font-size: var(--font-size-xxl); }
.font-xl { font-size: var(--font-size-xl); }
.font-lg { font-size: var(--font-size-lg); }
.font-md { font-size: var(--font-size-md); }
.font-sm { font-size: var(--font-size-sm); }
.font-xs { font-size: var(--font-size-xs); }
.font-xxs { font-size: var(--font-size-xxs); }

/* ========== 通用行高类 ========== */
.line-height-tight { line-height: var(--line-height-tight); }
.line-height-normal { line-height: var(--line-height-normal); }
.line-height-relaxed { line-height: var(--line-height-relaxed); }
.line-height-loose { line-height: var(--line-height-loose); }

/* ========== 通用字重类 ========== */
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* ========== 通用文字颜色类 ========== */
.text-primary { color: var(--color-text-primary); }
.text-regular { color: var(--color-text-regular); }
.text-secondary { color: var(--color-text-secondary); }
.text-placeholder { color: var(--color-text-placeholder); }
