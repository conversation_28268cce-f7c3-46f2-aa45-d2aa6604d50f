/**
 * API 工具函数 - 封装云函数调用
 * 简化 uniCloud.callFunction 的使用
 */

import { getLearningLanguage } from './userSettings.js'

/**
 * 调用 API 代理云函数
 * @param {string} method HTTP 方法
 * @param {string} path API 路径
 * @param {Object} data 请求数据（POST/PUT）或查询参数（GET）
 * @param {Object} headers 请求头
 * @returns {Promise} API 响应
 */
export async function callAPI(method, path, data = null, headers = {}) {
  console.log('=== callAPI 开始 ===')
  console.log('传入参数:', { method, path, data, headers })

  try {
    // 自动添加用户语言头 - 仿照H5项目的httpClient
    const userLanguage = getLearningLanguage()
    console.log('用户语言:', userLanguage)
    if (userLanguage) {
      headers['X-User-Language'] = userLanguage
    }
  } catch (error) {
    console.error('获取用户语言失败:', error)
    // 继续执行，不影响API调用
  }

  try {
    // 获取存储的 token
    const token = uni.getStorageSync('auth_token')

    // 构建请求头
    const requestHeaders = { ...headers }
    if (token) {
      requestHeaders.authorization = `Bearer ${token}`
    }

    console.log('最终请求参数:', { method, path, data, headers: requestHeaders })

    // 构建云函数调用参数
    const cloudFunctionData = {
      method,
      path,
      data,
      headers: requestHeaders
    }



    const result = await uniCloud.callFunction({
      name: 'api-proxy',
      data: cloudFunctionData
    })

    console.log('云函数调用结果:', result)

    // 云函数直接返回服务器原始数据
    console.log('API请求成功:', { path })
    return result.result

  } catch (error) {
    console.error('API请求失败:', { path, error: error.message })

    // 云函数调用失败或服务器错误
    throw new Error('网络请求失败，请检查网络连接')
  }
}

/**
 * GET 请求
 * @param {string} path API 路径
 * @param {Object} params 查询参数
 * @param {Object} headers 请求头
 * @returns {Promise} API 响应
 */
export function apiGet(path, params = {}, headers = {}) {
  console.log('=== apiGet 调用 ===')
  console.log('apiGet 参数:', { path, params, headers })
  return callAPI('GET', path, params, headers)
}

/**
 * POST 请求
 * @param {string} path API 路径
 * @param {Object} data 请求数据
 * @param {Object} headers 请求头
 * @returns {Promise} API 响应
 */
export function apiPost(path, data, headers = {}) {
  return callAPI('POST', path, data, headers)
}

/**
 * PUT 请求
 * @param {string} path API 路径
 * @param {Object} data 请求数据
 * @param {Object} headers 请求头
 * @returns {Promise} API 响应
 */
export function apiPut(path, data, headers = {}) {
  return callAPI('PUT', path, data, headers)
}

/**
 * DELETE 请求
 * @param {string} path API 路径
 * @param {Object} headers 请求头
 * @returns {Promise} API 响应
 */
export function apiDelete(path, headers = {}) {
  return callAPI('DELETE', path, null, headers)
}



/**
 * 显示网络错误提示
 * @param {Error} error 错误对象
 */
export function showNetworkError(error) {
  let message = '网络错误'
  
  if (error.statusCode === 401) {
    message = '登录已过期'
  } else if (error.statusCode === 403) {
    message = '没有权限'
  } else if (error.statusCode === 404) {
    message = '资源不存在'
  } else if (error.statusCode >= 500) {
    message = '服务器错误'
  } else if (error.message) {
    message = error.message
  }
  
  uni.showToast({
    title: message,
    icon: 'error',
    duration: 2000
  })
}
