/**
 * 后台音频播放器 - 本地文件缓存版本
 */
export class BackgroundAudioPlayer {
  constructor() {
    // 播放状态
    this.timeline = []
    this.currentTime = 0
    this.duration = 0
    this.isPlaying = false
    this.currentIndex = 0
    this.currentAudioStartTime = 0
    this.loopEnabled = false // 循环播放状态

    // 事件回调
    this.onTimeUpdate = null
    this.onEnded = null
    this.onLoadingChange = null
    this.onPlayStateChange = null

    // 标记是否是内部切换音频片段
    this.isInternalSwitch = false
    
    // 定时器
    this.timeUpdateTimer = null
    this.loadingTimer = null
    this.loadingThreshold = 500
    
    // 音频管理器
    this.audioManager = null
    
    // 本地文件缓存
    this.fileManager = wx.getFileSystemManager()
    this.cacheDir = `${wx.env.USER_DATA_PATH}/audio_cache`

    this.initCacheDir()
    this.setupAudioManager()
  }

  // ==================== 音频管理器设置 ====================
  setupAudioManager() {
    this.audioManager = wx.getBackgroundAudioManager()

    this.audioManager.onPlay(() => {
      this.isPlaying = true
      this.startTimeUpdateTimer()

      // 音频开始播放时立即清理loading状态
      if (this.loadingTimer) {
        clearTimeout(this.loadingTimer)
        this.loadingTimer = null
      }
      if (this.onLoadingChange) {
        this.onLoadingChange(false)
      }

      // 只有非内部切换时才触发播放状态变化回调
      if (this.onPlayStateChange && !this.isInternalSwitch) {
        this.onPlayStateChange(true)
      }

      // 重置内部切换标记
      this.isInternalSwitch = false
    })

    this.audioManager.onPause(() => {
      this.isPlaying = false
      this.stopTimeUpdateTimer()

      if (this.onPlayStateChange) {
        this.onPlayStateChange(false)
      }
    })

    this.audioManager.onStop(() => {
      this.isPlaying = false
      this.stopTimeUpdateTimer()
      if (this.onPlayStateChange) {
        this.onPlayStateChange(false)
      }
    })



    this.audioManager.onWaiting(() => {
      if (this.loadingTimer) {
        clearTimeout(this.loadingTimer)
        this.loadingTimer = null
      }

      this.loadingTimer = setTimeout(() => {
        if (this.onLoadingChange) {
          this.onLoadingChange(true)
        }
      }, this.loadingThreshold)
    })

    this.audioManager.onCanplay(() => {
      if (this.loadingTimer) {
        clearTimeout(this.loadingTimer)
        this.loadingTimer = null
      }
      
      if (this.onLoadingChange) {
        this.onLoadingChange(false)
      }
    })

    this.audioManager.onError((error) => {
      console.error('音频播放错误:', {
        error: error,
        currentIndex: this.currentIndex,
        currentSrc: this.audioManager.src,
        isPlaying: this.isPlaying
      })

      if (this.loadingTimer) {
        clearTimeout(this.loadingTimer)
        this.loadingTimer = null
      }

      if (this.onLoadingChange) {
        this.onLoadingChange(false)
      }

      // 检查是否是本地文件播放失败，如果是则尝试网络播放
      const currentItem = this.timeline[this.currentIndex]
      if (currentItem && currentItem.type === 'audio') {
        const currentSrc = this.audioManager.src
        const localPath = this.getLocalPath(currentItem.audioUrl)

        // 如果当前使用的是本地文件且播放失败，尝试使用网络音频
        if (currentSrc === localPath) {
          console.log('本地文件播放失败，尝试网络音频')
          this.audioManager.src = currentItem.audioUrl
          if (this.isPlaying) {
            setTimeout(() => {
              this.audioManager.play()
            }, 100)
          }
          return // 不跳到下一个，先尝试网络播放
        }
      }

      // 如果网络播放也失败，或者其他错误，则跳到下一个
      if (this.currentIndex < this.timeline.length - 1) {
        this.currentIndex++
        setTimeout(() => {
          this.playCurrentIndex()
        }, 100)
      } else {
        // 最后一个音频也播放失败，停止播放
        this.isPlaying = false
        this.stopTimeUpdateTimer()
        if (this.onPlayStateChange) {
          this.onPlayStateChange(false)
        }
      }
    })

    this.audioManager.onEnded(() => {
      this.handleAudioEnded()
    })
  }

  // ==================== 本地文件缓存 ====================
  initCacheDir() {
    try {
      this.fileManager.mkdirSync(this.cacheDir, true)
      console.log('📁 音频缓存目录创建成功')
    } catch (error) {
      const errorMsg = error.errMsg || error.message || ''
      if (!errorMsg.includes('file already exists')) {
        console.error('📁 缓存目录创建失败:', error)
      }
    }
  }

  getLocalPath(audioUrl) {
    // 从URL中提取文件名，确保不包含协议和域名
    let fileName = audioUrl.split('/').pop().split('?')[0] || 'audio.mp3'

    // 如果文件名包含特殊字符，进行清理
    fileName = fileName.replace(/[^a-zA-Z0-9._-]/g, '_')

    return `${this.cacheDir}/${fileName}`
  }

  checkLocalFile(audioUrl) {
    const localPath = this.getLocalPath(audioUrl)
    try {
      const stats = this.fileManager.statSync(localPath)
      return stats.isFile() && stats.size > 0
    } catch (error) {
      return false
    }
  }

  async downloadAudio(audioUrl) {
    const localPath = this.getLocalPath(audioUrl)

    return new Promise((resolve, reject) => {
      wx.downloadFile({
        url: audioUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            try {
              this.fileManager.copyFileSync(res.tempFilePath, localPath)
              resolve(localPath)
            } catch (error) {
              const errorMsg = error.errMsg || error.message || ''
              console.error('📦 音频保存失败:', errorMsg)

              // 如果是存储空间不足，打印当前缓存文件体积并尝试清理
              if (errorMsg.includes('storage limit') || errorMsg.includes('exceeded')) {
                const cacheInfo = this.getCacheSize()
                console.error('💾 存储空间不足! 当前缓存状态:', cacheInfo.summary)
                console.error('💾 详细信息:', {
                  文件数量: cacheInfo.fileCount,
                  总大小MB: cacheInfo.totalSizeMB,
                  总大小字节: cacheInfo.totalSize
                })

                // 立即清理部分旧文件
                try {
                  this.partialCleanupCache()
                } catch (cleanupError) {
                  console.error('部分清理失败:', cleanupError)
                }
              }

              reject(error)
            }
          } else {
            reject(new Error(`下载失败: ${res.statusCode}`))
          }
        },
        fail: reject
      })
    })
  }

  async preloadAudio(index) {
    const item = this.timeline[index]
    if (!item || item.type !== 'audio' || !item.audioUrl) {
      return
    }

    try {
      // 检查本地文件是否已存在
      if (this.checkLocalFile(item.audioUrl)) {
        console.log(`📦 音频已缓存 [${index}]`)
        return
      }

      // 移除下载前的缓存检查，由统一的缓存管理器处理

      console.log(`📥 开始预加载 [${index}]`)
      await this.downloadAudio(item.audioUrl)
    } catch (error) {
      console.warn(`预加载失败 [${index}]:`, error.message)
    }
  }

  // 移除缓存清理逻辑，由统一的 CacheManager 处理

  // 部分清理缓存（存储空间不足时使用）
  partialCleanupCache() {
    try {
      const files = this.fileManager.readdirSync(this.cacheDir)

      if (files.length <= 10) {
        return
      }

      // 获取当前正在播放的音频文件路径
      const currentAudioUrl = this.timeline[this.currentIndex]?.audioUrl
      const currentLocalPath = currentAudioUrl ? this.getLocalPath(currentAudioUrl) : null

      // 获取文件信息并按修改时间排序
      const fileInfos = files.map(fileName => {
        try {
          const filePath = `${this.cacheDir}/${fileName}`
          const stats = this.fileManager.statSync(filePath)
          return {
            filePath,
            fileName,
            lastModifiedTime: stats.lastModifiedTime,
            isCurrentPlaying: filePath === currentLocalPath
          }
        } catch (error) {
          return null
        }
      }).filter(Boolean)

      // 按修改时间排序（最旧的在前）
      fileInfos.sort((a, b) => a.lastModifiedTime - b.lastModifiedTime)

      // 只删除最旧的30%文件，保护当前播放文件
      const deleteCount = Math.max(1, Math.floor(fileInfos.length * 0.3))
      let deletedCount = 0

      for (let i = 0; i < fileInfos.length && deletedCount < deleteCount; i++) {
        const info = fileInfos[i]
        if (!info.isCurrentPlaying) {
          try {
            this.fileManager.unlinkSync(info.filePath)
            deletedCount++
          } catch (error) {
            console.warn(`删除缓存文件失败: ${info.fileName}`, error)
          }
        }
      }

      console.log(`部分清理完成: 删除了 ${deletedCount} 个旧文件`)
    } catch (error) {
      console.error('部分清理失败:', error)
    }
  }

  // 计算当前缓存文件总体积
  getCacheSize() {
    try {
      const files = this.fileManager.readdirSync(this.cacheDir)
      let totalSize = 0
      let fileCount = 0

      files.forEach(fileName => {
        try {
          const filePath = `${this.cacheDir}/${fileName}`
          const stats = this.fileManager.statSync(filePath)
          totalSize += stats.size
          fileCount++
        } catch (error) {
          // 单个文件读取失败，跳过
        }
      })

      const totalSizeMB = (totalSize / (1024 * 1024)).toFixed(2)
      return {
        totalSize,
        totalSizeMB,
        fileCount,
        summary: `${fileCount}个文件，共${totalSizeMB}MB`
      }
    } catch (error) {
      return {
        totalSize: 0,
        totalSizeMB: '0.00',
        fileCount: 0,
        summary: '无法获取缓存信息'
      }
    }
  }







  // ==================== 播放控制 ====================
  setTimeline(timeline) {
    this.timeline = timeline
    this.duration = timeline.reduce((total, item) => total + (item.duration || 0), 0)
    this.currentTime = 0
    this.currentIndex = 0
    this.currentAudioStartTime = 0

    this.startInitialPreload()
  }

  async startInitialPreload() {
    // 初始预加载前3个音频
    this.triggerPreloadNext()
  }

  play(isUserAction = false) {
    if (this.isPlaying) return

    // 确保有有效的时间线和当前索引
    if (!this.timeline || this.timeline.length === 0) {
      console.error('播放失败: 没有有效的时间线')
      return
    }

    // 确保当前索引有效
    if (this.currentIndex >= this.timeline.length) {
      this.currentIndex = 0
    }

    this.isPlaying = true
    this.startTimeUpdateTimer()
    this.playCurrentIndex()
  }

  pause() {
    if (!this.isPlaying) return
    this.audioManager.pause()
  }

  // 设置循环播放
  setLoop(enabled) {
    const wasEnabled = this.loopEnabled
    this.loopEnabled = enabled

    // 如果在播放期间切换循环状态，可以在这里添加额外的逻辑
    // 例如：更新UI显示、记录日志等
    if (wasEnabled !== enabled) {
      console.log(`循环播放已${enabled ? '开启' : '关闭'}`)
    }
  }

  stop() {
    this.audioManager.stop()
    this.isPlaying = false
    this.currentTime = 0
    this.currentIndex = 0
    this.currentAudioStartTime = 0
    this.stopTimeUpdateTimer()
  }

  playCurrentIndex() {
    const item = this.timeline[this.currentIndex]

    if (!item || item.type !== 'audio') {
      console.error('无效的音频项目:', item)
      return
    }

    this.setAudioSource(item)
    this.audioManager.play()
    this.triggerPreloadNext()
  }

  setAudioSource(item) {
    if (!this.audioManager) return

    this.audioManager.title = item.content || '音频播放'
    this.audioManager.singer = '精听练习'
    this.audioManager.epname = 'Echo Lab'
    this.audioManager.coverImgUrl = 'https://echolab.oss-cn-hongkong.aliyuncs.com/logo.jpg'

    // 直接检查本地文件是否存在
    if (this.checkLocalFile(item.audioUrl)) {
      const localPath = this.getLocalPath(item.audioUrl)
      this.audioManager.src = localPath
    } else {
      this.audioManager.src = item.audioUrl
    }
  }

  triggerPreloadNext() {
    // 预加载当前位置后面的3个音频
    const preloadCount = 3

    for (let i = 1; i <= preloadCount; i++) {
      const targetIndex = this.currentIndex + i
      if (targetIndex < this.timeline.length) {
        this.preloadAudio(targetIndex)
      }
    }
  }

  handleAudioEnded() {
    // 统一的下一步播放逻辑
    this.playNext()
  }

  // 播放下一个音频片段（支持循环）
  playNext() {
    if (this.currentIndex < this.timeline.length - 1) {
      // 不是最后一个片段，播放下一个
      this.goToIndex(this.currentIndex + 1)
    } else {
      // 是最后一个片段，检查循环设置
      if (this.loopEnabled) {
        // 循环播放：回到开头
        this.goToIndex(0)
      } else {
        // 非循环播放：结束播放
        this.stopPlayback()
      }
    }
  }

  // 跳转到指定索引并播放
  goToIndex(index) {
    if (index < 0 || index >= this.timeline.length) return

    this.isInternalSwitch = true // 标记为内部切换
    this.currentIndex = index
    this.updateCurrentAudioStartTime()
    this.currentTime = this.currentAudioStartTime
    this.playCurrentIndex()
  }

  // 停止播放并重置
  stopPlayback() {
    this.isPlaying = false
    this.stopTimeUpdateTimer()

    // 重置到开头
    this.currentIndex = 0
    this.currentTime = 0
    this.currentAudioStartTime = 0

    if (this.onEnded) {
      this.onEnded()
    }
  }

  // ==================== 时间管理 ====================
  startTimeUpdateTimer() {
    this.stopTimeUpdateTimer()
    // 降低更新频率从100ms到200ms，减少性能消耗
    this.timeUpdateTimer = setInterval(() => {
      if (this.isPlaying) {
        this.updateCurrentTime()
        if (this.onTimeUpdate) {
          this.onTimeUpdate(this.currentTime)
        }
      }
    }, 150)
  }

  stopTimeUpdateTimer() {
    if (this.timeUpdateTimer) {
      clearInterval(this.timeUpdateTimer)
      this.timeUpdateTimer = null
    }
  }

  updateCurrentTime() {
    if (!this.audioManager || !this.isPlaying) return

    // 直接获取音频管理器的当前播放时间
    const audioCurrentTime = this.audioManager.currentTime || 0

    // 计算全局时间线时间
    this.currentTime = this.currentAudioStartTime + audioCurrentTime
  }

  // 计算指定索引的开始时间
  getStartTimeByIndex(index) {
    let time = 0
    for (let i = 0; i < index; i++) {
      time += this.timeline[i].duration || 0
    }
    return time
  }

  // 根据时间找到对应的索引
  findIndexByTime(time) {
    let accumulatedTime = 0
    for (let i = 0; i < this.timeline.length; i++) {
      const itemDuration = this.timeline[i].duration || 0
      if (time < accumulatedTime + itemDuration) {
        return i
      }
      accumulatedTime += itemDuration
    }
    return this.timeline.length - 1
  }

  updateCurrentAudioStartTime() {
    this.currentAudioStartTime = this.getStartTimeByIndex(this.currentIndex)
  }

  seekTo(time) {
    this.currentIndex = this.findIndexByTime(time)
    this.currentAudioStartTime = this.getStartTimeByIndex(this.currentIndex)
    this.currentTime = time

    const item = this.timeline[this.currentIndex]
    if (item && item.type === 'audio') {
      const audioSeekTime = time - this.currentAudioStartTime
      this.setAudioSource(item)
      this.audioManager.seek(audioSeekTime)
      if (this.isPlaying) {
        this.audioManager.play()
      }
    }
  }

  jumpToNext() {
    if (this.currentIndex < this.timeline.length - 1) {
      this.goToIndex(this.currentIndex + 1)
    } else if (this.loopEnabled) {
      // 如果是最后一个且开启循环，跳到开头
      this.goToIndex(0)
    }
  }

  jumpToPrevious() {
    if (this.currentIndex > 0) {
      this.goToIndex(this.currentIndex - 1)
    } else if (this.loopEnabled) {
      // 如果是第一个且开启循环，跳到最后
      this.goToIndex(this.timeline.length - 1)
    }
  }

  // 获取循环播放状态
  getLoopEnabled() {
    return this.loopEnabled
  }

  // 检查是否可以播放下一个
  canPlayNext() {
    return this.currentIndex < this.timeline.length - 1 || this.loopEnabled
  }

  // 检查是否可以播放上一个
  canPlayPrevious() {
    return this.currentIndex > 0 || this.loopEnabled
  }



  // 移除紧急清理逻辑，由统一的 CacheManager 处理

  destroy() {
    this.stop()
    this.stopTimeUpdateTimer()

    if (this.audioManager) {
      this.audioManager.stop()
    }
  }
}
