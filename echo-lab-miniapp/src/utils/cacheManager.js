/**
 * 缓存管理工具
 * 统一管理应用的各种缓存
 */

class CacheManager {
  constructor() {
    this.fileManager = wx.getFileSystemManager()
    this.audioCacheDir = `${wx.env.USER_DATA_PATH}/audio_cache`
    this.initCacheDir()
  }

  /**
   * 初始化缓存目录
   */
  initCacheDir() {
    try {
      this.fileManager.mkdirSync(this.audioCacheDir, true)
    } catch (error) {
      const errorMsg = error.errMsg || error.message || ''
      if (!errorMsg.includes('file already exists')) {
        console.error('📁 缓存目录创建失败:', error)
      }
    }
  }

  /**
   * 应用启动时的缓存检查和清理
   */
  startupCleanup() {
    try {
      console.log('🚀 启动时缓存清理')
      this.cleanupAudioCache()
    } catch (error) {
      console.warn('启动缓存清理失败:', error)
    }
  }

  /**
   * 手动触发缓存清理（供播放器等组件调用）
   */
  manualCleanup() {
    try {
      console.log('🔧 手动触发缓存清理')
      this.cleanupAudioCache()
    } catch (error) {
      console.warn('手动缓存清理失败:', error)
    }
  }



  /**
   * 清理音频缓存 - 保持最新50个文件（适应小程序存储限制）
   */
  cleanupAudioCache() {
    try {
      const files = this.fileManager.readdirSync(this.audioCacheDir)
      const maxFiles = 50 // 降低到50个文件，适应小程序存储限制

      if (files.length <= maxFiles) {
        console.log(`📦 缓存文件数量正常 (${files.length}/${maxFiles})`)
        return
      }

      console.log(`📦 开始清理音频缓存 (${files.length}/${maxFiles})`)

      // 按修改时间排序，删除最旧的文件
      const fileInfos = files.map(fileName => {
        try {
          const filePath = `${this.audioCacheDir}/${fileName}`
          const stats = this.fileManager.statSync(filePath)
          return { filePath, fileName, lastModifiedTime: stats.lastModifiedTime }
        } catch (error) {
          return null
        }
      }).filter(Boolean)

      fileInfos.sort((a, b) => a.lastModifiedTime - b.lastModifiedTime)
      const toDelete = fileInfos.slice(0, fileInfos.length - maxFiles)

      let deletedCount = 0
      toDelete.forEach(({ filePath, fileName }) => {
        try {
          this.fileManager.unlinkSync(filePath)
          deletedCount++
        } catch (error) {
          console.warn(`删除缓存文件失败: ${fileName}`, error)
        }
      })

      console.log(`📦 缓存清理完成: 删除了 ${deletedCount} 个旧文件，保留 ${fileInfos.length - deletedCount} 个文件`)
    } catch (error) {
      console.warn('音频缓存清理失败:', error)
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    const stats = {
      audioCache: { count: 0, size: 0 }
    }

    try {
      // 音频缓存统计
      try {
        const files = this.fileManager.readdirSync(this.audioCacheDir)
        stats.audioCache.count = files.length

        // 计算总大小
        let totalSize = 0
        files.forEach(fileName => {
          try {
            const filePath = `${this.audioCacheDir}/${fileName}`
            const fileStats = this.fileManager.statSync(filePath)
            totalSize += fileStats.size
          } catch (error) {
            // 忽略单个文件错误
          }
        })
        stats.audioCache.size = totalSize

        console.log(`📊 缓存统计: ${files.length}个文件, 总大小: ${(totalSize / 1024 / 1024).toFixed(2)}MB`)

        // 打印每个文件的详细信息
        console.log('📁 缓存文件详情:')
        files.forEach((fileName, index) => {
          try {
            const filePath = `${this.audioCacheDir}/${fileName}`
            const fileStats = this.fileManager.statSync(filePath)
            const sizeKB = (fileStats.size / 1024).toFixed(1)
            const modifiedTime = new Date(fileStats.lastModifiedTime).toLocaleString()
            console.log(`  ${index + 1}. ${fileName} - ${sizeKB}KB - ${modifiedTime}`)

            // 检查文件是否真的可以读取
            if (index === 0) {
              try {
                const fileData = this.fileManager.readFileSync(filePath)
                console.log(`  ✅ 文件可读取，实际大小: ${fileData.byteLength} bytes`)
              } catch (readError) {
                console.log(`  ❌ 文件无法读取: ${readError.message}`)
              }
            }
          } catch (error) {
            console.log(`  ${index + 1}. ${fileName} - 读取失败: ${error.message}`)
          }
        })

        // 检查是否接近小程序文件系统限制
        if (totalSize > 8 * 1024 * 1024) { // 8MB
          console.warn(`⚠️ 缓存大小可能接近小程序限制: ${(totalSize / 1024 / 1024).toFixed(2)}MB`)
        }
      } catch (error) {
        // 目录不存在或读取失败
      }
    } catch (error) {
      console.warn('获取缓存统计失败:', error)
    }

    return stats
  }

  /**
   * 检查用户数据目录的详细使用情况
   */
  checkUserDataDirectory() {
    try {
      const userDataPath = wx.env.USER_DATA_PATH
      console.log(`📁 用户数据路径: ${userDataPath}`)

      // 列出用户数据目录下的所有内容
      const items = this.fileManager.readdirSync(userDataPath)
      console.log(`📁 用户数据目录内容: ${items.length}个项目`)

      let totalSize = 0
      items.forEach((item, index) => {
        try {
          const itemPath = `${userDataPath}/${item}`
          const stats = this.fileManager.statSync(itemPath)
          const sizeKB = (stats.size / 1024).toFixed(1)
          totalSize += stats.size

          if (index < 10) { // 只显示前10个
            console.log(`  ${index + 1}. ${item} - ${sizeKB}KB ${stats.isDirectory() ? '(目录)' : '(文件)'}`)
          }
        } catch (error) {
          console.log(`  ${index + 1}. ${item} - 读取失败`)
        }
      })

      console.log(`📊 用户数据目录总大小: ${(totalSize / 1024 / 1024).toFixed(2)}MB`)

      if (items.length > 10) {
        console.log(`  ... 还有 ${items.length - 10} 个项目`)
      }
    } catch (error) {
      console.warn('检查用户数据目录失败:', error)
    }
  }

  /**
   * 清空所有缓存
   */
  clearAllCache() {
    try {
      // 显示清理前的状态
      console.log('🗑️ 清理前状态:')
      this.checkUserDataDirectory()

      // 清空音频缓存
      try {
        this.fileManager.rmdirSync(this.audioCacheDir, true)
        this.initCacheDir() // 重新创建目录
        console.log('🗑️ 已清空音频缓存')
      } catch (error) {
        console.warn('清空音频缓存失败:', error)
      }

      // 清空其他可能的缓存目录
      try {
        const userDataPath = wx.env.USER_DATA_PATH
        const items = this.fileManager.readdirSync(userDataPath)

        items.forEach(item => {
          if (item.includes('cache') || item.includes('temp')) {
            try {
              const itemPath = `${userDataPath}/${item}`
              const stats = this.fileManager.statSync(itemPath)
              if (stats.isDirectory()) {
                this.fileManager.rmdirSync(itemPath, true)
                console.log(`🗑️ 已清空: ${item}`)
              }
            } catch (error) {
              // 清理失败，继续
            }
          }
        })
      } catch (error) {
        console.warn('清空其他缓存失败:', error)
      }

      console.log('🗑️ 所有缓存清理完成')

      // 显示清理后的状态
      console.log('🗑️ 清理后状态:')
      this.checkUserDataDirectory()
    } catch (error) {
      console.warn('清空缓存失败:', error)
      throw error
    }
  }
}

// 创建单例实例
const cacheManager = new CacheManager()

export default cacheManager
