/**
 * 加密服务 - 基于echo-lab项目的cryptoService适配小程序
 * 用于加密和解密视频配置JSON数据
 */
// 在小程序环境中使用ES6导入
import CryptoJS from 'crypto-js'

// 加密密钥（与H5项目保持一致）
const SECRET_KEY = "echo-lab-secure-key-2024"

/**
 * 加密JSON数据
 * @param {Object} data - 要加密的JSON对象
 * @returns {string} - 加密后的字符串
 */
export function encryptJSON(data) {
  try {
    // 将对象转换为JSON字符串（压缩格式）
    const jsonString = JSON.stringify(data)

    // 使用AES加密
    const encrypted = CryptoJS.AES.encrypt(jsonString, SECRET_KEY).toString()

    // 返回加密后的字符串
    return encrypted
  } catch (error) {
    console.error("加密数据失败:", error)
    // 加密失败时返回原始JSON字符串（压缩格式）
    return JSON.stringify(data)
  }
}

/**
 * 解密JSON数据
 * @param {string} encryptedData - 加密的字符串
 * @returns {Object|null} - 解密后的JSON对象，解密失败时返回null
 */
export function decryptJSON(encryptedData) {
  try {
    // 检查是否是加密数据
    if (!encryptedData || typeof encryptedData !== "string") {
      throw new Error("无效的加密数据")
    }



    // 尝试解析为JSON（检查是否已经是JSON字符串）
    try {
      const parsed = JSON.parse(encryptedData)
      // 如果能成功解析为JSON，说明数据未加密，直接返回
      return parsed
    } catch (e) {
      // 解析失败，说明可能是加密数据，继续解密流程
    }

    // 使用AES解密
    const decrypted = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY).toString(
      CryptoJS.enc.Utf8
    )

    if (!decrypted) {
      throw new Error("解密结果为空")
    }

    // 将解密后的JSON字符串转换为对象
    return JSON.parse(decrypted)
  } catch (error) {
    console.error("解密数据失败:", error)

    // 尝试直接解析为JSON（兼容未加密的数据）
    try {
      return JSON.parse(encryptedData)
    } catch (e) {
      console.error("解析JSON失败:", e)
      throw new Error(`解密和解析都失败: ${error.message}`)
    }
  }
}

/**
 * 检测数据是否已加密
 * @param {string} data - 要检查的数据
 * @returns {boolean} - 是否已加密
 */
export function isEncrypted(data) {
  if (!data || typeof data !== "string") {
    return false
  }

  // 尝试解析为JSON
  try {
    JSON.parse(data)
    // 如果能成功解析为JSON，说明数据未加密
    return false
  } catch (e) {
    // 解析失败，可能是加密数据
    return true
  }
}
