/**
 * 错误处理工具 - 小程序专用
 * 提供统一的错误处理和用户提示
 */
import { isNetworkConnected, checkNetworkStatus, showNetworkStatusToast } from './networkStatus.js'

/**
 * 显示网络错误提示
 * @param {Error} error - 错误对象
 * @param {string} context - 错误上下文
 */
export async function showNetworkError(error, context = '') {
  // 处理网络错误

  // 检查网络状态
  try {
    const networkStatus = await checkNetworkStatus()

    // 如果网络断开，优先显示网络断开提示
    if (!networkStatus.isConnected) {
      showNetworkStatusToast(networkStatus)
      return
    }
  } catch (checkError) {
    // 检查网络状态失败，继续执行
  }

  let message = '网络请求失败'

  if (error && error.statusCode) {
    switch (error.statusCode) {
      case 401:
        message = '登录已过期，请重新登录'
        break
      case 403:
        message = '没有权限访问此内容'
        break
      case 404:
        message = '请求的内容不存在'
        break
      case 500:
        message = '服务器内部错误'
        break
      case 502:
      case 503:
      case 504:
        message = '服务器暂时不可用，请稍后重试'
        break
      default:
        if (error.statusCode >= 400 && error.statusCode < 500) {
          message = '请求参数错误'
        } else if (error.statusCode >= 500) {
          message = '服务器错误，请稍后重试'
        }
    }
  } else if (error && error.message) {
    if (error.message.includes('timeout')) {
      message = '请求超时，请检查网络连接'
    } else if (error.message.includes('Network Error') || error.message.includes('network')) {
      message = '网络连接失败，请检查网络设置'
    } else {
      message = error.message
    }
  }

  if (context) {
    message = `${context}: ${message}`
  }

  uni.showToast({
    title: message,
    icon: 'none',
    duration: 3000
  })
}

/**
 * 显示加载错误提示
 * @param {string} content - 内容类型
 */
export function showLoadingError(content = '内容') {
  uni.showToast({
    title: '加载失败',
    icon: 'none',
    duration: 2000
  })
}

/**
 * 显示播放错误提示
 * @param {Error} error - 错误对象
 */
export function showPlaybackError(error) {
  console.error('播放错误:', error)
  
  let message = '播放失败'
  
  if (error.message) {
    if (error.message.includes('audio')) {
      message = '音频无法播放'
    } else if (error.message.includes('network')) {
      message = '网络连接失败'
    } else if (error.message.includes('format')) {
      message = '音频格式不支持'
    }
  }
  
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  })
}

/**
 * 显示成功提示
 * @param {string} message - 成功消息
 */
export function showSuccess(message) {
  uni.showToast({
    title: message,
    icon: 'success',
    duration: 1500
  })
}

/**
 * 显示警告提示
 * @param {string} message - 警告消息
 */
export function showWarning(message) {
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  })
}

/**
 * 显示确认对话框
 * @param {string} title - 标题
 * @param {string} content - 内容
 * @returns {Promise<boolean>} 用户是否确认
 */
export function showConfirm(title, content) {
  return new Promise((resolve) => {
    uni.showModal({
      title,
      content,
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

// 加载状态管理
let loadingCount = 0
let loadingTimer = null

/**
 * 显示加载中提示
 * @param {string} title - 加载提示文字
 * @param {Object} options - 配置选项
 */
export function showLoading(title = '加载中...', options = {}) {
  const {
    mask = true,
    timeout = 30000, // 30秒超时
    showTimeout = true
  } = options

  loadingCount++

  // 清除之前的超时定时器
  if (loadingTimer) {
    clearTimeout(loadingTimer)
  }

  uni.showLoading({
    title,
    mask
  })

  // 设置超时处理
  if (timeout > 0 && showTimeout) {
    loadingTimer = setTimeout(() => {
      if (loadingCount > 0) {
        hideLoading()
        uni.showToast({
          title: '加载超时',
          icon: 'none',
          duration: 2000
        })
      }
    }, timeout)
  }
}

/**
 * 隐藏加载中提示
 */
export function hideLoading() {
  loadingCount = Math.max(0, loadingCount - 1)

  if (loadingCount === 0) {
    try {
      uni.hideLoading()
    } catch (error) {
      // 忽略 hideLoading 失败的错误（通常是因为没有显示 loading）
      console.warn('hideLoading failed:', error.errMsg || error.message)
    }

    // 清除超时定时器
    if (loadingTimer) {
      clearTimeout(loadingTimer)
      loadingTimer = null
    }
  }
}

/**
 * 强制隐藏加载提示
 */
export function forceHideLoading() {
  loadingCount = 0

  try {
    uni.hideLoading()
  } catch (error) {
    // 忽略 hideLoading 失败的错误（通常是因为没有显示 loading）
    console.warn('forceHideLoading failed:', error.errMsg || error.message)
  }

  if (loadingTimer) {
    clearTimeout(loadingTimer)
    loadingTimer = null
  }
}

/**
 * 显示带进度的加载提示
 * @param {string} title - 加载提示文字
 * @param {number} progress - 进度百分比 (0-100)
 */
export function showLoadingWithProgress(title = '加载中...', progress = 0) {
  const progressText = progress > 0 ? ` ${Math.round(progress)}%` : ''
  showLoading(`${title}${progressText}`)
}

/**
 * 网络重试工具函数
 * @param {Function} asyncFn - 异步函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} retryDelay - 重试延迟（毫秒）
 * @returns {Promise} 执行结果
 */
export async function withNetworkRetry(asyncFn, maxRetries = 3, retryDelay = 1000) {
  let lastError = null

  for (let i = 0; i <= maxRetries; i++) {
    try {
      // 检查网络状态
      if (!isNetworkConnected()) {
        const networkStatus = await checkNetworkStatus()
        if (!networkStatus.isConnected) {
          throw new Error('网络连接已断开')
        }
      }

      return await asyncFn()
    } catch (error) {
      lastError = error

      // 如果是最后一次重试，直接抛出错误
      if (i === maxRetries) {
        break
      }

      // 如果是网络错误，等待网络恢复
      if (isNetworkError(error)) {
        console.log(`网络错误，等待 ${retryDelay}ms 后重试 (${i + 1}/${maxRetries})`)
        await new Promise(resolve => setTimeout(resolve, retryDelay))

        // 检查网络是否恢复
        const networkStatus = await checkNetworkStatus()
        if (!networkStatus.isConnected) {
          // 网络仍未恢复，继续等待
          continue
        }
      } else {
        // 非网络错误，正常延迟重试
        console.log(`请求失败，等待 ${retryDelay}ms 后重试 (${i + 1}/${maxRetries})`)
        await new Promise(resolve => setTimeout(resolve, retryDelay))
      }
    }
  }

  throw lastError
}

/**
 * 判断是否为网络错误
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否为网络错误
 */
function isNetworkError(error) {
  if (!error) return false

  const networkErrorMessages = [
    'network error',
    'timeout',
    'connection failed',
    'no internet',
    'offline',
    '网络连接'
  ]

  const errorMessage = (error.message || '').toLowerCase()
  return networkErrorMessages.some(msg => errorMessage.includes(msg))
}

/**
 * 处理认证错误
 */
export function handleAuthError() {
  // 清除本地存储的认证信息
  uni.removeStorageSync('auth_token')
  uni.removeStorageSync('user_info')
  
  // 显示提示
  uni.showToast({
    title: '登录已过期',
    icon: 'none',
    duration: 2000
  })
  
  // 跳转到登录页面
  setTimeout(() => {
    uni.reLaunch({
      url: '/pages/index/index'
    })
  }, 2000)
}

/**
 * 重试机制包装器
 * @param {Function} fn - 要重试的函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delay - 重试延迟（毫秒）
 * @returns {Promise} 函数执行结果
 */
export async function withRetry(fn, maxRetries = 3, delay = 1000) {
  let lastError
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error
      
      if (i === maxRetries) {
        throw error
      }
      
      // 如果是认证错误，不重试
      if (error.statusCode === 401) {
        throw error
      }
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
    }
  }
  
  throw lastError
}

/**
 * 安全执行异步函数
 * @param {Function} fn - 异步函数
 * @param {string} errorContext - 错误上下文
 * @returns {Promise} 执行结果
 */
export async function safeAsync(fn, errorContext = '') {
  try {
    return await fn()
  } catch (error) {
    console.error(`安全执行失败 [${errorContext}]:`, error)
    showNetworkError(error, errorContext)
    throw error
  }
}
