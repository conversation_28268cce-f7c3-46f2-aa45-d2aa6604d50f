/**
 * 图片处理工具 - 小程序版本
 * 基于H5版本的ResponsiveImage方案，适配小程序环境
 */

/**
 * 获取最优图片尺寸档位
 * @param {number} width - 容器宽度
 * @returns {number} 最优宽度
 */
function getOptimalSize(width) {
  // 定义尺寸档位，与H5版本保持一致
  const sizes = [240, 360, 480, 720, 960, 1200, 1440]
  
  // 找到最接近且大于等于目标宽度的档位
  for (const size of sizes) {
    if (size >= width) {
      return size
    }
  }
  
  // 如果都小于目标宽度，返回最大档位
  return sizes[sizes.length - 1]
}

/**
 * 生成响应式图片URL
 * @param {string} baseUrl - 原始图片URL
 * @param {number} width - 目标宽度（rpx）
 * @returns {string} 处理后的图片URL
 */
export function getResponsiveImageUrl(baseUrl, width = 480) {
  if (!baseUrl) return baseUrl

  // 将rpx转换为px（小程序中1rpx约等于0.5px）
  const pxWidth = Math.round(width * 0.5)
  const optimalWidth = getOptimalSize(pxWidth)

  // 如果URL已经包含OSS处理参数，先移除
  const cleanUrl = baseUrl.split('?')[0]

  // 添加OSS图片处理参数
  return `${cleanUrl}?x-oss-process=image/resize,w_${optimalWidth}`
}

/**
 * 生成缩略图URL
 * @param {string} baseUrl - 原始图片URL
 * @param {number} width - 缩略图宽度
 * @param {number} height - 缩略图高度
 * @returns {string} 缩略图URL
 */
export function getThumbnailUrl(baseUrl, width = 240, height = 180) {
  if (!baseUrl) return baseUrl

  const cleanUrl = baseUrl.split('?')[0]
  return `${cleanUrl}?x-oss-process=image/resize,m_fill,w_${width},h_${height}`
}

/**
 * 生成头像URL
 * @param {string} baseUrl - 原始图片URL
 * @param {number} size - 头像尺寸
 * @returns {string} 头像URL
 */
export function getAvatarUrl(baseUrl, size = 120) {
  if (!baseUrl) return baseUrl

  const cleanUrl = baseUrl.split('?')[0]
  return `${cleanUrl}?x-oss-process=image/resize,m_fill,w_${size},h_${size}/circle,r_${size/2}`
}

/**
 * 获取图片信息
 * @param {string} imageUrl - 图片URL
 * @returns {Promise<Object>} 图片信息
 */
export function getImageInfo(imageUrl) {
  return new Promise((resolve, reject) => {
    uni.getImageInfo({
      src: imageUrl,
      success: (res) => {
        resolve({
          width: res.width,
          height: res.height,
          path: res.path,
          orientation: res.orientation,
          type: res.type
        })
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

/**
 * 预加载图片
 * @param {string|Array} imageUrls - 图片URL或URL数组
 * @returns {Promise} 预加载结果
 */
export function preloadImages(imageUrls) {
  const urls = Array.isArray(imageUrls) ? imageUrls : [imageUrls]
  
  const promises = urls.map(url => {
    return new Promise((resolve) => {
      uni.getImageInfo({
        src: url,
        success: () => resolve(true),
        fail: () => resolve(false)
      })
    })
  })
  
  return Promise.all(promises)
}

/**
 * 检查图片是否有效
 * @param {string} imageUrl - 图片URL
 * @returns {Promise<boolean>} 是否有效
 */
export async function isImageValid(imageUrl) {
  if (!imageUrl) return false
  
  try {
    await getImageInfo(imageUrl)
    return true
  } catch (error) {
    return false
  }
}

export default {
  getResponsiveImageUrl,
  getThumbnailUrl,
  getAvatarUrl,
  getImageInfo,
  preloadImages,
  isImageValid
}
