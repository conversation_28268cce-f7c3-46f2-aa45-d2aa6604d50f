/**
 * 网络状态检测工具
 * 提供网络连接状态监控和处理
 */

// 网络状态
let networkStatus = {
  isConnected: true,
  networkType: 'unknown'
}

// 网络状态监听器
const networkListeners = new Set()

/**
 * 初始化网络状态监控
 */
export function initNetworkMonitor() {
  // 获取初始网络状态
  uni.getNetworkType({
    success: (res) => {
      updateNetworkStatus(res.networkType !== 'none', res.networkType)
    },
    fail: () => {
      updateNetworkStatus(false, 'unknown')
    }
  })

  // 监听网络状态变化
  uni.onNetworkStatusChange((res) => {
    updateNetworkStatus(res.isConnected, res.networkType)
  })
}

/**
 * 更新网络状态
 * @param {boolean} isConnected - 是否连接
 * @param {string} networkType - 网络类型
 */
function updateNetworkStatus(isConnected, networkType) {
  const oldStatus = { ...networkStatus }
  networkStatus.isConnected = isConnected
  networkStatus.networkType = networkType

  // 通知监听器
  networkListeners.forEach(listener => {
    try {
      listener(networkStatus, oldStatus)
    } catch (error) {
      console.error('网络状态监听器执行失败:', error)
    }
  })

  console.log('网络状态更新:', networkStatus)
}

/**
 * 获取当前网络状态
 * @returns {Object} 网络状态对象
 */
export function getNetworkStatus() {
  return { ...networkStatus }
}

/**
 * 检查网络是否连接
 * @returns {boolean} 是否连接
 */
export function isNetworkConnected() {
  return networkStatus.isConnected
}

/**
 * 获取网络类型
 * @returns {string} 网络类型
 */
export function getNetworkType() {
  return networkStatus.networkType
}

/**
 * 添加网络状态监听器
 * @param {Function} listener - 监听器函数
 */
export function addNetworkListener(listener) {
  if (typeof listener === 'function') {
    networkListeners.add(listener)
  }
}

/**
 * 移除网络状态监听器
 * @param {Function} listener - 监听器函数
 */
export function removeNetworkListener(listener) {
  networkListeners.delete(listener)
}

/**
 * 检查网络连接状态（异步）
 * @returns {Promise<Object>} 网络状态
 */
export function checkNetworkStatus() {
  return new Promise((resolve) => {
    uni.getNetworkType({
      success: (res) => {
        const status = {
          isConnected: res.networkType !== 'none',
          networkType: res.networkType
        }
        updateNetworkStatus(status.isConnected, status.networkType)
        resolve(status)
      },
      fail: () => {
        const status = {
          isConnected: false,
          networkType: 'unknown'
        }
        updateNetworkStatus(false, 'unknown')
        resolve(status)
      }
    })
  })
}

/**
 * 网络重连检测
 * @param {number} maxRetries - 最大重试次数
 * @param {number} retryDelay - 重试延迟（毫秒）
 * @returns {Promise<boolean>} 是否重连成功
 */
export async function waitForNetworkReconnect(maxRetries = 5, retryDelay = 2000) {
  for (let i = 0; i < maxRetries; i++) {
    const status = await checkNetworkStatus()
    if (status.isConnected) {
      return true
    }
    
    if (i < maxRetries - 1) {
      await new Promise(resolve => setTimeout(resolve, retryDelay))
    }
  }
  
  return false
}

/**
 * 显示网络状态提示
 * @param {Object} status - 网络状态
 */
export function showNetworkStatusToast(status = networkStatus) {
  if (!status.isConnected) {
    uni.showToast({
      title: '网络连接已断开',
      icon: 'none',
      duration: 2000
    })
  } else {
    const networkTypeMap = {
      'wifi': 'WiFi',
      '2g': '2G',
      '3g': '3G',
      '4g': '4G',
      '5g': '5G',
      'unknown': '未知网络'
    }
    
    uni.showToast({
      title: '网络已连接',
      icon: 'success',
      duration: 1500
    })
  }
}

/**
 * 网络状态装饰器
 * 在网络断开时自动处理请求
 */
export function withNetworkCheck(asyncFn) {
  return async function(...args) {
    if (!isNetworkConnected()) {
      throw new Error('网络连接已断开，请检查网络设置')
    }
    
    try {
      return await asyncFn.apply(this, args)
    } catch (error) {
      // 如果是网络错误，检查网络状态
      if (isNetworkError(error)) {
        await checkNetworkStatus()
        if (!isNetworkConnected()) {
          throw new Error('网络连接已断开，请检查网络设置')
        }
      }
      throw error
    }
  }
}

/**
 * 判断是否为网络错误
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否为网络错误
 */
function isNetworkError(error) {
  if (!error) return false
  
  const networkErrorMessages = [
    'network error',
    'timeout',
    'connection failed',
    'no internet',
    'offline'
  ]
  
  const errorMessage = (error.message || '').toLowerCase()
  return networkErrorMessages.some(msg => errorMessage.includes(msg))
}

// 自动初始化网络监控
if (typeof uni !== 'undefined') {
  initNetworkMonitor()
}
