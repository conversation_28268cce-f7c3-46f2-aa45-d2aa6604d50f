/**
 * Ruby标签生成工具 - 基于echo-lab项目适配小程序
 * 用于生成日语注音等标注内容
 */

/**
 * 判断字符是否需要显示标注
 * @param {Object} char - 字符对象
 * @returns {boolean} 是否需要标注
 */
function needsAnnotation(char) {
  // 如果没有reading字段或reading为空，不显示标注
  if (!char.reading || char.reading.trim() === '') {
    return false
  }

  // 如果reading和原字符相同，不显示标注
  if (char.reading === char.char) {
    return false
  }

  return true
}

/**
 * 生成带有Ruby标签的HTML内容
 * @param {string} content - 原始文本内容
 * @param {Object} annotation - 标注对象，包含reading和characters字段
 * @returns {string} - 带有Ruby标签的HTML内容
 */
export function generateRubyHTML(content, annotation) {
  // 如果没有内容或标注，直接返回加粗的原始内容
  if (!content || !annotation) {
    return `<b>${content}</b>`
  }

  // 如果没有字符级标注，直接返回加粗的原始内容
  if (!annotation.characters || annotation.characters.length === 0) {
    return `<b>${content}</b>`
  }

  // 使用字符级标注生成Ruby标签
  const characters = annotation.characters
  let html = "<b>" // 整段话开始加粗

  for (let i = 0; i < characters.length; i++) {
    const char = characters[i]

    // 有标注信息就显示ruby标签，没有就显示原字符
    if (needsAnnotation(char)) {
      html += `<ruby>${char.char}<rt>${char.reading}</rt></ruby>`
    } else {
      html += char.char
    }
  }

  html += "</b>" // 整段话结束加粗
  return html
}

/**
 * 解析Ruby HTML内容，提取纯文本
 * @param {string} rubyHTML - 包含Ruby标签的HTML
 * @returns {string} 纯文本内容
 */
export function extractTextFromRuby(rubyHTML) {
  if (!rubyHTML) return ''
  
  // 移除所有HTML标签，只保留文本内容
  return rubyHTML
    .replace(/<ruby[^>]*>/gi, '')
    .replace(/<\/ruby>/gi, '')
    .replace(/<rt[^>]*>.*?<\/rt>/gi, '')
    .replace(/<[^>]*>/g, '')
    .trim()
}

/**
 * 检查内容是否包含Ruby标签
 * @param {string} content - 内容字符串
 * @returns {boolean} 是否包含Ruby标签
 */
export function hasRubyTags(content) {
  if (!content) return false
  return /<ruby[^>]*>.*?<\/ruby>/i.test(content)
}

/**
 * 为小程序rich-text组件格式化Ruby内容
 * @param {string} content - 原始文本内容
 * @param {Object} annotation - 标注对象
 * @returns {Array} rich-text组件可用的nodes数组
 */
export function formatRubyForRichText(content, annotation) {
  // 如果没有内容或标注，返回简单的文本节点
  if (!content || !annotation || !annotation.characters) {
    return [{
      name: 'span',
      attrs: { style: 'font-weight: bold;' },
      children: [{ type: 'text', text: content || '' }]
    }]
  }

  const characters = annotation.characters
  const nodes = []
  
  // 开始加粗容器
  const boldContainer = {
    name: 'span',
    attrs: { style: 'font-weight: bold;' },
    children: []
  }

  for (let i = 0; i < characters.length; i++) {
    const char = characters[i]

    if (needsAnnotation(char)) {
      // 创建ruby节点
      boldContainer.children.push({
        name: 'ruby',
        children: [
          { type: 'text', text: char.char },
          {
            name: 'rt',
            attrs: { style: 'font-size: 0.6em; opacity: 0.9;' },
            children: [{ type: 'text', text: char.reading }]
          }
        ]
      })
    } else {
      // 普通文本节点
      boldContainer.children.push({
        type: 'text',
        text: char.char
      })
    }
  }

  nodes.push(boldContainer)
  return nodes
}

/**
 * 简化版Ruby生成，适用于小程序环境
 * @param {string} content - 原始文本
 * @param {Object} annotation - 标注信息
 * @returns {string} 简化的标注文本
 */
export function generateSimpleAnnotation(content, annotation) {
  if (!content || !annotation || !annotation.characters) {
    return content || ''
  }

  // 对于小程序，可以生成简化的标注格式
  // 例如：漢字(かんじ) 的格式
  const characters = annotation.characters
  let result = ''
  let currentGroup = ''
  let currentReading = ''

  for (let i = 0; i < characters.length; i++) {
    const char = characters[i]

    if (needsAnnotation(char)) {
      if (currentGroup && currentReading) {
        // 输出之前的组
        result += `${currentGroup}(${currentReading})`
        currentGroup = ''
        currentReading = ''
      }
      currentGroup += char.char
      currentReading += char.reading
    } else {
      if (currentGroup && currentReading) {
        // 输出之前的组
        result += `${currentGroup}(${currentReading})`
        currentGroup = ''
        currentReading = ''
      }
      result += char.char
    }
  }

  // 处理最后一组
  if (currentGroup && currentReading) {
    result += `${currentGroup}(${currentReading})`
  }

  return result
}
