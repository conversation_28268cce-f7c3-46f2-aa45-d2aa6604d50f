/**
 * 全局分享配置管理
 * 统一管理小程序的分享配置，避免重复代码
 */

// 默认分享配置
const DEFAULT_SHARE_CONFIG = {
  // 分享给好友的默认配置
  appMessage: {
    title: '磨耳朵听力 - 轻松练听力，随时随地学外语',
    path: '/pages/index/index',
    imageUrl: 'https://f6zx3qc.oss-cn-beijing.aliyuncs.com/pengyouquan.png'
  },

  // 分享到朋友圈的默认配置
  timeline: {
    title: '磨耳朵听力 - 不费脑的外语精听练习',
    query: 'from=timeline',
    imageUrl: 'https://f6zx3qc.oss-cn-beijing.aliyuncs.com/pengyouquan.png'
  }
}

// 页面特定的分享配置
const PAGE_SHARE_CONFIGS = {
  // 首页
  'pages/index/index': {
    appMessage: {
      title: '磨耳朵听力 - 轻松练听力，随时随地学外语',
      path: '/pages/index/index'
    }
  },

  // 个人页面
  'pages/profile/profile': {
    appMessage: {
      title: '磨耳朵听力 - 不费脑的外语精听练习',
      path: '/pages/index/index'
    }
  },

  // 收藏页面
  'pages/favorites/favorites': {
    appMessage: {
      title: '磨耳朵听力 - 我的收藏内容',
      path: '/pages/index/index'
    }
  },

  // 播放页面
  'pages/player/player': {
    appMessage: {
      title: '磨耳朵听力 - 不费脑的外语精听练习',
      path: '/pages/index/index'
    }
  },

  // 设置页面
  'pages/settings/settings': {
    appMessage: {
      title: '磨耳朵听力 - 轻松练听力，随时随地学外语',
      path: '/pages/index/index'
    }
  },

  // 关于页面
  'pages/about/about': {
    appMessage: {
      title: '磨耳朵听力 - 轻松练听力，随时随地学外语',
      path: '/pages/index/index'
    }
  }
}

/**
 * 获取当前页面的分享配置
 * @param {string} pagePath - 页面路径，如 'pages/index/index'
 * @returns {Object} 分享配置对象
 */
export function getShareConfig(pagePath) {
  const pageConfig = PAGE_SHARE_CONFIGS[pagePath] || {}
  
  return {
    // 分享给好友配置
    appMessage: {
      ...DEFAULT_SHARE_CONFIG.appMessage,
      ...pageConfig.appMessage
    },
    
    // 分享到朋友圈配置
    timeline: {
      ...DEFAULT_SHARE_CONFIG.timeline,
      ...pageConfig.timeline
    }
  }
}

/**
 * 获取当前页面路径
 * @returns {string} 当前页面路径
 */
export function getCurrentPagePath() {
  try {
    // 使用 uni-app 的 API
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    return currentPage ? currentPage.route : ''
  } catch (error) {
    console.warn('获取当前页面路径失败:', error)
    return ''
  }
}

/**
 * 创建标准的分享配置对象
 * 可以直接在页面的 export default 中使用
 * @param {string} pagePath - 页面路径（可选，不传则自动获取）
 * @returns {Object} 包含 onShareAppMessage 和 onShareTimeline 的对象
 */
export function createShareConfig(pagePath) {
  return {
    onShareAppMessage() {
      const currentPath = pagePath || getCurrentPagePath()
      const config = getShareConfig(currentPath)
      return config.appMessage
    },

    onShareTimeline() {
      const currentPath = pagePath || getCurrentPagePath()
      const config = getShareConfig(currentPath)
      return config.timeline
    }
  }
}

/**
 * 更新全局分享图片
 * @param {string} imageUrl - 分享图片URL
 */
export function updateShareImage(imageUrl) {
  DEFAULT_SHARE_CONFIG.appMessage.imageUrl = imageUrl
  DEFAULT_SHARE_CONFIG.timeline.imageUrl = imageUrl
}

export default {
  getShareConfig,
  getCurrentPagePath,
  createShareConfig,
  updateShareImage
}
