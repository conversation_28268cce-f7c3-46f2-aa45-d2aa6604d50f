/**
 * 时间线生成工具
 * 根据内容配置和播放设置生成时间线
 */

import { getCachedSilenceUrl } from "./silenceGenerator.js";

/**
 * 从节点配置中提取序列数据
 */
export function extractSequencesFromNodes(configJson) {
  if (!configJson || !configJson.nodes) {
    return [];
  }

  const sequences = [];
  const nodes = configJson.nodes;
  const resources = configJson.resources || {};

  // 查找所有文本序列节点和文本内容节点
  Object.values(nodes).forEach((node) => {
    // 处理文本序列节点
    if (node.type === "textSequence" || node.type === "videoConfig") {
      try {
        // 检查节点是否有预处理的序列
        if (node.params?.sequence?.length > 0) {
          // 直接使用节点中的预处理序列
          const items = node.params.sequence.map((item) => ({
            id: item.id,
            content: item.content,
            language: item.language || "ja",
            type: item.type || "normal",
            audioUrl: item.audioUrl,
            duration: item.duration || 2,
          }));

          if (items.length > 0) {
            sequences.push({
              id: node.id,
              name: node.customName || `序列 ${sequences.length + 1}`,
              items: items,
            });
            return;
          }
        }

        // 如果节点没有预处理的序列，则从源节点获取内容
        const sourceNodes = node.sourceIds
          ? node.sourceIds.map((id) => nodes[id]).filter(Boolean)
          : [];

        if (sourceNodes.length > 0) {
          const items = [];
          sourceNodes.forEach((sourceNode) => {
            if (sourceNode.params?.segments?.length > 0) {
              sourceNode.params.segments.forEach((segment) => {
                let audioUrl = null;
                if (resources.audioItems?.length > 0) {
                  const audioItem = resources.audioItems.find(
                    (item) => item.text === segment.content
                  );
                  if (audioItem) {
                    audioUrl = audioItem.url || audioItem.audioUrl;
                  }
                }

                const audioDuration = findAudioDuration(configJson, segment);

                items.push({
                  id: segment.id,
                  content: segment.content,
                  language: segment.language || "ja",
                  type: segment.type || "normal",
                  difficulty: segment.difficulty || "normal", // 添加difficulty字段
                  audioUrl: audioUrl,
                  duration: audioDuration || 2,
                });
              });
            } else if (sourceNode.params?.text) {
              const text = sourceNode.params.text;
              const segmentId = `seg_${Math.random().toString(36).substring(2, 15)}`;

              let audioUrl = null;
              if (resources.audioItems?.length > 0) {
                const audioItem = resources.audioItems.find(
                  (item) => item.text === text
                );
                if (audioItem) {
                  audioUrl = audioItem.url || audioItem.audioUrl;
                }
              }

              const tempSegment = { id: segmentId, content: text };
              const audioDuration = findAudioDuration(configJson, tempSegment);

              items.push({
                id: segmentId,
                content: text,
                language: sourceNode.params.language || "ja",
                type: sourceNode.params.type || "normal",
                difficulty: sourceNode.params.difficulty || "normal", // 添加difficulty字段
                audioUrl: audioUrl,
                duration: audioDuration || 2,
              });
            }
          });

          if (items.length > 0) {
            let sequenceName = node.customName;
            if (!sequenceName) {
              sequenceName = `序列 ${sequences.length + 1}`;
            }

            sequences.push({
              id: node.id,
              name: sequenceName,
              items: items,
            });
          }
        }
      } catch (error) {
        // 处理序列节点失败
      }
    }
    // 处理文本内容节点
    else if (node.type === "textContent") {
      try {
        const items = [];
        
        if (node.params?.segments?.length > 0) {
          node.params.segments.forEach((segment) => {
            let audioUrl = null;
            if (resources.audioItems?.length > 0) {
              const audioItem = resources.audioItems.find(
                (item) => item.text === segment.content
              );
              if (audioItem) {
                audioUrl = audioItem.url || audioItem.audioUrl;
              }
            }

            const audioDuration = findAudioDuration(configJson, segment);

            items.push({
              id: segment.id,
              content: segment.content,
              language: segment.language || "ja",
              type: segment.type || "normal",
              difficulty: segment.difficulty || "normal", // 添加difficulty字段
              audioUrl: audioUrl,
              duration: audioDuration || 2,
            });
          });
        } else if (node.params?.text) {
          const text = node.params.text;
          const segmentId = `seg_${Math.random().toString(36).substring(2, 15)}`;

          let audioUrl = null;
          if (resources.audioItems?.length > 0) {
            const audioItem = resources.audioItems.find(
              (item) => item.text === text
            );
            if (audioItem) {
              audioUrl = audioItem.url || audioItem.audioUrl;
            }
          }

          const tempSegment = { id: segmentId, content: text };
          const audioDuration = findAudioDuration(configJson, tempSegment);

          items.push({
            id: segmentId,
            content: text,
            language: node.params.language || "ja",
            type: node.params.type || "normal",
            difficulty: node.params.difficulty || "normal", // 添加difficulty字段
            audioUrl: audioUrl,
            duration: audioDuration || 2,
          });
        }

        if (items.length > 0) {
          sequences.push({
            id: node.id,
            name: node.customName || `文本内容 ${sequences.length + 1}`,
            items: items,
          });
        }
      } catch (error) {
        // 处理文本内容节点失败
      }
    }
  });

  return sequences;
}

/**
 * 获取视频配置信息
 */
function getVideoConfig(configJson) {
  if (!configJson?.nodes) return null;

  const videoConfigNode = Object.values(configJson.nodes).find(
    (node) => node.type === "videoConfig"
  );

  return videoConfigNode?.params || null;
}

/**
 * 生成播放时间线
 */
export function generateTimeline(configJson, settings) {
  if (!configJson || !settings?.sections) {
    return [];
  }

  const sequences = extractSequencesFromNodes(configJson);
  if (sequences.length === 0) {
    return [];
  }

  const timeline = [];
  let currentTime = 0;

  // 获取视频配置信息
  const videoConfig = getVideoConfig(configJson);

  // 如果有封面图片，添加到时间线的开头
  if (videoConfig?.cover?.imageUrl) {
    const coverDuration = Math.max(0.1, videoConfig.cover.duration || 3);

    timeline.push({
      id: "cover",
      type: "audio",
      content: "",
      audioUrl: getCachedSilenceUrl(coverDuration),
      imageUrl: videoConfig.cover.imageUrl,
      startTime: 0,
      duration: coverDuration,
      speed: 1.0, // 保持1.0倍速
      contentType: "image",
      isCover: true,
    });

    currentTime += coverDuration;
  }

  const configJsonRef = configJson;

  // 遍历所有环节
  settings.sections.forEach((section) => {
    const processingMode = section.processingMode || "sequence";

    const sourceItems = getSourceItems(
      sequences,
      section,
      processingMode,
      configJsonRef
    );

    if (!sourceItems?.length) {
      return;
    }

    currentTime = processSequenceSection(
      timeline,
      sourceItems,
      currentTime,
      section,
      configJsonRef
    );
  });

  calculateDisplayDurations(timeline);
  return timeline;
}

/**
 * 获取源内容项
 */
function getSourceItems(sequences, section, processingMode, configJson) {
  if (processingMode === "sequence") {
    // 基于序列模式 - 首先查找文本序列节点的自定义序列
    if (configJson?.nodes) {
      for (const nodeId in configJson.nodes) {
        const node = configJson.nodes[nodeId];
        if (node.params?.sequence?.length > 0) {
          return node.params.sequence.map((item) => ({
            id: item.id,
            content: item.content,
            language: item.language || "ja",
            type: item.type || "normal",
            difficulty: item.difficulty || "normal",
            audioUrl: item.audioUrl,
            duration: item.duration || 2,
          }));
        }
      }
    }

    // 如果没有找到自定义序列，使用合并序列
    if (sequences.length > 0) {
      const mergedSequence = sequences[sequences.length - 1];
      if (mergedSequence?.items?.length > 0) {
        return mergedSequence.items;
      }
    }

    return [];
  } else if (processingMode === "source") {
    // 基于源节点模式
    let sourceNodeIds = [];

    if (section.sourceNodeIds?.length > 0) {
      sourceNodeIds = section.sourceNodeIds;
    } else if (section.sourceNodeId) {
      sourceNodeIds = [section.sourceNodeId];
    }

    if (sourceNodeIds.length === 0) {
      return [];
    }

    let allSourceItems = [];

    for (const sourceNodeId of sourceNodeIds) {
      const sourceSequence = sequences.find((seq) => seq.id === sourceNodeId);

      if (sourceSequence?.items?.length > 0) {
        allSourceItems = allSourceItems.concat(sourceSequence.items);
        continue;
      }
    }

    return allSourceItems;
  }

  return [];
}

/**
 * 查找音频URL
 */
function findAudioUrl(configJson, segment) {
  if (!configJson?.resources?.audioItems) return null;

  const audioItems = configJson.resources.audioItems;
  const segmentId = segment.id;
  const text = segment.content;

  // 优先通过ID匹配
  if (segmentId) {
    const idMatch = audioItems.find(
      (item) => item.id === segmentId || item.sequenceId === segmentId
    );
    if (idMatch?.url || idMatch?.audioUrl) {
      return idMatch.url || idMatch.audioUrl;
    }
  }

  // 文本匹配
  const audioItem = audioItems.find((item) => item.text === text);
  if (audioItem?.url || audioItem?.audioUrl) {
    return audioItem.url || audioItem.audioUrl;
  }

  return null;
}

/**
 * 查找音频时长
 */
function findAudioDuration(configJson, segment) {
  if (!configJson?.resources?.audioItems) return null;

  const audioItems = configJson.resources.audioItems;
  const segmentId = segment.id;
  const text = segment.content;

  // 优先通过ID匹配
  if (segmentId) {
    const idMatch = audioItems.find(
      (item) => item.id === segmentId || item.sequenceId === segmentId
    );
    if (idMatch?.duration) {
      return parseFloat(idMatch.duration);
    }
  }

  // 文本匹配
  const audioItem = audioItems.find((item) => item.text === text);
  if (audioItem?.duration) {
    return parseFloat(audioItem.duration);
  }

  return null;
}

/**
 * 处理序列环节
 */
function processSequenceSection(timeline, sourceItems, startTime, section, configJson) {
  let currentTime = startTime;

  const repeatCount = section.repeatCount || 1;
  const defaultPauseDuration = section.pauseDuration || 2500;

  // 翻译相关配置
  const enableTranslation = section.enableTranslation === true;
  const translationLanguage = section.translationLanguage || "";
  const translationPosition = section.translationPosition || 2;
  const validTranslationPosition = Math.min(Math.max(1, translationPosition), repeatCount);

  // 关键词相关配置
  const insertKeywords = section.enableKeywords === true;
  const keywordRepeatCount = section.keywordRepeatCount || 2;
  const keywordPosition = section.keywordPosition || 2;
  const validKeywordPosition = Math.min(Math.max(1, keywordPosition), repeatCount);

  // 处理每个内容项（按H5版本的逻辑：每个内容项单独重复）
  sourceItems.forEach((item) => {
    // 检查文本类型
    const isTransitionText = item.type === "transition";

    // 如果是转场文本，只播放一次，否则重复指定次数
    // 如果是简单句子，强制只播放一次
    let actualRepeatCount;
    if (isTransitionText) {
      actualRepeatCount = 1;
    } else if (item.difficulty === "simple") {
      actualRepeatCount = 1;
    } else {
      actualRepeatCount = repeatCount;
    }

    // 移除倍速处理逻辑

    // 处理重复停顿
    let repeatPauses = [];
    if (section.repeatPauses?.length > 0) {
      repeatPauses = section.repeatPauses.slice(0, actualRepeatCount);
      while (repeatPauses.length < actualRepeatCount) {
        repeatPauses.push(defaultPauseDuration);
      }
    } else {
      repeatPauses = Array(actualRepeatCount).fill(defaultPauseDuration);
    }

    // 重复播放当前内容项
    for (let i = 0; i < actualRepeatCount; i++) {
      const currentPauseDuration = repeatPauses[i];

      // 获取标注信息（如果有）
      let annotation = null;
      if (configJson?.resources?.annotations) {
        annotation = configJson.resources.annotations[item.id];
      }

      // 添加主要内容项
      timeline.push({
        id: `${item.id}_${i}`,
        originalId: item.id,
        type: "audio",
        content: item.content,
        audioUrl: item.audioUrl, // 直接使用原始音频URL
        startTime: currentTime,
        duration: item.duration, // 使用原始时长
        speed: 1.0, // 固定为1.0倍速
        sectionId: section.id,
        sectionName: section.name || section.title,
        repeatIndex: i,
        isRepeat: i > 0,
        processingMode: "sequence",
        language: item.language,
        annotation: annotation, // 添加标注信息
        isTransitionText: isTransitionText,
        isKeyword: false,
        isPause: false,
        displayInPlaylist: true,
      });

      currentTime += item.duration; // 使用原始时长

      // 添加停顿（除了最后一次重复）
      if (i < actualRepeatCount - 1 && currentPauseDuration > 0) {
        const pauseDurationSeconds = currentPauseDuration / 1000;
        const silenceUrl = getCachedSilenceUrl(pauseDurationSeconds);

        if (silenceUrl) {
          timeline.push({
            id: `${item.id}_pause_${i}`,
            originalId: item.id,
            type: "audio",
            content: "",
            audioUrl: silenceUrl,
            startTime: currentTime,
            duration: pauseDurationSeconds,
            speed: 1.0,
            sectionId: section.id,
            sectionName: section.name || section.title,
            repeatIndex: i,
            isRepeat: true,
            processingMode: "sequence",
            language: item.language,
            annotation: annotation, // 添加标注信息
            isTransitionText: isTransitionText,
            isKeyword: false,
            isPause: true,
            displayInPlaylist: false,
          });

          currentTime += pauseDurationSeconds;
        }
      }

      // 处理关键词插入
      if (!isTransitionText && i === validKeywordPosition - 1 && insertKeywords &&
          (item.type === "normal" || item.type === undefined)) {
        currentTime = addKeywords(timeline, configJson, item, currentTime, section,
                                 keywordRepeatCount, repeatPauses,
                                 validKeywordPosition, defaultPauseDuration);
      }

      // 处理翻译插入
      if (!isTransitionText && i === validTranslationPosition - 1 &&
          enableTranslation && translationLanguage) {
        currentTime = addTranslation(timeline, configJson, item, currentTime, section,
                                   translationLanguage, validTranslationPosition,
                                   repeatPauses, defaultPauseDuration);
      }
    }
  });

  return currentTime;
}

/**
 * 计算显示时长
 */
function calculateDisplayDurations(timeline) {
  // 为所有项目设置默认的displayDuration
  timeline.forEach((item) => {
    if (!item.displayDuration) {
      item.displayDuration = item.duration;
    }
  });

  // 只处理有内容的音频项目
  const audioItems = timeline.filter(
    (item) => item.type === "audio" && item.content
  );

  if (audioItems.length === 0) {
    return;
  }

  audioItems.forEach((currentItem, index) => {
    const nextItem = audioItems[index + 1];

    if (nextItem) {
      const gap = nextItem.startTime - (currentItem.startTime + currentItem.duration);

      if (gap > 0) {
        currentItem.displayDuration = currentItem.duration + gap;
      }
    }
  });
}

/**
 * 添加关键词到时间线
 */
function addKeywords(timeline, configJson, item, currentTime, section,
                    keywordRepeatCount, repeatPauses,
                    validKeywordPosition, defaultPauseDuration) {
  // 查找原始分句，获取关键词列表
  const originalSegment = findSegmentById(configJson, item.id);

  if (originalSegment?.keywords?.length > 0) {
    // 处理每个关键词
    for (const keyword of originalSegment.keywords) {
      const keywordText = keyword.text;
      const keywordId = keyword.id;
      const keywordLanguage = keyword.language || item.language;

      // 查找关键词音频
      const keywordAudioUrl = findKeywordAudioUrl(configJson, keywordId, keywordLanguage);
      const keywordAudioDuration = findKeywordAudioDuration(configJson, keywordId, keywordLanguage);

      // 获取关键词的标注信息
      let keywordAnnotation = null;
      if (configJson?.resources?.annotations) {
        keywordAnnotation = configJson.resources.annotations[keywordId];
      }

      // 重复播放关键词
      for (let k = 0; k < keywordRepeatCount; k++) {
        // 添加关键词音频项目到时间线
        timeline.push({
          id: `${item.id}_keyword_${keywordId}_${k}`,
          originalId: item.id,
          type: "audio",
          keywordId: keywordId,
          content: keywordText,
          audioUrl: keywordAudioUrl, // 直接使用原始音频URL
          startTime: currentTime,
          duration: keywordAudioDuration || 1, // 使用原始时长
          speed: 1.0, // 固定为1.0倍速
          sectionId: section.id,
          sectionName: section.name || section.title,
          repeatIndex: validKeywordPosition - 1,
          isKeyword: true,
          keywordIndex: k,
          language: keywordLanguage,
          processingMode: "sequence",
          annotation: keywordAnnotation,
          displayInPlaylist: true,
        });

        currentTime += keywordAudioDuration || 1; // 使用原始时长

        // 添加关键词停顿
        const keywordPauseDuration = repeatPauses[validKeywordPosition - 1] || defaultPauseDuration;
        const actualKeywordPause = keywordPauseDuration / 2; // 关键词停顿为正常停顿的一半

        if (actualKeywordPause > 0) {
          const pauseDurationSeconds = actualKeywordPause / 1000;
          const silenceUrl = getCachedSilenceUrl(pauseDurationSeconds);

          if (silenceUrl) {
            timeline.push({
              id: `${item.id}_keyword_${keywordId}_${k}_pause`,
              originalId: item.id,
              type: "audio",
              keywordId: keywordId,
              content: "",
              audioUrl: silenceUrl,
              startTime: currentTime,
              duration: pauseDurationSeconds,
              speed: 1.0,
              sectionId: section.id,
              sectionName: section.name || section.title,
              repeatIndex: validKeywordPosition - 1,
              isKeyword: true,
              keywordIndex: k,
              language: keywordLanguage,
              processingMode: "sequence",
              annotation: keywordAnnotation,
              isPause: true,
              displayInPlaylist: false,
            });

            currentTime += pauseDurationSeconds;
          }
        }
      }
    }
  }

  return currentTime;
}

/**
 * 添加翻译到时间线
 */
function addTranslation(timeline, configJson, item, currentTime, section,
                       translationLanguage, validTranslationPosition,
                       repeatPauses, defaultPauseDuration) {
  // 查找翻译内容
  let translationText = null;

  if (configJson.resources?.translations?.[translationLanguage]) {
    const translations = configJson.resources.translations[translationLanguage];
    if (translations[item.id]) {
      translationText = translations[item.id];
    }
  }

  if (translationText) {
    // 查找翻译音频
    const translationAudioUrl = findTranslationAudioUrl(configJson, item.id, translationLanguage);
    const translationAudioDuration = findTranslationAudioDuration(configJson, item.id, translationLanguage);

    // 获取翻译的标注信息
    let translationAnnotation = null;
    if (configJson?.resources?.annotations) {
      translationAnnotation = configJson.resources.annotations[`${item.id}_${translationLanguage}`];
    }

    const translationPauseDuration = repeatPauses[validTranslationPosition - 1] || defaultPauseDuration;

    // 添加翻译音频项目到时间线
    timeline.push({
      id: `${item.id}_translation_${validTranslationPosition}`,
      originalId: item.id,
      type: "audio",
      content: translationText,
      audioUrl: translationAudioUrl, // 直接使用原始音频URL
      startTime: currentTime,
      duration: translationAudioDuration || 2, // 使用原始时长
      speed: 1.0, // 固定为1.0倍速
      sectionId: section.id,
      sectionName: section.name || section.title,
      repeatIndex: validTranslationPosition - 1,
      isTranslation: true,
      language: translationLanguage,
      processingMode: "sequence",
      annotation: translationAnnotation,
      displayInPlaylist: true,
    });

    currentTime += translationAudioDuration || 2; // 使用原始时长

    // 添加翻译停顿
    if (translationPauseDuration > 0) {
      const pauseDurationSeconds = translationPauseDuration / 1000;
      const silenceUrl = getCachedSilenceUrl(pauseDurationSeconds);

      if (silenceUrl) {
        timeline.push({
          id: `${item.id}_translation_${validTranslationPosition}_pause`,
          originalId: item.id,
          type: "audio",
          content: "",
          audioUrl: silenceUrl,
          startTime: currentTime,
          duration: pauseDurationSeconds,
          speed: 1.0,
          sectionId: section.id,
          sectionName: section.name || section.title,
          repeatIndex: validTranslationPosition - 1,
          isTranslation: true,
          language: translationLanguage,
          processingMode: "sequence",
          annotation: translationAnnotation,
          isPause: true,
          displayInPlaylist: false,
        });

        currentTime += pauseDurationSeconds;
      }
    }
  }

  return currentTime;
}

/**
 * 根据ID查找分句
 */
function findSegmentById(configJson, segmentId) {
  if (!configJson?.nodes) return null;

  for (const node of Object.values(configJson.nodes)) {
    if (node.params?.segments?.length > 0) {
      const segment = node.params.segments.find(seg => seg.id === segmentId);
      if (segment) return segment;
    }
  }
  return null;
}

/**
 * 查找关键词音频URL
 */
function findKeywordAudioUrl(configJson, keywordId, language) {
  if (!configJson?.resources?.audioItems) return null;

  const audioItems = configJson.resources.audioItems;
  const idMatch = audioItems.find(item =>
    item.id === keywordId ||
    (item.keywordId === keywordId && item.language === language)
  );

  return idMatch ? (idMatch.url || idMatch.audioUrl) : null;
}

/**
 * 查找关键词音频时长
 */
function findKeywordAudioDuration(configJson, keywordId, language) {
  if (!configJson?.resources?.audioItems) return null;

  const audioItems = configJson.resources.audioItems;
  const idMatch = audioItems.find(item =>
    item.id === keywordId ||
    (item.keywordId === keywordId && item.language === language)
  );

  return idMatch?.duration ? parseFloat(idMatch.duration) : null;
}

/**
 * 查找翻译音频URL
 */
function findTranslationAudioUrl(configJson, sourceId, language) {
  if (!configJson?.resources?.audioItems) return null;

  const audioItems = configJson.resources.audioItems;
  const translationId = `${sourceId}_${language}`;
  const idMatch = audioItems.find(item =>
    item.id === translationId ||
    (item.sourceId === sourceId && item.targetLanguage === language) ||
    (item.audioType === "translation" && item.sourceId === sourceId && item.language === language)
  );

  return idMatch ? (idMatch.url || idMatch.audioUrl) : null;
}

/**
 * 查找翻译音频时长
 */
function findTranslationAudioDuration(configJson, sourceId, language) {
  if (!configJson?.resources?.audioItems) return null;

  const audioItems = configJson.resources.audioItems;
  const translationId = `${sourceId}_${language}`;
  const idMatch = audioItems.find(item =>
    item.id === translationId ||
    (item.sourceId === sourceId && item.targetLanguage === language) ||
    (item.audioType === "translation" && item.sourceId === sourceId && item.language === language)
  );

  return idMatch?.duration ? parseFloat(idMatch.duration) : null;
}


