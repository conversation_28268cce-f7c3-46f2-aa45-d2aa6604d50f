/**
 * 用户设置管理 - 基于H5项目的userSettings.js
 * 适配小程序环境，使用uni.storage
 * 统一管理语言、等级、引导状态等所有用户相关设置
 */

// 统一的存储键名
const STORAGE_KEY = 'echolab_user_settings';

// 设置版本（用于数据迁移）
const SETTINGS_VERSION = '1.0.0';

// 默认设置
const DEFAULT_SETTINGS = {
  version: SETTINGS_VERSION,
  language: {
    learning: null // 学习语言 - 默认为null，强制用户选择
  },
  level: {
    current: null, // 当前等级（保留兼容性）
    setAt: null // 设置时间
  },
  // 新的多选水平等级字段
  selectedLevels: [], // 用户选择的多个水平等级
  // 简化引导状态，只记录是否首次访问
  firstVisit: true,
  preferences: {
    // 其他用户偏好设置
    theme: 'light',
    autoPlay: true,
    showSubtitles: true
  },
  updatedAt: null
};

// 兼容旧版本的存储键名
const LEGACY_STORAGE_KEYS = {
  LEARNING_LANGUAGE: 'learning_language',
  USER_LEVEL: 'user_level',
  FIRST_VISIT_COMPLETED: 'first_visit_completed',
  USER_PREFERENCES: 'user_preferences'
}

/**
 * 获取用户设置
 * @returns {Object} 用户设置对象
 */
function getUserSettings() {
  try {
    // 首先尝试获取新版本的统一设置
    const settings = uni.getStorageSync(STORAGE_KEY);
    if (settings && typeof settings === 'object') {
      // 合并默认设置，确保所有字段都存在
      return {
        ...DEFAULT_SETTINGS,
        ...settings,
        language: { ...DEFAULT_SETTINGS.language, ...settings.language },
        level: { ...DEFAULT_SETTINGS.level, ...settings.level },
        preferences: { ...DEFAULT_SETTINGS.preferences, ...settings.preferences }
      };
    }

    // 如果没有新版本设置，尝试迁移旧版本数据
    const migratedSettings = migrateFromLegacyStorage();
    if (migratedSettings) {
      // 保存迁移后的设置
      saveUserSettings(migratedSettings);
      return migratedSettings;
    }

    // 返回默认设置
    return { ...DEFAULT_SETTINGS };
  } catch (error) {
    console.error('获取用户设置失败:', error);
    return { ...DEFAULT_SETTINGS };
  }
}

/**
 * 保存用户设置
 * @param {Object} updates 要更新的设置
 * @returns {boolean} 是否保存成功
 */
function saveUserSettings(updates) {
  try {
    const currentSettings = getUserSettings();
    const newSettings = {
      ...currentSettings,
      ...updates,
      updatedAt: new Date().toISOString()
    };

    // 深度合并嵌套对象
    if (updates.language) {
      newSettings.language = { ...currentSettings.language, ...updates.language };
    }
    if (updates.level) {
      newSettings.level = { ...currentSettings.level, ...updates.level };
    }
    if (updates.preferences) {
      newSettings.preferences = { ...currentSettings.preferences, ...updates.preferences };
    }
    // 处理多选水平等级
    if (updates.selectedLevels !== undefined) {
      newSettings.selectedLevels = updates.selectedLevels;
    }

    uni.setStorageSync(STORAGE_KEY, newSettings);
    return true;
  } catch (error) {
    console.error('保存用户设置失败:', error);
    return false;
  }
}

/**
 * 从旧版本存储迁移数据
 * @returns {Object|null} 迁移后的设置或null
 */
function migrateFromLegacyStorage() {
  try {
    const learningLanguage = uni.getStorageSync(LEGACY_STORAGE_KEYS.LEARNING_LANGUAGE);
    const userLevel = uni.getStorageSync(LEGACY_STORAGE_KEYS.USER_LEVEL);
    const firstVisitCompleted = uni.getStorageSync(LEGACY_STORAGE_KEYS.FIRST_VISIT_COMPLETED);

    if (!learningLanguage && !userLevel && !firstVisitCompleted) {
      return null; // 没有旧数据需要迁移
    }

    const migratedSettings = {
      ...DEFAULT_SETTINGS,
      language: {
        learning: learningLanguage || null
      },
      level: {
        current: userLevel || null,
        setAt: userLevel ? new Date().toISOString() : null
      },
      firstVisit: !firstVisitCompleted
    };

    // 清理旧版本数据
    uni.removeStorageSync(LEGACY_STORAGE_KEYS.LEARNING_LANGUAGE);
    uni.removeStorageSync(LEGACY_STORAGE_KEYS.USER_LEVEL);
    uni.removeStorageSync(LEGACY_STORAGE_KEYS.FIRST_VISIT_COMPLETED);

    return migratedSettings;
  } catch (error) {
    console.error('数据迁移失败:', error);
    return null;
  }
}

/**
 * 获取学习语言
 * @returns {string|null} 学习语言代码
 */
export function getLearningLanguage() {
  const settings = getUserSettings();
  return settings.language.learning;
}

/**
 * 设置学习语言
 * @param {string} languageCode 语言代码
 * @returns {boolean} 是否成功
 */
export function setLearningLanguage(languageCode) {
  // 如果语言发生变化，清除之前的等级设置（仿照H5逻辑）
  const currentLanguage = getLearningLanguage();
  const shouldClearLevel = currentLanguage && currentLanguage !== languageCode;

  const updates = {
    language: {
      learning: languageCode
    }
  };

  // 如果语言变化，同时清除等级
  if (shouldClearLevel) {
    updates.level = {
      current: null,
      setAt: null
    };
  }

  return saveUserSettings(updates);
}

/**
 * 获取用户等级
 * @returns {string|null} 用户等级
 */
export function getUserLevel() {
  const settings = getUserSettings();
  return settings.level.current;
}

/**
 * 设置用户等级（保留兼容性）
 * @param {string} level 用户等级
 * @returns {boolean} 是否成功
 */
export function setUserLevel(level) {
  return saveUserSettings({
    level: {
      current: level,
      setAt: level ? new Date().toISOString() : null
    }
  });
}

/**
 * 获取用户选择的多个水平等级
 * @returns {Array} 水平等级数组
 */
export function getSelectedLevels() {
  const settings = getUserSettings();
  return settings.selectedLevels || [];
}

/**
 * 设置用户选择的多个水平等级
 * @param {Array} levels 水平等级数组
 * @returns {boolean} 是否成功
 */
export function setSelectedLevels(levels) {
  return saveUserSettings({
    selectedLevels: Array.isArray(levels) ? levels : []
  });
}

/**
 * 检查是否首次访问
 * @returns {boolean} 是否首次访问
 */
export function isFirstVisit() {
  const settings = getUserSettings();
  return settings.firstVisit;
}

/**
 * 标记首次访问完成
 * @returns {boolean} 是否成功
 */
export function setFirstVisitCompleted() {
  return saveUserSettings({
    firstVisit: false
  });
}

/**
 * 检查是否需要显示引导
 * @returns {boolean} 是否需要显示引导
 */
export function shouldShowOnboarding() {
  const settings = getUserSettings();
  // 只要没有设置学习语言，就需要显示引导
  return !settings.language.learning;
}

/**
 * 重置用户设置（用于测试或重新引导）
 * @param {boolean} clearLanguageAndLevel 是否同时清除语言和等级设置
 * @returns {boolean} 是否重置成功
 */
export function resetUserSettings(clearLanguageAndLevel = false) {
  const updates = {
    firstVisit: true
  };

  // 如果需要，同时清除语言和等级设置
  if (clearLanguageAndLevel) {
    updates.language = {
      learning: null
    };
    updates.level = {
      current: null,
      setAt: null
    };
  }

  return saveUserSettings(updates);
}

/**
 * 获取用户偏好设置
 * @returns {Object} 用户偏好设置
 */
export function getUserPreferences() {
  const settings = getUserSettings();
  return settings.preferences;
}

/**
 * 设置用户偏好
 * @param {Object} preferences 用户偏好设置
 * @returns {boolean} 是否成功
 */
export function setUserPreferences(preferences) {
  return saveUserSettings({
    preferences: preferences
  });
}

/**
 * 清除所有用户设置
 * @returns {boolean} 是否成功
 */
export function clearAllUserSettings() {
  try {
    uni.removeStorageSync(STORAGE_KEY);
    // 同时清理可能存在的旧版本数据
    Object.values(LEGACY_STORAGE_KEYS).forEach(key => {
      uni.removeStorageSync(key);
    });
    return true;
  } catch (error) {
    console.error('清除用户设置失败:', error);
    return false;
  }
}
