'use strict';

const axios = require('axios');

/**
 * API代理云函数 - 纯转发，不做任何处理
 */
exports.main = async (event, context) => {
  console.log('云函数接收参数:', JSON.stringify(event, null, 2));

  // 直接从event中获取参数
  const method = event.method || 'GET';
  const path = event.path;
  const data = event.data;
  const headers = event.headers || {};

  // 参数验证
  if (!path) {
    throw new Error('path参数必需');
  }

  try {
    // 构建完整URL
    const baseUrl = 'https://echolab.club';
    const fullUrl = `${baseUrl}${path}`;

    console.log('转发到:', fullUrl);

    // axios配置
    const config = {
      method: method,
      url: fullUrl,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      timeout: 30000
    };

    // GET请求用params，其他用data
    if (method === 'GET' && data) {
      config.params = data;
    } else if (data) {
      config.data = data;
    }

    const response = await axios(config);

    // 直接返回响应数据
    return response.data;

  } catch (error) {
    console.error('请求失败:', error.message);
    throw new Error(`API请求失败: ${error.message}`);
  }
};
