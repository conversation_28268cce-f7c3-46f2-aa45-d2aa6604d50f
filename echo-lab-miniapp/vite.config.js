import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni';
import { resolve } from 'path';

export default defineConfig({
  plugins: [uni()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3001,
    host: '0.0.0.0'
  },
  build: {
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,  // 保留console语句
        drop_debugger: true, // 保留debugger语句
      },
    },
  },
  // 注意：此配置仅在CLI构建时生效（如H5）
  // 小程序必须使用HBuilderX，有自己的构建优化流程
});
