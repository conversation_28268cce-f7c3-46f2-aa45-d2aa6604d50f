# Echo Lab - 视频内容创建工具

Echo Lab 是一个基于节点系统的视频内容创建工具，允许用户通过连接不同类型的节点来创建和编辑视频内容。

## 功能特点

- **节点系统**：通过连接不同类型的节点来创建视频内容
- **文本处理**：支持文本输入、分句、标注和翻译
- **音频生成**：支持将文本转换为音频
- **播放控制**：支持定义内容的播放顺序和步骤
- **视频配置**：支持配置视频输出参数

## 节点类型

Echo Lab 支持以下类型的节点：

- **文本节点**：用于输入和编辑文本内容
- **分句节点**：将文本分割为句子
- **标注节点**：为文本添加标注（如注音、注释等）
- **翻译节点**：将文本翻译为其他语言
- **文本序列节点**：管理文本序列和顺序
- **音频节点**：生成和管理音频内容
- **播放步骤节点**：定义内容的播放顺序和步骤
- **视频配置节点**：配置视频输出参数

## 技术栈

- **前端框架**：Vue 3
- **UI 组件库**：Element Plus
- **状态管理**：Pinia
- **路由**：Vue Router
- **构建工具**：Vite

## 架构设计

Echo Lab 采用声明式配置和运行时生成架构：

1. **节点只存储配置，不存储数据**：节点只存储配置信息，数据在运行时生成
2. **配置与数据分离**：配置信息和生成的数据分开管理
3. **简化错误处理**：通过明确的数据流和状态管理简化错误处理
4. **明确组件职责**：每个组件都有明确的职责，避免职责混淆

## 开发指南

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 使用指南

1. 打开编辑器页面
2. 添加节点：点击右下角的"+"按钮，选择要添加的节点类型
3. 连接节点：将节点的输出点连接到另一个节点的输入点
4. 配置节点：选中节点，在右侧属性面板中配置节点参数
5. 预览结果：在节点的预览区域查看处理结果
6. 导出配置：在视频配置节点中导出最终配置

## 许可证

[MIT](LICENSE)
