<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF导出调试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
</head>
<body>
    <h1>PDF导出调试</h1>
    <button onclick="testSimplePDF()">测试简单PDF</button>
    <button onclick="testComplexPDF()">测试复杂PDF</button>
    
    <div id="test-content" style="display: none;">
        <h2>测试内容</h2>
        <p>这是一段中文测试内容。</p>
        <p>これは日本語のテストです。</p>
        <table border="1">
            <tr>
                <th>序号</th>
                <th>内容</th>
            </tr>
            <tr>
                <td>1</td>
                <td>A：今日は良い天気ですね。</td>
            </tr>
            <tr>
                <td>2</td>
                <td>B：はい、とても気持ちがいいです。</td>
            </tr>
        </table>
    </div>
    
    <script>
        async function testSimplePDF() {
            const element = document.getElementById('test-content');
            element.style.display = 'block';
            
            const options = {
                margin: 0.5,
                filename: '简单测试.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { scale: 1 },
                jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
            };
            
            try {
                await html2pdf().set(options).from(element).save();
                alert('简单PDF导出成功！');
            } catch (error) {
                console.error('简单PDF导出失败:', error);
                alert('简单PDF导出失败: ' + error.message);
            }
            
            element.style.display = 'none';
        }
        
        async function testComplexPDF() {
            // 创建临时DOM元素
            const tempDiv = document.createElement("div");
            tempDiv.style.position = "absolute";
            tempDiv.style.left = "-9999px";
            tempDiv.style.top = "-9999px";
            tempDiv.style.width = "210mm";
            tempDiv.style.backgroundColor = "white";
            tempDiv.style.fontFamily = '"Microsoft YaHei", "SimSun", "Arial Unicode MS", sans-serif';
            tempDiv.style.fontSize = "14px";
            tempDiv.style.lineHeight = "1.6";
            tempDiv.style.padding = "20px";
            tempDiv.style.color = "#333";

            // 创建标题
            const titleDiv = document.createElement("div");
            titleDiv.style.fontSize = "18px";
            titleDiv.style.fontWeight = "bold";
            titleDiv.style.textAlign = "center";
            titleDiv.style.marginBottom = "30px";
            titleDiv.style.borderBottom = "2px solid #333";
            titleDiv.style.paddingBottom = "10px";
            titleDiv.textContent = "复杂测试内容";
            tempDiv.appendChild(titleDiv);

            // 创建表格
            const table = document.createElement("table");
            table.style.width = "100%";
            table.style.borderCollapse = "collapse";
            table.style.marginTop = "20px";

            // 创建表头
            const thead = document.createElement("thead");
            const headerRow = document.createElement("tr");
            
            const indexHeader = document.createElement("th");
            indexHeader.textContent = "序号";
            indexHeader.style.border = "1px solid #ddd";
            indexHeader.style.padding = "8px";
            indexHeader.style.backgroundColor = "#f5f5f5";
            indexHeader.style.fontWeight = "bold";
            indexHeader.style.textAlign = "center";
            indexHeader.style.width = "50px";
            
            const contentHeader = document.createElement("th");
            contentHeader.textContent = "内容";
            contentHeader.style.border = "1px solid #ddd";
            contentHeader.style.padding = "8px";
            contentHeader.style.backgroundColor = "#f5f5f5";
            contentHeader.style.fontWeight = "bold";
            contentHeader.style.textAlign = "center";
            
            headerRow.appendChild(indexHeader);
            headerRow.appendChild(contentHeader);
            thead.appendChild(headerRow);
            table.appendChild(thead);

            // 创建表体
            const tbody = document.createElement("tbody");
            const testData = [
                "A：今日は良い天気ですね。",
                "B：はい、とても気持ちがいいです。",
                "散歩でもしませんか？",
                "いいですね。一緒に行きましょう。"
            ];
            
            testData.forEach((line, index) => {
                const row = document.createElement("tr");
                
                const indexCell = document.createElement("td");
                indexCell.textContent = (index + 1).toString();
                indexCell.style.border = "1px solid #ddd";
                indexCell.style.padding = "8px";
                indexCell.style.textAlign = "center";
                indexCell.style.width = "50px";
                
                const contentCell = document.createElement("td");
                contentCell.textContent = line;
                contentCell.style.border = "1px solid #ddd";
                contentCell.style.padding = "8px";
                contentCell.style.wordWrap = "break-word";
                contentCell.style.wordBreak = "break-all";
                
                row.appendChild(indexCell);
                row.appendChild(contentCell);
                tbody.appendChild(row);
            });
            
            table.appendChild(tbody);
            tempDiv.appendChild(table);
            document.body.appendChild(tempDiv);

            // 让元素可见以便调试
            tempDiv.style.left = "0px";
            tempDiv.style.top = "0px";
            tempDiv.style.position = "relative";
            tempDiv.style.zIndex = "9999";

            console.log('临时元素内容:', tempDiv.innerHTML);
            console.log('表格行数:', tempDiv.querySelectorAll('tr').length);

            // 等待渲染
            await new Promise(resolve => setTimeout(resolve, 1000));

            const options = {
                margin: 0.5,
                filename: '复杂测试.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { 
                    scale: 1,
                    useCORS: true,
                    allowTaint: true,
                    letterRendering: true
                },
                jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
            };

            try {
                await html2pdf().set(options).from(tempDiv).save();
                alert('复杂PDF导出成功！');
            } catch (error) {
                console.error('复杂PDF导出失败:', error);
                alert('复杂PDF导出失败: ' + error.message);
            }

            // 清理
            setTimeout(() => {
                if (document.body.contains(tempDiv)) {
                    document.body.removeChild(tempDiv);
                }
            }, 2000);
        }
    </script>
</body>
</html>
