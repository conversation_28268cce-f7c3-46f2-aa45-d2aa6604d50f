<!--
  数据统计组件
  显示系统的各种统计数据
-->
<template>
  <div class="data-statistics">
    <div class="statistics-header">
      <h2 class="section-title">数据统计</h2>
      <p class="section-description">查看系统的使用情况和数据统计</p>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><i-ep-refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <div class="statistics-content" v-loading="loading">
      <!-- 概览卡片 -->
      <div class="overview-cards">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><i-ep-filter /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ statistics.totalFilters }}</div>
            <div class="stat-label">过滤器总数</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon active">
            <el-icon><i-ep-check /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ statistics.activeFilters }}</div>
            <div class="stat-label">启用的过滤器</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon content">
            <el-icon><i-ep-document /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ statistics.totalContents }}</div>
            <div class="stat-label">内容总数</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon user">
            <el-icon><i-ep-user /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ statistics.totalUsers }}</div>
            <div class="stat-label">用户总数</div>
          </div>
        </div>
      </div>

      <!-- 详细统计 -->
      <div class="detailed-stats">
        <!-- 过滤器类型分布 -->
        <el-card class="stat-detail-card">
          <template #header>
            <h3 class="card-title">过滤器类型分布</h3>
          </template>
          <div class="filter-type-stats">
            <div
              v-for="typeStats in statistics.filterTypeStats"
              :key="typeStats.type"
              class="type-stat-item"
            >
              <div class="type-info">
                <span class="type-name">{{ typeStats.name }}</span>
                <span class="type-count">{{ typeStats.count }}个</span>
              </div>
              <div class="type-progress">
                <el-progress
                  :percentage="getTypePercentage(typeStats.count)"
                  :show-text="false"
                  :stroke-width="8"
                />
              </div>
            </div>
          </div>
        </el-card>

        <!-- 使用频率统计 -->
        <el-card class="stat-detail-card">
          <template #header>
            <h3 class="card-title">过滤器使用频率</h3>
          </template>
          <div class="usage-stats">
            <div
              v-for="usage in statistics.filterUsageStats"
              :key="usage.filterId"
              class="usage-item"
            >
              <div class="usage-info">
                <span class="filter-name">{{ usage.filterName }}</span>
                <span class="usage-count">{{ usage.usageCount }}次</span>
              </div>
              <div class="usage-bar">
                <div
                  class="usage-fill"
                  :style="{ width: getUsagePercentage(usage.usageCount) + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 最近活动 -->
        <el-card class="stat-detail-card full-width">
          <template #header>
            <h3 class="card-title">最近活动</h3>
          </template>
          <div class="recent-activities">
            <div
              v-for="activity in statistics.recentActivities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <el-icon>
                  <component :is="getActivityIcon(activity.type)" />
                </el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-text">{{ activity.description }}</div>
                <div class="activity-time">{{ formatTime(activity.createdAt) }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const loading = ref(false)
const statistics = ref({
  totalFilters: 0,
  activeFilters: 0,
  totalContents: 0,
  totalUsers: 0,
  filterTypeStats: [],
  filterUsageStats: [],
  recentActivities: []
})

// 获取类型百分比
const getTypePercentage = (count) => {
  if (statistics.value.totalFilters === 0) return 0
  return Math.round((count / statistics.value.totalFilters) * 100)
}

// 获取使用频率百分比
const getUsagePercentage = (count) => {
  const maxUsage = Math.max(...statistics.value.filterUsageStats.map(item => item.usageCount))
  if (maxUsage === 0) return 0
  return Math.round((count / maxUsage) * 100)
}

// 获取活动图标
const getActivityIcon = (type) => {
  const iconMap = {
    'filter_created': 'i-ep-plus',
    'filter_updated': 'i-ep-edit',
    'filter_deleted': 'i-ep-delete',
    'filter_enabled': 'i-ep-check',
    'filter_disabled': 'i-ep-close',
    'content_created': 'i-ep-document-add',
    'user_registered': 'i-ep-user-filled'
  }
  return iconMap[type] || 'i-ep-info-filled'
}

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString()
  }
}

// 刷新数据
const refreshData = async () => {
  await loadStatistics()
}

// 加载统计数据
const loadStatistics = async () => {
  loading.value = true
  try {
    // 这里可以调用API获取统计数据
    console.log('加载统计数据')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    statistics.value = {
      totalFilters: 45,
      activeFilters: 38,
      totalContents: 1250,
      totalUsers: 89,
      filterTypeStats: [
        { type: 'language_level', name: '语言等级', count: 15 },
        { type: 'content_type', name: '内容类型', count: 8 },
        { type: 'topic', name: '主题', count: 12 },
        { type: 'material', name: '教材', count: 10 }
      ],
      filterUsageStats: [
        { filterId: 1, filterName: 'N5', usageCount: 1250 },
        { filterId: 2, filterName: '对话', usageCount: 980 },
        { filterId: 3, filterName: '日常生活', usageCount: 750 },
        { filterId: 4, filterName: 'N4', usageCount: 650 },
        { filterId: 5, filterName: '阅读', usageCount: 520 }
      ],
      recentActivities: [
        {
          id: 1,
          type: 'filter_created',
          description: '创建了新的过滤器"商务日语"',
          createdAt: Date.now() - 300000
        },
        {
          id: 2,
          type: 'filter_updated',
          description: '更新了过滤器"N5"的描述',
          createdAt: Date.now() - 1800000
        },
        {
          id: 3,
          type: 'content_created',
          description: '新增了内容"基础对话练习"',
          createdAt: Date.now() - 3600000
        },
        {
          id: 4,
          type: 'user_registered',
          description: '新用户注册',
          createdAt: Date.now() - 7200000
        },
        {
          id: 5,
          type: 'filter_enabled',
          description: '启用了过滤器"高级语法"',
          createdAt: Date.now() - 10800000
        }
      ]
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadStatistics()
})
</script>

<style scoped>
.data-statistics {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.statistics-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.section-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.section-description {
  margin: 0;
  color: var(--el-text-color-secondary);
  font-size: 0.875rem;
}

.header-actions {
  flex-shrink: 0;
}

.statistics-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  background: white;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.2s ease;
}

.stat-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.stat-icon.active {
  background: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.stat-icon.content {
  background: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
}

.stat-icon.user {
  background: var(--el-color-info-light-9);
  color: var(--el-color-info);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--el-text-color-primary);
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--el-text-color-secondary);
  margin-top: 0.25rem;
}

.detailed-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.stat-detail-card {
  border-radius: 8px;
}

.stat-detail-card.full-width {
  grid-column: 1 / -1;
}

.card-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.filter-type-stats,
.usage-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.type-stat-item,
.usage-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.type-info,
.usage-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.type-name,
.filter-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.type-count,
.usage-count {
  font-size: 0.875rem;
  color: var(--el-text-color-secondary);
}

.usage-bar {
  height: 8px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  overflow: hidden;
}

.usage-fill {
  height: 100%;
  background: var(--el-color-primary);
  transition: width 0.3s ease;
}

.recent-activities {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--el-bg-color-page);
  border-radius: 6px;
}

.activity-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 0.875rem;
  color: var(--el-text-color-primary);
  line-height: 1.4;
}

.activity-time {
  font-size: 0.75rem;
  color: var(--el-text-color-secondary);
  margin-top: 0.25rem;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .statistics-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .overview-cards {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .detailed-stats {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 1rem;
  }
  
  .stat-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }
  
  .stat-value {
    font-size: 1.5rem;
  }
}
</style>
