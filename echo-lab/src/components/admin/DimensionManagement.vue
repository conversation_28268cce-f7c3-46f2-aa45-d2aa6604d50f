<!--
  维度管理页面
  提供基本的CRUD功能
-->
<template>
  <div class="dimension-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">维度管理</h1>
        <p class="page-description">管理过滤器的维度类型，支持创建和编辑自定义维度</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><i-ep-plus /></el-icon>
          创建维度
        </el-button>
        <el-button @click="loadDimensions" :loading="loading">
          <el-icon><i-ep-refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>



    <!-- 维度列表 -->
    <div class="dimensions-container" v-loading="loading">
      <div v-if="dimensions.length === 0" class="empty-state">
        <el-empty description="暂无维度数据">
          <el-button type="primary" @click="showCreateDialog = true">
            创建第一个维度
          </el-button>
        </el-empty>
      </div>
      
      <div v-else class="dimensions-grid">
        <div
          v-for="dimension in dimensions"
          :key="dimension.type"
          class="dimension-card"
        >
          <div class="dimension-info">
            <div class="dimension-header">
              <h3 class="dimension-name">{{ dimension.name }}</h3>
              <div class="dimension-actions">
                <el-button
                  v-if="!isSystemDimension(dimension.type)"
                  type="primary"
                  size="small"
                  @click="editDimension(dimension)"
                  link
                >
                  编辑
                </el-button>
                <el-button
                  v-if="!isSystemDimension(dimension.type)"
                  type="danger"
                  size="small"
                  @click="deleteDimension(dimension)"
                  link
                >
                  删除
                </el-button>
              </div>
            </div>

            <div class="dimension-details">
              <div class="detail-item">
                <span class="label">类型:</span>
                <code class="dimension-type">{{ dimension.type }}</code>
              </div>
              <div class="detail-item">
                <span class="label">过滤器:</span>
                <span class="filter-count">{{ dimension.filterCount }} 个</span>
              </div>
              <div class="detail-item">
                <el-tag
                  :type="isSystemDimension(dimension.type) ? 'info' : 'success'"
                  size="small"
                >
                  {{ isSystemDimension(dimension.type) ? '系统维度' : '自定义维度' }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建维度对话框 -->
    <StandardDialog
      v-model="showCreateDialog"
      title="创建新维度"
      width="500px"
      :show-confirm="true"
      confirm-text="创建"
      @confirm="handleCreateDimension"
      @cancel="resetCreateForm"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="维度名称" prop="name">
          <el-input
            v-model="createForm.name"
            placeholder="请输入维度名称，如：难度等级"
            maxlength="50"
            show-word-limit
          />
          <div class="form-tip">
            用于显示的中文名称
          </div>
        </el-form-item>
        
        <el-form-item label="类型键值" prop="type">
          <el-input
            v-model="createForm.type"
            placeholder="请输入类型键值，如：difficulty_level"
            maxlength="50"
          />
          <div class="form-tip">
            只能包含小写字母、数字和下划线，用于程序识别
          </div>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入维度描述（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </StandardDialog>

    <!-- 编辑维度对话框 -->
    <StandardDialog
      v-model="showEditDialog"
      title="编辑维度"
      width="500px"
      :show-confirm="true"
      confirm-text="保存"
      @confirm="handleEditDimension"
      @cancel="resetEditForm"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="维度名称" prop="name">
          <el-input
            v-model="editForm.name"
            placeholder="请输入维度名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="类型键值" prop="type">
          <el-input
            v-model="editForm.type"
            placeholder="请输入类型键值"
            maxlength="50"
          />
          <div class="form-tip">
            修改类型键值会影响所有使用该维度的过滤器
          </div>
        </el-form-item>
      </el-form>
    </StandardDialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import StandardDialog from '@/components/common/StandardDialog.vue'
import dimensionService from '@/services/dimensionService'

// 响应式数据
const loading = ref(false)
const dimensions = ref([])
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const editingDimension = ref(null)

// 创建表单
const createFormRef = ref()
const createForm = ref({
  name: '',
  type: '',
  description: ''
})

const createRules = {
  name: [
    { required: true, message: '请输入维度名称', trigger: ['blur', 'change'] },
    { min: 2, max: 50, message: '维度名称长度在 2 到 50 个字符', trigger: ['blur', 'change'] }
  ],
  type: [
    { required: true, message: '请输入类型键值', trigger: ['blur', 'change'] },
    { min: 2, max: 50, message: '类型键值长度在 2 到 50 个字符', trigger: ['blur', 'change'] },
    { pattern: /^[a-z0-9_]+$/, message: '只能包含小写字母、数字和下划线', trigger: ['blur', 'change'] }
  ]
}

// 编辑表单
const editFormRef = ref()
const editForm = ref({
  name: '',
  type: '',
  originalType: ''
})

const editRules = {
  name: [
    { required: true, message: '请输入维度名称', trigger: 'blur' },
    { min: 2, max: 50, message: '维度名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请输入类型键值', trigger: 'blur' },
    { min: 2, max: 50, message: '类型键值长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-z0-9_]+$/, message: '只能包含小写字母、数字和下划线', trigger: 'blur' }
  ]
}

// 加载维度列表
const loadDimensions = async () => {
  loading.value = true
  try {
    const response = await dimensionService.getAllDimensions()
    dimensions.value = response.dimensions || []
  } catch (error) {
    console.error('加载维度失败:', error)
    ElMessage.error('加载维度失败')
  } finally {
    loading.value = false
  }
}



// 创建维度
const handleCreateDimension = async () => {
  if (!createFormRef.value) return

  try {
    await createFormRef.value.validate()
    await dimensionService.createDimension(createForm.value)

    // 创建成功后的操作
    ElMessage.success('创建成功')
    await loadDimensions()
    showCreateDialog.value = false
    resetCreateForm()
  } catch (error) {
    console.error('创建维度失败:', error)
    ElMessage.error('创建失败')
  }
}

// 编辑维度
const editDimension = (dimension) => {
  editingDimension.value = dimension
  editForm.value = {
    name: dimension.name,
    type: dimension.type,
    originalType: dimension.type
  }
  showEditDialog.value = true
}

const handleEditDimension = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()
    await dimensionService.updateDimension(editForm.value.originalType, {
      newType: editForm.value.type,
      newName: editForm.value.name
    })

    // 更新成功后的操作
    ElMessage.success('更新成功')
    await loadDimensions()
    showEditDialog.value = false
    resetEditForm()
  } catch (error) {
    console.error('更新维度失败:', error)
    ElMessage.error('更新失败')
  }
}

// 删除维度
const deleteDimension = async (dimension) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除维度"${dimension.name}"吗？这将删除该维度下的所有${dimension.filterCount}个过滤器，此操作不可恢复。`,
      '确认删除',
      { type: 'warning' }
    )

    await dimensionService.deleteDimension(dimension.type)
    await loadDimensions()
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除维度失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 检查是否为系统维度
const isSystemDimension = (type) => {
  const systemDimensions = ['language_level', 'content_type', 'topic', 'material']
  return systemDimensions.includes(type)
}

// 重置表单
const resetCreateForm = () => {
  createForm.value = {
    name: '',
    type: '',
    description: ''
  }
  if (createFormRef.value) {
    createFormRef.value.resetFields()
  }
}

const resetEditForm = () => {
  editForm.value = {
    name: '',
    type: '',
    originalType: ''
  }
  editingDimension.value = null
  if (editFormRef.value) {
    editFormRef.value.resetFields()
  }
}

// 页面初始化
onMounted(() => {
  loadDimensions()
})
</script>

<style scoped>
.dimension-management-page {
  padding: 1.5rem;
  background: var(--el-bg-color-page);
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.page-description {
  margin: 0;
  color: var(--el-text-color-secondary);
  font-size: 0.875rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
  flex-shrink: 0;
}

.stats-section {
  margin-bottom: 1.5rem;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 1rem;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: var(--el-color-primary);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--el-text-color-secondary);
  font-size: 0.875rem;
}

.dimensions-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 400px;
}

.dimensions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1rem;
}

.dimension-card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;
  position: relative;
}

.dimension-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dimension-card.system-dimension {
  border-color: var(--el-color-info);
  background: var(--el-color-info-light-9);
}

.dimension-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.dimension-name {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  flex: 1;
}

.dimension-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.dimension-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.label {
  color: var(--el-text-color-secondary);
  font-weight: 500;
  min-width: 5rem;
}

.dimension-type {
  background: var(--el-bg-color-page);
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
}

.filter-count,
.create-time {
  color: var(--el-text-color-regular);
}

.dimension-status {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
}

.form-tip {
  font-size: 0.75rem;
  color: var(--el-text-color-secondary);
  margin-top: 0.25rem;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .dimension-management-page {
    padding: 1rem;
  }

  .page-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
  }
  
  .dimensions-grid {
    grid-template-columns: 1fr;
  }
  
  .dimension-header {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .dimension-actions {
    justify-content: flex-start;
  }
}
</style>
