<!--
  过滤器创建对话框
  用于创建新的过滤器
-->
<template>
  <StandardDialog
    v-model="dialogVisible"
    title="添加过滤器"
    width="600px"
    :show-confirm="true"
    confirm-text="创建"
    cancel-text="取消"
    @confirm="handleCreate"
    @cancel="handleClose"
  >
    <div class="filter-create-form" v-loading="loading">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
        label-position="left"
      >
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入过滤器名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="键值" prop="key">
          <el-input
            v-model="formData.key"
            placeholder="请输入过滤器键值（英文）"
            maxlength="50"
            show-word-limit
          />
          <div class="form-tip">
            键值用于程序识别，建议使用英文和下划线，如：n5_level
          </div>
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-select
            v-model="formData.type"
            placeholder="请选择或输入过滤器类型"
            style="width: 100%"
            filterable
            allow-create
            default-first-option
          >
            <el-option
              v-for="type in availableTypes"
              :key="type.key"
              :label="type.name"
              :value="type.key"
            />
          </el-select>
          <div class="form-tip">
            选择现有维度类型或输入新的维度类型（如：difficulty、skill_level等）
          </div>
        </el-form-item>

        <el-form-item label="语言" prop="languages">
          <el-select
            v-model="formData.languages"
            placeholder="请选择适用语言（可多选）"
            style="width: 100%"
            multiple
            clearable
            collapse-tags
            collapse-tags-tooltip
          >
            <el-option label="日语" value="ja" />
            <el-option label="英语" value="en" />
            <el-option label="中文简体" value="zh-CN" />
            <el-option label="中文繁体" value="zh-TW" />
          </el-select>
          <div class="form-tip">
            不选择任何语言表示适用于所有语言
          </div>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入过滤器描述（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="状态" prop="isActive">
          <el-switch
            v-model="formData.isActive"
            active-text="启用"
            inactive-text="禁用"
          />
          <div class="form-tip">
            禁用的过滤器不会在筛选界面显示
          </div>
        </el-form-item>
      </el-form>

      <!-- 快速创建模板 -->
      <div class="quick-templates">
        <h4 class="template-title">快速创建模板</h4>
        <div class="template-buttons">
          <el-button
            v-for="template in quickTemplates"
            :key="template.key"
            size="small"
            @click="applyTemplate(template)"
          >
            {{ template.name }}
          </el-button>
        </div>
      </div>
    </div>
  </StandardDialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import StandardDialog from '@/components/common/StandardDialog.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  filterTypes: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'create'])

// 响应式数据
const loading = ref(false)
const formRef = ref(null)
const formData = ref({
  name: '',
  key: '',
  type: '',
  languages: [],
  description: '',
  isActive: true
})

// 快速创建模板
const quickTemplates = ref([
  {
    key: 'n5',
    name: 'N5等级',
    data: {
      name: 'N5',
      key: 'n5',
      type: 'language_level',
      languages: ['ja'],
      description: '日语能力考试N5等级'
    }
  },
  {
    key: 'dialogue',
    name: '对话类型',
    data: {
      name: '对话',
      key: 'dialogue',
      type: 'content_type',
      languages: [],
      description: '对话形式的学习内容'
    }
  },
  {
    key: 'daily_life',
    name: '日常生活',
    data: {
      name: '日常生活',
      key: 'daily_life',
      type: 'topic',
      languages: [],
      description: '日常生活相关主题'
    }
  }
])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const availableTypes = computed(() => {
  return props.filterTypes.filter(type => type.key !== 'all')
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入过滤器名称', trigger: 'blur' },
    { min: 1, max: 50, message: '名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  key: [
    { required: true, message: '请输入过滤器键值', trigger: 'blur' },
    { min: 1, max: 50, message: '键值长度在 1 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '键值只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择过滤器类型', trigger: 'change' }
  ]
}

// 应用模板
const applyTemplate = (template) => {
  formData.value = {
    ...formData.value,
    ...template.data
  }
}

// 处理创建
const handleCreate = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    emit('create', { ...formData.value })
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  }
}

// 处理关闭
const handleClose = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  // 重置表单数据
  formData.value = {
    name: '',
    key: '',
    type: '',
    languages: [],
    description: '',
    isActive: true
  }
}
</script>

<style scoped>
.filter-create-form {
  padding: 0.5rem 0;
}

.form-tip {
  font-size: 0.75rem;
  color: var(--el-text-color-secondary);
  margin-top: 0.25rem;
  line-height: 1.4;
}

.quick-templates {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--el-border-color-lighter);
}

.template-title {
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.template-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}
</style>
