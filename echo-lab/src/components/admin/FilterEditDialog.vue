<!--
  过滤器编辑对话框
  用于编辑现有过滤器
-->
<template>
  <StandardDialog v-model="dialogVisible" title="编辑过滤器" width="600px" :show-confirm="true" confirm-text="保存"
    cancel-text="取消" @confirm="handleSave" @close="handleClose">
    <div class="filter-edit-form" v-loading="loading">
      <el-form ref="formRef"
        :model="{ name: filterName, key: filterKey, type: filterType, description: filterDescription }" :rules="formRules"
        label-width="80px" label-position="left">
        <el-form-item label="名称" prop="name">
          <el-input v-model="filterName" placeholder="请输入过滤器名称" maxlength="50" show-word-limit />
        </el-form-item>

        <el-form-item label="键值" prop="key">
          <el-input v-model="filterKey" placeholder="请输入过滤器键值（英文）" maxlength="50" show-word-limit />
          <div class="form-tip">
            键值用于程序识别，建议使用英文和下划线，如：n5_level
          </div>
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-select v-model="filterType" placeholder="请选择或输入过滤器类型" style="width: 100%" filterable allow-create
            default-first-option>
            <el-option v-for="type in availableTypes" :key="type.key" :label="type.name" :value="type.key" />
          </el-select>
          <div class="form-tip">
            选择现有维度类型或输入新的维度类型
          </div>
        </el-form-item>

        <el-form-item label="语言" prop="languages">
          <el-select v-model="filterLanguages" placeholder="请选择适用语言（可多选）" style="width: 100%" multiple clearable
            collapse-tags collapse-tags-tooltip>
            <el-option label="日语" value="ja" />
            <el-option label="英语" value="en" />
            <el-option label="中文简体" value="zh-CN" />
            <el-option label="中文繁体" value="zh-TW" />
          </el-select>
          <div class="form-tip">
            不选择任何语言表示适用于所有语言
          </div>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="filterDescription" type="textarea" :rows="3" placeholder="请输入过滤器描述（可选）" maxlength="200"
            show-word-limit />
        </el-form-item>

        <el-form-item label="状态" prop="isActive">
          <el-switch v-model="filterIsActive" active-text="启用" inactive-text="禁用" />
          <div class="form-tip">
            禁用的过滤器不会在筛选界面显示
          </div>
        </el-form-item>
      </el-form>
    </div>
  </StandardDialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import StandardDialog from '@/components/common/StandardDialog.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  filter: {
    type: Object,
    default: null
  },
  filterTypes: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'save'])

// 响应式数据
const loading = ref(false)
const formRef = ref(null)

// 直接定义各个字段，不用嵌套对象
const filterName = ref('')
const filterKey = ref('')
const filterType = ref('')
const filterDescription = ref('')
const filterIsActive = ref(true)
const filterLanguages = ref([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const availableTypes = computed(() => {
  return props.filterTypes.filter(type => type.key !== 'all')
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入过滤器名称', trigger: 'blur' },
    { min: 1, max: 50, message: '名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  key: [
    { required: true, message: '请输入过滤器键值', trigger: 'blur' },
    { min: 1, max: 50, message: '键值长度在 1 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '键值只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择过滤器类型', trigger: 'change' }
  ]
}

// 初始化表单数据
const initFormData = () => {
  if (props.filter) {
    filterName.value = props.filter.name || ''
    filterKey.value = props.filter.key || ''
    filterType.value = props.filter.type || ''
    filterDescription.value = props.filter.description || ''
    filterIsActive.value = props.filter.isActive !== false
    filterLanguages.value = Array.isArray(props.filter.languages) ? props.filter.languages.slice() : []
  }
}

// 监听对话框打开
watch(() => props.modelValue, (isVisible) => {
  if (isVisible) {
    initFormData()
  }
})

// 处理保存
const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const saveData = {
      name: filterName.value,
      key: filterKey.value,
      type: filterType.value,
      languages: filterLanguages.value.slice(),
      description: filterDescription.value,
      isActive: filterIsActive.value
    }

    emit('save', saveData)
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  }
}

// 处理关闭
const handleClose = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.filter-edit-form {
  padding: 0.5rem 0;
}

.form-tip {
  font-size: 0.75rem;
  color: var(--el-text-color-secondary);
  margin-top: 0.25rem;
  line-height: 1.4;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}
</style>
