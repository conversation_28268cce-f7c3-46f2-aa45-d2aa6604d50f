<!--
  系统设置组件
  用于管理系统配置
-->
<template>
  <div class="system-settings">
    <div class="settings-header">
      <h2 class="section-title">系统设置</h2>
      <p class="section-description">管理系统的基本配置和参数</p>
    </div>

    <div class="settings-content">
      <!-- 过滤器设置 -->
      <el-card class="setting-card">
        <template #header>
          <div class="card-header">
            <h3 class="card-title">
              <el-icon><i-ep-filter /></el-icon>
              过滤器设置
            </h3>
          </div>
        </template>
        
        <div class="setting-items">
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-name">默认显示数量</h4>
              <p class="setting-desc">每个维度默认显示的过滤器数量</p>
            </div>
            <div class="setting-control">
              <el-input-number
                v-model="settings.defaultFilterCount"
                :min="5"
                :max="50"
                :step="5"
                @change="updateSetting('defaultFilterCount')"
              />
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-name">启用多语言过滤</h4>
              <p class="setting-desc">是否根据用户语言自动过滤显示内容</p>
            </div>
            <div class="setting-control">
              <el-switch
                v-model="settings.enableLanguageFilter"
                @change="updateSetting('enableLanguageFilter')"
              />
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-name">缓存过期时间</h4>
              <p class="setting-desc">过滤器数据缓存的过期时间（分钟）</p>
            </div>
            <div class="setting-control">
              <el-input-number
                v-model="settings.cacheExpireMinutes"
                :min="5"
                :max="1440"
                :step="5"
                @change="updateSetting('cacheExpireMinutes')"
              />
            </div>
          </div>
        </div>
      </el-card>

      <!-- 内容设置 -->
      <el-card class="setting-card">
        <template #header>
          <div class="card-header">
            <h3 class="card-title">
              <el-icon><i-ep-document /></el-icon>
              内容设置
            </h3>
          </div>
        </template>
        
        <div class="setting-items">
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-name">每页显示数量</h4>
              <p class="setting-desc">内容列表每页显示的内容数量</p>
            </div>
            <div class="setting-control">
              <el-input-number
                v-model="settings.contentPageSize"
                :min="10"
                :max="100"
                :step="10"
                @change="updateSetting('contentPageSize')"
              />
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-name">启用内容预览</h4>
              <p class="setting-desc">是否在列表中显示内容预览</p>
            </div>
            <div class="setting-control">
              <el-switch
                v-model="settings.enableContentPreview"
                @change="updateSetting('enableContentPreview')"
              />
            </div>
          </div>
        </div>
      </el-card>

      <!-- 用户设置 -->
      <el-card class="setting-card">
        <template #header>
          <div class="card-header">
            <h3 class="card-title">
              <el-icon><i-ep-user /></el-icon>
              用户设置
            </h3>
          </div>
        </template>
        
        <div class="setting-items">
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-name">允许用户注册</h4>
              <p class="setting-desc">是否允许新用户注册账号</p>
            </div>
            <div class="setting-control">
              <el-switch
                v-model="settings.allowUserRegistration"
                @change="updateSetting('allowUserRegistration')"
              />
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-name">默认用户角色</h4>
              <p class="setting-desc">新注册用户的默认角色</p>
            </div>
            <div class="setting-control">
              <el-select
                v-model="settings.defaultUserRole"
                @change="updateSetting('defaultUserRole')"
              >
                <el-option label="普通用户" value="user" />
                <el-option label="内容创作者" value="creator" />
              </el-select>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <div class="settings-actions">
        <el-button @click="resetSettings" :loading="loading">
          重置为默认值
        </el-button>
        <el-button type="primary" @click="saveAllSettings" :loading="loading">
          保存所有设置
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const settings = ref({
  // 过滤器设置
  defaultFilterCount: 20,
  enableLanguageFilter: true,
  cacheExpireMinutes: 60,
  
  // 内容设置
  contentPageSize: 20,
  enableContentPreview: true,
  
  // 用户设置
  allowUserRegistration: true,
  defaultUserRole: 'user'
})

// 默认设置
const defaultSettings = {
  defaultFilterCount: 20,
  enableLanguageFilter: true,
  cacheExpireMinutes: 60,
  contentPageSize: 20,
  enableContentPreview: true,
  allowUserRegistration: true,
  defaultUserRole: 'user'
}

// 更新单个设置
const updateSetting = async (key) => {
  try {
    // 这里可以调用API保存单个设置
    console.log(`更新设置 ${key}:`, settings.value[key])
    ElMessage.success('设置已更新')
  } catch (error) {
    console.error('更新设置失败:', error)
    ElMessage.error('更新设置失败')
  }
}

// 重置设置
const resetSettings = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有设置为默认值吗？',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    settings.value = { ...defaultSettings }
    ElMessage.success('设置已重置为默认值')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置设置失败:', error)
      ElMessage.error('重置设置失败')
    }
  }
}

// 保存所有设置
const saveAllSettings = async () => {
  loading.value = true
  try {
    // 这里可以调用API保存所有设置
    console.log('保存所有设置:', settings.value)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('所有设置已保存')
  } catch (error) {
    console.error('保存设置失败:', error)
    ElMessage.error('保存设置失败')
  } finally {
    loading.value = false
  }
}

// 加载设置
const loadSettings = async () => {
  loading.value = true
  try {
    // 这里可以调用API加载设置
    console.log('加载系统设置')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 使用默认设置
    settings.value = { ...defaultSettings }
  } catch (error) {
    console.error('加载设置失败:', error)
    ElMessage.error('加载设置失败')
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.system-settings {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.settings-header {
  margin-bottom: 1rem;
}

.section-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.section-description {
  margin: 0;
  color: var(--el-text-color-secondary);
  font-size: 0.875rem;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.setting-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.setting-items {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.setting-info {
  flex: 1;
}

.setting-name {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.setting-desc {
  margin: 0;
  font-size: 0.75rem;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

.setting-control {
  flex-shrink: 0;
  min-width: 120px;
}

.settings-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .setting-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .setting-control {
    min-width: auto;
  }
  
  .settings-actions {
    flex-direction: column;
  }
}
</style>
