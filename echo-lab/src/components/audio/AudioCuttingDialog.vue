<!--
  音频切割对话框组件
  使用 WaveSurfer.js 实现专业的音频波形图和切割功能
-->
<template>
  <standard-dialog v-model="dialogVisible" title="音频切割工具" width="90%" :before-close="handleDialogClose">

    <div class="audio-cutting-container">
      <!-- 音频文件管理区域 -->
      <div class="audio-files-section">
        <h4>📁 音频文件管理</h4>
        <div class="audio-files-list">
          <!-- 音频文件列表 -->
          <div v-if="audioFiles.length > 0" class="files-grid">
            <div v-for="(file, index) in audioFiles" :key="file.id" class="audio-file-item"
              :class="{ 'active': currentAudioId === file.id }">
              <div class="file-info">
                <el-icon class="file-icon">
                  <i-ep-video-play />
                </el-icon>
                <span class="file-name">{{ file.name }}</span>
                <span class="file-duration" v-if="file.duration">{{ formatDuration(file.duration) }}</span>
              </div>
              <div class="file-actions">
                <el-button v-if="currentAudioId !== file.id" size="small" type="primary"
                  @click="switchToAudioFile(file.id)">
                  使用
                </el-button>
                <span v-else class="current-label">当前</span>
                <el-button size="small" type="danger" @click="removeAudioFile(file.id)">
                  删除
                </el-button>
              </div>
            </div>
          </div>

          <!-- 添加音频文件按钮 -->
          <div class="add-audio-section">
            <el-upload ref="uploadRef" class="audio-upload-compact" :auto-upload="false" :show-file-list="false"
              accept="audio/*" :on-change="handleFileSelect">
              <el-button type="primary" plain>
                <el-icon><i-ep-upload-filled /></el-icon>
                {{ audioFiles.length === 0 ? '上传音频文件' : '添加更多文件' }}
              </el-button>
              <div class="upload-tip">支持 MP3、WAV、M4A 格式</div>
            </el-upload>
          </div>
        </div>
      </div>

      <!-- 步骤1: 音频处理区域 -->
      <div class="cutting-section">
        <div class="step-header">
          <h3>步骤1: 添加音频区域</h3>
        </div>
        <!-- 当前音频信息 -->
        <div v-if="currentAudioFile" class="current-audio-info">
          <div class="audio-file-info">
            <span class="file-name">🎵 当前音频: {{ currentAudioFile.name }}</span>
            <span class="file-duration" v-if="audioDuration > 0">
              ({{ formatDuration(audioDuration) }})
            </span>
            <el-button size="small" @click="switchToFileManagement">切换音频文件</el-button>
          </div>
        </div>

        <!-- WaveSurfer 波形图容器 -->
        <div class="waveform-container" :class="{ 'loading-state': isWaveSurferLoading }">
          <!-- 操作提示 -->
          <div class="waveform-tips">
            <el-text size="small" type="info">
              💡 拖拽波形图选择音频区域，然后建立与文本的对应关系
            </el-text>
          </div>
          <div id="waveform-container" ref="waveformRef" class="waveform">
            <!-- Loading状态 -->
            <div v-if="isWaveSurferLoading" class="loading-indicator">
              <div class="loading-content">
                <el-icon class="loading-icon">
                  <i-ep-loading />
                </el-icon>
                <span class="loading-text">{{ loadingText }}</span>
              </div>
            </div>
            <!-- 空状态 -->
            <div v-else-if="!currentAudioFile" class="empty-state">
              <el-icon class="empty-icon">
                <i-ep-upload />
              </el-icon>
              <p class="empty-text">请上传音频文件开始切割</p>
              <p class="empty-hint">支持 MP3、WAV、M4A 格式</p>
            </div>
          </div>
        </div>

        <!-- 控制按钮 -->
        <div class="waveform-controls">
          <div class="control-buttons">
            <el-button-group>
              <el-button size="small" @click="playPause">
                <el-icon>
                  <i-ep-video-play v-if="!isPlaying" />
                  <i-ep-video-pause v-else />
                </el-icon>
                {{ isPlaying ? '暂停' : '播放' }}
              </el-button>
              <el-button size="small" @click="addRegionAtCursor">
                <el-icon>
                  <i-ep-crop />
                </el-icon>
                添加区域
              </el-button>
              <el-button size="small" @click="clearAllRegions">
                <el-icon>
                  <i-ep-refresh-left />
                </el-icon>
                清除所有区域
              </el-button>
              <el-button size="small" type="warning" plain @click="showDeleteRegionTip">
                <el-icon>
                  <i-ep-delete />
                </el-icon>
                删除单个区域
              </el-button>
              <el-button size="small" @click="zoomIn">
                <el-icon>
                  <i-ep-zoom-in />
                </el-icon>
                放大
              </el-button>
              <el-button size="small" @click="zoomOut">
                <el-icon>
                  <i-ep-zoom-out />
                </el-icon>
                缩小
              </el-button>
            </el-button-group>
          </div>

        </div>
      </div>

      <!-- 步骤2: 区域列表和文本对应 -->
      <div class="mapping-section">
        <div class="step-header">
          <h3>步骤2: 建立对应关系</h3>
        </div>
        <div class="mapping-header">
          <h4>音频区域与文本对应 ({{ allRegions.length }} 个区域)</h4>
          <div class="mapping-actions">
            <el-button size="small" type="primary" @click="autoMapping">
              自动对应
            </el-button>
            <el-button size="small" type="success" @click="createManualMapping"
              :disabled="selectedTextIndex === -1 || selectedRegionIndex === -1">
              建立对应
            </el-button>
            <el-button size="small" @click="clearAllMappings">
              清除对应
            </el-button>
          </div>
        </div>

        <div class="mapping-content">
          <!-- 文本分句列表 -->
          <div class="text-segments">
            <h5>文本分句 ({{ textSegments.length }} 个)
              <span v-if="selectedTextIndex !== -1" class="selection-hint">
                - 已选择第{{ selectedTextIndex + 1 }}个
              </span>
            </h5>
            <div class="segment-list">
              <div v-for="(segment, index) in textSegments" :key="segment.id" class="text-segment-item" :class="{
                'selected': selectedTextIndex === index,
                'mapped': isMapped('text', index)
              }" @click="selectTextSegment(index)">
                <div class="segment-number">{{ index + 1 }}</div>
                <div class="segment-content">
                  <div class="segment-text">{{ segment.content }}</div>
                  <div class="segment-meta">
                    <el-tag size="small" :type="getSpeakerTagType(segment.speaker)">
                      {{ segment.speaker || '默认' }}
                    </el-tag>
                    <span class="segment-language">{{ getLanguageLabel(segment.language) }}</span>
                    <span v-if="isMapped('text', index)" class="mapping-indicator">
                      ↔ {{ getMappedRegionDisplayText(index) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 音频区域列表 -->
          <div class="audio-regions">
            <h5>音频区域 ({{ allRegions.length }} 个)
              <span v-if="selectedRegionIndex !== -1" class="selection-hint">
                - 已选择第{{ selectedRegionIndex + 1 }}个
              </span>
            </h5>

            <div class="segment-list">
              <div v-for="(region, index) in allRegions" :key="`${region.fileId}_${region.id}`"
                class="audio-segment-item" :class="{
                  'selected': selectedRegionIndex === index,
                  'mapped': isMapped('region', index),
                  'current-file': region.fileId === currentAudioId
                }" @click="selectRegion(index)">
                <div class="segment-number">{{ region.fileRegionIndex + 1 }}</div>
                <div class="segment-content">
                  <div class="segment-file">
                    <el-tag size="small" :type="region.fileId === currentAudioId ? 'primary' : 'info'">
                      {{ region.fileName }}
                    </el-tag>
                  </div>
                  <div class="segment-time">
                    {{ formatTime(region.start) }} - {{ formatTime(region.end) }}
                    <span v-if="isMapped('region', index)" class="mapping-indicator">
                      ↔ 文本{{ getMappedTextIndex(index) + 1 }}
                    </span>
                  </div>
                  <div class="segment-duration">
                    时长: {{ formatDuration(region.end - region.start) }}
                  </div>
                  <div class="region-actions">
                    <el-button size="small" @click.stop="playRegion(region)"
                      :disabled="region.fileId !== currentAudioId">
                      <el-icon>
                        <i-ep-video-play />
                      </el-icon>
                      试听
                    </el-button>
                    <el-button size="small" type="danger" @click.stop="removeRegion(region)"
                      :disabled="region.fileId !== currentAudioId">
                      <el-icon>
                        <i-ep-delete />
                      </el-icon>
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤3: 对应关系映射 -->
        <div class="step-header">
          <h3>步骤3: 保存音频</h3>
        </div>
        <div class="mapping-result">
          <div class="mapping-header">
            <h5>对应关系 ({{ allMappings.length }} 个)</h5>
            <el-button type="primary" size="small" @click="saveAudioSegments" :disabled="allMappings.length === 0"
              :loading="isUploading">
              {{ getSaveButtonText() }}
            </el-button>
          </div>
          <div class="mapping-list">
            <div v-for="(mapping, index) in allMappings" :key="index" class="mapping-item">
              <div class="mapping-text">
                {{ index + 1 }}. {{ getTextSegmentContent(mapping.textIndex) }}
              </div>
              <div class="mapping-arrow">→</div>
              <div class="mapping-audio">
                <el-tag size="small" :type="mapping.fileId === currentAudioId ? 'primary' : 'info'">
                  {{ mapping.fileName }}
                </el-tag>
                区域{{ (allRegions[mapping.regionIndex]?.fileRegionIndex || 0) + 1 }}
                ({{ formatTime(allRegions[mapping.regionIndex]?.start) }} -
                {{ formatTime(allRegions[mapping.regionIndex]?.end) }})
              </div>

              <!-- 处理状态显示 -->
              <div class="mapping-status">
                <template v-if="getSegmentStatus(mapping.textIndex) === 'pending'">
                  <el-tag size="small" type="info">⏸️ 等待中</el-tag>
                </template>
                <template v-else-if="getSegmentStatus(mapping.textIndex) === 'uploading'">
                  <el-tag size="small" type="warning">⏳ 上传中</el-tag>
                </template>
                <template v-else-if="getSegmentStatus(mapping.textIndex) === 'success'">
                  <el-tag size="small" type="success">✅ 已保存</el-tag>
                </template>
                <template v-else-if="getSegmentStatus(mapping.textIndex) === 'failed'">
                  <el-tag size="small" type="danger">❌ 失败</el-tag>
                  <el-button size="small" type="primary" @click="retrySegment(mapping.textIndex)"
                    style="margin-left: 4px;">
                    重试
                  </el-button>
                </template>

              </div>

              <el-button size="small" type="danger" @click="removeMapping(index)"
                :disabled="mapping.fileId !== currentAudioId || processingState.isProcessing">
                <el-icon>
                  <i-ep-delete />
                </el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <div class="footer-left">
          <!-- 对应关系统计 -->
          <div class="footer-info">
            <div class="info-line">
              <span class="info-label">🔗 对应关系：</span>
              <span class="info-value">{{ totalMappingsCount }} 个</span>
              <span class="mapping-detail">
                / {{ props.textSegments.length }} 个文本分句
              </span>
              <span v-if="totalMappingsCount < props.textSegments.length" class="warning-text">
                （还有 {{ props.textSegments.length - totalMappingsCount }} 个未对应）
              </span>
            </div>
          </div>
        </div>
        <div class="footer-right">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="confirmData" :disabled="!hasSuccessfulSegments">
            确认数据
          </el-button>
        </div>
      </div>
    </template>
  </standard-dialog>
</template>

<script setup>
import { ref, computed, watch, onUnmounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import StandardDialog from '../common/StandardDialog.vue';
import { getLanguageLabel } from '@/config/languages';
import { getSpeakerTagType } from '../nodes/ResourceNodeUtils';
import httpClient from '@/utils/httpClient';
import * as lamejs from '@breezystack/lamejs';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  textSegments: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue', 'confirm']);

// 对话框状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 多音频文件管理
const audioFiles = ref([]); // 所有音频文件列表
const currentAudioId = ref(null); // 当前使用的音频文件ID
const audioDuration = ref(0);
const isPlaying = ref(false);

// 计算当前音频文件
const currentAudioFile = computed(() => {
  return audioFiles.value.find(file => file.id === currentAudioId.value) || null;
});

// WaveSurfer 相关
const waveformRef = ref(null);
let wavesurfer = null;
let regionsPlugin = null;
const isWaveSurferLoading = ref(false);
const loadingText = ref('正在初始化音频处理器...');

// 区域和映射
const regions = ref([]);
const selectedTextIndex = ref(-1);
const selectedRegionIndex = ref(-1);
const mappings = ref([]);

// 上传状态
const isUploading = ref(false);

// 按时间排序的区域（当前文件）
const sortedRegions = computed(() => {
  return [...regions.value].sort((a, b) => {
    // 1. 首先按开始时间排序
    if (a.start !== b.start) {
      return a.start - b.start;
    }

    // 2. 开始时间相同时，按结束时间排序（短的在前）
    if (a.end !== b.end) {
      return a.end - b.end;
    }

    // 3. 完全相同时，按创建时间排序
    return a.createdAt - b.createdAt;
  });
});



// 统计信息计算属性
const totalMappingsCount = computed(() => {
  return audioFiles.value.reduce((total, file) => {
    // 如果是当前文件，使用实时数据，否则使用保存的数据
    if (file.id === currentAudioId.value) {
      return total + mappings.value.length;
    } else {
      return total + (file.mappings ? file.mappings.length : 0);
    }
  }, 0);
});

// 所有文件的区域（用于显示）
const allRegions = computed(() => {
  const allRegionsList = [];

  audioFiles.value.forEach(file => {
    // 获取文件的区域数据
    let fileRegions;

    // 如果是当前文件，使用实时的排序区域
    if (file.id === currentAudioId.value) {
      fileRegions = sortedRegions.value;
    } else {
      // 其他文件使用保存的区域数据，并进行排序
      fileRegions = (file.regions || []).slice().sort((a, b) => {
        if (a.start !== b.start) return a.start - b.start;
        if (a.end !== b.end) return a.end - b.end;
        return a.createdAt - b.createdAt;
      });
    }

    // 添加到全局列表
    fileRegions.forEach((region, index) => {
      allRegionsList.push({
        ...region,
        fileId: file.id,
        fileName: file.name,
        fileRegionIndex: index, // 在该文件中按时间排序的索引
        globalIndex: allRegionsList.length // 全局索引
      });
    });
  });

  return allRegionsList;
});

// 所有文件的映射关系（用于显示）
const allMappings = computed(() => {
  const allMappingsList = [];

  audioFiles.value.forEach(file => {
    // 获取文件的映射数据
    let fileMappings = file.mappings || [];

    // 如果是当前文件，使用实时映射
    if (file.id === currentAudioId.value && mappings.value.length > 0) {
      fileMappings = mappings.value;
    }

    if (fileMappings.length > 0) {
      fileMappings.forEach(mapping => {
        // 使用区域ID查找全局索引
        const globalRegionIndex = allRegions.value.findIndex(region =>
          region.fileId === file.id && region.id === mapping.regionId
        );

        if (globalRegionIndex !== -1) {
          allMappingsList.push({
            textIndex: mapping.textIndex,
            regionIndex: globalRegionIndex, // 全局索引用于显示
            regionId: mapping.regionId,
            fileId: file.id,
            fileName: file.name,
            createdAt: mapping.createdAt
          });
        }
      });
    }
  });

  return allMappingsList;
});

// 监听当前音频文件变化，当有文件时才初始化 WaveSurfer
watch(currentAudioFile, async (newFile) => {
  if (newFile && !wavesurfer && dialogVisible.value) {
    // 等待 DOM 更新完成
    await nextTick();
    await loadWaveSurfer();
  }
}, { flush: 'post' });

// 监听对话框关闭，清理资源
watch(dialogVisible, (newValue) => {
  if (!newValue) {
    // 对话框关闭时清理 WaveSurfer 实例
    if (wavesurfer) {
      wavesurfer.destroy();
      wavesurfer = null;
      regionsPlugin = null;
    }
    // 清理全局 AudioContext
    cleanupAudioContext();
  }
});

// 全局 AudioContext 管理器
let globalAudioContext = null;

// 获取或创建全局 AudioContext
function getAudioContext() {
  if (!globalAudioContext || globalAudioContext.state === 'closed') {
    globalAudioContext = new AudioContext({
      sampleRate: 24000
    });
  }
  return globalAudioContext;
}

// 清理全局 AudioContext
function cleanupAudioContext() {
  if (globalAudioContext && globalAudioContext.state !== 'closed') {
    globalAudioContext.close().catch(error => {
      console.warn('关闭全局音频上下文失败:', error);
    });
    globalAudioContext = null;
  }
}

onUnmounted(() => {
  if (wavesurfer) {
    wavesurfer.destroy();
  }
  // 清理全局 AudioContext
  cleanupAudioContext();
});

// 动态加载 WaveSurfer.js
async function loadWaveSurfer() {
  if (wavesurfer) return;

  try {
    const WaveSurfer = (await import('wavesurfer.js')).default;
    const RegionsPlugin = (await import('wavesurfer.js/dist/plugins/regions.js')).default;
    const TimelinePlugin = (await import('wavesurfer.js/dist/plugins/timeline.js')).default;

    await initWaveSurfer(WaveSurfer, RegionsPlugin, TimelinePlugin);

    console.log('音频处理器初始化完成');
  } catch (error) {
    console.error('加载 WaveSurfer.js 失败:', error);
    throw new Error('加载音频处理库失败: ' + error.message);
  }
}

// 初始化 WaveSurfer
async function initWaveSurfer(WaveSurfer, RegionsPlugin, TimelinePlugin) {
  // 检查容器元素是否存在
  if (!waveformRef.value) {
    throw new Error('波形图容器元素未找到');
  }

  // 创建 regions 插件
  regionsPlugin = RegionsPlugin.create({
    dragSelection: {
      slop: 5
    }
  });

  // 创建 WaveSurfer 实例
  wavesurfer = WaveSurfer.create({
    container: waveformRef.value,
    waveColor: '#409eff',
    progressColor: '#67c23a',
    cursorColor: '#f56c6c',
    barWidth: 2,
    barRadius: 3,
    barAlign: 'center',
    responsive: true,
    backend: 'WebAudio', // 更精确同步
    partialRender: true, // 提高性能
    height: 80, // 更紧凑的波形图高度
    normalize: true,
    plugins: [
      regionsPlugin,
      TimelinePlugin.create({
        height: 20,
        insertPosition: 'beforebegin'
      })
    ]
  });

  // 绑定事件
  setupWaveSurferEvents();
}

// 设置 WaveSurfer 事件监听
function setupWaveSurferEvents() {
  if (!wavesurfer || !regionsPlugin) return;

  // 播放状态事件
  wavesurfer.on('play', () => {
    isPlaying.value = true;
  });

  wavesurfer.on('pause', () => {
    isPlaying.value = false;
  });

  wavesurfer.on('finish', () => {
    isPlaying.value = false;
  });

  wavesurfer.on('ready', () => {
    const duration = wavesurfer.getDuration();
    audioDuration.value = duration;

    // 同时更新文件数据中的时长
    if (currentAudioId.value) {
      const currentFile = audioFiles.value.find(f => f.id === currentAudioId.value);
      if (currentFile) {
        currentFile.duration = duration;
      }
    }

    console.log('WaveSurfer ready, duration:', duration);
    ElMessage.success(`音频加载完成 (${formatDuration(duration)})`);
  });

  // 添加错误处理
  wavesurfer.on('error', (error) => {
    console.error('WaveSurfer 错误:', error);
    ElMessage.error('音频加载失败: ' + error.message);
  });

  // 添加加载事件
  wavesurfer.on('loading', (percent) => {
    console.log('音频加载进度:', percent + '%');
  });

  // 区域事件
  regionsPlugin.on('region-created', (region) => {
    // 为新区域添加颜色和标识
    region.setOptions({
      color: getRandomRegionColor(),
      drag: true,
      resize: true
    });

    // 添加到区域列表
    regions.value.push({
      id: region.id,
      start: region.start,
      end: region.end,
      region: region,
      createdAt: Date.now() // 添加创建时间用于排序
    });


  });

  regionsPlugin.on('region-updated', (region) => {
    // 更新区域信息
    const regionIndex = regions.value.findIndex(r => r.id === region.id);
    if (regionIndex !== -1) {
      regions.value[regionIndex].start = region.start;
      regions.value[regionIndex].end = region.end;
    }
  });

  regionsPlugin.on('region-removed', (region) => {
    // 从区域列表中移除
    const regionIndex = regions.value.findIndex(r => r.id === region.id);
    if (regionIndex !== -1) {
      const removedRegion = regions.value[regionIndex];
      regions.value.splice(regionIndex, 1);

      // 移除相关的映射（使用区域ID匹配）
      mappings.value = mappings.value.filter(mapping =>
        mapping.regionId !== removedRegion.id
      );
    }
  });

  regionsPlugin.on('region-clicked', (region) => {
    // 点击区域时选中（需要找到在全局区域列表中的索引）
    const globalRegionIndex = allRegions.value.findIndex(r =>
      r.id === region.id && r.fileId === currentAudioId.value
    );
    selectedRegionIndex.value = globalRegionIndex;
  });


}

// 文件选择处理
async function handleFileSelect(file) {
  try {
    console.log('选择文件对象:', file);
    console.log('文件属性:', {
      name: file.name,
      size: file.size,
      type: file.type,
      raw: file.raw
    });

    // 获取实际的文件对象
    const actualFile = file.raw || file;
    const fileName = file.name || actualFile.name;
    const fileType = file.type || actualFile.type || '';
    const fileSize = file.size || actualFile.size || 0;

    console.log('实际文件信息:', {
      name: fileName,
      size: fileSize,
      type: fileType
    });

    // 检查文件类型（如果有的话）
    if (fileType && !fileType.startsWith('audio/')) {
      // 如果没有type信息，通过文件扩展名判断
      const audioExtensions = ['.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac'];
      const hasAudioExtension = audioExtensions.some(ext =>
        fileName.toLowerCase().endsWith(ext)
      );

      if (!hasAudioExtension) {
        ElMessage.error('请选择音频文件');
        return;
      }
    }

    // 检查文件大小 (50MB限制)
    if (fileSize > 50 * 1024 * 1024) {
      ElMessage.error('音频文件大小不能超过50MB');
      return;
    }

    // 生成唯一ID
    const fileId = `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 添加到音频文件列表
    const audioFileData = {
      id: fileId,
      name: fileName,
      file: actualFile,
      duration: 0,
      regions: [], // 每个文件独立的区域
      mappings: [] // 每个文件独立的映射
    };

    console.log('创建音频文件数据:', audioFileData);
    audioFiles.value.push(audioFileData);

    // 如果是第一个文件，自动设为当前文件
    if (audioFiles.value.length === 1) {
      await switchToAudioFile(fileId);
    } else {
      ElMessage.success(`音频文件 "${fileName}" 已添加，点击"使用"按钮切换到该文件`);
    }

  } catch (error) {
    console.error('添加音频文件失败:', error);
    ElMessage.error('添加音频文件失败: ' + error.message);
  }
}

// 切换到指定音频文件
async function switchToAudioFile(fileId) {
  try {
    const targetFile = audioFiles.value.find(f => f.id === fileId);
    if (!targetFile) {
      ElMessage.error('音频文件不存在');
      return;
    }

    // 开始加载，显示loading
    isWaveSurferLoading.value = true;
    loadingText.value = '正在准备音频文件...';

    // 保存当前文件的状态
    if (currentAudioId.value) {
      saveCurrentFileState();
    }

    // 切换到新文件
    currentAudioId.value = fileId;

    // 确保 WaveSurfer 已初始化
    if (!wavesurfer) {
      loadingText.value = '正在初始化音频处理器...';
      await loadWaveSurfer();
    }

    // 验证音频文件
    loadingText.value = '正在验证音频文件...';
    await validateAudioFile(targetFile.file);

    // 加载音频文件
    loadingText.value = `正在加载音频文件 "${targetFile.name}"...`;
    console.log('开始加载音频文件:', {
      name: targetFile.name,
      size: targetFile.file.size,
      type: targetFile.file.type || '未知类型',
      fileObject: targetFile.file
    });
    await wavesurfer.loadBlob(targetFile.file);

    // 生成波形图
    loadingText.value = '正在生成波形图...';

    // 恢复该文件的状态
    restoreFileState(targetFile);

    ElMessage.success(`已切换到音频文件: ${targetFile.name}`);

  } catch (error) {
    console.error('切换音频文件失败:', error);
    ElMessage.error('切换音频文件失败: ' + error.message);
  } finally {
    // 无论成功还是失败，都要隐藏loading
    isWaveSurferLoading.value = false;
  }
}

// 保存当前文件的状态
function saveCurrentFileState() {
  const currentFile = audioFiles.value.find(f => f.id === currentAudioId.value);
  if (currentFile) {
    // 保存区域数据时，移除对 WaveSurfer 实例的引用，避免内存泄漏
    currentFile.regions = regions.value.map(region => ({
      id: region.id,
      start: region.start,
      end: region.end,
      createdAt: region.createdAt
      // 不保存 region 属性（WaveSurfer 实例引用）
    }));
    currentFile.mappings = [...mappings.value];
    currentFile.duration = audioDuration.value;
    console.log(`已保存文件状态 ${currentFile.name}: ${regions.value.length} 个区域, ${mappings.value.length} 个映射`);
  } else {
    console.warn('保存状态失败：找不到当前文件', currentAudioId.value);
  }
}

// 恢复文件状态
function restoreFileState(fileData) {
  // 清除当前状态（静默清除，不显示消息）
  if (regionsPlugin) {
    regionsPlugin.clearRegions();
  }
  regions.value = [];
  mappings.value = [];

  // 恢复区域
  if (fileData.regions && fileData.regions.length > 0) {
    fileData.regions.forEach(regionData => {
      if (regionsPlugin) {
        const region = regionsPlugin.addRegion({
          start: regionData.start,
          end: regionData.end,
          color: getRandomRegionColor(),
          drag: true,
          resize: true
        });
        // 注意：regionsPlugin.addRegion 会触发 region-created 事件
        // 该事件会自动将区域添加到 regions.value，所以这里不需要手动添加
      }
    });
  }

  // 恢复映射
  if (fileData.mappings && fileData.mappings.length > 0) {
    mappings.value = [...fileData.mappings];
  }

  // 恢复时长
  if (fileData.duration > 0) {
    audioDuration.value = fileData.duration;
  }

  console.log(`已恢复文件状态: ${fileData.regions?.length || 0} 个区域, ${fileData.mappings?.length || 0} 个映射`);
}

// 删除音频文件
function removeAudioFile(fileId) {
  ElMessageBox.confirm(
    '确定要删除这个音频文件吗？相关的区域和对应关系也会被删除。',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 如果删除的是当前文件，需要切换到其他文件或清空
    if (currentAudioId.value === fileId) {
      const otherFile = audioFiles.value.find(f => f.id !== fileId);
      if (otherFile) {
        switchToAudioFile(otherFile.id);
      } else {
        // 没有其他文件了，清空状态
        currentAudioId.value = null;
        clearAllRegions();
        mappings.value = [];
        audioDuration.value = 0;

        // 销毁 WaveSurfer
        if (wavesurfer) {
          wavesurfer.destroy();
          wavesurfer = null;
          regionsPlugin = null;
        }
      }
    }

    // 从列表中删除
    audioFiles.value = audioFiles.value.filter(f => f.id !== fileId);
    ElMessage.success('音频文件已删除');
  }).catch(() => {
    // 用户取消
  });
}

// 切换到文件管理界面
function switchToFileManagement() {
  // 保存当前状态
  if (currentAudioId.value) {
    saveCurrentFileState();
  }

  // 清空当前选择，显示文件管理界面
  currentAudioId.value = null;

  // 销毁 WaveSurfer
  if (wavesurfer) {
    wavesurfer.destroy();
    wavesurfer = null;
    regionsPlugin = null;
  }
}

// 验证音频文件
async function validateAudioFile(file) {
  try {
    // 使用全局音频上下文来验证文件
    const audioContext = getAudioContext();
    const arrayBuffer = await file.arrayBuffer();

    // 尝试解码音频数据
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

    console.log('音频文件验证成功:', {
      duration: audioBuffer.duration,
      sampleRate: audioBuffer.sampleRate,
      channels: audioBuffer.numberOfChannels
    });

    return audioBuffer;
  } catch (error) {
    console.error('音频文件验证失败:', error);
    throw new Error('音频文件格式不支持或已损坏: ' + error.message);
  }
}

// 等待 WaveSurfer 初始化完成
async function waitForWaveSurfer() {
  let attempts = 0;
  const maxAttempts = 50; // 最多等待5秒

  while (!wavesurfer && attempts < maxAttempts) {
    await new Promise(resolve => setTimeout(resolve, 100));
    attempts++;
  }

  if (!wavesurfer) {
    throw new Error('WaveSurfer 初始化超时');
  }
}

// 播放/暂停控制
function playPause() {
  if (!wavesurfer) {
    ElMessage.warning('音频处理器未初始化');
    return;
  }

  wavesurfer.playPause();
}

// 在当前播放位置添加区域
function addRegionAtCursor() {
  if (!wavesurfer || !regionsPlugin) {
    ElMessage.warning('音频处理器未初始化');
    return;
  }

  const currentTime = wavesurfer.getCurrentTime();
  const duration = wavesurfer.getDuration();

  // 创建一个2秒的区域
  const regionDuration = Math.min(2, duration - currentTime);
  const start = currentTime;
  const end = currentTime + regionDuration;

  if (end > duration) {
    ElMessage.warning('无法在当前位置添加区域');
    return;
  }

  regionsPlugin.addRegion({
    start,
    end,
    color: getRandomRegionColor(),
    drag: true,
    resize: true
  });

  ElMessage.success(`已添加区域: ${formatTime(start)} - ${formatTime(end)}`);
}

// 清除所有区域
function clearAllRegions() {
  if (regionsPlugin) {
    regionsPlugin.clearRegions();
  }

  regions.value = [];
  mappings.value = [];
  ElMessage.success('已清除所有区域');
}

// 显示删除单个区域的提示
function showDeleteRegionTip() {
  ElMessage({
    message: '💡 要删除单个区域，请在下方"音频区域列表"中点击对应区域的删除按钮',
    type: 'info',
    duration: 4000,
    showClose: true
  });
}

// 放大波形图
function zoomIn() {
  if (!wavesurfer) {
    ElMessage.warning('音频处理器未初始化');
    return;
  }

  const currentZoom = wavesurfer.options.minPxPerSec || 50;
  wavesurfer.zoom(Math.min(currentZoom * 1.5, 500));
}

// 缩小波形图
function zoomOut() {
  if (!wavesurfer) {
    ElMessage.warning('音频处理器未初始化');
    return;
  }

  const currentZoom = wavesurfer.options.minPxPerSec || 50;
  wavesurfer.zoom(Math.max(currentZoom / 1.5, 10));
}

// 获取随机区域颜色
function getRandomRegionColor() {
  const colors = [
    'rgba(255, 0, 0, 0.3)',
    'rgba(0, 255, 0, 0.3)',
    'rgba(0, 0, 255, 0.3)',
    'rgba(255, 255, 0, 0.3)',
    'rgba(255, 0, 255, 0.3)',
    'rgba(0, 255, 255, 0.3)',
    'rgba(255, 165, 0, 0.3)',
    'rgba(128, 0, 128, 0.3)'
  ];

  return colors[Math.floor(Math.random() * colors.length)];
}

function selectTextSegment(index) {
  selectedTextIndex.value = index;
}

function selectRegion(index) {
  selectedRegionIndex.value = index;
}

// 播放指定区域
function playRegion(regionData) {
  if (!wavesurfer) {
    ElMessage.warning('音频处理器未初始化');
    return;
  }

  const region = regionData.region;
  const start = region ? region.start : regionData.start;
  const end = region ? region.end : regionData.end;

  // 停止当前播放
  if (wavesurfer.isPlaying()) {
    wavesurfer.pause();
  }

  // 设置播放位置到区域开始
  wavesurfer.setTime(start);

  // 开始播放
  wavesurfer.play();

  // 清理之前的监听器
  if (wavesurfer._currentStopPlayback) {
    wavesurfer.un('audioprocess', wavesurfer._currentStopPlayback);
  }

  // 在区域结束时自动暂停
  const stopPlayback = () => {
    if (wavesurfer.getCurrentTime() >= end) {
      wavesurfer.pause();
      wavesurfer.un('audioprocess', stopPlayback);
      wavesurfer._currentStopPlayback = null;
    }
  };

  wavesurfer._currentStopPlayback = stopPlayback;
  wavesurfer.on('audioprocess', stopPlayback);

  ElMessage.success(`播放区域: ${formatTime(start)} - ${formatTime(end)}`);
}

// 删除指定区域
function removeRegion(regionData) {
  if (!regionsPlugin) {
    ElMessage.warning('区域插件未初始化');
    return;
  }

  // 检查是否是当前文件的区域
  if (regionData.fileId !== currentAudioId.value) {
    ElMessage.warning('只能删除当前文件的区域');
    return;
  }

  // 如果有 WaveSurfer 区域实例，直接删除
  if (regionData.region && regionData.region.remove) {
    regionData.region.remove(); // 会触发 region-removed 事件，自动处理清理逻辑
    ElMessage.success('已删除区域');
    return;
  }

  // 如果没有 WaveSurfer 区域实例，手动从数据中删除
  // 这种情况发生在已保存的区域被重新加载后
  const regionIndex = regions.value.findIndex(r =>
    r.start === regionData.start && r.end === regionData.end
  );

  if (regionIndex !== -1) {
    const removedRegion = regions.value[regionIndex];

    // 从 regions.value 中删除
    regions.value.splice(regionIndex, 1);

    // 删除相关的映射（使用区域ID匹配）
    mappings.value = mappings.value.filter(mapping =>
      mapping.regionId !== removedRegion.id
    );

    ElMessage.success('已删除区域');
  } else {
    ElMessage.warning('找不到要删除的区域');
  }
}

function autoMapping() {
  // 保存当前文件状态
  if (currentAudioId.value) {
    saveCurrentFileState();
  }

  let totalMappings = 0;
  let textIndex = 0;

  // 按文件顺序，为每个文件内部的区域建立对应关系
  audioFiles.value.forEach(file => {
    let fileRegions = file.regions || [];
    if (fileRegions.length === 0) return;

    // 对文件内的区域按时间排序
    fileRegions = [...fileRegions].sort((a, b) => {
      if (a.start !== b.start) return a.start - b.start;
      if (a.end !== b.end) return a.end - b.end;
      return a.createdAt - b.createdAt;
    });

    // 清空该文件的映射
    file.mappings = [];

    // 为该文件的区域按顺序建立对应关系
    for (let i = 0; i < fileRegions.length && textIndex < props.textSegments.length; i++) {
      const region = fileRegions[i];

      if (file.id === currentAudioId.value) {
        // 当前文件：添加到实时映射
        mappings.value.push({
          textIndex: textIndex,
          regionId: region.id,
          createdAt: Date.now()
        });
      } else {
        // 其他文件：添加到文件映射
        file.mappings.push({
          textIndex: textIndex,
          regionId: region.id,
          createdAt: Date.now()
        });
      }

      textIndex++;
      totalMappings++;
    }
  });

  ElMessage.success(`已自动对应 ${totalMappings} 个片段`);
}

// 手动建立对应关系
function createManualMapping() {
  if (selectedTextIndex.value === -1 || selectedRegionIndex.value === -1) {
    ElMessage.warning('请先选择文本分句和音频区域');
    return;
  }

  // 获取选中的区域（使用全局索引转换为实际区域）
  const selectedRegion = allRegions.value[selectedRegionIndex.value];
  if (!selectedRegion) {
    ElMessage.warning('选中的区域不存在');
    return;
  }

  // 检查是否已经存在对应关系
  const existingTextMapping = mappings.value.find(m => m.textIndex === selectedTextIndex.value);
  const existingRegionMapping = mappings.value.find(m => m.regionId === selectedRegion.id);

  if (existingTextMapping) {
    // 找到已存在映射的区域在全局列表中的位置
    const existingGlobalRegion = allRegions.value.find(r => r.id === existingTextMapping.regionId);
    const existingRegionNumber = existingGlobalRegion ? existingGlobalRegion.fileRegionIndex + 1 : '?';
    ElMessage.warning(`文本${selectedTextIndex.value + 1}已经对应了区域${existingRegionNumber}`);
    return;
  }

  if (existingRegionMapping) {
    ElMessage.warning(`区域${selectedRegion.fileRegionIndex + 1}已经对应了文本${existingRegionMapping.textIndex + 1}`);
    return;
  }

  // 创建新的对应关系（使用区域ID）
  mappings.value.push({
    textIndex: selectedTextIndex.value,
    regionId: selectedRegion.id, // 使用区域ID而不是索引
    createdAt: Date.now()
  });

  ElMessage.success(`已建立对应：文本${selectedTextIndex.value + 1} ↔ 区域${selectedRegion.fileRegionIndex + 1}`);

  // 清除选择状态
  selectedTextIndex.value = -1;
  selectedRegionIndex.value = -1;
}

// 清除所有对应关系
function clearAllMappings() {
  // 保存当前文件状态
  if (currentAudioId.value) {
    saveCurrentFileState();
  }

  // 清除所有文件的映射
  audioFiles.value.forEach(file => {
    file.mappings = [];
  });

  // 清除当前文件的实时映射
  mappings.value = [];

  // 清除选择状态
  selectedTextIndex.value = -1;
  selectedRegionIndex.value = -1;

  ElMessage.success('已清除所有文件的对应关系');
}

// 检查是否已映射（处理全局索引）
function isMapped(type, index) {
  if (type === 'text') {
    // 检查所有文件的映射
    return allMappings.value.some(m => m.textIndex === index);
  } else if (type === 'region') {
    // 检查所有文件的映射
    return allMappings.value.some(m => m.regionIndex === index);
  }
  return false;
}

// 获取映射的区域索引（处理全局索引）
function getMappedRegionIndex(textIndex) {
  const mapping = allMappings.value.find(m => m.textIndex === textIndex);
  return mapping ? mapping.regionIndex : -1;
}

// 获取映射区域的显示文本
function getMappedRegionDisplayText(textIndex) {
  const mapping = allMappings.value.find(m => m.textIndex === textIndex);
  if (!mapping) return '';

  const region = allRegions.value[mapping.regionIndex];
  if (!region) return `区域${mapping.regionIndex + 1}`;

  // 显示格式：简化文件名-区域号
  let fileName = region.fileName.replace(/\.[^/.]+$/, ''); // 去掉文件扩展名

  // 如果文件名太长，只显示前8个字符
  if (fileName.length > 8) {
    fileName = fileName.substring(0, 8) + '...';
  }

  return `${fileName}-区域${region.fileRegionIndex + 1}`;
}

// 获取映射的文本索引（处理全局索引）
function getMappedTextIndex(regionIndex) {
  const mapping = allMappings.value.find(m => m.regionIndex === regionIndex);
  return mapping ? mapping.textIndex : -1;
}

function removeMapping(index) {
  // 获取要删除的映射
  const mappingToRemove = allMappings.value[index];
  if (!mappingToRemove) return;

  // 如果是当前文件的映射，从 mappings.value 中删除
  if (mappingToRemove.fileId === currentAudioId.value) {
    const localIndex = mappings.value.findIndex(m =>
      m.textIndex === mappingToRemove.textIndex &&
      m.regionId === mappingToRemove.regionId
    );
    if (localIndex !== -1) {
      mappings.value.splice(localIndex, 1);
    }
  } else {
    // 如果是其他文件的映射，从对应文件的数据中删除
    const file = audioFiles.value.find(f => f.id === mappingToRemove.fileId);
    if (file && file.mappings) {
      const fileLocalIndex = file.mappings.findIndex(m =>
        m.textIndex === mappingToRemove.textIndex &&
        m.regionId === mappingToRemove.regionId
      );
      if (fileLocalIndex !== -1) {
        file.mappings.splice(fileLocalIndex, 1);
      }
    }
  }
}

function getTextSegmentContent(textIndex) {
  return props.textSegments[textIndex]?.content || '';
}

// 根据ID获取文本段
function getTextSegmentById(textSegmentId) {
  return props.textSegments.find(segment => segment.id === textSegmentId);
}

// 获取文本段的处理状态
function getSegmentStatus(textIndex) {
  const textSegment = props.textSegments[textIndex];
  if (!textSegment) return null;

  return textSegmentStatus.value.get(textSegment.id) || null;
}

// 获取保存按钮文案
function getSaveButtonText() {
  if (!isUploading.value) {
    return '保存音频';
  }

  if (processingState.value.isProcessing) {
    const { currentBatch, totalBatches, processedFiles, totalFiles } = processingState.value;
    return `处理中 ${currentBatch}/${totalBatches} (${processedFiles}/${totalFiles})`;
  }

  return '处理中...';
}

// 检查是否有成功的音频段
const hasSuccessfulSegments = computed(() => {
  return savedAudioSegments.value.size > 0;
});

// 保存音频段（原来的 confirmCutting 功能）
async function saveAudioSegments() {
  if (allMappings.value.length === 0) {
    ElMessage.warning('请先建立文本与音频的对应关系');
    return;
  }

  try {
    isUploading.value = true;

    // 保存当前文件状态到 audioFileData.mappings 中
    if (currentAudioId.value) {
      saveCurrentFileState();
    }

    // 调用合并上传和保存接口
    const allExportedSegments = await uploadAndSaveAllAudioSegments();

    // 显示完成结果，让用户确认后再关闭
    const totalSegments = allExportedSegments.length;
    const failedCount = getTotalFailedCount();

    if (failedCount === 0) {
      // 全部成功
      ElMessage.success(`🎉 音频保存完成！成功处理 ${totalSegments} 个音频片段`);
    } else {
      // 有失败的文件
      ElMessage.warning(`📊 音频保存完成！成功：${totalSegments} 个，失败：${failedCount} 个。失败的可以重试。`);
    }

  } catch (error) {
    console.error('音频保存失败:', error);
    ElMessage.error('音频保存失败: ' + error.message);
  } finally {
    isUploading.value = false;
  }
}

// 确认数据（只通知外部，不做保存）
function confirmData() {
  // 收集所有成功保存的音频段数据
  const successfulSegments = [];

  savedAudioSegments.value.forEach((audioSegment, textSegmentId) => {
    // 使用完整的音频段信息
    successfulSegments.push(audioSegment);
  });

  if (successfulSegments.length === 0) {
    ElMessage.warning('没有成功的音频段可以确认');
    return;
  }

  // 通知外部组件
  emit('confirm', {
    audioSegments: successfulSegments
  });

  ElMessage.success(`已确认 ${successfulSegments.length} 个音频段数据`);

  // 清理状态
  resetAllState();
  dialogVisible.value = false;
}

// 重试单个文本段
async function retrySegment(textIndex) {
  const textSegment = props.textSegments[textIndex];
  if (!textSegment) return;

  try {
    // 设置状态为上传中
    textSegmentStatus.value.set(textSegment.id, PROCESSING_STATUS.UPLOADING);

    // 重新生成音频数据
    const audioData = await regenerateAudioForSegment(textSegment.id);
    if (!audioData) {
      throw new Error('无法重新生成音频数据');
    }

    // 上传单个文件
    const result = await uploadBatch([audioData.audioBlob], [audioData.metadata], 'retry');

    if (result.length > 0) {
      textSegmentStatus.value.set(textSegment.id, PROCESSING_STATUS.SUCCESS);
      // 保存完整的音频段信息
      savedAudioSegments.value.set(textSegment.id, result[0]);
      ElMessage.success(`文本段 ${textIndex + 1} 重试成功`);
    } else {
      throw new Error('上传失败');
    }

  } catch (error) {
    console.error('重试失败:', error);
    textSegmentStatus.value.set(textSegment.id, PROCESSING_STATUS.FAILED);
    ElMessage.error(`文本段 ${textIndex + 1} 重试失败: ${error.message}`);
  }
}

// 获取失败文件总数
function getTotalFailedCount() {
  let failedCount = 0;
  textSegmentStatus.value.forEach(status => {
    if (status === PROCESSING_STATUS.FAILED) {
      failedCount++;
    }
  });
  return failedCount;
}

// 从音频缓冲区中提取片段
function extractAudioSegment(audioBuffer, startTime, endTime, audioContext) {
  const sampleRate = audioBuffer.sampleRate;
  const startSample = Math.floor(startTime * sampleRate);
  const endSample = Math.floor(endTime * sampleRate);
  const segmentLength = endSample - startSample;

  // 使用传入的音频上下文创建新的音频缓冲区
  const segmentBuffer = audioContext.createBuffer(
    audioBuffer.numberOfChannels,
    segmentLength,
    sampleRate
  );

  // 复制音频数据
  for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
    const originalData = audioBuffer.getChannelData(channel);
    const segmentData = segmentBuffer.getChannelData(channel);

    for (let i = 0; i < segmentLength; i++) {
      segmentData[i] = originalData[startSample + i] || 0;
    }
  }

  return segmentBuffer;
}

// 将音频缓冲区转换为MP3格式
function audioBufferToMp3(buffer) {
  
  const sampleRate = buffer.sampleRate;
  const numberOfChannels = buffer.numberOfChannels;
  const length = buffer.length;
  
  // 创建MP3编码器，使用128kbps比特率
  const mp3encoder = new lamejs.Mp3Encoder(numberOfChannels, sampleRate, 128);
  
  // 准备音频数据
  const samples = new Int16Array(length * numberOfChannels);
  
  // 转换音频数据为16位整数
  for (let i = 0; i < length; i++) {
    for (let channel = 0; channel < numberOfChannels; channel++) {
      const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
      samples[i * numberOfChannels + channel] = sample * 0x7FFF;
    }
  }
  
  // 编码为MP3
  const mp3Data = [];
  const blockSize = 1152; // MP3编码块大小
  
  for (let i = 0; i < samples.length; i += blockSize * numberOfChannels) {
    const sampleBlock = samples.subarray(i, i + blockSize * numberOfChannels);
    let mp3buf;
    
    if (numberOfChannels === 1) {
      mp3buf = mp3encoder.encodeBuffer(sampleBlock);
    } else {
      // 分离左右声道
      const left = new Int16Array(blockSize);
      const right = new Int16Array(blockSize);
      
      for (let j = 0; j < blockSize && (i + j * 2) < samples.length; j++) {
        left[j] = sampleBlock[j * 2] || 0;
        right[j] = sampleBlock[j * 2 + 1] || 0;
      }
      
      mp3buf = mp3encoder.encodeBuffer(left, right);
    }
    
    if (mp3buf.length > 0) {
      mp3Data.push(mp3buf);
    }
  }
  
  // 完成编码
  const finalBuffer = mp3encoder.flush();
  if (finalBuffer.length > 0) {
    mp3Data.push(finalBuffer);
  }
  
  // 合并所有MP3数据
  const totalLength = mp3Data.reduce((sum, buf) => sum + buf.length, 0);
  const result = new Uint8Array(totalLength);
  let offset = 0;
  
  for (const buf of mp3Data) {
    result.set(buf, offset);
    offset += buf.length;
  }
  
  return new Blob([result], { type: 'audio/mp3' });
}

// 分批上传和保存音频片段（优化大量文件处理）
async function uploadAndSaveAllAudioSegments() {
  const allExportedSegments = [];
  const allAudioFiles = [];
  const allMetadata = [];

  // 收集所有需要处理的音频文件和元数据
  for (const audioFileData of audioFiles.value) {
    if (!audioFileData.mappings || audioFileData.mappings.length === 0) {
      continue;
    }

    try {
      // 使用全局音频上下文来处理该文件
      const audioContext = getAudioContext();
      const arrayBuffer = await audioFileData.file.arrayBuffer();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

      // 准备处理项目
      const processingItems = audioFileData.mappings.map((mapping, index) => ({
        mapping,
        index,
        region: audioFileData.regions.find(r => r.id === mapping.regionId),
        textSegment: props.textSegments[mapping.textIndex]
      })).filter(item => item.region && item.textSegment);

      // 处理每个音频片段
      for (const item of processingItems) {
        const { region, textSegment } = item;

        try {
          // 切割音频片段
          const segmentBuffer = extractAudioSegment(audioBuffer, region.start, region.end, audioContext);

          // 转换为MP3格式
          const audioBlob = audioBufferToMp3(segmentBuffer);

          // 添加到文件列表
          allAudioFiles.push(audioBlob);

          // 添加到元数据列表
          allMetadata.push({
            textSegmentId: textSegment.id,
            text: textSegment.content,
            language: textSegment.language,
            speed: textSegment.speed || 1.0,
            voice_db_id: textSegment.voice_db_id || 1,
            duration: region.end - region.start,
            audioSource: 'manual'
          });

        } catch (error) {
          console.error(`处理音频片段失败:`, error);
          throw error;
        }
      }
    } catch (error) {
      console.error(`处理音频文件失败:`, error);
      throw error;
    }
  }

  if (allAudioFiles.length === 0) {
    throw new Error('没有可处理的音频片段');
  }

  // 统一使用分批处理，无论文件数量多少
  const batchResult = await uploadInBatches(allAudioFiles, allMetadata, BATCH_CONFIG.BATCH_SIZE);

  // 处理分批结果
  if (batchResult.failedItems.length > 0) {
    showProcessingResult(batchResult);
  }

  return batchResult.successItems;
}

// 单批次上传
async function uploadBatch(audioFiles, metadata, batchIndex) {
  const formData = new FormData();

  // 添加音频文件
  audioFiles.forEach((audioBlob, index) => {
    formData.append('audioFiles', audioBlob, `audio_segment_${batchIndex}_${index}.mp3`);
  });

  // 添加元数据
  formData.append('audioMetadata', JSON.stringify(metadata));

  try {
    // 使用 httpClient 的 upload 方法，设置5分钟超时
    const result = await httpClient.upload('/api/audio/upload-and-save', formData, {
      timeout: 5 * 60 * 1000, // 5分钟超时
    });

    if (!result.success) {
      throw new Error(`批次 ${batchIndex + 1} 保存失败: ${result.error || '未知错误'}`);
    }

    // 转换结果格式
    const exportedSegments = result.results
      .filter(r => r.success)
      .map(r => ({
        url: r.audio.url,
        duration: r.audio.duration,
        textSegmentId: r.textSegmentId,
        audioSource: r.audio.audioSource
      }));

    return exportedSegments;
  } catch (error) {
    // 处理超时错误
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      throw new Error(`批次 ${batchIndex + 1} 上传超时，请检查网络连接或减少批次大小`);
    }
    // 处理其他错误
    throw new Error(`批次 ${batchIndex + 1} 上传失败: ${error.message}`);
  }
}

// 配置常量
const BATCH_CONFIG = {
  BATCH_SIZE: 5,        // 每批5个文件，更稳定
  BATCH_DELAY: 300      // 批次间延迟300ms
};

// 处理状态枚举
const PROCESSING_STATUS = {
  PENDING: 'pending',     // 等待中
  UPLOADING: 'uploading', // 上传中
  SUCCESS: 'success',     // 已保存
  FAILED: 'failed'        // 失败
};

// 文本段处理状态
const textSegmentStatus = ref(new Map());

// 成功保存的音频段数据
const savedAudioSegments = ref(new Map());

// 处理状态
const processingState = ref({
  isProcessing: false,
  currentBatch: 0,
  totalBatches: 0,
  processedFiles: 0,
  totalFiles: 0
});

// 分批上传处理（简化版本）
async function uploadInBatches(allAudioFiles, allMetadata, batchSize) {
  const allExportedSegments = [];
  const failedItems = [];
  const totalBatches = Math.ceil(allAudioFiles.length / batchSize);

  // 初始化处理状态
  processingState.value = {
    isProcessing: true,
    currentBatch: 0,
    totalBatches,
    processedFiles: 0,
    totalFiles: allAudioFiles.length
  };

  // 初始化所有文本段状态为等待中
  textSegmentStatus.value.clear();
  allMetadata.forEach(metadata => {
    textSegmentStatus.value.set(metadata.textSegmentId, PROCESSING_STATUS.PENDING);
  });

  // 自动处理所有批次
  for (let i = 0; i < totalBatches; i++) {
    processingState.value.currentBatch = i + 1;

    const startIndex = i * batchSize;
    const endIndex = Math.min(startIndex + batchSize, allAudioFiles.length);

    const batchAudioFiles = allAudioFiles.slice(startIndex, endIndex);
    const batchMetadata = allMetadata.slice(startIndex, endIndex);

    // 设置当前批次文件状态为上传中
    batchMetadata.forEach(metadata => {
      textSegmentStatus.value.set(metadata.textSegmentId, PROCESSING_STATUS.UPLOADING);
    });

    try {
      const batchResults = await uploadBatch(batchAudioFiles, batchMetadata, i);
      allExportedSegments.push(...batchResults);

      // 更新成功文件状态并保存完整的音频段信息
      batchResults.forEach(result => {
        textSegmentStatus.value.set(result.textSegmentId, PROCESSING_STATUS.SUCCESS);
        // 保存完整的音频段信息
        savedAudioSegments.value.set(result.textSegmentId, {
          url: result.url,
          duration: result.duration,
          textSegmentId: result.textSegmentId,
          audioSource: result.audioSource
        });
      });

      // 检查是否有部分失败的文件
      const successIds = new Set(batchResults.map(r => r.textSegmentId));
      batchMetadata.forEach(metadata => {
        if (!successIds.has(metadata.textSegmentId)) {
          textSegmentStatus.value.set(metadata.textSegmentId, PROCESSING_STATUS.FAILED);
          failedItems.push({
            textSegmentId: metadata.textSegmentId,
            text: metadata.text.substring(0, 20) + '...',
            error: '部分处理失败'
          });
        }
      });

      processingState.value.processedFiles += batchAudioFiles.length;

      // 批次间延迟
      if (i < totalBatches - 1) {
        await new Promise(resolve => setTimeout(resolve, BATCH_CONFIG.BATCH_DELAY));
      }

    } catch (error) {
      // 设置失败文件状态
      batchMetadata.forEach(metadata => {
        textSegmentStatus.value.set(metadata.textSegmentId, PROCESSING_STATUS.FAILED);
        failedItems.push({
          textSegmentId: metadata.textSegmentId,
          text: metadata.text.substring(0, 20) + '...',
          error: error.message
        });
      });

      processingState.value.processedFiles += batchAudioFiles.length;
    }
  }

  processingState.value.isProcessing = false;

  // 返回结果和失败信息
  return {
    successItems: allExportedSegments,
    failedItems,
    summary: {
      total: allAudioFiles.length,
      success: allExportedSegments.length,
      failed: failedItems.length
    }
  };
}

// 显示处理结果（简化版本）
function showProcessingResult(batchResult) {
  const { summary } = batchResult;

  if (summary.failed === 0) {
    ElMessage.success(`所有 ${summary.total} 个音频文件处理完成！`);
  } else {
    ElMessage.warning(`处理完成：${summary.success}个成功，${summary.failed}个失败。失败的文件可在列表中单独重试。`);
  }
}

// 重新生成指定文本段的音频数据
async function regenerateAudioForSegment(textSegmentId) {
  for (const audioFileData of audioFiles.value) {
    if (!audioFileData.mappings?.length) continue;

    const mapping = audioFileData.mappings.find(m => {
      const textSegment = props.textSegments[m.textIndex];
      return textSegment?.id === textSegmentId;
    });

    if (mapping) {
      const region = audioFileData.regions.find(r => r.id === mapping.regionId);
      const textSegment = props.textSegments[mapping.textIndex];

      if (region && textSegment) {
        try {
          const audioContext = getAudioContext();
          const arrayBuffer = await audioFileData.file.arrayBuffer();
          const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

          const segmentBuffer = extractAudioSegment(audioBuffer, region.start, region.end, audioContext);
          const audioBlob = audioBufferToMp3(segmentBuffer);

          return {
            audioBlob,
            metadata: {
              textSegmentId: textSegment.id,
              text: textSegment.content,
              language: textSegment.language,
              speed: textSegment.speed || 1.0,
              voice_db_id: textSegment.voice_db_id,
              duration: region.end - region.start,
              audioSource: 'manual'
            }
          };
        } catch (error) {
          console.error('处理音频片段失败:', error);
          throw error;
        }
      }
    }
  }

  return null;
}

// 重置所有状态
function resetAllState() {
  // 清空音频文件列表
  audioFiles.value = [];
  currentAudioId.value = null;

  // 清空当前状态
  regions.value = [];
  mappings.value = [];
  selectedTextIndex.value = -1;
  selectedRegionIndex.value = -1;
  audioDuration.value = 0;
  isPlaying.value = false;

  // 清空处理状态
  textSegmentStatus.value.clear();
  savedAudioSegments.value.clear();

  // 销毁 WaveSurfer
  if (wavesurfer) {
    wavesurfer.destroy();
    wavesurfer = null;
    regionsPlugin = null;
  }
}

function handleDialogClose() {
  const hasAnyData = audioFiles.value.length > 0 ||
    mappings.value.length > 0 ||
    audioFiles.value.some(file =>
      (file.mappings && file.mappings.length > 0) ||
      (file.regions && file.regions.length > 0)
    );

  if (hasAnyData) {
    ElMessageBox.confirm(
      '确定要关闭吗？未保存的更改将会丢失。',
      '关闭确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      resetAllState();
      dialogVisible.value = false;
    }).catch(() => {
      // 用户取消
    });
  } else {
    dialogVisible.value = false;
  }
}

// 工具函数
function formatTime(seconds) {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

function formatDuration(seconds) {
  return `${seconds.toFixed(1)}秒`;
}
</script>

<style scoped>
.audio-cutting-container {
  min-height: 500px;
}

/* 工作流步骤样式 */
.workflow-container {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.workflow-step {
  background-color: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  overflow: hidden;
}

.step-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.step-number {
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1rem;
  flex-shrink: 0;
}

.step-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #303133;
}

.step-description {
  margin: 0;
  font-size: 0.875rem;
  color: #606266;
  margin-left: auto;
}

.step-content {
  padding: 1.5rem;
}

.step-arrow {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 2rem;
  margin: -0.5rem 0;
  position: relative;
  z-index: 1;
}

.arrow-icon {
  font-size: 1.5rem;
  color: #409eff;
  background-color: #ffffff;
  border: 2px solid #409eff;
  border-radius: 50%;
  padding: 0.25rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 保存音频步骤样式 */
.save-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px solid #e4e7ed;
}

.summary-stats {
  flex: 1;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-weight: 500;
  color: #606266;
}

.stat-value {
  font-weight: bold;
  color: #409eff;
  font-size: 1.125rem;
}

.stat-detail {
  color: #909399;
  font-size: 0.875rem;
}

.stat-warning {
  color: #e6a23c;
  font-size: 0.875rem;
  font-weight: 500;
}

.save-action {
  display: flex;
  align-items: center;
}

.save-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.save-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.info-stats {
  flex: 1;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-weight: 500;
  color: #606266;
}

.stat-value {
  font-weight: bold;
  color: #409eff;
}

.stat-detail {
  color: #909399;
  font-size: 0.875rem;
}

.stat-warning {
  color: #e6a23c;
  font-size: 0.875rem;
  font-weight: 500;
}

.save-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.progress-info {
  width: 100%;
  max-width: 300px;
}

.progress-text {
  margin: 0.5rem 0 0 0;
  font-size: 0.875rem;
  color: #606266;
  text-align: center;
}

.save-results {
  border-top: 1px solid #e4e7ed;
  padding-top: 1.5rem;
}

.save-results h5 {
  margin: 0 0 1rem 0;
  color: #303133;
  font-size: 1rem;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 200px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.result-text {
  flex: 1;
  font-size: 0.875rem;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.result-duration {
  font-size: 0.75rem;
  color: #909399;
  font-weight: 500;
}

/* 音频文件管理区域样式 */
.audio-files-section {
  margin-bottom: 1rem;
  /* 减少下边距 */
}

.audio-files-section h4 {
  margin: 0 0 0.5rem 0;
  /* 减少下边距 */
  color: #303133;
  font-size: 1rem;
  /* 减小字体 */
}

.audio-files-list {
  border: 1px solid #e4e7ed;
  border-radius: 0.5rem;
  overflow: hidden;
}

.files-grid {
  background-color: #f8f9fa;
  padding: 0.75rem;
  /* 减少内边距 */
}

.audio-file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  /* 减少内边距 */
  margin-bottom: 0.375rem;
  /* 减少下边距 */
  background-color: white;
  border: 1px solid #e4e7ed;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.audio-file-item:last-child {
  margin-bottom: 0;
}

.audio-file-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.audio-file-item:hover {
  border-color: #c0c4cc;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.file-icon {
  color: #409eff;
  font-size: 1.125rem;
}

.file-name {
  font-weight: 500;
  color: #303133;
}

.file-duration {
  color: #909399;
  font-size: 0.875rem;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.current-label {
  color: #67c23a;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0 0.5rem;
}

.add-audio-section {
  border-top: 1px solid #e4e7ed;
  padding: 1rem;
  text-align: center;
}

.audio-upload-compact {
  display: inline-block;
}

.audio-upload-compact .el-button {
  margin-bottom: 0.5rem;
}

.upload-tip {
  font-size: 0.75rem;
  color: #909399;
  margin-top: 0.5rem;
}

.upload-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* 当前音频信息样式 */
.current-audio-info {
  margin-bottom: 0.5rem;
  /* 减少下边距 */
  padding: 0.75rem;
  /* 减少内边距 */
  background-color: #f0f9ff;
  border: 1px solid #e0f2fe;
  border-radius: 0.5rem;
}

.audio-file-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.audio-file-info .file-name {
  font-weight: 600;
  color: #303133;
  flex: 1;
}

.audio-file-info .file-duration {
  color: #606266;
}

.waveform-container {
  position: relative;
  border: 1px solid #e4e7ed;
  border-radius: 0.5rem;
  overflow: hidden;
  margin: 1rem 0;
  /* 移除固定高度，让内容自适应 */
}

/* 波形图操作提示 */
.waveform-tips {
  position: absolute;
  top: 8px;
  right: 12px;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.95);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.waveform {
  background-color: #f8f9fa;
  position: relative;
  /* 使用固定高度 */
  height: 180px;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
}

/* 强制 WaveSurfer 容器占用完整空间 */
.waveform>div {
  margin: 0 !important;
  padding: 0 !important;
}

.loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(248, 249, 250, 0.95);
  z-index: 10;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
}

.loading-icon {
  font-size: 1.5rem;
  color: #409eff;
}

.loading-text {
  color: #606266;
  font-size: 0.875rem;
  font-weight: 500;
}

/* 空状态样式 */
.empty-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.empty-icon {
  font-size: 2.5rem;
  color: #c0c4cc;
  margin-bottom: 1rem;
}

.empty-text {
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0 0.5rem 0;
  color: #606266;
}

.empty-hint {
  font-size: 0.875rem;
  margin: 0;
  color: #909399;
}

.waveform-controls {
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
}


.control-tips {
  text-align: center;
  padding: 0.5rem;
  background-color: #f0f9ff;
  border-radius: 0.25rem;
  border: 1px solid #e0f2fe;
}

/* 对应关系头部样式 */
.mapping-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.mapping-header h5 {
  margin: 0;
  color: #303133;
  font-size: 1rem;
}

/* 底部对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem 0;
}

.footer-left {
  flex: 1;
}

.footer-right {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.footer-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.processing-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  background-color: #f0f9ff;
  border: 1px solid #e0f2fe;
  border-radius: 0.5rem;
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.loading-icon {
  font-size: 1.5rem;
  color: #409eff;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  flex: 1;
}

.loading-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #303133;
  margin-bottom: 0.25rem;
}

.loading-detail {
  font-size: 0.8rem;
  color: #606266;
}

.info-line {
  display: flex;
  align-items: center;
  font-size: 0.8125rem;
  line-height: 1.4;
}

.info-label {
  color: #606266;
  font-weight: 500;
  min-width: 5rem;
}

.info-value {
  color: #409eff;
  font-weight: 600;
  margin-right: 0.5rem;
}



.mapping-detail {
  color: #909399;
  font-size: 0.75rem;
  margin-left: 0.25rem;
}

.warning-text {
  color: #f56c6c;
  font-size: 0.75rem;
  margin-left: 0.5rem;
}

/* 映射区域样式 */
.mapping-section {
  margin-top: .5rem;
}

.mapping-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.mapping-header h4 {
  margin: 0;
  color: #303133;
  font-size: 1.125rem;
}

.mapping-actions {
  display: flex;
  gap: 0.5rem;
}

.selection-hint {
  color: #67c23a;
  font-size: 0.875rem;
  font-weight: normal;
}

.mapping-indicator {
  color: #67c23a;
  font-size: 0.75rem;
  font-weight: bold;
  margin-left: 0.5rem;
}

.mapping-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: .5rem;
}

.text-segments,
.audio-regions {
  border: 1px solid #e4e7ed;
  border-radius: 0.5rem;
  overflow: hidden;
}

.text-segments h5,
.audio-regions h5 {
  margin: 0;
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  color: #303133;
  font-size: 1rem;
  font-weight: 600;
}

.segment-list {
  max-height: 300px;
  overflow-y: auto;
}

.text-segment-item,
.audio-segment-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.text-segment-item:hover,
.audio-segment-item:hover {
  background-color: #f8f9fa;
}

.text-segment-item.selected,
.audio-segment-item.selected {
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

.text-segment-item.mapped,
.audio-segment-item.mapped {
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.text-segment-item.mapped.selected,
.audio-segment-item.mapped.selected {
  background-color: #d9f7be;
  border-color: #73d13d;
}

.audio-segment-item.current-file {
  border-left: 3px solid #409eff;
}

.audio-segment-item:not(.current-file) {
  opacity: 0.7;
}

.segment-file {
  margin-bottom: 0.25rem;
}

.segment-number {
  flex-shrink: 0;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #409eff;
  color: white;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: bold;
}

.segment-content {
  flex: 1;
  min-width: 0;
}

.segment-text {
  font-size: 0.875rem;
  color: #303133;
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

.segment-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.segment-language {
  font-size: 0.75rem;
  color: #909399;
}

.segment-time {
  font-size: 0.875rem;
  color: #303133;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.segment-duration {
  font-size: 0.75rem;
  color: #909399;
  margin-bottom: 0.5rem;
}

.region-actions {
  display: flex;
  gap: 0.5rem;
}

/* 映射结果样式 */
.mapping-result {
  border: 1px solid #e4e7ed;
  border-radius: 0.5rem;
  overflow: hidden;
}

.mapping-result h5 {
  margin: 0;
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  color: #303133;
  font-size: 1rem;
  font-weight: 600;
}

.mapping-list {
  max-height: 200px;
  overflow-y: auto;
}

.mapping-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.mapping-item:last-child {
  border-bottom: none;
}

.mapping-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
  justify-content: flex-end;
}

.mapping-text {
  flex: 2;
  font-size: 0.875rem;
  color: #303133;
}

.mapping-arrow {
  flex-shrink: 0;
  color: #909399;
  font-weight: bold;
}

.mapping-audio {
  flex: 1;
  font-size: 0.875rem;
  color: #606266;
}

/* 步骤标题样式 */
.step-header {
  padding: .5rem 0;
  border-bottom: 2px solid #e4e7ed;
  margin-bottom: .5rem;
}

.step-header h3 {
  margin: 0;
  font-size: 1.125rem;
  color: #409eff;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.step-header h3::before {
  content: '';
  width: 4px;
  height: 1.2rem;
  background-color: #409eff;
  margin-right: 0.75rem;
  border-radius: 2px;
}
</style>
