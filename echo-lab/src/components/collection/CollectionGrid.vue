<!--
  合集网格组件
  用于展示合集卡片的网格布局
-->
<template>
  <div class="collection-grid" :class="{ 'is-loading': loading, 'mobile-layout': isMobile }">
    <!-- 加载状态 -->
    <div v-if="loading" class="grid-loading">
      <div v-for="i in 6" :key="i" class="skeleton-card">
        <el-skeleton animated>
          <template #template>
            <div class="skeleton-cover"></div>
            <div class="skeleton-content">
              <el-skeleton-item variant="h3" style="width: 80%; margin-bottom: 0.5rem;" />
              <el-skeleton-item variant="text" style="width: 100%; margin-bottom: 0.25rem;" />
              <el-skeleton-item variant="text" style="width: 60%; margin-bottom: 0.75rem;" />
              <div style="display: flex; gap: 0.5rem; margin-bottom: 0.5rem;">
                <el-skeleton-item variant="text" style="width: 3rem;" />
                <el-skeleton-item variant="text" style="width: 3rem;" />
              </div>
              <el-skeleton-item variant="text" style="width: 40%;" />
            </div>
          </template>
        </el-skeleton>
      </div>
    </div>

    <!-- 内容为空 -->
    <div v-else-if="collections.length === 0" class="grid-empty">
      <el-empty :description="emptyText" :image-size="100">
        <template #description>
          <p>{{ emptyText }}</p>
        </template>
        <template #default v-if="showCreateButton">
          <el-button type="primary" @click="$emit('create')">
            创建第一个合集
          </el-button>
        </template>
      </el-empty>
    </div>

    <!-- 合集网格 -->
    <div v-else class="grid-container">
      <div v-for="collection in collections" :key="collection.id" class="grid-item"
        :class="{ 'unavailable': collection.isAvailable === false }">
        <!-- 不可用状态遮罩 -->
        <div v-if="collection.isAvailable === false" class="unavailable-overlay">
          <el-tag :type="getStatusTagType(collection.unavailableReason)" size="small">
            {{ getStatusText(collection.unavailableReason) }}
          </el-tag>
        </div>

        <CollectionCard :collection="collection" :show-actions="showActions" :show-author="showAuthor"
          :show-view-count="showViewCount" :show-favorite-count="showFavoriteCount"
          :show-favorite-button="showFavoriteButton" :show-status="showStatus" @edit="handleEdit" @delete="handleDelete" @publish="handlePublish"
          @unpublish="handleUnpublish" @play="handlePlay" @toggle-favorite="handleToggleFavorite" />
      </div>
    </div>

    <!-- 加载更多 -->
    <div v-if="showLoadMore && hasMore" class="load-more-section">
      <el-button
        type="primary"
        size="large"
        :loading="loading"
        @click="$emit('load-more')"
        class="load-more-btn"
      >
        {{ loading ? '加载中...' : '加载更多' }}
      </el-button>
    </div>

    <!-- 没有更多数据提示 -->
    <div v-if="showLoadMore && !hasMore && collections.length > 0" class="no-more-tip">
      <el-divider>
        <span class="no-more-text">已显示全部合集</span>
      </el-divider>
    </div>

    <!-- 分页 -->
    <div v-if="showPagination && pagination && pagination.total > 0" class="grid-pagination">
      <el-pagination :current-page="currentPage" :page-size="pageSize" :total="pagination.total"
        :page-sizes="isMobile ? [12, 24] : [12, 24, 48, 96]"
        :layout="isMobile ? 'prev, pager, next' : 'total, sizes, prev, pager, next, jumper'" :small="isMobile"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, toRefs } from 'vue';
import CollectionCard from './CollectionCard.vue';
import { isMobileDevice } from '@/utils/deviceDetector';

// 设备检测
const isMobile = ref(isMobileDevice());

// Props
const props = defineProps({
  collections: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: Object,
    default: null
  },
  showPagination: {
    type: Boolean,
    default: true
  },
  showLoadMore: {
    type: Boolean,
    default: false
  },
  hasMore: {
    type: Boolean,
    default: false
  },
  total: {
    type: Number,
    default: 0
  },
  showActions: {
    type: Boolean,
    default: false
  },
  showAuthor: {
    type: Boolean,
    default: false
  },
  showViewCount: {
    type: Boolean,
    default: false
  },
  showFavoriteCount: {
    type: Boolean,
    default: false
  },
  showFavoriteButton: {
    type: Boolean,
    default: false
  },
  showCreateButton: {
    type: Boolean,
    default: false
  },
  showStatus: {
    type: Boolean,
    default: true
  },
  emptyText: {
    type: String,
    default: '暂无合集'
  }
});

// Emits
const emit = defineEmits([
  'pagination-change',
  'edit',
  'delete',
  'publish',
  'unpublish',
  'play',
  'toggle-favorite',
  'create',
  'load-more'
]);

// 解构 props 以便在模板中直接使用
const {
  collections,
  loading,
  pagination,
  showPagination,
  showLoadMore,
  hasMore,
  total,
  showActions,
  showAuthor,
  showViewCount,
  showFavoriteCount,
  showFavoriteButton,
  showCreateButton,
  showStatus,
  emptyText
} = toRefs(props);

// 分页状态
const currentPage = ref(props.pagination?.page || 1);
const pageSize = ref(props.pagination?.pageSize || 10);

// 监听分页属性变化
watch(() => props.pagination, (newPagination) => {
  if (newPagination) {
    currentPage.value = newPagination.page || 1;
    pageSize.value = newPagination.pageSize || 10;
  }
}, { immediate: true });

// 处理分页变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  emit('pagination-change', {
    page: currentPage.value,
    pageSize: pageSize.value
  });
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  emit('pagination-change', {
    page: currentPage.value,
    pageSize: pageSize.value
  });
};

// 处理合集操作
const handleEdit = (collection) => {
  emit('edit', collection);
};

const handleDelete = (collection) => {
  emit('delete', collection);
};

const handlePublish = (collection) => {
  emit('publish', collection);
};

const handleUnpublish = (collection) => {
  emit('unpublish', collection);
};

const handlePlay = (collection) => {
  emit('play', collection);
};

const handleToggleFavorite = (collection) => {
  emit('toggle-favorite', collection);
};

// 获取状态标签类型
const getStatusTagType = (reason) => {
  switch (reason) {
    case 'deleted':
      return 'danger';
    case 'unpublished':
      return 'warning';
    case 'private':
      return 'info';
    default:
      return 'warning';
  }
};

// 获取状态文本
const getStatusText = (reason) => {
  switch (reason) {
    case 'deleted':
      return '已删除';
    case 'unpublished':
      return '已下架';
    case 'private':
      return '已私有';
    default:
      return '不可用';
  }
};
</script>

<style scoped>
.collection-grid {
  width: 100%;
}

.grid-loading {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(16rem, 1fr));
  gap: 1rem;
  padding: 1rem 0;
  width: 100%;
  box-sizing: border-box;
}

/* 移动端加载状态优化 */
.mobile-layout .grid-loading {
  grid-template-columns: repeat(auto-fill, minmax(11rem, 1fr));
  gap: 0.75rem;
  padding: 0.75rem 0;
}

.skeleton-card {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.08);
}

.skeleton-cover {
  width: 100%;
  height: 10rem;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.skeleton-content {
  padding: 1rem;
}

.grid-empty {
  padding: 3.75rem 1.25rem;
  text-align: center;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(16rem, 1fr));
  gap: 1rem;
  padding: 1rem 0;
  width: 100%;
  box-sizing: border-box;
}

/* 手机端网格布局 */
.mobile-layout .grid-container {
  grid-template-columns: repeat(auto-fill, minmax(11rem, 1fr));
  gap: 0.75rem;
  padding: 0.75rem 0;
}

.grid-item {
  width: 100%;
  position: relative;
}

/* 不可用合集样式 */
.grid-item.unavailable {
  opacity: 0.7;
  filter: grayscale(0.2);
  transition: all 0.25s ease;
}

.grid-item.unavailable:hover {
  opacity: 0.85;
  filter: grayscale(0.1);
}

.unavailable-overlay {
  position: absolute;
  top: 0.5rem;
  right: 3.5rem;
  z-index: 10;
  pointer-events: none;
}

.load-more-section {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
}

.load-more-btn {
  min-width: 8rem;
  border-radius: 2rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.load-more-btn:hover {
  transform: translateY(-0.125rem);
  box-shadow: 0 0.5rem 1rem rgba(64, 158, 255, 0.3);
}

.no-more-tip {
  margin-top: 2rem;
  text-align: center;
}

.no-more-text {
  color: #909399;
  font-size: 0.875rem;
}

.grid-pagination {
  margin-top: 1.875rem;
  display: flex;
  justify-content: center;
}

/* 手机端优化 */
.mobile-layout .load-more-section {
  margin-top: 1.5rem;
}

.mobile-layout .load-more-btn {
  min-width: 7rem;
  font-size: 0.875rem;
}

.mobile-layout .grid-pagination {
  margin-top: 1.25rem;
}

/* 统一的响应式设计，不使用媒体查询 */
</style>
