<!--
  字符级标注组件
  用于显示汉字上方的假名标注
-->
<template>
  <div class="character-annotation-wrapper">
    <div class="annotated-word">
      <span v-for="(char, index) in characters" :key="index" class="annotated-char" @click="editCharacter(index)">
        <span class="furigana-container">
          <span v-if="needsFurigana(char)" class="furigana">{{ char.reading }}</span>
          <span :class="getCharClass(char)" class="base-char">{{ char.char }}</span>
        </span>
      </span>
    </div>

    <!-- 编辑假名标注对话框 -->
    <standard-dialog v-model="editDialogVisible" title="编辑假名标注" width="50%" :show-confirm="true" confirm-text="保存"
      cancel-text="取消" @confirm="saveEdit" @cancel="cancelEdit" :key="editingIndex">
      <div v-if="editingIndex !== null" class="edit-dialog-content">
        <!-- 固定的上方区域 -->
        <div class="dialog-header-fixed">
          <div class="selected-text-preview">
            <span class="selected-text-label">完整句子：</span>
            <span class="character-display">{{ getFullSentence() }}</span>
          </div>



          <!-- 字符分组控制区域 -->
          <div class="character-grouping-controls">
            <el-button size="small" @click="resetCharacterGroups">重置分组</el-button>
            <el-button size="small" @click="mergeSelectedGroups" :disabled="selectedGroups.length < 2">
              合并选中 ({{ selectedGroups.length }})
            </el-button>
            <span class="grouping-hint">点击字符组选中，然后合并</span>
          </div>
        </div>

        <!-- 可滚动的内容区域 -->
        <div class="dialog-content-scrollable">
          <!-- 字符标注模式 -->
          <div class="character-annotation-mode">
            <div class="character-groups">
              <div v-for="(group, groupIndex) in characterGroups" :key="groupIndex"
                class="character-group-item"
                :class="{ 
                  'selected': selectedGroups.includes(groupIndex),
                  'clicked-char': group.isClicked 
                }"
                @click="toggleGroupSelection(groupIndex)">
                <div class="character-group-label">{{ group.chars }}</div>
                <el-input v-model="group.reading" placeholder="假名" size="small"
                  @input="updateGroupReadings"
                  @click.stop></el-input>
              </div>
            </div>
          </div>
        </div>
      </div>
    </standard-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import StandardDialog from './StandardDialog.vue';

const props = defineProps({
  characters: {
    type: Array,
    required: true,
    default: () => []
  },
  editable: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:characters']);

// 编辑状态
const editDialogVisible = ref(false);
const editingIndex = ref(null);
const editingReading = ref('');

const characterReadings = ref({}); // 逐字标注模式下的字符读音
const characterGroups = ref([]); // 字符分组
const selectedGroups = ref([]); // 选中的分组索引
const clickedCharIndex = ref(null); // 点击的字符索引

// 判断字符是否需要显示假名
function needsFurigana(char) {
  // 如果有读音且读音与字符不同，则显示假名
  return char.reading && char.reading !== char.char;
}

// 获取字符的CSS类
function getCharClass(char) {
  // 根据字符类型返回不同的CSS类
  if (isKanji(char.char)) {
    return 'kanji';
  } else if (isKana(char.char)) {
    return 'kana';
  } else {
    return 'other';
  }
}

// 判断是否为汉字
function isKanji(char) {
  // 日语汉字Unicode范围
  return /[\u4E00-\u9FAF]/.test(char);
}

// 判断是否为假名
function isKana(char) {
  // 平假名和片假名的Unicode范围
  return /[\u3040-\u309F\u30A0-\u30FF]/.test(char);
}

// 编辑字符
function editCharacter(index) {
  if (!props.editable) return;

  // 编辑模式：显示完整句子，但定位到点击的字符
  editingIndex.value = 0; // 始终编辑整个句子
  clickedCharIndex.value = index; // 记录点击的字符位置
  
  // 生成完整句子的读音
  const fullReading = props.characters.map(char => char.reading || char.char).join('');
  editingReading.value = fullReading;

  // 初始化逐字标注数据（使用完整句子）
  initCharacterReadings(0);

  // 显示对话框
  editDialogVisible.value = true;
}

// 初始化逐字标注数据（使用完整句子）
function initCharacterReadings(index) {
  // 清空之前的数据
  characterReadings.value = {};
  selectedGroups.value = [];

  // 默认保持原有字符组合，不拆分
  characterGroups.value = props.characters.map((char, i) => ({
    chars: char.char,
    reading: char.reading || char.char,
    isClicked: i === clickedCharIndex.value
  }));
}

// 更新字符读音
function updateCharacterReadings() {
  // 将所有字符读音合并为一个字符串
  const combinedReading = Object.values(characterReadings.value).join('');
  editingReading.value = combinedReading;
}

// 更新分组读音
function updateGroupReadings() {
  const combinedReading = characterGroups.value.map(group => group.reading).join('');
  editingReading.value = combinedReading;
}

// 重置字符分组
function resetCharacterGroups() {
  // 从当前分组拆分为最小字素
  const segmenter = new Intl.Segmenter('ja', { granularity: 'grapheme' });
  const newGroups = [];
  
  characterGroups.value.forEach(group => {
    const charArray = Array.from(segmenter.segment(group.chars), segment => segment.segment);
    
    charArray.forEach(c => {
      newGroups.push({
        chars: c,
        reading: c,
        isClicked: false // 重置后清除高亮
      });
    });
  });
  
  characterGroups.value = newGroups;
  selectedGroups.value = [];
  updateGroupReadings();
}

// 切换分组选中状态
function toggleGroupSelection(groupIndex) {
  const index = selectedGroups.value.indexOf(groupIndex);
  if (index > -1) {
    selectedGroups.value.splice(index, 1);
  } else {
    selectedGroups.value.push(groupIndex);
  }
  // 按索引排序
  selectedGroups.value.sort((a, b) => a - b);
}

// 合并选中的分组
function mergeSelectedGroups() {
  if (selectedGroups.value.length < 2) return;
  
  // 按索引排序
  const sortedIndices = [...selectedGroups.value].sort((a, b) => a - b);
  
  // 合并字符和读音
  const mergedChars = sortedIndices.map(i => characterGroups.value[i].chars).join('');
  const mergedReading = sortedIndices.map(i => characterGroups.value[i].reading).join('');
  
  // 创建新的分组数组
  const newGroups = [];
  let mergedGroup = null;
  
  characterGroups.value.forEach((group, index) => {
    if (sortedIndices.includes(index)) {
      if (!mergedGroup) {
        mergedGroup = {
          chars: mergedChars,
          reading: mergedReading
        };
        newGroups.push(mergedGroup);
      }
    } else {
      newGroups.push(group);
    }
  });
  
  characterGroups.value = newGroups;
  selectedGroups.value = [];
  updateGroupReadings();
}

// 获取完整句子
function getFullSentence() {
  return props.characters.map(char => char.char).join('');
}

// 取消编辑
function cancelEdit() {
  editDialogVisible.value = false;
  editingIndex.value = null;
  editingReading.value = '';
  characterReadings.value = {};
  characterGroups.value = [];
  selectedGroups.value = [];
  clickedCharIndex.value = null;
}

// 保存编辑
function saveEdit() {
  if (editingIndex.value === null) return;

  // 创建新的字符数组，避免直接修改props
  const updatedCharacters = [...props.characters];

  // 按照用户定义的分组创建字符对象
  // 确保更新了最新的合并读音
  updateGroupReadings();

  // 创建新的字符数组，按照分组创建
  const newCharacters = [];

  // 处理每个分组
  characterGroups.value.forEach(group => {
    newCharacters.push({
      char: group.chars,
      reading: group.reading || group.chars,
      isEdited: true
    });
  });

  // 替换所有字符
  updatedCharacters.splice(0, updatedCharacters.length, ...newCharacters);

  // 发送更新事件
  emit('update:characters', updatedCharacters);

  // 关闭对话框
  editDialogVisible.value = false;
  editingIndex.value = null;
  editingReading.value = '';
  characterReadings.value = {};
  characterGroups.value = [];
  selectedGroups.value = [];
  clickedCharIndex.value = null;
}
</script>

<style scoped>
.character-annotation-wrapper {
  display: inline-block;
  width: 100%;
}

.annotated-word {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 0.5em;
  margin-bottom: 0.5em;
  padding: 1em 0.5em 0.5em;
  border-radius: 0.25rem;
  background-color: #f8f9fa;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
  line-height: 1.5;
  font-size: 1em;
}

.annotated-word:hover {
  background-color: #ecf5ff;
  border-color: #c6e2ff;
}

.annotated-char {
  display: inline-block;
  text-align: center;
  cursor: pointer;
  margin: 0 0.1em;
  padding: 0.1em 0.2em;
  transition: all 0.2s;
  border-radius: 0.25rem;
}

.annotated-char:hover {
  background-color: rgba(64, 158, 255, 0.1);
}



.furigana-container {
  display: inline-block;
  position: relative;
  text-align: center;
  vertical-align: bottom;
  min-width: 1em;
  padding-top: 0.5em;
}

.base-char {
  display: inline-block;
  position: relative;
}

.furigana {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.6em;
  line-height: 1;
  text-align: center;
  color: #409EFF;
  font-weight: normal;
  white-space: nowrap;
}



.edit-dialog-content {
  display: flex;
  flex-direction: column;
  height: 60vh;
  max-height: 500px;
}

.dialog-header-fixed {
  flex-shrink: 0;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  border-radius: 0.25rem 0.25rem 0 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.selected-text-preview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.selected-text-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #606266;
}

.character-display {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  padding: 0.25rem 0.5rem;
  background-color: #fff;
  border-radius: 0.25rem;
  border: 1px solid #dcdfe6;
}

.annotation-mode-selector {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.mode-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #606266;
}

.dialog-content-scrollable {
  flex: 1;
  overflow-y: auto;
  padding: 0.75rem;
}

.whole-annotation-mode {
  padding-top: 0.5rem;
}

.character-by-character {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.character-input-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.character-label {
  font-size: 1.5rem;
  min-width: 3rem;
  text-align: center;
  padding: 0.5rem;
  background-color: #fff;
  border-radius: 0.25rem;
  border: 1px solid #dcdfe6;
}

.character-grouping-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: #fff;
  border-radius: 0.25rem;
  border: 1px solid #e9ecef;
}

.grouping-hint {
  font-size: 0.75rem;
  color: #6c757d;
  font-style: italic;
  margin-left: auto;
}

.character-groups {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.character-group-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background-color: #fff;
  border-radius: 0.25rem;
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s;
}

.character-group-item:hover {
  border-color: #409eff;
  background-color: #f0f8ff;
}

.character-group-item.selected {
  border-color: #409eff;
  background-color: #e6f3ff;
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.3);
}

.character-group-item.clicked-char {
  border-color: #f56c6c;
  background-color: #fef0f0;
  box-shadow: 0 0 0 1px rgba(245, 108, 108, 0.3);
}

.character-group-label {
  font-size: 1rem;
  min-width: 2.5rem;
  text-align: center;
  padding: 0.25rem 0.5rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  border: 1px solid #dee2e6;
  font-weight: 500;
}
</style>
