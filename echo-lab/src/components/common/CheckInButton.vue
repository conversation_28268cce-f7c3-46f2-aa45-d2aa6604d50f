<!--
  签到按钮组件
  显示在标题栏的签到按钮
-->
<template>
  <div class="check-in-button">
    <el-button 
      :type="getButtonType"
      size="small"
      @click="goToCheckIn"
      :loading="loading"
      class="check-in-btn">
      <el-icon><i-ep-calendar /></el-icon>
      <span class="btn-text">签到</span>
      <el-tag v-if="userStore.isLoggedIn && todayChecked" type="success" size="small" class="check-tag">✓</el-tag>
    </el-button>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useCheckInStore } from '@/stores/checkInStore';
import { useUserStore } from '@/stores/userStore';
import { ElMessage } from 'element-plus';

const router = useRouter();
const checkInStore = useCheckInStore();
const userStore = useUserStore();

// 计算属性
const todayChecked = computed(() => checkInStore.todayChecked);
const loading = computed(() => checkInStore.loading);

// 按钮类型
const getButtonType = computed(() => {
  if (!userStore.isLoggedIn) return 'default';
  return todayChecked.value ? 'success' : 'primary';
});

// 跳转到签到页面
const goToCheckIn = () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再使用签到功能');
    router.push('/login');
    return;
  }
  router.push('/checkin');
};

// 组件挂载时初始化数据
onMounted(() => {
  if (userStore.isLoggedIn) {
    checkInStore.initCheckIn();
  }
});
</script>

<style scoped>
.check-in-button {
  display: flex;
  align-items: center;
}

.check-in-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.3s ease;
  position: relative;
}

.check-in-btn:hover {
  transform: scale(1.05);
}

.btn-text {
  font-size: 0.875rem;
}

.check-tag {
  margin-left: 0.25rem;
  font-size: 0.75rem;
  padding: 0 0.25rem;
}
</style>