<!--
  语言等级选择组件 - H5版本
  用于首次访问引导和设置修改
-->
<template>
  <div class="language-level-selector">
    <div class="selector-title" v-if="title">{{ title }}</div>
    
    <div class="columns-container">
      <!-- 左列：语言选择 -->
      <div class="column language-column">
        <div class="column-header">语言</div>
        <div class="column-content">
          <div
            v-for="lang in availableLanguages"
            :key="lang.value"
            class="option-item"
            :class="{ 
              selected: selectedLanguage === lang.value,
              disabled: lang.status !== 'available'
            }"
            @click="selectLanguage(lang.value)"
          >
            <div class="radio-icon">
              <el-icon v-if="selectedLanguage === lang.value">
                <i-ep-check />
              </el-icon>
              <div v-else class="radio-circle"></div>
            </div>
            <div class="option-content">
              <span class="option-text">{{ lang.label }}</span>
              <el-tag
                v-if="lang.status !== 'available'"
                :type="getStatusTagType(lang.status)"
                size="small"
                class="status-tag">
                {{ lang.statusText }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 右列：等级选择 -->
      <div class="column level-column">
        <div class="column-header">等级 <span class="optional-hint">(可选)</span></div>
        <div class="column-content">
          <div v-if="!selectedLanguage" class="empty-hint">
            <span class="hint-text">请先选择语言</span>
          </div>
          <div v-else>
            <div
              v-for="level in availableLevels"
              :key="level.key"
              class="option-item"
              :class="{ selected: selectedLevels.includes(level.key) }"
              @click="toggleLevel(level.key)"
            >
              <div class="checkbox-icon">
                <el-icon v-if="selectedLevels.includes(level.key)">
                  <i-ep-check />
                </el-icon>
                <div v-else class="checkbox-square"></div>
              </div>
              <div class="option-content">
                <span class="option-text">{{ level.name }}</span>
                <span class="option-desc">{{ level.description }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { SUPPORTED_LANGUAGES } from '@/config/languages.js'
import { getLanguageLevels } from '@/config/languageLevels.js'

// Props
const props = defineProps({
  // 当前选中的语言
  language: {
    type: String,
    default: null
  },
  // 当前选中的等级数组
  levels: {
    type: Array,
    default: () => []
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 是否允许清空语言选择
  allowClearLanguage: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:language', 'update:levels', 'change'])

// 内部状态
const selectedLanguage = ref(props.language)
const selectedLevels = ref([...props.levels])

// 可用选项
const availableLanguages = computed(() => SUPPORTED_LANGUAGES.filter(lang => lang.status !== 'hidden'))
const availableLevels = computed(() => {
  if (!selectedLanguage.value) return []
  return getLanguageLevels(selectedLanguage.value)
})

// 监听props变化
watch(() => props.language, (newVal) => {
  selectedLanguage.value = newVal
})

watch(() => props.levels, (newVal) => {
  selectedLevels.value = [...newVal]
})

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case 'coming-soon':
      return 'warning'
    case 'planned':
      return 'info'
    default:
      return 'info' // 默认返回 'info' 而不是空字符串
  }
}

// 选择语言
const selectLanguage = (langValue) => {
  const lang = SUPPORTED_LANGUAGES.find(l => l.value === langValue)
  
  if (lang.status !== 'available') {
    if (lang.status === 'coming-soon') {
      ElMessage.info(`${lang.label}内容正在制作中，敬请期待！`)
    } else if (lang.status === 'planned') {
      ElMessage.info(`${lang.label}内容计划中，敬请期待！`)
    }
    return
  }

  // 如果点击已选中的语言且允许清空，则清空选择
  if (selectedLanguage.value === langValue && props.allowClearLanguage) {
    selectedLanguage.value = null
    selectedLevels.value = []
  } else {
    selectedLanguage.value = langValue
    // 切换语言时清空等级选择
    selectedLevels.value = []
  }
  
  // 发出事件
  emit('update:language', selectedLanguage.value)
  emit('update:levels', selectedLevels.value)
  emit('change', {
    language: selectedLanguage.value,
    levels: selectedLevels.value
  })
}

// 切换等级
const toggleLevel = (levelKey) => {
  const index = selectedLevels.value.indexOf(levelKey)
  if (index > -1) {
    selectedLevels.value.splice(index, 1)
  } else {
    selectedLevels.value.push(levelKey)
  }
  
  // 发出事件
  emit('update:levels', selectedLevels.value)
  emit('change', {
    language: selectedLanguage.value,
    levels: selectedLevels.value
  })
}

// 重置选择
const reset = () => {
  selectedLanguage.value = null
  selectedLevels.value = []
  emit('update:language', null)
  emit('update:levels', [])
  emit('change', {
    language: null,
    levels: []
  })
}

// 暴露方法给父组件
defineExpose({
  reset
})
</script>

<style scoped>
.language-level-selector {
  width: 100%;
}

.selector-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 1rem;
  text-align: center;
}

.columns-container {
  display: flex;
  gap: 2rem;
  min-height: 300px;
}

.column {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.column-header {
  background: #f5f5f5;
  padding: 0.75rem 1rem;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
}

.optional-hint {
  font-size: 0.8rem;
  color: #999;
  font-weight: normal;
}

.column-content {
  max-height: 250px;
  overflow-y: auto;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.option-item:hover:not(.disabled) {
  background: #f8f9fa;
}

.option-item.selected {
  background: #e3f2fd;
  border-color: #2196f3;
}

.option-item.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.option-item:last-child {
  border-bottom: none;
}

/* 手机端样式 */
@media (max-width: 768px) {
  .columns-container {
    gap: 0.5rem !important;
    min-height: 300px !important;
  }

  .column {
    border-radius: 8px !important;
  }

  .column-header {
    padding: 0.75rem !important;
    font-size: 0.875rem !important;
  }

  .column-content {
    max-height: 250px !important;
  }

  .option-item {
    padding: 0.75rem !important;
    min-height: 44px !important;
    font-size: 0.875rem !important;
  }
}

/* 基础样式 */
.column {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.column-header {
  background: #f5f5f5;
  padding: 0.75rem 1rem;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
}

.optional-hint {
  font-size: 0.8rem;
  color: #999;
  font-weight: normal;
}

.column-content {
  max-height: 250px;
  overflow-y: auto;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.option-item:hover:not(.disabled) {
  background: #f8f9fa;
}

.option-item.selected {
  background: #e3f2fd;
  border-color: #2196f3;
}

.option-item.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.option-item:last-child {
  border-bottom: none;
}

.radio-icon, .checkbox-icon {
  margin-right: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.radio-circle {
  width: 16px;
  height: 16px;
  border: 2px solid #ccc;
  border-radius: 50%;
}

.checkbox-square {
  width: 16px;
  height: 16px;
  border: 2px solid #ccc;
  border-radius: 3px;
}

.option-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.option-text {
  font-weight: 500;
  color: #333;
}

.option-desc {
  font-size: 0.85rem;
  color: #666;
}

.status-tag {
  margin-left: 0.5rem;
}

.empty-hint {
  padding: 2rem 1rem;
  text-align: center;
}

.hint-text {
  color: #999;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .columns-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .column {
    min-height: 200px;
  }
  
  .column-content {
    max-height: 180px;
  }
}
</style>
