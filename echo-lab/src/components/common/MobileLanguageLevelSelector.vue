<template>
  <div class="selector">
    <div class="row">
      <div class="col">
        <div class="list">
          <div
            v-for="lang in availableLanguages"
            :key="lang.value"
            class="option"
            :class="{ selected: selectedLanguage === lang.value }"
            @click="selectLanguage(lang.value)"
          >
            {{ lang.label }}
          </div>
        </div>
      </div>

      <div class="col">
        <div class="list">
          <div v-if="!selectedLanguage" class="placeholder">
            请先选择语言
          </div>
          <div
            v-else
            v-for="level in availableLevels"
            :key="level.key"
            class="option"
            :class="{ selected: selectedLevels.includes(level.key) }"
            @click="toggleLevel(level.key)"
          >
            <div class="level-name">{{ level.name }}</div>
            <div class="level-desc">{{ level.description }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { SUPPORTED_LANGUAGES } from '@/config/languages.js'
import { getLanguageLevels } from '@/config/languageLevels.js'

// Props
const props = defineProps({
  language: {
    type: String,
    default: null
  },
  levels: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:language', 'update:levels', 'change'])

// 响应式数据
const selectedLanguage = ref(props.language)
const selectedLevels = ref([...props.levels])

// 可用语言列表
const availableLanguages = computed(() => {
  return SUPPORTED_LANGUAGES.filter(lang => lang.status === 'available')
})

// 可用等级列表
const availableLevels = computed(() => {
  if (!selectedLanguage.value) return []
  return getLanguageLevels(selectedLanguage.value)
})

// 选择语言
function selectLanguage(languageCode) {
  selectedLanguage.value = languageCode
  selectedLevels.value = [] // 清空等级选择
  
  emit('update:language', languageCode)
  emit('update:levels', [])
  emit('change', {
    language: languageCode,
    levels: []
  })
}

// 切换等级
function toggleLevel(levelKey) {
  const index = selectedLevels.value.indexOf(levelKey)
  if (index > -1) {
    selectedLevels.value.splice(index, 1)
  } else {
    selectedLevels.value.push(levelKey)
  }
  
  emit('update:levels', [...selectedLevels.value])
  emit('change', {
    language: selectedLanguage.value,
    levels: [...selectedLevels.value]
  })
}

// 监听props变化
watch(() => props.language, (newVal) => {
  selectedLanguage.value = newVal
})

watch(() => props.levels, (newVal) => {
  selectedLevels.value = [...newVal]
})
</script>

<style scoped>
.selector {
  height: 100%;
}

.row {
  display: flex;
  gap: 0.5rem;
  height: 100%;
  padding: 0.25rem;
}

.col {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-height: 18.75rem;
}

.list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.option {
  padding: 0.5rem;
  margin-bottom: 0.25rem;
  background: #f8f9fa;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.8rem;
}

.option:hover {
  background: #e9ecef;
}

.option.selected {
  background: #409eff;
  color: white;
}

.level-name {
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 0.125rem;
}

.level-desc {
  font-size: 0.75rem;
  opacity: 0.8;
  line-height: 1.2;
}

.placeholder {
  padding: 1.5rem 0.5rem;
  text-align: center;
  color: #999;
  font-size: 0.8rem;
}
</style>
