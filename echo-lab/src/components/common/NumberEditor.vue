<template>
  <div class="number-editor">
    <div class="number-editor-container">
      <!-- 减号按钮 -->
      <button type="button" class="number-editor-button decrease" @click="decrease"
        :disabled="disabled || (modelValue <= min)">
        <span class="button-icon">-</span>
      </button>

      <!-- 数字输入框 -->
      <div class="number-editor-input-container">
        <input type="text" class="number-editor-input" :value="displayValue" @input="handleInput" @blur="handleBlur"
          :disabled="disabled" :placeholder="placeholder" />
        <span v-if="suffix" class="number-editor-suffix">{{ suffix }}</span>
      </div>

      <!-- 加号按钮 -->
      <button type="button" class="number-editor-button increase" @click="increase"
        :disabled="disabled || (modelValue >= max)">
        <span class="button-icon">+</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';

const props = defineProps({
  modelValue: {
    type: Number,
    required: true
  },
  min: {
    type: Number,
    default: -Infinity
  },
  max: {
    type: Number,
    default: Infinity
  },
  step: {
    type: Number,
    default: 1
  },
  precision: {
    type: Number,
    default: 0
  },
  disabled: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: ''
  },
  suffix: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

// 计算显示值（根据精度格式化）
const displayValue = computed(() => {
  if (props.modelValue === undefined || props.modelValue === null) {
    return '';
  }

  if (props.precision === 0) {
    return props.modelValue.toString();
  }

  return props.modelValue.toFixed(props.precision);
});

// 减少值
const decrease = () => {
  if (props.disabled || props.modelValue <= props.min) return;

  const newValue = Math.max(props.min, parseFloat((props.modelValue - props.step).toFixed(props.precision)));
  emit('update:modelValue', newValue);
  emit('change', newValue);
};

// 增加值
const increase = () => {
  if (props.disabled || props.modelValue >= props.max) return;

  const newValue = Math.min(props.max, parseFloat((props.modelValue + props.step).toFixed(props.precision)));
  emit('update:modelValue', newValue);
  emit('change', newValue);
};

// 处理输入
const handleInput = (event) => {
  const value = event.target.value;

  // 允许空值、负号和小数点
  if (value === '' || value === '-' || value === '.') {
    return;
  }

  const numValue = parseFloat(value);

  // 检查是否为有效数字
  if (isNaN(numValue)) {
    event.target.value = displayValue.value;
    return;
  }
};

// 处理失焦
const handleBlur = (event) => {
  const value = event.target.value;

  // 如果为空，设置为最小值
  if (value === '' || value === '-' || value === '.') {
    const newValue = props.min;
    emit('update:modelValue', newValue);
    emit('change', newValue);
    return;
  }

  let numValue = parseFloat(value);

  // 限制在最小值和最大值之间
  numValue = Math.max(props.min, Math.min(props.max, numValue));

  // 根据精度格式化
  numValue = parseFloat(numValue.toFixed(props.precision));

  emit('update:modelValue', numValue);
  emit('change', numValue);
};
</script>

<style scoped>
.number-editor {
  display: flex;
  width: 100%;
}

.number-editor-container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 2rem;
  border: 1px solid #dcdfe6;
  border-radius: 0.25rem;
  overflow: hidden;
}

.number-editor-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 2rem;
  height: 100%;
  background-color: #f5f7fa;
  border: none;
  cursor: pointer;
  font-size: 1.25rem;
  font-weight: bold;
  color: #606266;
  transition: all 0.3s;
}

.number-editor-button:hover:not(:disabled) {
  background-color: #e4e7ed;
}

.number-editor-button:disabled {
  cursor: not-allowed;
  color: #c0c4cc;
}

.number-editor-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
}

.number-editor-input {
  width: 100%;
  height: 100%;
  padding: 0 0.5rem;
  border: none;
  outline: none;
  text-align: center;
  font-size: 0.875rem;
  color: #606266;
}

.number-editor-input:disabled {
  background-color: #f5f7fa;
  cursor: not-allowed;
  color: #c0c4cc;
}

.number-editor-suffix {
  position: absolute;
  right: 0.5rem;
  color: #909399;
  font-size: 0.75rem;
}

.button-icon {
  line-height: 1;
}
</style>
