<!--
  智能页面头部组件
  根据设备类型和导航上下文自动决定是否显示返回按钮
-->
<template>
  <div class="smart-page-header" :class="{
    'mobile-header': isMobile,
    'desktop-header': !isMobile
  }">
    <!-- 左侧操作区插槽 -->
    <div class="header-left">
      <slot name="left">
        <!-- 默认显示返回按钮 -->
        <el-button v-if="shouldShowBackButton" @click="handleBack" class="back-button"
          :class="{ 'mobile-back': isMobile, 'desktop-back': !isMobile }" :type="isMobile ? 'text' : 'default'">
          <el-icon>
            <i-ep-arrow-left />
          </el-icon>
          {{ computedBackText }}
        </el-button>
      </slot>
    </div>

    <!-- 页面标题 -->
    <h1 class="page-title" :class="{ 'with-back': shouldShowBackButton || $slots.left }">{{ title }}</h1>

    <!-- 右侧操作区插槽 -->
    <div class="header-actions">
      <slot name="actions"></slot>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { isMobileDevice } from '@/utils/deviceDetector';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  forceShowBack: {
    type: Boolean,
    default: false
  },
  backText: {
    type: String,
    default: ''
  },
  customBackAction: {
    type: Function,
    default: null
  }
});

const route = useRoute();
const router = useRouter();
const isMobile = computed(() => isMobileDevice());

// 检查是否可以返回上一页
const canGoBack = computed(() => {
  return window.history.length > 1;
});

// 移动端返回按钮规则
const mobileBackRules = {
  '/': false,           // 首页不显示
  '/favorites': true,   // 收藏页显示
  '/templates': true,   // 模板页显示
  '/settings': true,    // 设置页显示
  '/profile': true,     // 个人信息显示
  '/feedback': true,    // 反馈页显示
  '/install-guide': true, // 安装指南显示
  '/content-info': true,  // 内容信息显示
  '/login': false,      // 登录页不显示
  '/upgrade': true,     // 升级页显示
};

// 桌面端返回按钮规则
const desktopBackRules = {
  '/': false,           // 首页不显示
  '/favorites': false,  // 收藏页不显示（有侧边栏）
  '/templates': false,  // 模板页不显示（有侧边栏）
  '/settings': true,    // 设置页显示
  '/profile': true,     // 个人信息显示
  '/feedback': true,    // 反馈页显示
  '/install-guide': true, // 安装指南显示
  '/content-info': true,  // 内容信息显示
  '/login': false,      // 登录页不显示
  '/upgrade': true,     // 升级页显示
};

// 判断是否应该显示返回按钮
const shouldShowBackButton = computed(() => {
  // 强制显示返回按钮
  if (props.forceShowBack) return true;

  const currentPath = route.path;
  const rules = isMobile.value ? mobileBackRules : desktopBackRules;

  // 检查精确匹配
  if (rules.hasOwnProperty(currentPath)) {
    return rules[currentPath];
  }

  // 检查路径模式匹配
  if (currentPath.startsWith('/player/')) {
    return isMobile.value; // 移动端播放器显示返回，桌面端不显示
  }

  if (currentPath.startsWith('/admin/')) {
    return true; // 管理后台页面都显示返回
  }

  // 移动端默认显示返回按钮（除了首页）
  if (isMobile.value) {
    return currentPath !== '/';
  }

  // 桌面端默认不显示返回按钮
  return false;
});

// 返回按钮文本
const computedBackText = computed(() => {
  if (props.backText) return props.backText;

  if (isMobile.value) {
    // 移动端根据来源页面智能显示
    if (canGoBack.value) return '返回';
    return '首页';
  }
  return '返回';
});

// 处理返回操作
const handleBack = () => {
  // 如果有自定义返回操作，优先执行
  if (props.customBackAction) {
    props.customBackAction();
    return;
  }

  // 如果可以返回上一页，则返回
  if (canGoBack.value) {
    router.back();
  } else {
    // 如果没有历史记录，返回首页
    router.push('/');
  }
};

// 暴露给父组件的方法
defineExpose({
  handleBack
});
</script>

<style scoped>
.smart-page-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: #ffffff;
  border-bottom: 1px solid #e4e7ed;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

/* 移动端样式 */
.mobile-header {
  padding: 0.5rem 0.75rem;
  min-height: 3rem;
}

.mobile-header .back-button {
  color: #409eff;
  font-size: 0.9rem;
  padding: 0.25rem 0.5rem;
}

.mobile-header .page-title {
  font-size: 1.1rem;
  margin: 0;
  font-weight: 600;
  color: #303133;
  flex: 1;
  text-align: center;
}

.mobile-header .page-title.with-back {
  margin-right: 3rem;
  /* 平衡左侧返回按钮 */
}

/* 桌面端样式 */
.desktop-header {
  padding: 1rem 1.5rem;
  min-height: 4rem;
}

.desktop-header .back-button {
  font-size: 0.9rem;
}

.desktop-header .page-title {
  font-size: 1.5rem;
  margin: 0;
  font-weight: 600;
  color: #303133;
  flex: 1;
}

/* 通用样式 */
.back-button {
  flex-shrink: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}
</style>
