<!--
  统一过滤器选择组件 - 双列选择版本
  支持语言等级、内容类型、主题、教材等过滤器
  左列：过滤器类型，右列：具体过滤器
  直接嵌入使用，无弹窗
-->
<template>
  <div class="content-filter-selector" v-loading="loading">
    <!-- 双列选择布局 -->
    <div class="columns-container">
      <!-- 左列：过滤器类型 -->
      <div class="column type-column">
        <div class="column-header">过滤器类型</div>
        <div class="column-content">
          <div v-for="filterType in availableFilterTypes" :key="filterType.type" class="option-item"
            :class="{ selected: selectedFilterType === filterType.type }" @click="selectFilterType(filterType.type)">
            <div class="option-content">
              <span class="option-text">{{ filterType.name }}</span>
              <span class="option-desc" v-if="filterType.description">{{ filterType.description }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右列：具体过滤器 -->
      <div class="column filter-column">
        <div class="column-header">
          可选过滤器
          <span class="optional-hint" v-if="maxSelection > 0">(最多{{ maxSelection }}个)</span>
        </div>
        <div class="column-content">
          <div v-if="!selectedFilterType" class="empty-hint">
            <span class="hint-text">请先选择过滤器类型</span>
          </div>
          <div v-else>
            <div v-for="filter in availableFiltersForType" :key="filter.id" class="option-item" :class="{
              selected: selectedFilterIds.includes(filter.id),
              disabled: !filter.isActive || (maxSelection > 0 && !selectedFilterIds.includes(filter.id) && selectedFilterIds.length >= maxSelection)
            }" @click="filter.isActive ? toggleFilter(filter) : null">
              <div class="checkbox-icon">
                <el-icon v-if="selectedFilterIds.includes(filter.id)">
                  <Check />
                </el-icon>
                <div v-else class="checkbox-square"></div>
              </div>
              <div class="option-content">
                <span class="option-text">{{ filter.name }}</span>
                <span class="option-desc" v-if="filter.description">{{ filter.description }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 选择统计信息 -->
    <div class="selection-info">
      <div class="selected-count">
        <el-icon>
          <Check />
        </el-icon>
        已选择 <strong>{{ selectedFilterIds.length }}</strong> 个过滤器
      </div>
      <div class="filter-logic-tip">
        <el-icon>
          <InfoFilled />
        </el-icon>
        同类型内为"或"关系，不同类型间为"且"关系
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, InfoFilled } from '@element-plus/icons-vue'
import filterService from '@/services/filterService'
import { useLanguageStore } from '@/stores/languageStore'

const languageStore = useLanguageStore()

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  maxSelection: {
    type: Number,
    default: 0 // 0 表示无限制
  },
  language: {
    type: String,
    default: null // 如果指定语言，则只显示该语言的过滤器
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const loading = ref(false)
const filterTypes = ref([])
const allFilters = ref([])
const selectedFilterIds = ref([...props.modelValue])
const selectedFilterType = ref('') // 当前选择的过滤器类型

// 计算属性
const selectedFilters = computed(() => {
  return allFilters.value.filter(filter => selectedFilterIds.value.includes(filter.id))
})

// 可用的过滤器类型（根据语言过滤）
const availableFilterTypes = computed(() => {
  const currentLanguage = props.language || languageStore.currentLearningLanguage || 'ja'

  return filterTypes.value.filter(type => {
    // 检查该类型下是否有适用于当前语言的过滤器
    const typeFilters = allFilters.value.filter(filter =>
      filter.type === type.type &&
      (filter.languages === null || filter.languages.length === 0 || filter.languages.includes(currentLanguage))
    )
    return typeFilters.length > 0
  })
})

// 当前选择类型下的可用过滤器
const availableFiltersForType = computed(() => {
  if (!selectedFilterType.value) return []

  const currentLanguage = props.language || languageStore.currentLearningLanguage || 'ja'

  return allFilters.value.filter(filter =>
    filter.type === selectedFilterType.value &&
    (filter.languages === null || filter.languages.length === 0 || filter.languages.includes(currentLanguage))
  ).sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
})

// 方法
const loadFilters = async (forceRefresh = false) => {
  loading.value = true
  try {
    const currentLanguage = props.language || languageStore.currentLearningLanguage || 'ja'

    // 添加时间戳参数强制刷新
    const timestamp = forceRefresh ? Date.now() : undefined

    // 获取过滤器类型和过滤器数据
    const [typesResponse, filtersResponse] = await Promise.all([
      filterService.getFilterTypes(),
      filterService.getFilters({ language: currentLanguage, _t: timestamp })
    ])

    if (typesResponse.success) {
      filterTypes.value = typesResponse.data || []
    }

    if (filtersResponse.success) {
      // 将分组的过滤器数据转换为平铺数组
      const flatFilters = []
      Object.values(filtersResponse.data || {}).forEach(typeFilters => {
        if (Array.isArray(typeFilters)) {
          flatFilters.push(...typeFilters)
        }
      })
      allFilters.value = flatFilters
    }

    // 默认选择第一个可用的过滤器类型
    if (availableFilterTypes.value.length > 0 && !selectedFilterType.value) {
      selectedFilterType.value = availableFilterTypes.value[0].type
    }

  } catch (error) {
    console.error('加载过滤器失败:', error)
    ElMessage.error('加载过滤器失败')
  } finally {
    loading.value = false
  }
}

// 选择过滤器类型
const selectFilterType = (type) => {
  selectedFilterType.value = type
}

// 切换过滤器选择
const toggleFilter = (filter) => {
  if (!filter || !filter.isActive) {
    return
  }

  const newSelectedIds = [...selectedFilterIds.value]
  const index = newSelectedIds.indexOf(filter.id)

  if (index > -1) {
    // 取消选择
    newSelectedIds.splice(index, 1)
  } else {
    // 选择过滤器
    if (props.maxSelection > 0 && newSelectedIds.length >= props.maxSelection) {
      ElMessage.warning(`最多只能选择 ${props.maxSelection} 个过滤器`)
      return
    }
    newSelectedIds.push(filter.id)
  }

  selectedFilterIds.value = newSelectedIds

  // 发出事件
  emit('update:modelValue', newSelectedIds)
  emit('change', newSelectedIds, getFiltersByType())
}

// 按类型分组获取过滤器
const getFiltersByType = () => {
  const filtersByType = {}
  selectedFilters.value.forEach(filter => {
    if (!filtersByType[filter.type]) {
      filtersByType[filter.type] = []
    }
    filtersByType[filter.type].push(filter.id)
  })
  return filtersByType
}

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  selectedFilterIds.value = [...newVal]
})

watch(() => props.language, () => {
  loadFilters()
})

// 组件挂载时加载数据
onMounted(() => {
  loadFilters(true) // 强制刷新
})

// 暴露刷新方法给父组件
defineExpose({
  refreshFilters: () => loadFilters(true)
})
</script>

<style scoped>
.content-filter-selector {
  width: 100%;
}

/* 双列选择布局 */
.columns-container {
  display: flex;
  gap: 1rem;
  height: 400px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  overflow: hidden;
}

.column {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.type-column {
  border-right: 1px solid var(--el-border-color-light);
  max-width: 200px;
}

.column-header {
  background: var(--el-bg-color-page);
  padding: 0.75rem 1rem;
  font-weight: 500;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-light);
  font-size: 0.875rem;
}

.multi-select-hint {
  font-size: 0.75rem;
  color: var(--el-color-primary);
  font-weight: normal;
  margin-left: 0.25rem;
}

.optional-hint {
  font-size: 0.75rem;
  color: var(--el-text-color-secondary);
  font-weight: normal;
}

.column-content {
  flex: 1;
  overflow-y: auto;
  background: white;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid var(--el-border-color-lighter);
  transition: all 0.2s ease;
}

.option-item:hover:not(.disabled) {
  background: var(--el-bg-color-page);
}

.option-item.selected {
  background: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
  border-left: 3px solid var(--el-color-primary);
}

.option-item.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.option-item:last-child {
  border-bottom: none;
}

.radio-icon,
.checkbox-icon {
  margin-right: 0.5rem;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.radio-circle {
  width: 12px;
  height: 12px;
  border: 2px solid var(--el-border-color);
  border-radius: 50%;
}

.checkbox-square {
  width: 12px;
  height: 12px;
  border: 2px solid var(--el-border-color);
  border-radius: 2px;
  transition: all 0.2s ease;
}

.option-item.selected .checkbox-square {
  background: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

.checkbox-icon .el-icon {
  color: var(--el-color-primary);
  font-size: 14px;
}

.option-content {
  flex: 1;
  min-width: 0;
}

.option-text {
  display: block;
  font-size: 0.875rem;
  color: var(--el-text-color-primary);
  line-height: 1.4;
}

.option-desc {
  display: block;
  font-size: 0.75rem;
  color: var(--el-text-color-secondary);
  line-height: 1.3;
  margin-top: 0.125rem;
}

.empty-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: var(--el-text-color-secondary);
}

.hint-text {
  font-size: 0.875rem;
}

.selection-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.selected-count {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: var(--el-color-primary);
  font-weight: 500;
}

.filter-logic-tip {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: var(--el-text-color-secondary);
}

.selected-count {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.filter-logic-tip {
  color: var(--el-text-color-secondary);
}
</style>
