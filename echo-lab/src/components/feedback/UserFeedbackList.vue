<template>
  <div class="user-feedback-list">
    <!-- 筛选工具栏 -->
    <div class="filter-toolbar">
      <el-select v-model="filterType" placeholder="反馈类型" clearable @change="handleFilterChange">
        <el-option label="功能建议（改进现有功能）" value="suggestion" />
        <el-option label="问题反馈（报告错误）" value="bug" />
        <el-option label="新功能请求（添加全新功能）" value="feature" />
        <el-option label="其他" value="other" />
      </el-select>

      <el-select v-model="filterStatus" placeholder="处理状态" clearable @change="handleFilterChange">
        <el-option label="待处理" value="pending" />
        <el-option label="处理中" value="processing" />
        <el-option label="已解决" value="resolved" />
        <el-option label="已拒绝" value="rejected" />
      </el-select>
    </div>

    <!-- 反馈列表 -->
    <el-table v-loading="loading" :data="feedbacks" style="width: 100%" empty-text="暂无反馈记录">
      <!-- 反馈类型 -->
      <el-table-column prop="type" label="类型" width="120">
        <template #default="{ row }">
          <el-tag :type="getFeedbackTypeTag(row.type)">
            {{ getFeedbackTypeLabel(row.type) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 反馈内容 -->
      <el-table-column prop="content" label="内容" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="feedback-content">{{ row.content }}</div>
        </template>
      </el-table-column>

      <!-- 处理状态 -->
      <el-table-column prop="status" label="状态" width="120">
        <template #default="{ row }">
          <el-tag :type="getFeedbackStatusTag(row.status)">
            {{ getFeedbackStatusLabel(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 提交时间 -->
      <el-table-column prop="created_at" label="提交时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>

      <!-- 操作 -->
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button type="primary" size="small" text @click="viewFeedbackDetail(row)">
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 加载更多 -->
    <div v-if="hasMore" class="load-more-section">
      <el-button type="primary" size="large" :loading="loadingMore" @click="loadMoreFeedbacks" class="load-more-btn">
        <span v-if="!loadingMore">加载更多</span>
        <span v-else>加载中...</span>
      </el-button>
    </div>

    <!-- 统计信息 -->
    <div v-if="total > 0" class="feedback-stats">
      <span class="stats-text">
        已显示 {{ feedbacks.length }} / {{ total }} 条反馈
      </span>
    </div>

    <!-- 反馈详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="反馈详情" width="600px" :destroy-on-close="true">
      <div v-if="selectedFeedback" class="feedback-detail">
        <div class="detail-item">
          <div class="detail-label">反馈类型：</div>
          <div class="detail-value">
            <el-tag :type="getFeedbackTypeTag(selectedFeedback.type)">
              {{ getFeedbackTypeLabel(selectedFeedback.type) }}
            </el-tag>
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-label">处理状态：</div>
          <div class="detail-value">
            <el-tag :type="getFeedbackStatusTag(selectedFeedback.status)">
              {{ getFeedbackStatusLabel(selectedFeedback.status) }}
            </el-tag>
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-label">提交时间：</div>
          <div class="detail-value">
            {{ formatDate(selectedFeedback.created_at) }}
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-label">反馈内容：</div>
          <div class="detail-value content-box">
            {{ selectedFeedback.content }}
          </div>
        </div>

        <div v-if="selectedFeedback.adminReply" class="detail-item">
          <div class="detail-label">管理员回复：</div>
          <div class="detail-value content-box admin-reply">
            {{ selectedFeedback.adminReply }}
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { getUserFeedbacks } from "@/services/feedbackService";
import { ElMessage } from "element-plus";

// 加载状态
const loading = ref(false);
const loadingMore = ref(false);

// 反馈列表
const feedbacks = ref([]);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(12);
const total = ref(0);

// 筛选
const filterType = ref("");
const filterStatus = ref("");

// 计算是否还有更多数据
const hasMore = computed(() => {
  return feedbacks.value.length < total.value;
});

// 详情对话框
const detailDialogVisible = ref(false);
const selectedFeedback = ref(null);

// 获取反馈列表
async function fetchFeedbacks(append = false) {
  if (append) {
    loadingMore.value = true;
  } else {
    loading.value = true;
  }

  try {
    const result = await getUserFeedbacks({
      page: currentPage.value,
      pageSize: pageSize.value,
      type: filterType.value,
      status: filterStatus.value
    });

    if (result.success) {
      if (append) {
        // 追加模式：将新数据添加到现有列表
        feedbacks.value = [...feedbacks.value, ...result.feedbacks];
      } else {
        // 重新加载模式：替换现有数据
        feedbacks.value = result.feedbacks;
      }
      total.value = result.pagination.total;
    } else {
      ElMessage.error(result.error || "获取反馈列表失败");
    }
  } catch (error) {
    console.error("获取反馈列表失败:", error);
    ElMessage.error("获取反馈列表失败，请稍后重试");
  } finally {
    loading.value = false;
    loadingMore.value = false;
  }
}

// 处理筛选变化
function handleFilterChange() {
  currentPage.value = 1;
  fetchFeedbacks();
}

// 加载更多反馈
async function loadMoreFeedbacks() {
  if (loadingMore.value || !hasMore.value) return;

  currentPage.value += 1;
  await fetchFeedbacks(true);
}

// 查看反馈详情
function viewFeedbackDetail(feedback) {
  selectedFeedback.value = feedback;
  detailDialogVisible.value = true;
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit"
  });
}

// 获取反馈类型标签
function getFeedbackTypeLabel(type) {
  const typeMap = {
    suggestion: "功能建议",
    bug: "问题反馈",
    feature: "新功能请求",
    other: "其他"
  };
  return typeMap[type] || type;
}

// 获取反馈类型标签样式
function getFeedbackTypeTag(type) {
  const typeMap = {
    suggestion: "success",
    bug: "danger",
    feature: "warning",
    other: "info"
  };
  return typeMap[type] || "";
}

// 获取反馈状态标签
function getFeedbackStatusLabel(status) {
  const statusMap = {
    pending: "待处理",
    processing: "处理中",
    resolved: "已解决",
    rejected: "已拒绝"
  };
  return statusMap[status] || status;
}

// 获取反馈状态标签样式
function getFeedbackStatusTag(status) {
  const statusMap = {
    pending: "info",
    processing: "warning",
    resolved: "success",
    rejected: "danger"
  };
  return statusMap[status] || "";
}

// 组件挂载时获取反馈列表
onMounted(() => {
  fetchFeedbacks();
});
</script>

<style scoped>
.user-feedback-list {
  width: 100%;
}

.filter-toolbar {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.load-more-section {
  margin-top: 1.5rem;
  text-align: center;
}

.load-more-btn {
  min-width: 8rem;
  font-size: 0.875rem;
}

.feedback-stats {
  margin-top: 1rem;
  text-align: center;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.stats-text {
  font-size: 0.875rem;
  color: #606266;
}

.feedback-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.feedback-detail {
  padding: 1rem;
}

.detail-item {
  margin-bottom: 1rem;
  display: flex;
}

.detail-label {
  font-weight: bold;
  width: 6rem;
  flex-shrink: 0;
}

.detail-value {
  flex: 1;
}

.content-box {
  background-color: #f5f7fa;
  padding: 1rem;
  border-radius: 0.25rem;
  white-space: pre-wrap;
  word-break: break-word;
}

.admin-reply {
  background-color: #ecf5ff;
  border-left: 0.25rem solid #409eff;
}
</style>
