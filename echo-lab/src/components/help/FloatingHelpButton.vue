<!--
  浮动帮助按钮组件
  在编辑页、播放页、内容管理页显示，固定在页面右下角
  需要明确指定 page-type 属性
-->
<template>
  <div v-if="shouldShowButton" class="floating-help-button-container" :class="{ 'mobile-device': isMobile }">
    <el-button type="info" size="small" class="floating-help-button" @click="showHelp" :title="helpButtonTitle">
      <el-icon>
        <i-ep-question-filled />
      </el-icon>
      帮助
    </el-button>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { isMobileDevice } from '@/utils/deviceDetector';

// Props
const props = defineProps({
  pageType: {
    type: String,
    required: true
  }
});

// 检测是否为移动设备
const isMobile = isMobileDevice();

// 获取路由器
const router = useRouter();

// 显示帮助按钮（现在总是显示，因为 pageType 是必需的）
const shouldShowButton = computed(() => {
  return true;
});

// 动态帮助按钮标题
const helpButtonTitle = computed(() => {
  switch (props.pageType) {
    case 'player':
      return '查看播放页使用帮助';
    case 'editor':
      return '查看编辑器使用指南';
    case 'content':
      return '查看内容管理指南';
    default:
      return '查看使用帮助';
  }
});

// 在新窗口打开对应的帮助页面
const showHelp = () => {
  const helpUrl = router.resolve({
    name: 'Help',
    params: { pageType: props.pageType }
  }).href;
  window.open(helpUrl, '_blank');
};
</script>

<style scoped>
.floating-help-button-container {
  position: fixed;
  right: 1.5rem;
  bottom: 5rem;
  /* 在反馈按钮上方 */
  z-index: 100;
}

.floating-help-button {
  border-radius: 1.5rem;
  padding: 0.5rem 1rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.2);
  background-color: #909399;
  border-color: #909399;
  color: white;
  transition: all 0.3s ease;
}

.floating-help-button:hover {
  background-color: #73767a;
  border-color: #73767a;
  transform: translateY(-0.125rem);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.3);
}

.floating-help-button:focus {
  background-color: #73767a;
  border-color: #73767a;
}

/* 移动端样式 */
.mobile-device .floating-help-button-container {
  right: 1rem;
  bottom: 4rem;
  /* 在反馈按钮上方 */
}

.mobile-device .floating-help-button {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}
</style>
