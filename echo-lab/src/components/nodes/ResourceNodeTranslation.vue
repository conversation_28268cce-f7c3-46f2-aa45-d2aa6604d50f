<!--
  资源节点翻译组件
  处理文本翻译功能
-->
<template>
  <div class="translation-component">
    <div class="preview-section">
      <div class="preview-header">
        <div>翻译：</div>
        <el-button-group>
          <el-button size="small" type="primary" @click="showDialog" :disabled="!hasSourceNode">
            <el-icon class="el-icon--left">
              <i-ep-edit />
            </el-icon>
            编辑翻译
          </el-button>
        </el-button-group>
      </div>
      <div class="preview-info"
        :class="{ 'success-bg': getTranslationPercentage() === 100 && totalTranslationCount > 0, 'warning-bg': getTranslationPercentage() < 100 && totalTranslationCount > 0 }">
        <div class="preview-progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: getTranslationPercentage() + '%' }"></div>
          </div>
          <div class="progress-text">
            已翻译: {{ translationCount }}/{{ totalTranslationCount }}
            <span v-if="getTranslationPercentage() === 100" class="status-tag success">完成</span>
            <span v-else-if="totalTranslationCount > 0" class="status-tag warning">未完成</span>
            <span v-else class="status-tag info">无需翻译</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 翻译编辑对话框 -->
    <standard-dialog v-model="dialogVisible" title="编辑翻译" width="80%" class="translation-dialog">
      <div class="dialog-toolbar">
        <div class="toolbar-main">
          <div class="targets-section">
            <span class="label">目标语言：</span>
            <el-select v-model="params.targets" multiple @change="handleTargetsChange" size="large"
              class="language-select">
              <el-option v-for="lang in translationLanguages" :key="lang.value" :label="lang.label"
                :value="lang.value" />
            </el-select>
          </div>
          
          <!-- 勾选控制区域 -->
          <div class="selection-controls">
            <el-checkbox
              :model-value="isAllSelected"
              @change="toggleSelectAll"
              :indeterminate="selectedCount > 0 && !isAllSelected"
            >
              全选
            </el-checkbox>
            <span class="selection-info">
              已选择: {{ selectedCount }}/{{ filteredTranslations.length }}
            </span>
            <el-button
              size="small"
              type="warning"
              @click="selectUntranslated"
              :disabled="totalUntranslatedCount === 0"
            >
              选择未翻译的 ({{ totalUntranslatedCount }})
            </el-button>
          </div>
        </div>
      </div>

      <div class="translations-table-container" v-if="params.targets.length > 0">
        <el-table-v2 ref="translationTableRef" :data="filteredTranslations" :columns="translationTableColumns"
          :width="1200" :height="360" :estimated-row-height="110" row-key="id" border>
          <template #cell="{ column, rowData, rowIndex }">
            <!-- 勾选列 -->
            <div v-if="column.key === 'selection'" class="cell-content center">
              <el-checkbox
                :model-value="selectedItems.has(rowData.id)"
                @change="toggleItemSelection(rowData)"
              />
            </div>
            
            <!-- 序号列 -->
            <div v-else-if="column.key === 'index'" class="cell-content center">
              {{ rowIndex + 1 }}
            </div>

            <!-- 原文列 -->
            <div v-else-if="column.key === 'original'" class="cell-content">
              <div class="original-text-cell">{{ rowData.content }}</div>
              <div class="language-detection">
                <span class="language-label">语言:</span>
                <el-tag size="small" type="info">{{ getLanguageLabel(rowData.detectedLanguage) }}</el-tag>
              </div>
            </div>

            <!-- 翻译列 -->
            <div v-else-if="column.key.startsWith('translation_')" class="cell-content">
              <div class="translation-cell">
                <el-input v-model="rowData.translations[column.targetLang]" type="textarea" :rows="3"
                  :class="{ 'empty-translation': !rowData.translations[column.targetLang] || !rowData.translations[column.targetLang].trim() }"
                  :placeholder="`请输入${getLanguageLabel(column.targetLang)}翻译`" />
                <el-button type="primary" size="small" @click="translateItem(rowData, column.targetLang)"
                  :loading="rowData.translatingStates && rowData.translatingStates[column.targetLang]"
                  class="translate-btn">
                  <el-icon style="margin-right: 0.25rem;">
                    <i-ep-edit />
                  </el-icon>
                  翻译
                </el-button>
              </div>
            </div>
          </template>
        </el-table-v2>
      </div>
      <div class="empty-state" v-else>
        <el-empty description="请选择目标语言" />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveTranslations">保存修改</el-button>
          <el-dropdown>
            <el-button type="success" :loading="isTranslating" :disabled="selectedCount === 0">
              <el-icon style="margin-right: 0.25rem;">
                <i-ep-edit />
              </el-icon>
              批量翻译选中项 ({{ selectedCount }})
              <el-icon class="el-icon--right">
                <i-ep-arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="translateSelected(false)" :disabled="selectedCount === 0">
                  翻译选中项 ({{ selectedCount }})
                </el-dropdown-item>
                <el-dropdown-item @click="translateSelected(true)" :disabled="selectedUntranslatedCount === 0">
                  补全选中项中的未翻译 ({{ selectedUntranslatedCount }})
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>
    </standard-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import StandardDialog from '../common/StandardDialog.vue';
import { getLanguageLabel, SUPPORTED_LANGUAGES } from '@/config/languages';
import { translateService } from '@/services/api';
import { TRANSLATION_TIMEOUT } from '@/config/translation';

const props = defineProps({
  nodeId: {
    type: String,
    required: true
  },
  processedResult: {
    type: Object,
    default: () => null
  },
  params: {
    type: Object,
    required: true
  },
  hasSourceNode: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:params', 'process-node']);

// 对话框状态
const dialogVisible = ref(false);
const isTranslating = ref(false);

// 翻译项
const translationItems = ref([]);

// 表格引用
const translationTableRef = ref(null);

// 勾选功能相关变量
const selectedItems = ref(new Set()); // 勾选的项目ID集合

// el-table-v2 动态列配置
const translationTableColumns = computed(() => {
  const columns = [
    {
      key: 'selection',
      title: '',
      dataKey: 'selection',
      width: 50,
      align: 'center'
    },
    {
      key: 'index',
      title: '序号',
      dataKey: 'index',
      width: 80,
      align: 'center'
    },
    {
      key: 'original',
      title: '原文',
      dataKey: 'original',
      width: 200
    }
  ];

  // 为每个目标语言添加一列
  props.params.targets.forEach(target => {
    columns.push({
      key: `translation_${target}`,
      title: getLanguageLabel(target),
      dataKey: `translation_${target}`,
      width: 250,
      targetLang: target // 自定义属性，用于在模板中识别目标语言
    });
  });

  return columns;
});

// 监听对话框显示状态，延时刷新表格
watch(dialogVisible, (newVal) => {
  if (newVal) {
    // 对话框打开时，延时刷新表格以确保正确渲染
    nextTick(() => {
      setTimeout(() => {
        // 触发resize事件让表格重新计算尺寸
        window.dispatchEvent(new Event('resize'));

        // 如果表格有scrollToTop方法，调用它来触发重新渲染
        if (translationTableRef.value && typeof translationTableRef.value.scrollToTop === 'function') {
          translationTableRef.value.scrollToTop();
        }
      }, 200);
    });
  }
});

// 翻译相关计算属性
const translationCount = computed(() => {
  if (!props.processedResult) return 0;

  // 优先使用 sourceSegments 作为数据源，确保一致性
  if (!props.processedResult.sourceSegments || props.processedResult.sourceSegments.length === 0) {
    return 0;
  }

  // 获取目标语言列表
  const targets = props.params.targets || [];
  if (targets.length === 0) return 0;

  // 获取所有非关键词的分句
  const validSegments = props.processedResult.sourceSegments.filter(seg =>
    !seg.isKeyword && seg.type !== 'keyword'
  );

  // 调试信息
  console.log('翻译计算调试信息:', {
    totalSegments: props.processedResult.sourceSegments.length,
    validSegments: validSegments.length,
    targets: targets,
    translations: props.processedResult.translations
  });

  // 计算已翻译的项目数量
  let count = 0;
  validSegments.forEach(segment => {
    targets.forEach(target => {
      // 检查翻译数据
      const hasTranslation = props.processedResult.translations &&
        props.processedResult.translations[target] &&
        props.processedResult.translations[target][segment.id] &&
        props.processedResult.translations[target][segment.id].trim();

      if (hasTranslation) {
        count++;
        console.log(`已翻译: ${segment.id} -> ${target}: "${props.processedResult.translations[target][segment.id].substring(0, 20)}..."`);
      } else {
        console.log(`未翻译: ${segment.id} -> ${target}`);
      }
    });
  });

  console.log(`翻译统计: ${count}/${targets.length * validSegments.length}`);
  return count;
});

const totalTranslationCount = computed(() => {
  if (!props.processedResult) return 0;

  // 优先使用 sourceSegments 作为数据源，确保一致性
  if (!props.processedResult.sourceSegments || props.processedResult.sourceSegments.length === 0) {
    return 0;
  }

  // 获取目标语言列表
  const targets = props.params.targets || [];
  if (targets.length === 0) return 0;

  // 获取所有非关键词的分句数量
  const validSegmentCount = props.processedResult.sourceSegments.filter(seg =>
    !seg.isKeyword && seg.type !== 'keyword'
  ).length;

  // 计算所有目标语言的翻译项总数（排除关键词）
  return targets.length * validSegmentCount;
});

// 可用于翻译的语言列表
const translationLanguages = computed(() => {
  // 获取当前源语言
  const sourceLanguages = new Set();

  // 从源段落中收集所有语言
  if (props.processedResult && props.processedResult.sourceSegments) {
    props.processedResult.sourceSegments.forEach(segment => {
      if (segment.language) {
        sourceLanguages.add(segment.language);
      }
    });
  }

  // 如果没有找到源语言，返回所有支持的语言
  if (sourceLanguages.size === 0) {
    return SUPPORTED_LANGUAGES;
  }

  // 返回所有不在源语言集合中的语言
  return SUPPORTED_LANGUAGES.filter(lang => !sourceLanguages.has(lang.value));
});

// 勾选相关计算属性
const selectedCount = computed(() => selectedItems.value.size);

const isAllSelected = computed(() => {
  return filteredTranslations.value.length > 0 &&
         selectedCount.value === filteredTranslations.value.length;
});

const selectedUntranslatedCount = computed(() => {
  return filteredTranslations.value.filter(item =>
    selectedItems.value.has(item.id) && !isFullyTranslated(item)
  ).length;
});

const totalUntranslatedCount = computed(() => {
  return filteredTranslations.value.filter(item => !isFullyTranslated(item)).length;
});

// 过滤翻译 - 排除关键词
const filteredTranslations = computed(() => {
  return translationItems.value.filter(item =>
    !item.isKeyword && item.type !== 'keyword'
  );
});

// 获取翻译完成百分比
function getTranslationPercentage() {
  if (!props.processedResult || !props.processedResult.translations || totalTranslationCount.value === 0) {
    return 0;
  }
  return Math.round((translationCount.value / totalTranslationCount.value) * 100);
}

// 处理目标语言变化
function handleTargetsChange() {
  emit('update:params', props.params);
  emit('process-node');
}

// 显示对话框
function showDialog() {
  // 强制重新处理节点
  emit('process-node');

  // 准备翻译项
  prepareTranslationItems();

  // 清理勾选状态
  selectedItems.value.clear();

  // 显示对话框
  dialogVisible.value = true;
}

// 准备翻译项
function prepareTranslationItems() {
  translationItems.value = [];

  // 如果没有处理结果，但有参数中的翻译数据，直接使用参数中的数据
  if (!props.processedResult || !props.processedResult.sourceSegments) {
    if (props.params.translations && Object.keys(props.params.translations).length > 0) {
      // 获取所有翻译项的ID
      const allIds = new Set();
      Object.values(props.params.translations).forEach(langTranslations => {
        Object.keys(langTranslations).forEach(id => allIds.add(id));
      });

      // 为每个ID创建翻译项
      allIds.forEach(id => {
        // 尝试从音频项中获取内容
        let content = '';
        let detectedLanguage = 'auto';

        // 如果有音频项，尝试从中获取内容
        if (props.params.audioItems) {
          const audioItem = props.params.audioItems.find(item => item.id === id);
          if (audioItem) {
            content = audioItem.text || '';
            detectedLanguage = audioItem.language || 'auto';
          }
        }

        const translations = {};
        props.params.targets.forEach(target => {
          if (props.params.translations[target]) {
            translations[target] = props.params.translations[target][id] || '';
          } else {
            translations[target] = '';
          }
        });

        // 初始化每个语言的翻译状态
        const translatingStates = {};
        props.params.targets.forEach(target => {
          translatingStates[target] = false;
        });

        translationItems.value.push({
          id: id,
          content: content || '未找到原文',
          translations,
          language: detectedLanguage,
          detectedLanguage: detectedLanguage,
          translatingStates
        });
      });
      return;
    }
  }

  // 正常处理流程：从处理结果中获取数据
  if (props.processedResult && props.processedResult.sourceSegments && props.processedResult.sourceSegments.length > 0) {
    props.processedResult.sourceSegments.forEach(segment => {
      const translations = {};

      // 为每个目标语言准备翻译
      props.params.targets.forEach(target => {
        // 优先使用处理结果中的翻译，如果没有则使用参数中的翻译
        if (props.processedResult.translations && props.processedResult.translations[target]) {
          translations[target] = props.processedResult.translations[target][segment.id] || '';
        } else if (props.params.translations && props.params.translations[target]) {
          translations[target] = props.params.translations[target][segment.id] || '';
        } else {
          translations[target] = '';
        }
      });

      // 使用源节点提供的语言信息
      const detectedLanguage = segment.language || 'auto';

      // 初始化每个语言的翻译状态
      const translatingStates = {};
      props.params.targets.forEach(target => {
        translatingStates[target] = false;
      });

      // 添加普通分句
      translationItems.value.push({
        id: segment.id,
        content: segment.content,
        translations,
        language: detectedLanguage,
        detectedLanguage: detectedLanguage,
        translatingStates,
        type: segment.type || 'normal',
        isKeyword: segment.isKeyword || segment.type === 'keyword'
      });

      // 处理关键词（如果有）
      if (segment.keywords && segment.keywords.length > 0) {
        segment.keywords.forEach(keyword => {
          const keywordTranslations = {};

          // 关键词不需要翻译，但需要创建空的翻译对象
          props.params.targets.forEach(target => {
            keywordTranslations[target] = '';
          });

          // 关键词的语言通常与其所属分句相同
          const keywordLanguage = keyword.language || segment.language || 'auto';

          // 初始化关键词的翻译状态
          const keywordTranslatingStates = {};
          props.params.targets.forEach(target => {
            keywordTranslatingStates[target] = false;
          });

          // 添加关键词翻译项
          translationItems.value.push({
            id: keyword.id,
            content: keyword.text,
            translations: keywordTranslations,
            language: keywordLanguage,
            detectedLanguage: keywordLanguage,
            translatingStates: keywordTranslatingStates,
            type: 'keyword',
            isKeyword: true,
            sourceSegmentId: segment.id
          });
        });
      }
    });
  }
}



// 移除translateAllLanguages函数，所有操作都限制在选中项

// 翻译单个项
async function translateItem(item, targetLang) {
  if (!item || !item.content || !targetLang) {
    return;
  }

  // 如果是关键词，不进行翻译
  if (item.isKeyword || item.type === 'keyword') {
    ElMessage.info('关键词不需要翻译');
    return;
  }

  try {
    // 设置特定语言的翻译状态
    if (!item.translatingStates) {
      item.translatingStates = {};
    }
    item.translatingStates[targetLang] = true;

    // 调用批量翻译API（单个项也使用批量接口）
    const result = await translateService.translateTexts([{
      id: item.id,
      content: item.content,
      language: item.language
    }], [targetLang]);

    if (result.success && result.translations && result.translations.length > 0) {
      const translation = result.translations[0];
      if (translation.translations[targetLang]) {
        item.translations[targetLang] = translation.translations[targetLang];
      }
    } else {
      throw new Error(result.error || '翻译失败');
    }
  } catch (error) {
    console.error('翻译失败:', error);
    ElMessage.error('翻译失败: ' + error.message);
  } finally {
    // 清除特定语言的翻译状态
    if (item.translatingStates) {
      item.translatingStates[targetLang] = false;
    }
  }
}

// 保存翻译
function saveTranslations() {
  if (translationItems.value.length === 0) {
    dialogVisible.value = false;
    return;
  }

  // 更新翻译
  const translations = {};

  // 为每个目标语言创建翻译对象
  props.params.targets.forEach(target => {
    translations[target] = {};

    // 添加每个项的翻译
    translationItems.value.forEach(item => {
      if (item.translations[target]) {
        translations[target][item.id] = item.translations[target];
      }
    });
  });

  // 更新节点参数
  const updatedParams = { ...props.params, translations };
  emit('update:params', updatedParams);

  // 重新处理节点
  emit('process-node');

  // 关闭对话框
  dialogVisible.value = false;

  ElMessage.success('翻译已保存');
}

// 勾选相关方法
// 切换单个项目的勾选状态
function toggleItemSelection(item) {
  if (selectedItems.value.has(item.id)) {
    selectedItems.value.delete(item.id);
  } else {
    selectedItems.value.add(item.id);
  }
}

// 切换全选状态
function toggleSelectAll() {
  if (isAllSelected.value) {
    // 取消全选
    selectedItems.value.clear();
  } else {
    // 全选
    filteredTranslations.value.forEach(item => {
      selectedItems.value.add(item.id);
    });
  }
}

// 选择未翻译的项目
function selectUntranslated() {
  selectedItems.value.clear();
  filteredTranslations.value.forEach(item => {
    if (!isFullyTranslated(item)) {
      selectedItems.value.add(item.id);
    }
  });
}

// 判断项目是否完全翻译
function isFullyTranslated(item) {
  return props.params.targets.every(target => 
    item.translations[target] && item.translations[target].trim()
  );
}

// 翻译选中的项目
async function translateSelected(onlyUntranslated = false) {
  const selectedItemsList = filteredTranslations.value.filter(item =>
    selectedItems.value.has(item.id) && !item.isKeyword && item.type !== 'keyword'
  );

  if (selectedItemsList.length === 0) {
    ElMessage.warning('没有选中的可翻译项目');
    return;
  }

  let itemsToTranslate = selectedItemsList;

  if (onlyUntranslated) {
    itemsToTranslate = selectedItemsList.filter(item => !isFullyTranslated(item));

    if (itemsToTranslate.length === 0) {
      ElMessage.warning('选中项目中没有需要翻译的内容');
      return;
    }
  }

  return batchTranslateItems(itemsToTranslate, onlyUntranslated, '选中');
}

// 批量翻译项目的核心逻辑
async function batchTranslateItems(itemsToTranslate, onlyUntranslated, description) {
  if (itemsToTranslate.length === 0) {
    return;
  }

  try {
    isTranslating.value = true;

    // 显示加载提示
    const loadingText = onlyUntranslated
      ? `正在补全${description}的翻译，请耐心等待...`
      : `正在重新翻译${description}，请耐心等待...`;

    const loadingInstance = ElLoading.service({
      text: loadingText,
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 准备翻译请求
    const textsToTranslate = itemsToTranslate.map(item => ({
      id: item.id,
      content: item.content,
      language: item.detectedLanguage
    }));

    // 分批处理，每批最多10个
    const BATCH_SIZE = 10;
    const batches = [];

    for (let i = 0; i < textsToTranslate.length; i += BATCH_SIZE) {
      batches.push(textsToTranslate.slice(i, i + BATCH_SIZE));
    }

    console.log(`翻译任务分为 ${batches.length} 批，每批最多 ${BATCH_SIZE} 个项目`);

    let successCount = 0;
    let failureCount = 0;

    // 处理每一批
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];

      // 更新加载提示
      loadingInstance.setText(`${loadingText} (${batchIndex + 1}/${batches.length}批)`);

      try {
        // 调用翻译API
        const result = await translateService.translateTexts(batch, props.params.targets);

        if (result.success && result.translations) {
          // 更新翻译结果
          result.translations.forEach(translation => {
            const item = translationItems.value.find(i => i.id === translation.id);
            if (item) {
              Object.keys(translation.translations).forEach(lang => {
                // 如果是只翻译未翻译部分，且该语言已有翻译，则不更新
                if (!onlyUntranslated || !item.translations[lang] || !item.translations[lang].trim()) {
                  item.translations[lang] = translation.translations[lang];
                  successCount++;
                }
              });
            }
          });
        } else {
          console.error(`第 ${batchIndex + 1} 批翻译失败:`, result.error);
          failureCount += batch.length;
        }
      } catch (error) {
        console.error(`第 ${batchIndex + 1} 批翻译失败:`, error);
        failureCount += batch.length;
      }
    }

    // 关闭加载提示
    loadingInstance.close();

    // 显示最终结果
    const actionText = onlyUntranslated ? '补全' : '翻译';
    if (successCount > 0) {
      ElMessage.success(`已完成${description}的${actionText}: 成功 ${successCount} 个${failureCount > 0 ? `, 失败 ${failureCount} 个` : ''}`);
    } else {
      ElMessage.error(`${description}的${actionText}全部失败`);
    }
  } catch (error) {
    console.error('批量翻译失败:', error);
    ElMessage.error('批量翻译失败: ' + error.message);
  } finally {
    isTranslating.value = false;
  }
}
</script>

<style scoped>
/* 预览区域样式 */
.preview-section {
  margin-top: 0.75rem;
  padding: 0.625rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: bold;
  color: #606266;
}

.preview-info {
  text-align: center;
  font-size: 0.875rem;
  padding: 0.625rem;
  border-radius: 0.25rem;
  margin-top: 0.75rem;
}

/* 统一的预览背景颜色 */
.success-bg {
  color: #67c23a;
  background-color: #f0f9eb;
}

.warning-bg {
  color: #e6a23c;
  background-color: #fdf6ec;
}

.preview-progress {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.progress-bar {
  height: 0.5rem;
  background-color: #e4e7ed;
  border-radius: 0.25rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #67C23A;
  border-radius: 0.25rem;
  transition: width 0.3s ease;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.status-tag {
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.status-tag.success {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 0.0625rem solid #e1f3d8;
}

.status-tag.warning {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 0.0625rem solid #faecd8;
}

.status-tag.info {
  background-color: #f4f4f5;
  color: #909399;
  border: 0.0625rem solid #e9e9eb;
}

/* 翻译对话框特殊样式 */
.translation-dialog :deep(.standard-dialog-content) {
  overflow: hidden !important;
  /* 禁用StandardDialog的滚动条 */
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dialog-toolbar {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  /* 工具栏不收缩 */
  padding: 0.5rem;
  /* 最小内边距 */
  border-bottom: 1px solid #e4e7ed;
}

.selection-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.selection-info {
  font-size: 0.875rem;
  color: #606266;
  white-space: nowrap;
}

.toolbar-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  width: 100%;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.dialog-actions {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  /* 进一步减少间距 */
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.translations-table-container {
  flex: 1;
  /* 占据剩余空间 */
  overflow: hidden;
  /* 让表格自己处理滚动 */
  min-height: 0;
  /* 确保flex子元素可以收缩 */
  padding: 0 0.75rem 0.75rem 0.75rem;
  /* 减少内边距 */
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  /* 减少内边距 */
}

.targets-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.label {
  white-space: nowrap;
  font-size: 0.9rem;
}

/* 自定义下拉选择框样式 */
.language-select {
  width: 25rem;
  max-width: 100%;
}

.search-input {
  width: 15.625rem;
  max-width: 100%;
}

:deep(.el-select) {
  flex: 1;
}

:deep(.el-select .el-input__wrapper) {
  padding: 0 0.75rem;
  min-height: 2.5rem;
}

:deep(.el-select .el-input__suffix) {
  padding-left: 0.75rem;
}

:deep(.el-select .el-select__tags) {
  padding: 0.25rem 0;
}

.original-text-cell {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  /* 减少间距 */
  max-height: 5rem;
  /* 减少最大高度 */
  overflow-y: auto;
  word-break: break-all;
  white-space: pre-wrap;
  padding: 0.375rem;
  /* 减少内边距 */
  border-radius: 0.25rem;
  line-height: 1.4;
  /* 减少行高 */
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  font-size: 0.875rem;
  /* 减少字体大小 */
}

.language-detection {
  display: flex;
  align-items: center;
  margin-top: 0.25rem;
  /* 减少间距 */
  font-size: 0.75rem;
}

.language-label {
  margin-right: 0.375rem;
  /* 减少间距 */
}

.empty-translation {
  border-color: #E6A23C;
  background-color: rgba(230, 162, 60, 0.05);
}

/* 翻译单元格样式 */
.translation-cell {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  /* 减少间距 */
}

.translate-btn {
  align-self: flex-start;
  margin-top: 0.125rem;
  /* 减少间距 */
}

/* el-table-v2 样式 */
.translations-table-container .cell-content {
  padding: 0.375rem;
  /* 减少单元格内边距 */
  display: flex;
  align-items: flex-start;
  word-break: break-word;
  flex-direction: column;
  justify-content: center;
  height: auto;
  min-height: auto;
}

.translations-table-container .cell-content.center {
  align-items: center;
  justify-content: center;
  flex-direction: row;
}

/* 翻译表格特殊样式 */
.translations-table-container .original-text-cell {
  max-height: none;
  overflow: visible;
  white-space: normal;
  word-wrap: break-word;
}

.translations-table-container .translation-cell {
  width: 100%;
  height: auto;
}
</style>
