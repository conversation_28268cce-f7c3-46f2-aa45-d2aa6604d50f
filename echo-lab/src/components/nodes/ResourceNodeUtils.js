/**
 * 资源节点工具函数
 * 提供资源节点组件使用的通用工具函数
 */

import { getSpeakerInfo } from '@/services/speakerMappingService';
import { getVoiceById } from '@/services/ttsInfoService';
import nodeFactory from '@/core/factories/NodeFactory';

/**
 * 获取源节点标签
 * @param {Object} sourceNode - 源节点对象
 * @returns {string} 源节点标签
 */
export function getSourceNodeLabel(sourceNode) {
  if (!sourceNode) return '未知节点';

  // 如果有自定义名称，优先显示
  if (sourceNode.customName) {
    return sourceNode.customName;
  }

  // 获取节点类型标签
  const typeLabel = nodeFactory.getNodeTypeConfig(sourceNode.type)?.label || sourceNode.type;

  // 使用节点编号
  const nodeNumber = sourceNode.number || 1;

  return `${typeLabel} #${nodeNumber}`;
}

/**
 * 获取状态标签类型
 * @param {string} status - 状态
 * @returns {string} 标签类型
 */
export function getStatusTagType(status) {
  switch (status) {
    case 'success':
      return 'success';
    case 'generating':
      return 'warning';
    case 'error':
      return 'danger';
    default:
      return 'info';
  }
}

/**
 * 获取状态标签文本
 * @param {string} status - 状态
 * @returns {string} 标签文本
 */
export function getStatusLabel(status) {
  switch (status) {
    case 'success':
      return '已生成';
    case 'generating':
      return '生成中';
    case 'error':
      return '生成失败';
    default:
      return '待生成';
  }
}

/**
 * 获取说话人标签类型 - 简化版本
 * @param {string} speaker - 说话人
 * @returns {string} 标签类型
 */
export function getSpeakerTagType(speaker) {
  // 统一使用简单的标签样式
  if (!speaker || speaker === 'default') {
    return 'info';
  }
  return 'primary';
}

/**
 * 获取用户自定义映射
 * @returns {Object|null} 用户自定义映射
 */
export function getUserMappings() {
  try {
    // 尝试从本地存储获取用户自定义映射
    const mappingsStr = localStorage.getItem('echolab_speaker_mappings');
    if (mappingsStr) {
      return JSON.parse(mappingsStr);
    }
  } catch (error) {
    console.error("获取本地存储映射失败:", error);
  }
  return null;
}

/**
 * 获取音频项的说话人信息（方案A：直接使用voice_db_id）
 * @param {Object} item - 音频项
 * @returns {Promise<Object>} 说话人信息
 */
export async function getItemSpeakerInfo(item) {
  // 按照方案A的设计，音频项必定有voice_db_id
  if (!item.voice_db_id) {
    throw new Error(`[方案A] 音频项缺少voice_db_id，这不应该发生！item: ${JSON.stringify(item)}`);
  }

  try {
    console.log(`[方案A] 使用音频项的voice_db_id: ${item.voice_db_id}, speaker: ${item.speaker}, language: ${item.language}`);
    const voiceInfo = await getVoiceById(item.voice_db_id);

    if (!voiceInfo) {
      throw new Error(`[方案A] 无法获取voice_db_id=${item.voice_db_id}对应的声音信息`);
    }

    console.log(`[方案A] 成功获取声音信息: ${voiceInfo.name}`);
    return {
      name: item.speaker || 'default',
      speaker_id: voiceInfo.speaker_id,
      id: item.voice_db_id,
      voice_info: voiceInfo,
      displayName: voiceInfo.name || `声音ID: ${item.voice_db_id}`,
      service_id: voiceInfo.service_id || "google",
    };
  } catch (error) {
    console.error(`[方案A] 获取声音信息失败:`, error);
    throw error; // 不再fallback，直接抛出错误，便于发现问题
  }
}



/**
 * 准备音频生成请求项（方案A：直接使用音频项的voice_db_id）
 * @param {Object} item - 音频项
 * @param {Object} speakerInfo - 说话人信息（用于获取service_id等信息）
 * @param {string} finalText - 处理后的文本
 * @returns {Object} 请求项
 */
export function prepareAudioRequestItem(item, speakerInfo, finalText) {
  // 按照方案A的设计，直接使用音频项的voice_db_id
  if (!item.voice_db_id) {
    throw new Error(`[方案A] 音频项缺少voice_db_id，这不应该发生！item: ${JSON.stringify(item)}`);
  }

  console.log(`[方案A] 准备音频请求: id=${item.id}, voice_db_id=${item.voice_db_id}`);

  return {
    id: item.id,
    text: finalText,
    language: item.language || 'auto',
    speed: item.speed || 1.0,
    voice_db_id: item.voice_db_id // 直接使用音频项的voice_db_id
  };
}


/**
 * 高亮处理后文本中的替换词
 * @param {Object} item - 音频项
 * @returns {string} 高亮后的HTML
 */
export function highlightReplacedWords(item) {
  // 如果没有替换或处理后文本，直接返回处理后文本
  if (!item || !item.processedText || !item.replacements || item.replacements.length === 0) {
    return item.processedText || '';
  }

  // 创建一个替换映射，按位置排序
  const replacementMap = [...item.replacements].sort((a, b) => a.position - b.position);

  // 获取处理后的文本
  let processedText = item.processedText;
  let result = '';
  let lastIndex = 0;

  // 遍历替换项，高亮显示替换词
  for (const rep of replacementMap) {
    // 查找处理后文本中的替换内容
    const index = processedText.indexOf(rep.replacement, lastIndex);
    if (index === -1) continue;

    // 添加未变化的文本
    result += processedText.substring(lastIndex, index);

    // 添加高亮的替换词
    result += `<span class="highlighted-replacement">${rep.replacement}</span>`;

    // 更新lastIndex
    lastIndex = index + rep.replacement.length;
  }

  // 添加剩余的文本
  result += processedText.substring(lastIndex);

  return result;
}
