<!--
  合集信息条组件
  显示合集基本信息，点击展开完整列表
-->
<template>
  <div class="collection-info" v-if="collection" @click="showDrawer = true">
    <div class="collection-icon">
      <el-icon><i-ep-folder /></el-icon>
    </div>
    <div class="collection-details">
      <span class="collection-name">合集：{{ collection.name }}</span>
      <span class="collection-count">{{ currentIndex + 1 }}/{{ collection.itemCount }}</span>
    </div>
    <div class="collection-arrow">
      <el-icon><i-ep-arrow-right /></el-icon>
    </div>
    
    <!-- 合集内容抽屉 -->
    <el-drawer v-model="showDrawer" :size="isMobile ? '80%' : '400px'" :direction="isMobile ? 'btt' : 'rtl'">
      <template #header>
        <div class="drawer-header">
          <h3>{{ collection.name }}</h3>
          <div class="play-mode-controls">
            <el-button-group size="small">
              <el-button :type="props.playMode === 'sequence' ? 'primary' : 'default'" @click="setPlayMode('sequence')">
                <el-icon><i-ep-sort /></el-icon>
                顺序
              </el-button>
              <el-button :type="props.playMode === 'reverse' ? 'primary' : 'default'" @click="setPlayMode('reverse')">
                <el-icon><i-ep-sort style='transform: rotate(180deg)' /></el-icon>
                倒序
              </el-button>
              <el-button :type="props.playMode === 'random' ? 'primary' : 'default'" @click="setPlayMode('random')">
                <el-icon><i-ep-refresh /></el-icon>
                随机
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>
      
      <div class="collection-list-container">
        <div class="collection-list">
        <div v-for="(item, index) in sortedItems" 
             :key="item.id" 
             class="collection-item"
             :class="{ 'current': index === currentItemInSortedList }"
             @click="selectItem(index)">
          <div class="item-index" :class="{ 'active': index === currentItemInSortedList }">{{ index + 1 }}</div>
          <div class="item-thumbnail">
            <ResponsiveImage v-if="item.content?.thumbnailUrl" :src="item.content.thumbnailUrl" alt="缩略图" loading="lazy" />
            <div v-else class="thumbnail-placeholder">
              <el-icon><i-ep-video-play /></el-icon>
            </div>
          </div>
          <div class="item-info">
            <div class="item-title">{{ item.content?.name || '未知内容' }}</div>
            <div class="item-meta" v-if="item.content?.description">
              {{ item.content.description }}
            </div>
          </div>
          <div class="item-status">
            <el-tag v-if="index === currentItemInSortedList" type="primary" size="small">正在播放</el-tag>
          </div>
        </div>
      </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { isMobileDevice } from '@/utils/deviceDetector';
import ResponsiveImage from '@/components/common/ResponsiveImage.vue';

const props = defineProps({
  collection: Object,
  currentIndex: { type: Number, default: 0 },
  playMode: { type: String, default: 'sequence' }
});

const emit = defineEmits(['select-item', 'play-mode-change']);

const route = useRoute();
const showDrawer = ref(false);
const isMobile = computed(() => isMobileDevice());

// 排序后的合集项目
const sortedItems = computed(() => {
  if (!props.collection?.items) return [];
  return [...props.collection.items].sort((a, b) => a.sortOrder - b.sortOrder);
});

// 计算当前项目在排序后列表中的索引
const currentItemInSortedList = computed(() => {
  // 确保 currentIndex 是有效的
  if (props.currentIndex < 0 || !sortedItems.value.length) {
    console.log('🎥 CollectionInfo: 无效的索引或空列表', {
      currentIndex: props.currentIndex,
      sortedItemsLength: sortedItems.value.length
    });
    return -1;
  }
  
  console.log('🎥 CollectionInfo: 当前项目索引', {
    currentIndex: props.currentIndex,
    sortedItemsLength: sortedItems.value.length,
    currentItem: sortedItems.value[props.currentIndex]?.content?.name || '未知'
  });
  
  // currentIndex 应该直接对应排序后的索引
  return props.currentIndex;
});

const selectItem = (sortedIndex) => {
  console.log('🎥 CollectionInfo: 选择项目', {
    sortedIndex,
    playMode: props.playMode,
    currentIndex: props.currentIndex
  });
  showDrawer.value = false;
  emit('select-item', sortedIndex, props.playMode);
};

const setPlayMode = (mode) => {
  emit('play-mode-change', mode);
  
  // 给用户即时反馈
  const modeText = {
    sequence: '顺序播放',
    reverse: '倒序播放', 
    random: '随机播放'
  };
  
  ElMessage({
    message: `已切换到${modeText[mode]}模式`,
    type: 'success',
    duration: 1500,
    showClose: false
  });
};

// 组件挂载时的初始化
onMounted(() => {
  console.log('🎥 CollectionInfo: 组件挂载', {
    collection: props.collection?.name || '无',
    currentIndex: props.currentIndex,
    playMode: props.playMode
  });
});

</script>

<style scoped>
.collection-info {
  display: flex;
  align-items: center;
  padding: 1rem 1.25rem;
  background: #007bff;
  border-radius: 0.5rem;
  cursor: pointer;
  margin-bottom: 1rem;
}

.collection-icon {
  color: white;
  margin-right: 1rem;
  font-size: 1.125rem;
}

.collection-details {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.collection-name {
  font-weight: 500;
  color: white;
  font-size: 0.875rem;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.collection-count {
  font-size: 0.6875rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.2;
}

.collection-arrow {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  margin-left: 0.5rem;
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.drawer-header h3 {
  margin: 0;
  flex: 1;
}

.play-mode-controls {
  flex-shrink: 0;
}

.collection-list-container {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.collection-list {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.collection-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background 0.15s ease;
}

.collection-item:hover {
  background: #f8f9fa;
}

.collection-item.current {
  background: #e3f2fd;
  border-left: 3px solid #2196f3;
}

.item-index {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 500;
  transition: all 0.2s ease;
}

.item-index.active,
.collection-item.current .item-index {
  background: #2196f3;
  color: white;
  transform: scale(1.1);
}

.item-thumbnail {
  width: 4rem;
  height: 2.25rem;
  border-radius: 0.25rem;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
}

.thumbnail-placeholder {
  color: #909399;
  font-size: 1.25rem;
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-title {
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-meta {
  font-size: 0.875rem;
  color: #909399;
  margin-top: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>