<script setup>
import { ref, computed, reactive, onMounted, onUnmounted } from 'vue';
import VideoPlayerBase from './VideoPlayerBase.vue';
import UnifiedControlPanel from './UnifiedControlPanel.vue';
import { useUserStore } from '@/stores/userStore';
import { checkFeaturePermission } from '@/services/featurePermissionService';

const props = defineProps({
  timeline: {
    type: Array,
    default: () => []
  },
  currentIndex: {
    type: Number,
    default: 0
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  videoConfig: {
    type: Object,
    default: () => ({
      cover: {
        imageUrl: "",
        duration: 3
      },
      copyright: {
        text: "",
        position: "bottomRight"
      }
    })
  },
  configJson: {
    type: Object,
    default: null
  },
  contentData: {
    type: Object,
    default: null
  },
});

const emit = defineEmits(['timeupdate', 'loading-change', 'open-settings', 'index-change', 'toggle-playlist', 'video-end']);

const playerBase = ref(null);
const loop = ref(false);
const showFirstTimeTooltip = ref(false);
const hasExportPermission = ref(false);
const userStore = useUserStore();
const progressValue = ref(0);

const uiState = computed(() => playerBase.value?.uiState || {});
const baseControlState = computed(() => playerBase.value?.controlState || { showControls: false });
const textStyle = computed(() => playerBase.value?.textStyle || {});
const fontSizeRange = computed(() => playerBase.value?.fontSizeRange || { min: 0.8, max: 2.0, step: 0.05 });
const colorOptions = computed(() => playerBase.value?.colorOptions || []);
const autoShutdownState = computed(() => playerBase.value?.autoShutdownState || { enabled: false, minutes: 15, remainingSeconds: 0 });
const canJumpToPrevious = computed(() => playerBase.value?.canJumpToPreviousSentence || false);
const canJumpToNext = computed(() => playerBase.value?.canJumpToNextSentence || false);
const isLoggedIn = computed(() => userStore.isLoggedIn);

const boundaryFeedback = reactive({ prev: false, next: false });
const controlState = reactive({
  showVolumeSlider: false,
  isVolumeSliderActive: false,
  volumeSliderTimeout: null,
  showVideoExportDialog: false,
  showUnifiedPanel: false
});

const handleBoundaryReached = (boundary) => {
  if (boundary === 'start') {
    boundaryFeedback.prev = true;
    setTimeout(() => { boundaryFeedback.prev = false; }, 300);
  } else if (boundary === 'end') {
    boundaryFeedback.next = true;
    setTimeout(() => { boundaryFeedback.next = false; }, 300);
  }
};

const handleTextStyleChange = (newStyle) => {
  playerBase.value?.handleTextStyleChange?.(newStyle);
};

const toggleAutoShutdown = () => {
  playerBase.value?.toggleAutoShutdown?.();
};

const setAutoShutdownTime = (minutes) => {
  playerBase.value?.setAutoShutdownTime?.(minutes);
};

const formatRemainingTime = () => {
  return playerBase.value?.formatRemainingTime?.() || '';
};

const onProgressInput = (value) => {
  progressValue.value = value;
  playerBase.value?.handleProgressChange?.(value);
};

const onProgressDragEnd = () => {
  playerBase.value?.endProgressDrag?.();
  if (playerBase.value?.uiState?.currentTime && playerBase.value?.uiState?.duration) {
    const newValue = (playerBase.value.uiState.currentTime / playerBase.value.uiState.duration) * 100;
    progressValue.value = newValue;
  }
};

const toggleLoop = (value) => {
  // 如果传入了value，直接设置；否则切换
  if (value !== undefined) {
    loop.value = value;
  } else {
    loop.value = !loop.value;
  }
};

const toggleAnnotation = (value) => {
  // 如果传入了value，直接设置；否则调用toggle方法
  if (value !== undefined && playerBase.value?.textStyle) {
    playerBase.value.textStyle.showAnnotation = value;
    // 保存到用户配置
    if (playerBase.value.updateUserConfig) {
      playerBase.value.updateUserConfig('player.textStyle.showAnnotation', value);
    }
  } else {
    playerBase.value?.toggleAnnotation?.();
  }
};

const dismissGuide = () => {
  showFirstTimeTooltip.value = false;
  localStorage.setItem('hasSeenPlayerGuide', 'true');
};

const handleVideoExportComplete = (_result) => {
  ElMessage.success('视频导出成功');
};

onMounted(() => {
  if (!localStorage.getItem('hasSeenPlayerGuide')) {
    showFirstTimeTooltip.value = true;
  }
  
  setTimeout(() => {
    if (playerBase.value && playerBase.value.progressValue !== undefined) {
      progressValue.value = playerBase.value.progressValue;
    }
  }, 100);

  const syncInterval = setInterval(() => {
    if (playerBase.value && playerBase.value.progressValue !== undefined) {
      if (!playerBase.value.controlState?.isUserInteracting) {
        progressValue.value = playerBase.value.progressValue;
      }
    }
  }, 200);

  onUnmounted(() => {
    clearInterval(syncInterval);
  });

  if (isLoggedIn.value) {
    checkFeaturePermission('video_export').then(result => {
      hasExportPermission.value = result;
    }).catch(() => {
      hasExportPermission.value = false;
    });
  }
});

onUnmounted(() => {
  if (controlState.volumeSliderTimeout) {
    clearTimeout(controlState.volumeSliderTimeout);
  }
});

defineExpose({
  togglePlay: () => playerBase.value?.togglePlay?.(),
  jumpToIndex: (index) => playerBase.value?.jumpToIndex?.(index),
  jumpToCurrentSentenceStart: () => playerBase.value?.jumpToCurrentSentenceStart?.(),
  jumpToPreviousSentence: () => playerBase.value?.jumpToPreviousSentence?.(),
  jumpToPreviousDirectly: () => playerBase.value?.jumpToPreviousDirectly?.(),
  jumpToNextSentence: () => playerBase.value?.jumpToNextSentence?.(),
  jumpToTime: (time) => playerBase.value?.jumpToTime?.(time),
  setPlaybackRate: (rate) => playerBase.value?.setPlaybackRate?.(rate),
  handleProgressChange: (value) => playerBase.value?.handleProgressChange?.(value),
  formatTime: (time) => playerBase.value?.formatTime?.(time) || '00:00',
  get uiState() { return playerBase.value?.uiState || {} },
  get controlState() { return playerBase.value?.controlState || {} },
});
</script>

<template>
  <div class="desktop-video-player" @mouseleave="playerBase?.hideControlsImmediately?.()" @mousemove="playerBase?.showControlsElements?.()">
    <div class="player-base-container">
      <VideoPlayerBase 
        ref="playerBase" 
        :timeline="props.timeline" 
        :currentIndex="props.currentIndex"
        :isLoading="props.isLoading" 
        :videoConfig="props.videoConfig" 
        :configJson="props.configJson"
        :contentData="props.contentData" 
        :loop="loop" 
        @timeupdate="$emit('timeupdate', $event)"
        @loading-change="$emit('loading-change', $event)" 
        @index-change="$emit('index-change', $event)"
        @toggle-playlist="$emit('toggle-playlist')" 
        @boundary-reached="handleBoundaryReached" 
        @video-end="$emit('video-end')" 
      />
    </div>

    <div class="player-controls" :class="{ 'show-controls': baseControlState.showControls }">
      <div class="loading-overlay" v-if="props.isLoading">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载内容...</div>
        <div class="loading-subtext">请稍候，内容准备中</div>
      </div>

      <div v-if="baseControlState.showControls" class="desktop-controls-group">
        <div class="controls-buttons-container">
          <div class="desktop-nav-button" @click.stop="playerBase?.jumpToPreviousSentence?.(); playerBase?.resetControlsTimeout?.()" :class="{ 'disabled': !canJumpToPrevious, 'boundary-feedback': boundaryFeedback.prev }">
            <el-icon><i-ep-arrow-left /></el-icon>
          </div>
          <div class="desktop-play-button" @click.stop="playerBase?.togglePlay?.()">
            <el-icon v-if="!uiState.isPlaying"><i-ep-video-play /></el-icon>
            <el-icon v-else><i-ep-video-pause /></el-icon>
          </div>
          <div class="desktop-nav-button" @click.stop="playerBase?.jumpToNextSentence?.(); playerBase?.resetControlsTimeout?.()" :class="{ 'disabled': !canJumpToNext, 'boundary-feedback': boundaryFeedback.next }">
            <el-icon><i-ep-arrow-right /></el-icon>
          </div>
        </div>
      </div>

      <div class="video-controls" v-show="!uiState.isPlaying || (uiState.isPlaying && baseControlState.showControls)">
        <div class="progress-slider-container">
          <el-slider v-model="progressValue" :min="0" :max="100" :format-tooltip="value => playerBase?.formatTime?.((value / 100) * uiState.duration) || '00:00'" @mousedown="playerBase?.startProgressDrag?.()" @mouseup="onProgressDragEnd" @touchstart="playerBase?.startProgressDrag?.()" @touchend="onProgressDragEnd" @input="onProgressInput" class="custom-slider" />
        </div>
        <div class="controls-bottom">
          <div class="controls-left">
            <el-tooltip content="播放/暂停" placement="top" effect="dark" :enterable="false">
              <div class="play-pause-control" @click="playerBase?.togglePlay?.()">
                <el-icon v-if="!uiState.isPlaying"><i-ep-video-play /></el-icon>
                <el-icon v-else><i-ep-video-pause /></el-icon>
              </div>
            </el-tooltip>
            <div class="time-display">
              <span>{{ playerBase?.formatTime?.(uiState.currentTime) || '00:00' }}</span>
              <span>/</span>
              <span>{{ playerBase?.formatTime?.(uiState.duration) || '00:00' }}</span>
            </div>
          </div>
          <div class="controls-right">
            <el-tooltip content="显示/隐藏内容列表" placement="top" effect="dark" :enterable="false">
              <div class="playlist-switch">
                <el-switch v-model="uiState.showPlaylist" @change="playerBase?.togglePlaylist?.()" active-text="" inactive-text="" size="small" class="custom-switch" />
              </div>
            </el-tooltip>
            <slot name="favorite-button"></slot>
            <el-popover v-model:visible="showFirstTimeTooltip" placement="top" width="200" trigger="manual">
              <template #reference>
                <div class="settings-control" @click="$emit('open-settings'); dismissGuide()">
                  <el-icon>
                    <svg viewBox="0 0 1024 1024" width="16" height="16">
                      <path fill="currentColor" d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM368 744c0 4.4-3.6 8-8 8h-80c-4.4 0-8-3.6-8-8V280c0-4.4 3.6-8 8-8h80c4.4 0 8 3.6 8 8v464zm192-280c0 4.4-3.6 8-8 8h-80c-4.4 0-8-3.6-8-8V280c0-4.4 3.6-8 8-8h80c4.4 0 8 3.6 8 8v184zm192 72c0 4.4-3.6 8-8 8h-80c-4.4 0-8-3.6-8-8V280c0-4.4 3.6-8 8-8h80c4.4 0 8 3.6 8 8v256z"></path>
                    </svg>
                  </el-icon>
                </div>
              </template>
              <div class="guide-content">
                <span>设置播放步骤、重复次数、语速、间隔及翻译语音</span>
                <el-icon class="close-icon" @click="dismissGuide"><i-ep-close /></el-icon>
              </div>
            </el-popover>
            <el-tooltip v-if="!showFirstTimeTooltip" content="播放设置" placement="top" effect="dark" :enterable="false">
              <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none;"></div>
            </el-tooltip>
            <el-tooltip content="更多设置" placement="top" effect="dark" :enterable="false">
              <UnifiedControlPanel
                :ui-state="uiState"
                :text-style="textStyle"
                :font-size-range="fontSizeRange"
                :color-options="colorOptions"
                :auto-shutdown-state="autoShutdownState"
                :timeline="props.timeline"
                :video-config="{
                  backgroundColor: textStyle.backgroundColor || '#000000',
                  textColor: textStyle.color || '#FFFFFF',
                  fontSize: textStyle.fontSize || 1.25,
                  copyright: props.videoConfig.copyright
                }"
                :config-json="props.configJson"
                :content-data="props.contentData"
                :loop="loop"
                @set-playback-rate="playerBase?.setPlaybackRate?.($event)"
                @toggle-loop="toggleLoop($event)"
                @text-style-change="handleTextStyleChange"
                @toggle-auto-shutdown="toggleAutoShutdown"
                @set-auto-shutdown-time="setAutoShutdownTime"
                @toggle-annotation="toggleAnnotation($event)"
                @export-complete="handleVideoExportComplete"
              />
            </el-tooltip>
          </div>
        </div>
      </div>

      <div v-if="autoShutdownState.enabled" class="auto-shutdown-indicator">
        <el-icon style="margin-right: 0.25rem;">
          <svg viewBox="0 0 1024 1024" width="16" height="16">
            <path fill="currentColor" d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"></path>
            <path fill="currentColor" d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"></path>
          </svg>
        </el-icon>
        {{ formatRemainingTime() }}
        <span class="close-btn" @click="toggleAutoShutdown">×</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.desktop-video-player {
  width: 100%;
  aspect-ratio: 16/9;
  display: flex;
  flex-direction: column;
  background-color: #000;
  color: #fff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.75rem rgba(0, 0, 0, 0.3);
  position: relative;
}

.player-base-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.player-controls {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 2;
  pointer-events: auto;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.player-controls.show-controls {
  opacity: 1;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.loading-spinner {
  width: 3.125rem;
  height: 3.125rem;
  border: 0.3125rem solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 1rem;
  color: #fff;
  font-size: 1.125rem;
}

.loading-subtext {
  margin-top: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  font-weight: 500;
}

.desktop-controls-group {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  z-index: 5;
}

.controls-buttons-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2.5rem;
  transform: translateY(-50%);
}

.desktop-nav-button {
  width: 2.8rem;
  height: 2.8rem;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  border: 0.0625rem solid rgba(255, 255, 255, 0.5);
  font-size: 1.25rem;
}

.desktop-nav-button:hover {
  background-color: rgba(0, 0, 0, 0.8);
  transform: scale(1.05);
}

.desktop-nav-button:active {
  transform: scale(0.95);
}

.desktop-nav-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.desktop-nav-button.disabled:hover {
  transform: none;
  background-color: rgba(0, 0, 0, 0.6);
}

@keyframes boundary-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-3px); }
  75% { transform: translateX(3px); }
}

.desktop-nav-button.boundary-feedback {
  animation: boundary-shake 0.3s ease-in-out;
}

.desktop-play-button {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background-color: rgba(255, 0, 0, 0.8);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  border: 0.125rem solid rgba(255, 255, 255, 0.8);
  font-size: 1.6rem;
}

.desktop-play-button:hover {
  background-color: rgba(255, 0, 0, 1);
  transform: scale(1.05);
}

.desktop-play-button:active {
  transform: scale(0.95);
}

.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 1rem;
  box-sizing: border-box;
  opacity: 1;
  transition: opacity 0.3s;
  z-index: 5;
}

.progress-slider-container {
  width: 100%;
  position: relative;
  z-index: 10;
  padding: 0 0.5rem;
  box-sizing: border-box;
}

.custom-slider {
  --el-slider-height: 0.5rem;
  --el-slider-button-size: 1rem;
  margin: 0;
}

.custom-slider :deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.2);
  height: 0.5rem;
  margin: 0;
}

.custom-slider :deep(.el-slider__bar) {
  background-color: #ff0000;
  height: 0.5rem;
}

.custom-slider :deep(.el-slider__button) {
  border: 0.125rem solid #ffffff;
  background-color: #ff0000;
  width: 1rem;
  height: 1rem;
  transition: transform 0.2s;
  box-shadow: 0 0 0.25rem rgba(0, 0, 0, 0.5);
}

.custom-slider :deep(.el-slider__button):hover {
  transform: scale(1.2);
}

.controls-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
}

.controls-left,
.controls-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.play-pause-control {
  width: 2.25rem;
  height: 2.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: #FFFFFF;
  font-size: 1.25rem;
  border-radius: 50%;
  transition: all 0.2s;
  background-color: rgba(255, 0, 0, 0.8);
}

.play-pause-control:hover {
  background-color: rgba(255, 0, 0, 1);
  transform: scale(1.05);
}

.play-pause-control .el-icon {
  font-size: 1.125rem !important;
}

.time-display {
  color: #fff;
  font-size: 0.875rem;
  margin-right: 0.75rem;
}

.time-display span {
  margin: 0 0.125rem;
}

.settings-control,
.playlist-switch {
  cursor: pointer;
  color: #fff;
  font-size: 1.125rem;
  transition: transform 0.2s;
  padding: 0.375rem;
  position: relative;
  width: 2.25rem;
  height: 2.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
}

.settings-control:hover,
.playlist-switch:hover {
  transform: scale(1.05);
  background-color: rgba(255, 255, 255, 0.1);
}

.settings-control .el-icon,
.playlist-switch .el-icon {
  font-size: 1.125rem !important;
  width: 1.125rem;
  height: 1.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-control svg {
  width: 1.125rem !important;
  height: 1.125rem !important;
}

.controls-right .el-icon,
.controls-left .el-icon {
  font-size: 1.125rem !important;
  width: 1.125rem;
  height: 1.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.playlist-switch {
  display: flex;
  align-items: center;
}

.custom-switch {
  --el-switch-on-color: #ff0000;
}

:deep(.el-switch__label) {
  color: #fff !important;
  font-size: 0.75rem;
}

:deep(.el-switch.is-checked .el-switch__core) {
  border-color: #ff0000 !important;
  background-color: #ff0000 !important;
}

.auto-shutdown-indicator {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  background: rgba(255, 0, 0, 0.9);
  color: #fff;
  padding: 0.5rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  z-index: 20;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  cursor: default;
}

.close-btn {
  margin-left: 0.75rem;
  cursor: pointer;
  font-weight: bold;
  font-size: 1.1rem;
  line-height: 1;
}

.close-btn:hover {
  color: #fff;
  opacity: 0.8;
}

.guide-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.875rem;
}

.close-icon {
  cursor: pointer;
  margin-left: 0.5rem;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.close-icon:hover {
  opacity: 1;
}
</style>