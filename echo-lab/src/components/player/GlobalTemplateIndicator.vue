<template>
  <div v-if="globalTemplateInfo" class="global-template-indicator">
    <div class="indicator-content">
      <div class="indicator-icon">
        <el-icon><i-ep-setting /></el-icon>
      </div>
      <div class="indicator-text">
        <span class="template-name">{{ globalTemplateInfo.name }}</span>
        <span class="template-status">
          {{ globalTemplateInfo.isActive ? '(全局策略生效)' : '(已被覆盖)' }}
        </span>
      </div>
      <div class="indicator-actions">
        <el-button 
          v-if="!globalTemplateInfo.isActive"
          size="small" 
          type="primary" 
          plain
          @click="$emit('restore-global')"
        >
          恢复全局策略
        </el-button>
        <el-button 
          v-else
          size="small" 
          plain
          @click="$emit('disable-global')"
        >
          使用自定义设置
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  globalTemplateInfo: {
    type: Object,
    default: null
  }
});

defineEmits(['restore-global', 'disable-global']);
</script>

<style scoped>
.global-template-indicator {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #0ea5e9;
  border-radius: 0.5rem;
}

.indicator-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.indicator-icon {
  color: #0ea5e9;
  font-size: 1.125rem;
}

.indicator-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.template-name {
  font-weight: 500;
  color: #0c4a6e;
}

.template-status {
  font-size: 0.75rem;
  color: #0369a1;
}

.indicator-actions {
  display: flex;
  gap: 0.5rem;
}
</style>