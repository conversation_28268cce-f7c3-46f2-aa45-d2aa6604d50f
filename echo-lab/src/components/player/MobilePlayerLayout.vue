<script setup>
import { ref } from 'vue';
import MobileVideoPlayer from './MobileVideoPlayer.vue';
import PlaybackContextPanel from './PlaybackContextPanel.vue';
import CollectionInfo from './CollectionInfo.vue';

const props = defineProps({
  timeline: { type: Array, default: () => [] },
  currentIndex: { type: Number, default: 0 },
  isLoading: { type: Boolean, default: false },
  showContextPanel: { type: Boolean, default: true },
  collection: { type: Object, default: null },
  collectionIndex: { type: Number, default: 0 },
  videoConfig: {
    type: Object,
    default: () => ({
      cover: {
        imageUrl: "",
        duration: 3
      },
      copyright: {
        text: "",
        position: "bottomRight"
      }
    })
  },
  collectionPlayMode: {
    type: String,
    default: 'sequence'
  },
  configJson: {
    type: Object,
    default: null
  },
  contentData: {
    type: Object,
    default: null
  },
  // 移除音频合成进度（HLS自动处理）
  // audioSynthesisProgress: {
  //   type: Object,
  //   default: () => ({
  //     total: 0,
  //     completed: 0,
  //     percentage: 0,
  //     isProcessing: false
  //   })
  // }
});

const emit = defineEmits([
  'index-change',
  'loading-change',
  'open-settings',
  'toggle-playlist',
  'select-item',
  'go-home',
  'collection-select',
  'video-end',
  'play-mode-change'
]);

const videoPlayerRef = ref(null);

const jumpToIndex = (index) => {
  console.log('🎯 MobilePlayerLayout: jumpToIndex 被调用', {
    index,
    currentIndex: props.currentIndex
  });
  emit('select-item', index);
};

const handleCollectionSelect = (index, playMode) => {
  console.log('🎯 MobilePlayerLayout: handleCollectionSelect 被调用', {
    index,
    playMode
  });
  emit('collection-select', index, playMode);
};

defineExpose({
  videoPlayerRef
});
</script>

<template>
  <div class="mobile-player-layout" :class="{ 'player-only': !props.showContextPanel }">
    <!-- 播放器区域（16:9） -->
    <div class="video-player-wrapper" :class="{ 'centered-wrapper': !props.showContextPanel }">


      <div class="aspect-ratio-container">
        <MobileVideoPlayer ref="videoPlayerRef"
          :timeline="props.timeline"
          :currentIndex="props.currentIndex" :isLoading="props.isLoading"
          :videoConfig="props.videoConfig"
          :configJson="props.configJson"
          :contentData="props.contentData"
          @index-change="emit('index-change', $event)" @loading-change="emit('loading-change', $event)"
          @open-settings="emit('open-settings')" @toggle-playlist="emit('toggle-playlist')" @video-end="emit('video-end')">
          <!-- 将收藏按钮放入右上角控制区 -->
          <template #favorite-button>
            <slot name="actions"></slot>
          </template>
        </MobileVideoPlayer>
      </div>
    </div>

    <!-- 播放列表区域 -->
    <div class="context-panel" v-if="props.showContextPanel">
      <!-- 合集信息 -->
      <CollectionInfo v-if="props.collection" :collection="props.collection" :current-index="props.collectionIndex" :play-mode="props.collectionPlayMode" @select-item="handleCollectionSelect" @play-mode-change="emit('play-mode-change', $event)" />
      
      <div class="panel-header">
        <h3>当前视频内容</h3>
      </div>
      <div class="scroll-wrapper">
        <PlaybackContextPanel :timeline="props.timeline" :currentIndex="props.currentIndex"
          @select-item="jumpToIndex" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.mobile-player-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

/* 当只显示播放器时的样式 */
.player-only {
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
}

/* 播放器区域 - 固定16:9比例 */
.video-player-wrapper {
  width: 100%;
  position: relative;
}

/* 当只显示播放器时，播放器容器居中 */
.centered-wrapper {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
  overflow: hidden;
}

.aspect-ratio-container {
  width: 100%;
  padding-bottom: 56.25%;
  /* 16:9 */
  position: relative;
}

.aspect-ratio-container>* {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.loading-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.85);
  color: white;
  width: 100%;
  height: 100%;
}

.error-hint {
  font-size: 0.875rem;
  color: #909399;
  margin-top: 0.625rem;
}

/* 播放列表 */
.context-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-top: 2px solid #f0f0f0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.panel-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
  text-align: center;
}

.panel-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
}

.scroll-wrapper {
  flex: 1;
  overflow-y: auto;
}
</style>
