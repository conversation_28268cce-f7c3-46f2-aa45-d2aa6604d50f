<script setup>
import { ref, computed, reactive, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import VideoPlayerBase from './VideoPlayerBase.vue';
import MobileMenuPortal from './MobileMenuPortal.vue';
import { exportOriginalContentToPDF } from '@/utils/pdfExporter';

// 接收与基础播放器相同的属性
const props = defineProps({
  timeline: {
    type: Array,
    default: () => []
  },

  currentIndex: {
    type: Number,
    default: 0
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  videoConfig: {
    type: Object,
    default: () => ({
      cover: {
        imageUrl: "",
        duration: 3
      },
      copyright: {
        text: "",
        position: "bottomRight"
      }
    })
  },

});

// 转发相同的事件
const emit = defineEmits(['timeupdate', 'loading-change', 'open-settings', 'index-change', 'toggle-playlist', 'video-end']);

// 引用基础播放器
const playerBase = ref(null);

// 移动端特有状态
const mobileState = reactive({
  showMobileMenu: false
});

// 子菜单状态
const showSpeedMenu = ref(false);
const showFontSizeMenu = ref(false);
const showColorMenu = ref(false);
const showTimerMenu = ref(false);

// 循环播放状态
const loop = ref(false);

// 首次访问提示
const showFirstTimeTooltip = ref(false);

const dismissGuide = () => {
  showFirstTimeTooltip.value = false;
  localStorage.setItem('hasSeenPlayerGuide', 'true');
};

// 计算属性 - 从基础播放器获取状态
const uiState = computed(() => playerBase.value?.uiState || {});
const baseControlState = computed(() => playerBase.value?.controlState || { showControls: false });
const textStyle = computed(() => playerBase.value?.textStyle || {});
const fontSizeRange = computed(() => playerBase.value?.fontSizeRange || { min: 0.8, max: 2.0, step: 0.05 });
const colorOptions = computed(() => playerBase.value?.colorOptions || []);
// 使用ref而不是computed，这样可以直接修改值
const progressValue = ref(0);
// 不再需要封面图片相关计算属性，已移至VideoPlayerBase
const autoShutdownState = computed(() => playerBase.value?.autoShutdownState || { enabled: false, minutes: 15, remainingSeconds: 0 });

// 是否可以跳转到上一个句子
const canJumpToPrevious = computed(() => playerBase.value?.canJumpToPreviousSentence || false);

// 是否可以跳转到下一个句子
const canJumpToNext = computed(() => playerBase.value?.canJumpToNextSentence || false);

// 内容元素点击处理已移至VideoPlayerBase

// 边界反馈状态
const boundaryFeedback = reactive({
  prev: false,
  next: false
});

// 处理边界反馈
const handleBoundaryReached = (boundary) => {
  if (boundary === 'start') {
    // 触发前一个按钮的反馈动画
    boundaryFeedback.prev = true;
    setTimeout(() => {
      boundaryFeedback.prev = false;
    }, 300); // 动画持续时间
  } else if (boundary === 'end') {
    // 触发后一个按钮的反馈动画
    boundaryFeedback.next = true;
    setTimeout(() => {
      boundaryFeedback.next = false;
    }, 300); // 动画持续时间
  }
};

// 移除进度文本函数（HLS自动处理加载）
// const getProgressText = (percentage) => {
//   if (percentage < 90) {
//     return `正在处理: ${percentage}%`;
//   } else if (percentage < 95) {
//     return '正在合成: 90%';
//   } else if (percentage < 100) {
//     return '即将完成: 95%';
//   } else {
//     return '处理完成: 100%';
//   }
// };

// 切换移动端菜单显示
const toggleMobileMenu = () => {
  mobileState.showMobileMenu = !mobileState.showMobileMenu;
  // 关闭所有子菜单
  showSpeedMenu.value = false;
  showFontSizeMenu.value = false;
  showColorMenu.value = false;
  showTimerMenu.value = false;

  // 如果打开菜单，隐藏其他控制元素
  if (mobileState.showMobileMenu && playerBase.value && playerBase.value.controlState) {
    playerBase.value.controlState.showControls = false;
  }
};

// 处理文本样式变更
const handleTextStyleChange = (newStyle) => {
  playerBase.value?.handleTextStyleChange?.(newStyle);
  
  // 保存到本地存储
  const currentStyle = JSON.parse(localStorage.getItem('playerTextStyle') || '{}');
  const updatedStyle = { ...currentStyle, ...newStyle };
  localStorage.setItem('playerTextStyle', JSON.stringify(updatedStyle));
};

// 处理循环播放切换
const handleLoopToggle = (value) => {
  loop.value = value;
  ElMessage.success(value ? '循环播放已开启' : '循环播放已关闭');
};

// 处理标注显示切换
const handleAnnotationToggle = (value) => {
  // 直接更新textStyle的值
  if (playerBase.value?.textStyle) {
    playerBase.value.textStyle.showAnnotation = value;
    // 保存到用户配置
    if (playerBase.value.updateUserConfig) {
      playerBase.value.updateUserConfig('player.textStyle.showAnnotation', value);
    }
    ElMessage.success(value ? '标注显示已开启' : '标注显示已关闭');
  }
};

// 导出原文PDF
const exportPDF = async () => {
  try {
    mobileState.showMobileMenu = false;

    if (!props.configJson) {
      ElMessage.error('无法获取内容配置，请稍后重试');
      return;
    }

    const contentTitle = props.contentData?.name || '原文内容';

    ElMessage.info('正在生成PDF，请稍候...');

    const result = await exportOriginalContentToPDF(props.configJson, contentTitle);

    ElMessage.success(result.message);

  } catch (error) {
    console.error('PDF导出失败:', error);
    ElMessage.error(error.message || 'PDF导出失败，请稍后重试');
  }
};

// 处理进度条输入
const onProgressInput = (value) => {
  // 直接更新进度条值
  progressValue.value = value;

  // 调用基础播放器的处理函数
  playerBase.value?.handleProgressChange?.(value);
};

// 处理进度条拖动结束
const onProgressDragEnd = () => {
  // 调用基础播放器的处理函数
  playerBase.value?.endProgressDrag?.();

  // 确保进度条值与当前时间同步
  if (playerBase.value?.uiState?.currentTime && playerBase.value?.uiState?.duration) {
    const newValue = (playerBase.value.uiState.currentTime / playerBase.value.uiState.duration) * 100;
    progressValue.value = newValue;
  }
};

// 打开文本样式设置 - 直接调用父组件的设置面板
const openTextStyleSettings = () => {
  // 发送打开设置事件
  emit('open-settings');
};

// 切换定时关闭状态
const toggleAutoShutdown = () => {
  playerBase.value?.toggleAutoShutdown?.();
};

// 设置定时关闭时间
const setAutoShutdownTime = (minutes) => {
  playerBase.value?.setAutoShutdownTime?.(minutes);
};

// 格式化剩余时间
const formatRemainingTime = () => {
  return playerBase.value?.formatRemainingTime?.() || '';
};

// 监听循环播放开关
const handleToggleLoop = (val) => {
  loop.value = val;
};

// 在组件挂载时从基础播放器获取初始进度值
onMounted(() => {
  // 添加一个定时器，确保基础播放器已经初始化
  setTimeout(() => {
    if (playerBase.value && playerBase.value.progressValue !== undefined) {
      progressValue.value = playerBase.value.progressValue;
    }
  }, 100);

  // 添加一个定时器，定期从基础播放器同步进度值
  const syncInterval = setInterval(() => {
    if (playerBase.value && playerBase.value.progressValue !== undefined) {
      // 只有当用户没有交互时才同步进度值
      if (!playerBase.value.controlState?.isUserInteracting) {
        progressValue.value = playerBase.value.progressValue;
      }
    }
  }, 200); // 每200毫秒同步一次

  // 检查首次访问
  if (!localStorage.getItem('hasSeenPlayerGuide')) {
    showFirstTimeTooltip.value = true;
  }

  // 在组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(syncInterval);
  });
});

// 暴露方法给父组件
defineExpose({
  // 提供一个获取基础播放器方法的代理
  togglePlay: () => playerBase.value?.togglePlay?.(),
  jumpToIndex: (index) => playerBase.value?.jumpToIndex?.(index),
  jumpToCurrentSentenceStart: () => playerBase.value?.jumpToCurrentSentenceStart?.(),
  jumpToTime: (time) => playerBase.value?.jumpToTime?.(time),
  setVolume: (value) => playerBase.value?.setVolume?.(value),
  setPlaybackRate: (rate) => playerBase.value?.setPlaybackRate?.(rate),
  // toggleFullscreen 已移除
  toggleMute: () => playerBase.value?.toggleMute?.(),
  handleProgressChange: (value) => playerBase.value?.handleProgressChange?.(value),
  formatTime: (time) => playerBase.value?.formatTime?.(time) || '00:00',
  // 定时关闭相关方法
  toggleAutoShutdown: () => playerBase.value?.toggleAutoShutdown?.(),
  setAutoShutdownTime: (minutes) => playerBase.value?.setAutoShutdownTime?.(minutes),
  formatRemainingTime: () => playerBase.value?.formatRemainingTime?.() || '',
  // 提供访问基础播放器状态的代理
  get uiState() { return playerBase.value?.uiState || {} },
  get controlState() { return playerBase.value?.controlState || {} },
  get textStyle() { return playerBase.value?.textStyle || {} },
  get progressValue() { return playerBase.value?.progressValue || 0 },
  get autoShutdownState() { return playerBase.value?.autoShutdownState || { enabled: false, minutes: 15, remainingSeconds: 0 } },
  get audioElement() { return playerBase.value?.audioElement },
});
</script>

<template>
  <div class="mobile-video-player">
    <!-- 基础播放器 - 不可见，仅用于逻辑 -->
    <VideoPlayerBase ref="playerBase" :timeline="props.timeline" :currentIndex="props.currentIndex"
      :isLoading="props.isLoading" :videoConfig="props.videoConfig" :loop="loop"
      @timeupdate="$emit('timeupdate', $event)" @loading-change="$emit('loading-change', $event)"
      @index-change="$emit('index-change', $event)" @toggle-playlist="$emit('toggle-playlist')"
      @boundary-reached="handleBoundaryReached" @video-end="$emit('video-end')" />

    <!-- 移动端特有UI -->
    <div class="player-content" @mousemove="playerBase?.resetControlsTimeout?.()"
      @touchstart="playerBase?.resetControlsTimeout?.()" :style="{ backgroundColor: textStyle.backgroundColor }">
      <!-- 简化的加载提示 -->
      <div class="loading-overlay" v-if="props.isLoading">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载内容...</div>
        <div class="loading-subtext">
          请稍候，内容准备中
        </div>
      </div>

      <!-- 内容显示已移至VideoPlayerBase.vue中处理 -->
      <div class="content-click-area" @click="uiState.isPlaying && playerBase?.togglePlay?.()"></div>

      <!-- 版权信息已移至ContentDisplay组件中处理 -->

      <!-- 移动端右上角控制区 -->
      <div class="mobile-top-controls">
        <!-- 播放列表开关 -->
        <div class="playlist-switch icon-wrapper">
          <el-switch v-model="uiState.showPlaylist" @change="playerBase?.togglePlaylist?.()" 
                     size="small" class="custom-switch" />
        </div>
        
        <!-- 收藏按钮插槽 -->
        <div class="icon-wrapper">
          <slot name="favorite-button"></slot>
        </div>
        
        <!-- 播放设置按钮 -->
        <el-popover
          v-model:visible="showFirstTimeTooltip"
          placement="bottom"
          width="180"
          trigger="manual"
        >
          <template #reference>
            <div class="settings-control icon-wrapper" @click="openTextStyleSettings(); dismissGuide()">
              <el-icon>
                <svg viewBox="0 0 1024 1024" width="20" height="20">
                  <path fill="currentColor"
                    d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM368 744c0 4.4-3.6 8-8 8h-80c-4.4 0-8-3.6-8-8V280c0-4.4 3.6-8 8-8h80c4.4 0 8 3.6 8 8v464zm192-280c0 4.4-3.6 8-8 8h-80c-4.4 0-8-3.6-8-8V280c0-4.4 3.6-8 8-8h80c4.4 0 8 3.6 8 8v184zm192 72c0 4.4-3.6 8-8 8h-80c-4.4 0-8-3.6-8-8V280c0-4.4 3.6-8 8-8h80c4.4 0 8 3.6 8 8v256z">
                  </path>
                </svg>
              </el-icon>
            </div>
          </template>
          <div class="guide-content">
            <span>设置播放步骤、重复次数、语速、间隔及翻译语音</span>
            <el-icon class="close-icon" @click="dismissGuide"><i-ep-close /></el-icon>
          </div>
        </el-popover>

        <!-- 更多功能按钮 -->
        <div class="more-control icon-wrapper" @click="toggleMobileMenu">
          <el-icon>
            <i-ep-setting />
          </el-icon>
        </div>
      </div>

      <!-- 暂停蒙层 (非播放状态显示) -->
      <div v-if="!uiState.isPlaying && !props.isLoading" class="pause-overlay" @click="playerBase?.togglePlay?.()">
      </div>

      <!-- 移动端控制按钮组 - 始终显示，但在播放时需要点击才会显示 -->
      <div v-if="baseControlState.showControls || !uiState.isPlaying" class="mobile-controls-group">
        <!-- 后退按钮 - 简化结构 -->
        <button class="mobile-nav-button" @click.stop="playerBase?.jumpToPreviousSentence?.()"
          :class="{ 'disabled': !canJumpToPrevious, 'boundary-feedback': boundaryFeedback.prev }">
          <el-icon>
            <i-ep-arrow-left />
          </el-icon>
        </button>

        <!-- 播放/暂停按钮 - 简化结构 -->
        <button class="unified-play-button" @click.stop="playerBase?.togglePlay?.()">
          <el-icon>
            <i-ep-video-play v-if="!uiState.isPlaying" />
            <i-ep-video-pause v-else />
          </el-icon>
        </button>

        <!-- 前进按钮 - 简化结构 -->
        <button class="mobile-nav-button" @click.stop="playerBase?.jumpToNextSentence?.()"
          :class="{ 'disabled': !canJumpToNext, 'boundary-feedback': boundaryFeedback.next }">
          <el-icon>
            <i-ep-arrow-right />
          </el-icon>
        </button>
      </div>

      <!-- 视频内控制元素 -->
      <div class="video-controls" v-show="!uiState.isPlaying || (uiState.isPlaying && baseControlState.showControls)">
        <div class="controls-bottom">
          <div class="mobile-controls-row">
            <!-- 时间显示 -->
            <div class="mobile-time-display">
              <span>{{ playerBase?.formatTime?.(uiState.currentTime) || '00:00' }}</span>
              <span>/</span>
              <span>{{ playerBase?.formatTime?.(uiState.duration) || '00:00' }}</span>
            </div>

            <!-- 进度条 -->
            <div class="mobile-progress-slider-container">
              <el-slider v-model="progressValue" :min="0" :max="100"
                :format-tooltip="value => playerBase?.formatTime?.((value / 100) * uiState.duration) || '00:00'"
                @mousedown="playerBase?.startProgressDrag?.()" @mouseup="onProgressDragEnd"
                @touchstart="playerBase?.startProgressDrag?.()" @touchend="onProgressDragEnd" @input="onProgressInput"
                class="video-progress-slider" />
            </div>


          </div>
        </div>
      </div>

      <!-- 定时关闭右下角悬浮提示 -->
      <div v-if="autoShutdownState.enabled" class="auto-shutdown-indicator">
        <el-icon style="margin-right: 0.25rem;"><svg viewBox="0 0 1024 1024" width="16" height="16">
            <path fill="currentColor"
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z">
            </path>
            <path fill="currentColor"
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z">
            </path>
          </svg></el-icon>
        {{ formatRemainingTime() }}
        <span class="close-btn" @click="toggleAutoShutdown">×</span>
      </div>
    </div>

    <!-- YouTube风格的移动端菜单 -->
    <div class="mobile-menu-overlay" v-show="mobileState.showMobileMenu" @click="toggleMobileMenu">
      <div class="mobile-menu" @click.stop>
        <div class="menu-header">
          <span>设置</span>
          <el-icon @click="toggleMobileMenu"><i-ep-close /></el-icon>
        </div>
        
        <div class="menu-item" @click="showSpeedMenu = !showSpeedMenu">
          <span>播放速度</span>
          <span class="menu-value">{{ uiState.playbackRate }}x</span>
        </div>
        
        <div class="menu-item" @click="showFontSizeMenu = !showFontSizeMenu">
          <span>字号</span>
          <span class="menu-value">{{ textStyle.fontSizePercent || 100 }}%</span>
        </div>
        
        <div class="menu-item" @click="showColorMenu = !showColorMenu">
          <span>颜色</span>
          <div class="color-preview" :style="{ backgroundColor: textStyle.color || '#FFFFFF' }"></div>
        </div>
        
        <div class="menu-item" @click="showTimerMenu = !showTimerMenu">
          <span>定时关闭</span>
          <span class="menu-value" v-if="autoShutdownState.enabled">{{ autoShutdownState.minutes }}分钟</span>
        </div>
        
        <div class="menu-item">
          <span>循环播放</span>
          <el-switch :model-value="loop"
                     @update:model-value="handleLoopToggle"
                     size="small" class="custom-switch" />
        </div>

        <div class="menu-item">
          <span>显示标注</span>
          <el-switch :model-value="textStyle.showAnnotation"
                     @update:model-value="handleAnnotationToggle"
                     size="small" class="custom-switch" />
        </div>

        <!-- 导出原文PDF -->
        <div class="menu-item" v-if="props.configJson" @click="exportPDF">
          <span>导出原文PDF</span>
          <el-icon class="menu-icon"><i-ep-document /></el-icon>
        </div>

      </div>
    </div>
    
    <!-- 子菜单 -->
    <div class="mobile-submenu-overlay" v-show="showSpeedMenu" @click="showSpeedMenu = false">
      <div class="mobile-submenu" @click.stop>
        <div class="submenu-header">
          <el-icon @click="showSpeedMenu = false"><i-ep-arrow-left /></el-icon>
          <span>播放速度</span>
        </div>
        <div v-for="speed in [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]" :key="speed" 
             class="submenu-item" :class="{ active: speed === uiState.playbackRate }"
             @click="playerBase?.setPlaybackRate?.(speed); showSpeedMenu = false; toggleMobileMenu()">
          {{ speed }}x
        </div>
      </div>
    </div>
    
    <div class="mobile-submenu-overlay" v-show="showFontSizeMenu" @click="showFontSizeMenu = false">
      <div class="mobile-submenu" @click.stop>
        <div class="submenu-header">
          <el-icon @click="showFontSizeMenu = false"><i-ep-arrow-left /></el-icon>
          <span>字号</span>
        </div>
        <div v-for="size in [70, 85, 100, 115, 130, 150]" :key="size" 
             class="submenu-item" :class="{ active: (textStyle.fontSizePercent || 100) === size }"
             @click="handleTextStyleChange({ fontSizePercent: size }); showFontSizeMenu = false; toggleMobileMenu()">
          {{ size }}%
        </div>
      </div>
    </div>
    
    <div class="mobile-submenu-overlay" v-show="showColorMenu" @click="showColorMenu = false">
      <div class="mobile-submenu" @click.stop>
        <div class="submenu-header">
          <el-icon @click="showColorMenu = false"><i-ep-arrow-left /></el-icon>
          <span>颜色</span>
        </div>
        <div v-for="color in colorOptions" :key="color.value" 
             class="submenu-item color-item" :class="{ active: textStyle.color === color.value }"
             @click="handleTextStyleChange({ color: color.value }); showColorMenu = false; toggleMobileMenu()">
          <div class="color-dot" :style="{ backgroundColor: color.value }"></div>
          <span>{{ color.label }}</span>
        </div>
      </div>
    </div>
    
    <div class="mobile-submenu-overlay" v-show="showTimerMenu" @click="showTimerMenu = false">
      <div class="mobile-submenu" @click.stop>
        <div class="submenu-header">
          <el-icon @click="showTimerMenu = false"><i-ep-arrow-left /></el-icon>
          <span>定时关闭</span>
        </div>
        <div class="submenu-item" @click="toggleAutoShutdown(); showTimerMenu = false; toggleMobileMenu()">
          关闭
        </div>
        <div v-for="time in [5, 10, 15, 30, 60, 90]" :key="time" 
             class="submenu-item" :class="{ active: autoShutdownState.enabled && autoShutdownState.minutes === time }"
             @click="setAutoShutdownTime(time); !autoShutdownState.enabled && toggleAutoShutdown(); showTimerMenu = false; toggleMobileMenu()">
          {{ time }}分钟
        </div>
      </div>
    </div>

    <!-- 添加文本样式设置对话框 -->
    <el-dialog v-model="baseControlState.showTextStyleDialog" title="文本样式设置" width="90%" :close-on-click-modal="true"
      :show-close="true">
      <div class="text-style-dialog-content">
        <!-- 字体大小设置 -->
        <div class="style-section">
          <div class="section-title">字体大小</div>
          <div class="font-size-slider">
            <el-slider v-model="textStyle.fontSizePercent" :min="fontSizeRange.min" :max="fontSizeRange.max"
              :step="fontSizeRange.step" :format-tooltip="value => `${value}%`"
              @change="handleTextStyleChange({ fontSizePercent: textStyle.fontSizePercent })" class="custom-slider" />
          </div>
        </div>

        <!-- 字体颜色设置 -->
        <div class="style-section">
          <div class="section-title">字体颜色</div>
          <div class="color-options">
            <div v-for="color in colorOptions" :key="color.value" class="color-option"
              :style="{ backgroundColor: color.value }" :class="{ 'active-color': textStyle.color === color.value }"
              @click="handleTextStyleChange({ color: color.value })" :title="color.label">
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 移除了视频导出对话框 -->
  </div>
</template>

<style scoped>
.mobile-video-player {
  width: 100%;
  aspect-ratio: 16/9;
  display: flex;
  flex-direction: column;
  background-color: #000;
  color: #fff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.75rem rgba(0, 0, 0, 0.3);
  position: relative;
}

.player-content {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
  overflow: hidden;
  transition: background-color 0.5s ease;
}

/* 内容点击区域 */
.content-click-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 加载提示样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

/* 封面图片样式 */
.cover-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 简化的播放按钮样式 */
.unified-play-button {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background-color: #ff0000;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  border: 0.125rem solid white;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.5);
  font-size: 1.5rem;
  padding: 0;
}

.unified-play-button:active {
  transform: scale(0.95);
}

/* 移动端控制按钮组 */
.mobile-controls-group {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  gap: 2rem;
  z-index: 5;
}

/* 移动端导航按钮 */
.mobile-nav-button {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  border: 0.125rem solid rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  padding: 0;
}

.mobile-nav-button:active {
  transform: scale(0.95);
}

.mobile-nav-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.mobile-nav-button.disabled:active {
  transform: none;
}

/* 边界反馈动画 */
@keyframes boundary-shake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-3px);
  }

  75% {
    transform: translateX(3px);
  }
}

.mobile-nav-button.boundary-feedback {
  animation: boundary-shake 0.3s ease-in-out;
}

/* 隐藏播放按钮 */
.unified-play-button.hidden {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s, visibility 0.2s;
}

/* 版权信息样式 */
.copyright-text {
  position: absolute;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0.0625rem 0.0625rem 0.125rem rgba(0, 0, 0, 0.7);
  z-index: 2;
}

.copyright-text.topLeft {
  top: 1rem;
  left: 1rem;
}

.copyright-text.topRight {
  top: 1rem;
  right: 1rem;
}

.copyright-text.bottomLeft {
  bottom: 1rem;
  left: 1rem;
}

.copyright-text.bottomRight {
  bottom: 1rem;
  right: 1rem;
}

/* 暂停蒙层样式 */
.pause-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100% - 3.5rem);
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  cursor: pointer;
}

.loading-spinner {
  width: 3.125rem;
  height: 3.125rem;
  border: 0.3125rem solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 1rem;
  color: #fff;
  font-size: 1.125rem;
}

.loading-subtext {
  margin-top: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  font-weight: 500;
}

/* 移除视频合成进度条样式（HLS自动处理） */
/* .synthesis-progress-bar {
  width: 80%;
  height: 0.625rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 0.3125rem;
  margin-top: 1.25rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
}

.synthesis-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff3333, #ff0000);
  transition: width 0.3s ease;
  border-radius: 0.3125rem;
} */

/* 移动端右上角控制区 */
.mobile-top-controls {
  position: absolute;
  top: 0.2rem;
  right: 0.2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  z-index: 5;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 0.5rem;
  padding: 0.25rem 0.5rem;
}

/* 统一图标包装器 */
.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
}

.settings-control,
.more-control,
.playlist-switch {
  cursor: pointer;
  color: #fff;
  font-size: 1.25rem;
  transition: transform 0.2s;
}

.settings-control:hover,
.more-control:hover,
.playlist-switch:hover {
  transform: scale(1.05);
}

/* 确保图标大小一致 */
.mobile-top-controls .el-icon {
  font-size: 1.25rem;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 自定义Switch开关样式 */
.playlist-switch {
  display: flex;
  align-items: center;
}

.custom-switch {
  --el-switch-on-color: #ff0000 !important;
  --el-switch-off-color: #dcdfe6;
}

:deep(.el-switch__label) {
  color: #fff !important;
  font-size: 0.75rem;
}

:deep(.el-switch.is-checked .el-switch__core) {
  border-color: #ff0000 !important;
  background-color: #ff0000 !important;
}

:deep(.el-switch__core) {
  border-color: #dcdfe6 !important;
  background-color: #dcdfe6 !important;
}

:deep(.el-switch.is-checked) {
  --el-switch-on-color: #ff0000 !important;
}

/* 视频内控制元素样式 */
.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 0;
  box-sizing: border-box;
  opacity: 1;
  transition: opacity 0.3s;
  z-index: 5;
}

/* 未播放状态下控制区域背景更明显 */
.mobile-video-player:not(.playing) .video-controls {
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.85));
}

.controls-bottom {
  padding: 0.5rem;
  box-sizing: border-box;
  width: 100%;
}

/* 移动端控制栏行 */
.mobile-controls-row {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 0.5rem;
}

/* 移动端时间显示 */
.mobile-time-display {
  color: #fff;
  font-size: 0.75rem;
  white-space: nowrap;
  min-width: 4.5rem;
}

/* 移动端进度条容器 */
.mobile-progress-slider-container {
  flex: 1;
  padding: 0;
  margin: 0 1rem 0 0;
  position: relative;
  z-index: 10;
  box-sizing: border-box;
}

/* 移动端全屏按钮 */
.mobile-fullscreen-button {
  cursor: pointer;
  color: #fff;
  font-size: 1.25rem;
  transition: transform 0.2s;
  padding: 0.25rem;
}

.mobile-fullscreen-button:hover {
  transform: scale(1.05);
}

/* 自定义Element Plus Slider样式 */
.video-progress-slider {
  --el-slider-height: 0.5rem;
  --el-slider-button-size: 0.875rem;
  margin: 0;
}

/* 设置滑块轨道颜色 */
.video-progress-slider :deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.2);
  height: 0.5rem;
  margin: 0;
}

/* 设置滑块已填充部分颜色 */
.video-progress-slider :deep(.el-slider__bar) {
  background-color: #ff0000;
  height: 0.5rem;
}

/* 设置滑块按钮样式 */
.video-progress-slider :deep(.el-slider__button) {
  border: 0.125rem solid #ffffff;
  background-color: #ff0000;
  width: 0.875rem;
  height: 0.875rem;
  transition: transform 0.2s;
  box-shadow: 0 0 0.25rem rgba(0, 0, 0, 0.5);
}

/* 悬停时放大滑块按钮 */
.video-progress-slider :deep(.el-slider__button):hover {
  transform: scale(1.2);
}

/* 自定义滑动条样式 */
.custom-slider {
  --el-slider-height: 0.5rem;
  --el-slider-button-size: 1rem;
  margin: 0;
}

.custom-slider :deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.2);
  height: 0.5rem;
  margin: 0;
}

.custom-slider :deep(.el-slider__bar) {
  background-color: #ff0000;
  height: 0.5rem;
}

.custom-slider :deep(.el-slider__button) {
  border: 0.125rem solid #ffffff;
  background-color: #ff0000;
  width: 1rem;
  height: 1rem;
  transition: transform 0.2s;
  box-shadow: 0 0 0.25rem rgba(0, 0, 0, 0.5);
}

.custom-slider :deep(.el-slider__button):hover {
  transform: scale(1.2);
}

/* 文本样式设置对话框样式 */
.text-style-dialog-content {
  padding: 1rem;
}

.style-section {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 0.875rem;
  color: #606266;
  margin-bottom: 0.75rem;
}

.font-size-slider {
  padding: 0 0.5rem;
}

.color-options {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.color-option {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active-color {
  border-color: #409eff;
  transform: scale(1.1);
}

.auto-shutdown-indicator {
  position: absolute;
  right: 0.7rem;
  bottom: 2.5rem;
  background: #ff0000;
  color: #fff;
  padding: 0.2rem 0.7rem;
  border-radius: 0.9rem;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  z-index: 20;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.18);
  cursor: default;
}

.auto-shutdown-indicator .el-icon {
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
}

.close-btn {
  margin-left: 0.5rem;
  cursor: pointer;
  font-weight: bold;
  font-size: 1rem;
  line-height: 1;
}

.close-btn:hover {
  color: #fff;
  opacity: 0.8;
}

/* YouTube风格移动端菜单 */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.mobile-menu {
  width: 100%;
  background: #fff;
  border-radius: 1rem 1rem 0 0;
  max-height: 70vh;
  overflow-y: auto;
}

.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  font-weight: 600;
  color: #333;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  color: #333;
}

.menu-item:hover {
  background: #f9f9f9;
}

.menu-value {
  color: #666;
  font-size: 0.9rem;
}

.menu-icon {
  color: #666;
  font-size: 1.2rem;
}

.color-preview {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  border: 1px solid #ddd;
}

.mobile-submenu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  display: flex;
  align-items: flex-end;
}

.mobile-submenu {
  width: 100%;
  background: #fff;
  border-radius: 1rem 1rem 0 0;
  max-height: 70vh;
  overflow-y: auto;
}

.submenu-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  font-weight: 600;
  color: #333;
}

.submenu-item {
  padding: 1rem;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #333;
}

.submenu-item:hover {
  background: #f9f9f9;
}

.submenu-item.active {
  background: #e3f2fd;
  color: #1976d2;
}

.color-dot {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  border: 1px solid #ddd;
}

.el-icon.active {
  color: #ff4444;
}

/* 引导气泡样式 */
.guide-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.875rem;
}

.close-icon {
  cursor: pointer;
  margin-left: 0.5rem;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.close-icon:hover {
  opacity: 1;
}


</style>
