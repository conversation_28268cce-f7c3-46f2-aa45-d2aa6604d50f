<template>
  <div class="playback-context-panel">
    <div class="timeline-items" ref="containerRef">
      <div v-for="(item, index) in filteredTimeline" :key="index" class="timeline-item"
        :class="{ 'active': index === getCurrentFilteredIndex }" :data-index="index" @click="selectItem(index)">
        <div class="item-time">{{ formatTime(item.startTime) }}</div>
        <div class="item-content">{{ item.content }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted, computed } from 'vue';

const props = defineProps({
  timeline: {
    type: Array,
    required: true
  },
  currentIndex: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['select-item']);
const containerRef = ref(null);

// 过滤掉停顿项目，只显示有内容的项目
const filteredTimeline = computed(() => {
  return props.timeline.filter(item => {
    // 过滤条件：不是停顿项目，且有内容或是封面
    return !item.isPause && (item.content?.trim() || item.isCover);
  }).map(item => {
    // 为封面项目添加显示内容
    if (item.isCover || item.type === 'cover') {
      return {
        ...item,
        content: '封面'
      };
    }
    return item;
  });
});

// 时间格式化
const formatTime = (time) => {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// 点击选中某项
const selectItem = (filteredIndex) => {
  console.log('🎯 PlaybackContextPanel: selectItem 被调用', {
    filteredIndex,
    filteredTimelineLength: filteredTimeline.value.length
  });
  
  // 获取过滤后的项目
  const filteredItem = filteredTimeline.value[filteredIndex];
  if (!filteredItem) {
    console.error('🎯 PlaybackContextPanel: 未找到过滤后的项目', filteredIndex);
    return;
  }

  // 在原始时间线中查找对应的索引
  const originalIndex = props.timeline.findIndex(item => {
    // 对于封面项目，直接比较类型和ID
    if ((filteredItem.isCover || filteredItem.type === 'cover') &&
        (item.isCover || item.type === 'cover')) {
      return filteredItem.id === item.id;
    }

    // 对于其他项目，使用原始时间和内容进行匹配
    const itemStartTime = item.startTime;
    const filteredItemStartTime = filteredItem.startTime;

    // 比较原始内容（过滤掉封面的显示修改）
    const itemOriginalContent = item.type === 'cover' ? '' : item.content;
    const filteredItemOriginalContent = filteredItem.type === 'cover' ? '' :
      (filteredItem.content === '封面' ? '' : filteredItem.content);

    return itemStartTime === filteredItemStartTime && itemOriginalContent === filteredItemOriginalContent;
  });
  
  console.log('🎯 PlaybackContextPanel: 索引映射结果', {
    filteredIndex,
    originalIndex,
    filteredItemContent: filteredItem.content?.substring(0, 20) || 'N/A'
  });
  
  // 如果找到了对应的索引，发送事件
  if (originalIndex !== -1) {
    emit('select-item', originalIndex);
  } else {
    console.error('🎯 PlaybackContextPanel: 未找到原始索引');
  }
};

// 获取当前项在过滤后时间线中的索引
const getCurrentFilteredIndex = computed(() => {
  // 验证索引有效性
  if (props.currentIndex < 0 || props.currentIndex >= props.timeline.length) {
    return -1;
  }
  
  // 获取当前项
  const currentItem = props.timeline[props.currentIndex];
  if (!currentItem) return -1;

  // 在过滤后的时间线中查找对应的索引
  const foundIndex = filteredTimeline.value.findIndex(item => {
    // 对于封面项目，直接比较类型和ID
    if ((currentItem.isCover || currentItem.type === 'cover') &&
        (item.isCover || item.type === 'cover')) {
      return currentItem.id === item.id;
    }

    // 对于其他项目，使用原始时间和内容进行匹配
    const itemStartTime = item.startTime;
    const currentItemStartTime = currentItem.startTime;

    // 比较原始内容而不是显示内容（因为封面的显示内容被修改了）
    const itemOriginalContent = item.type === 'cover' ? '' : item.content;
    const currentItemOriginalContent = currentItem.type === 'cover' ? '' : currentItem.content;

    return itemStartTime === currentItemStartTime && itemOriginalContent === currentItemOriginalContent;
  });
  
  console.log('🎥 PlaybackContextPanel: 高亮索引计算', {
    currentIndex: props.currentIndex,
    foundIndex,
    currentItem: currentItem?.content || '封面',
    filteredTimelineLength: filteredTimeline.value.length
  });
  
  return foundIndex;
});

const scrollToCurrentItem = () => {
  const container = containerRef.value;
  if (!container) {
    console.log('🎥 PlaybackContextPanel: 容器不存在，无法滚动');
    return;
  }

  // 获取当前项在过滤后时间线中的索引
  const filteredIndex = getCurrentFilteredIndex.value;
  if (filteredIndex === -1) {
    console.log('🎥 PlaybackContextPanel: 未找到当前项目，无法滚动');
    return;
  }

  const currentItem = container.querySelector(`.timeline-item[data-index="${filteredIndex}"]`);
  if (!currentItem) {
    console.log('🎥 PlaybackContextPanel: 未找到DOM元素，索引:', filteredIndex);
    return;
  }

  const containerRect = container.getBoundingClientRect();
  const itemRect = currentItem.getBoundingClientRect();

  const scrollTop = container.scrollTop + (itemRect.top - containerRect.top) - (container.clientHeight / 2) + (currentItem.clientHeight / 2);

  console.log('🎥 PlaybackContextPanel: 滚动到当前项目', {
    filteredIndex,
    scrollTop,
    currentScrollTop: container.scrollTop
  });

  requestAnimationFrame(() => {
    container.scrollTo({
      top: scrollTop,
      behavior: 'smooth'
    });
  });
};
// 监听索引变化触发滚动
// immediate: true 确保组件初始化时也会执行，无需在 onMounted 中重复调用
watch(() => props.currentIndex, async () => {
  await nextTick();
  scrollToCurrentItem();
}, { immediate: true });
</script>


<style scoped>
.playback-context-panel {
  height: 100%;
  background-color: #fff;
  overflow: hidden;
}

.timeline-items {
  overflow-y: auto;
  height: 100%;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.timeline-item {
  display: flex;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #f9f9f9;
  border-left: 3px solid transparent;
}

.timeline-item:hover {
  background-color: #f0f0f0;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.timeline-item.active {
  background-color: #ecf5ff;
  border-left: 3px solid #409EFF;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.15);
}

.item-time {
  min-width: 50px;
  color: #606266;
  font-size: 12px;
  font-weight: 500;
  margin-right: 12px;
  display: flex;
  align-items: center;
}

.item-content {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
  word-break: break-word;
  color: #303133;
}
</style>
