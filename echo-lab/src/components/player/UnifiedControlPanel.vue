<script setup>
import { ref, computed, reactive, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import VideoExportDialog from './VideoExportDialog.vue';
import { useUserStore } from '@/stores/userStore';
import { checkFeaturePermission } from '@/services/featurePermissionService';

const props = defineProps({
  // 播放器状态
  uiState: {
    type: Object,
    default: () => ({})
  },
  // 文本样式
  textStyle: {
    type: Object,
    default: () => ({})
  },
  // 字体大小范围
  fontSizeRange: {
    type: Object,
    default: () => ({ min: 0.8, max: 2.0, step: 0.05 })
  },
  // 颜色选项
  colorOptions: {
    type: Array,
    default: () => []
  },
  // 定时关闭状态
  autoShutdownState: {
    type: Object,
    default: () => ({ enabled: false, minutes: 15, remainingSeconds: 0 })
  },
  // 视频导出相关
  timeline: {
    type: Array,
    default: () => []
  },
  videoConfig: {
    type: Object,
    default: () => ({})
  },
  configJson: {
    type: Object,
    default: null
  },
  contentData: {
    type: Object,
    default: null
  },
  // 循环播放状态
  loop: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits([
  'set-playback-rate',
  'toggle-loop',
  'text-style-change',
  'toggle-auto-shutdown',
  'set-auto-shutdown-time',
  'toggle-annotation',
  'export-complete'
]);

// 控制面板状态
const panelState = reactive({
  showPanel: false,
  showSpeedMenu: false,
  showTimerMenu: false,
  showFontSizeMenu: false,
  showColorMenu: false,
  showVideoExportDialog: false
});

// 播放速度选项
const playbackSpeeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

// 定时关闭选项
const timerOptions = [5, 10, 15, 30, 60, 90];

// 用户权限检查
const userStore = useUserStore();
const isLoggedIn = computed(() => userStore.isLoggedIn);
const hasExportPermission = ref(false);

// 检查导出权限
onMounted(async () => {
  if (isLoggedIn.value) {
    try {
      hasExportPermission.value = await checkFeaturePermission('video_export');
    } catch (error) {
      console.error('检查视频导出权限失败:', error);
      hasExportPermission.value = false;
    }
  }
  
  // 加载本地保存的文本样式
  const savedStyle = JSON.parse(localStorage.getItem('playerTextStyle') || '{}');
  if (Object.keys(savedStyle).length > 0) {
    handleTextStyleChange(savedStyle);
  }
});

// 切换控制面板
const togglePanel = () => {
  panelState.showPanel = !panelState.showPanel;
  panelState.showSpeedMenu = false;
  panelState.showTimerMenu = false;
  panelState.showFontSizeMenu = false;
  panelState.showColorMenu = false;
};

// 切换速度菜单
const toggleSpeedMenu = () => {
  panelState.showSpeedMenu = !panelState.showSpeedMenu;
  panelState.showTimerMenu = false;
  panelState.showFontSizeMenu = false;
  panelState.showColorMenu = false;
};

// 切换定时菜单
const toggleTimerMenu = () => {
  panelState.showTimerMenu = !panelState.showTimerMenu;
  panelState.showSpeedMenu = false;
  panelState.showFontSizeMenu = false;
  panelState.showColorMenu = false;
};

// 切换字号菜单
const toggleFontSizeMenu = () => {
  panelState.showFontSizeMenu = !panelState.showFontSizeMenu;
  panelState.showSpeedMenu = false;
  panelState.showTimerMenu = false;
  panelState.showColorMenu = false;
};

// 切换颜色菜单
const toggleColorMenu = () => {
  panelState.showColorMenu = !panelState.showColorMenu;
  panelState.showSpeedMenu = false;
  panelState.showTimerMenu = false;
  panelState.showFontSizeMenu = false;
};

// 设置播放速度
const setPlaybackRate = (rate) => {
  emit('set-playback-rate', rate);
  panelState.showSpeedMenu = false;
  panelState.showPanel = false;
};

// 处理循环播放切换
const handleLoopToggle = (value) => {
  emit('toggle-loop', value);
  ElMessage.success(value ? '循环播放已开启' : '循环播放已关闭');
};

// 处理标注显示切换
const handleAnnotationToggle = (value) => {
  // 直接更新textStyle的值
  if (props.textStyle) {
    props.textStyle.showAnnotation = value;
  }
  // 发送事件通知父组件
  emit('toggle-annotation', value);
  ElMessage.success(value ? '标注显示已开启' : '标注显示已关闭');
};

// 处理文本样式变更
const handleTextStyleChange = (newStyle) => {
  emit('text-style-change', newStyle);
  
  // 保存到本地存储
  const currentStyle = JSON.parse(localStorage.getItem('playerTextStyle') || '{}');
  const updatedStyle = { ...currentStyle, ...newStyle };
  localStorage.setItem('playerTextStyle', JSON.stringify(updatedStyle));
};

// 切换定时关闭
const toggleAutoShutdown = () => {
  emit('toggle-auto-shutdown');
  panelState.showPanel = false;
};

// 设置定时器
const setTimer = (minutes) => {
  if (minutes === 0) {
    if (props.autoShutdownState.enabled) {
      emit('toggle-auto-shutdown');
    }
  } else {
    emit('set-auto-shutdown-time', minutes);
    if (!props.autoShutdownState.enabled) {
      emit('toggle-auto-shutdown');
    }
  }
  panelState.showTimerMenu = false;
  panelState.showPanel = false;
};

// 打开视频导出
const openVideoExport = () => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录后再导出视频');
    return;
  }
  if (!hasExportPermission.value) {
    ElMessage.warning('您没有视频导出权限，请升级会员等级');
    return;
  }
  if (!props.timeline || props.timeline.length === 0) {
    ElMessage.warning('没有可导出的内容');
    return;
  }
  panelState.showVideoExportDialog = true;
  panelState.showPanel = false;
};

// 格式化剩余时间
const formatRemainingTime = () => {
  const seconds = props.autoShutdownState.remainingSeconds;
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 点击外部关闭面板
const handleOutsideClick = (event) => {
  if (!event.target.closest('.unified-control-panel')) {
    panelState.showPanel = false;
    panelState.showSpeedMenu = false;
    panelState.showTimerMenu = false;
    panelState.showFontSizeMenu = false;
    panelState.showColorMenu = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleOutsideClick);
});

onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick);
});
</script>

<template>
  <div class="unified-control-panel">
    <!-- YouTube风格的设置按钮 -->
    <div class="settings-button" @click.stop="togglePanel">
      <el-icon><i-ep-setting /></el-icon>
    </div>

    <!-- YouTube风格的弹出菜单 -->
    <div class="settings-menu" v-show="panelState.showPanel" @click.stop>
      <!-- 播放速度 -->
      <div class="menu-item" @click="toggleSpeedMenu">
        <span>播放速度</span>
        <span class="menu-value">{{ uiState.playbackRate || 1.0 }}x</span>
      </div>
      
      <!-- 循环播放 -->
      <div class="menu-item">
        <span>循环播放</span>
        <el-switch
          :model-value="loop"
          @update:model-value="handleLoopToggle"
          size="small"
        />
      </div>

      <!-- 显示标注 -->
      <div class="menu-item">
        <span>显示标注</span>
        <el-switch
          :model-value="textStyle.showAnnotation"
          @update:model-value="handleAnnotationToggle"
          size="small"
        />
      </div>
      
      <!-- 字号 -->
      <div class="menu-item" @click="toggleFontSizeMenu">
        <span>字号</span>
        <span class="menu-value">{{ textStyle.fontSizePercent || 100 }}%</span>
      </div>
      
      <!-- 颜色 -->
      <div class="menu-item" @click="toggleColorMenu">
        <span>颜色</span>
        <div class="color-preview" :style="{ backgroundColor: textStyle.color || '#FFFFFF' }"></div>
      </div>
      
      <!-- 定时关闭 -->
      <div class="menu-item" @click="toggleTimerMenu">
        <span>定时关闭</span>
        <span class="menu-value" v-if="autoShutdownState.enabled">
          {{ autoShutdownState.minutes }}分钟
        </span>
        <el-icon class="menu-icon" :class="{ active: autoShutdownState.enabled }">
          <i-ep-timer />
        </el-icon>
      </div>
      
      <!-- 导出视频 -->
      <div class="menu-item" v-if="timeline?.length > 0" @click="openVideoExport">
        <span>导出视频</span>
        <el-icon class="menu-icon"><i-ep-video-camera /></el-icon>
      </div>
    </div>

    <!-- 播放速度子菜单 -->
    <div class="speed-submenu" v-show="panelState.showSpeedMenu" @click.stop>
      <div 
        v-for="speed in playbackSpeeds" 
        :key="speed" 
        class="speed-item"
        :class="{ active: speed === (uiState.playbackRate || 1.0) }"
        @click="setPlaybackRate(speed)"
      >
        {{ speed }}x
      </div>
    </div>

    <!-- 定时关闭子菜单 -->
    <div class="timer-submenu" v-show="panelState.showTimerMenu" @click.stop>
      <div class="timer-item" @click="setTimer(0)">
        关闭
      </div>
      <div 
        v-for="time in timerOptions" 
        :key="time" 
        class="timer-item"
        :class="{ active: autoShutdownState.enabled && autoShutdownState.minutes === time }"
        @click="setTimer(time)"
      >
        {{ time }}分钟
      </div>
    </div>

    <!-- 字号子菜单 -->
    <div class="font-size-submenu" v-show="panelState.showFontSizeMenu" @click.stop>
      <div 
        v-for="size in [70, 85, 100, 115, 130, 150]" 
        :key="size" 
        class="font-size-item"
        :class="{ active: (textStyle.fontSizePercent || 100) === size }"
        @click="handleTextStyleChange({ fontSizePercent: size }); panelState.showFontSizeMenu = false; panelState.showPanel = false;"
      >
        {{ size }}%
      </div>
    </div>

    <!-- 颜色子菜单 -->
    <div class="color-submenu" v-show="panelState.showColorMenu" @click.stop>
      <div 
        v-for="color in colorOptions" 
        :key="color.value" 
        class="color-item"
        :class="{ active: textStyle.color === color.value }"
        @click="handleTextStyleChange({ color: color.value }); panelState.showColorMenu = false; panelState.showPanel = false;"
      >
        <div class="color-dot" :style="{ backgroundColor: color.value }"></div>
        <span>{{ color.label }}</span>
      </div>
    </div>



    <!-- 视频导出对话框 -->
    <VideoExportDialog 
      v-model="panelState.showVideoExportDialog"
      :timeline="timeline"
      :video-config="videoConfig"
      :config-json="configJson"
      :content-name="contentData?.name"
      @export-complete="$emit('export-complete', $event)"
    />
  </div>
</template>

<style scoped>
.unified-control-panel {
  position: relative;
  z-index: 100;
}

.settings-button {
  width: 2.25rem;
  height: 2.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
  border-radius: 0.25rem;
  transition: all 0.2s;
}

.settings-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.settings-menu {
  position: absolute;
  bottom: 100%;
  right: 0;
  margin-bottom: 0.5rem;
  min-width: 160px;
  background: rgba(40, 40, 40, 0.95);
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  color: #fff;
  cursor: pointer;
  transition: background 0.2s;
  font-size: 0.875rem;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.menu-value {
  color: #aaa;
  font-size: 0.8rem;
}

.menu-icon {
  color: #aaa;
  transition: color 0.2s;
}

.menu-icon.active {
  color: #ff0000;
}

.speed-submenu,
.timer-submenu,
.font-size-submenu,
.color-submenu {
  position: absolute;
  bottom: 100%;
  right: 160px;
  margin-bottom: 0.5rem;
  min-width: 80px;
  background: rgba(40, 40, 40, 0.95);
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  overflow: hidden;
}

.color-submenu {
  min-width: 120px;
}

.speed-item,
.timer-item,
.font-size-item {
  padding: 0.5rem 1rem;
  color: #fff;
  cursor: pointer;
  transition: background 0.2s;
  font-size: 0.875rem;
  text-align: center;
}

.color-item {
  padding: 0.5rem 1rem;
  color: #fff;
  cursor: pointer;
  transition: background 0.2s;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.speed-item:hover,
.timer-item:hover,
.font-size-item:hover,
.color-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.speed-item.active,
.timer-item.active,
.font-size-item.active,
.color-item.active {
  background: rgba(255, 0, 0, 0.2);
  color: #ff0000;
}

.color-preview {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.color-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 文本样式对话框 */
.text-style-content {
  padding: 1rem;
}

.style-section {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 0.875rem;
  color: #606266;
  margin-bottom: 0.75rem;
}

.color-options {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.color-option {
  width: 2rem;
  height: 2rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active-color {
  border-color: #409eff;
  transform: scale(1.1);
}

.size-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.size-option {
  padding: 0.5rem;
  text-align: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.8rem;
  border: 1px solid transparent;
}

.size-option:hover {
  background: rgba(0, 0, 0, 0.2);
}

.size-option.active {
  background: rgba(64, 158, 255, 0.2);
  border-color: #409eff;
  color: #409eff;
}

/* 滚动条样式 */
.panel-body::-webkit-scrollbar {
  width: 4px;
}

.panel-body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.panel-body::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.panel-body::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 开关样式 */
:deep(.el-switch) {
  --el-switch-on-color: #ff0000 !important;
  --el-switch-off-color: #dcdfe6;
}

:deep(.el-switch.is-checked .el-switch__core) {
  border-color: #ff0000 !important;
  background-color: #ff0000 !important;
}

:deep(.el-switch__core) {
  border-color: #dcdfe6 !important;
  background-color: #dcdfe6 !important;
}
</style>