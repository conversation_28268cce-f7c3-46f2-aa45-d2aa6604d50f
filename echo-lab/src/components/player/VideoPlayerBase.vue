<script setup>
import { ref, reactive, onMounted, onUnmounted, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import { loadUserConfig, updateUserConfig } from '@/services/userConfigService';
import ContentDisplay from './ContentDisplay.vue';
import { isMobileDevice } from '@/utils/deviceDetector';
import { WebAudioPlayer } from '@/utils/webAudioPlayer';
import { track } from '@/utils/analytics';
import { EVENTS } from '@/constants/events';


// 接收属性
const props = defineProps({
  timeline: {
    type: Array,
    default: () => []
  },
  currentIndex: {
    type: Number,
    default: 0
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  videoConfig: {
    type: Object,
    default: () => ({
      cover: {
        imageUrl: "",
        duration: 3
      },
      copyright: {
        text: "",
        position: "bottomRight"
      }
    })
  },
  loop: {
    type: Boolean,
    default: false
  }
});

// 事件
const emit = defineEmits(['timeupdate', 'loading-change', 'open-settings', 'index-change', 'toggle-playlist', 'boundary-reached', 'video-end']);

// Web Audio 播放器
const webAudioPlayer = ref(null);

// UI状态 - 从音频元素同步
const uiState = reactive({
  currentTime: 0,
  duration: 0,
  isPlaying: false,
  playbackRate: 1,
  currentIndex: 0,
  showPlaylist: true, // 播放列表显示状态
  isBuffering: false, // 缓冲状态
});

// 基准字体大小 - 根据设备类型不同
// 设置不同设备的基准字体大小
const BASE_FONT_SIZE = isMobileDevice() ? 1.0 : 2.2; // 手机端1.0rem，电脑端2.2rem

// 文本样式设置
const textStyle = reactive({
  fontSizePercent: 100, // 默认为100%
  get fontSize() {
    const size = BASE_FONT_SIZE * (this.fontSizePercent / 100);
    return size;
  },
  color: '#FFFFFF', // 默认颜色
  textShadow: '0.0625rem 0.0625rem 0.125rem rgba(0, 0, 0, 0.7)',
  showAnnotation: true // 默认显示标注
});

// 字体大小百分比范围
const fontSizeRange = {
  min: 80,  // 80%
  max: 200, // 200%
  step: 5    // 5%步长
};

// 字体颜色选项
const colorOptions = [
  { value: '#FFFFFF', label: '白色' },
  { value: '#FFD700', label: '金色' },
  { value: '#FF6B6B', label: '红色' },
  { value: '#4ECDC4', label: '青色' },
  { value: '#9ACD32', label: '绿色' }
];

// 控制状态
const controlState = reactive({
  isUserInteracting: false,
  isDraggingProgress: false, // 是否正在拖动进度条
  showControls: false,
  showSpeedMenu: false,
  showMobileMenu: false,
  showTextStyleDialog: false, // 文本样式设置对话框显示状态
  showAutoShutdownPanel: false, // 定时关闭面板显示状态
  controlsTimeout: null,
});

// 定时关闭状态
const autoShutdownState = reactive({
  enabled: false, // 是否启用定时关闭
  minutes: 15, // 定时关闭时间（分钟），默认15分钟
  remainingSeconds: 0, // 剩余秒数
  timerId: null, // 定时器ID
});

// 上次更新时间
let lastUpdateTime = 0;

// 定时器引用集合，用于组件卸载时清理
const timers = new Set();

// 安全的 setTimeout 包装函数
const safeSetTimeout = (callback, delay) => {
  const timerId = setTimeout(() => {
    timers.delete(timerId);
    callback();
  }, delay);
  timers.add(timerId);
  return timerId;
};

// 进度条值 - 用于v-model绑定
const progressValue = ref(0);

// 监听 uiState.currentTime 和 uiState.duration 的变化，更新 progressValue
watch([() => uiState.currentTime, () => uiState.duration], ([currentTime, duration]) => {
  // 只在用户没有交互时更新 progressValue
  if (!controlState.isUserInteracting && !controlState.isDraggingProgress && duration) {
    progressValue.value = (currentTime / duration) * 100;
  }
}, { immediate: true });

// 当前时间线项
const currentItem = computed(() => {
  if (!props.timeline || props.timeline.length === 0) {
    return null;
  }

  const currentIdx = uiState.currentIndex;
  if (currentIdx >= 0 && currentIdx < props.timeline.length) {
    return props.timeline[currentIdx];
  }

  return null;
});



// 处理时间更新 - 来自Web Audio Player
const handleTimeUpdateInternal = (currentTime) => {
  if (!webAudioPlayer.value) return;

  // 总是更新当前时间（用于显示）
  uiState.currentTime = currentTime;

  // 只在用户没有交互时更新索引和进度条
  if (!controlState.isUserInteracting && !controlState.isDraggingProgress) {
    const oldIndex = uiState.currentIndex;

    // 更新索引
    updateCurrentIndex();

    // 更新进度条值
    if (uiState.duration > 0) {
      progressValue.value = (currentTime / uiState.duration) * 100;
    }

    // 索引变化时的日志
    if (oldIndex !== uiState.currentIndex && props.timeline && props.timeline[uiState.currentIndex]) {
      const currentItem = props.timeline[uiState.currentIndex];
      console.log(`索引更新: ${oldIndex} -> ${uiState.currentIndex}, 内容: "${currentItem.content || 'N/A'}"`);
    }

    // 发送时间更新事件给父组件
    emit('timeupdate', currentTime);
  }
};

// 更新当前内容索引
const updateCurrentIndex = () => {
  if (!props.timeline || props.timeline.length === 0) return;

  const currentTime = uiState.currentTime;

  // 查找当前时间对应的索引 - 使用displayDuration来确定内容显示时长
  for (let i = 0; i < props.timeline.length; i++) {
    const item = props.timeline[i];

    // 跳过停顿和封面项目，只处理有内容的项目
    if (item?.type === 'pause' || item?.type === 'cover') {
      continue;
    }

    const startTime = item.startTime;
    const displayDuration = item.displayDuration || item.duration;
    const endTime = startTime + displayDuration;

    // 精确的时间匹配 - 内容显示时长 = 音频时长 + 后面的间隔时长
    if (currentTime >= startTime && currentTime < endTime) {
      if (i !== uiState.currentIndex) {
        console.log(`📺 VideoPlayerBase: 内容索引更新`, {
          oldIndex: uiState.currentIndex,
          newIndex: i,
          content: item.content?.substring(0, 20) || 'N/A',
          currentTime: currentTime.toFixed(3),
          startTime: startTime.toFixed(3),
          endTime: endTime.toFixed(3)
        });

        uiState.currentIndex = i;
        // 只在非用户交互时才发送事件，避免与用户操作冲突
        if (!controlState.isUserInteracting) {
          emit('index-change', i);
        }
      }
      return;
    }
  }

  // 如果没找到匹配的索引，查找最后一个有内容的项目
  for (let i = props.timeline.length - 1; i >= 0; i--) {
    const item = props.timeline[i];
    if (item?.type !== 'pause' && item?.type !== 'cover') {
      const displayDuration = item.displayDuration || item.duration;
      if (currentTime >= (item.startTime + displayDuration)) {
        if (i !== uiState.currentIndex) {
          uiState.currentIndex = i;
          if (!controlState.isUserInteracting) {
            emit('index-change', i);
          }
        }
      }
      break;
    }
  }
};

// 初始化 Web Audio 播放器
const initWebAudioPlayer = async () => {
  // 如果正在加载或者没有时间轴数据，不进行初始化
  if (props.isLoading || !props.timeline || props.timeline.length === 0) {
    return;
  }

  // 清理现有的播放器实例
  if (webAudioPlayer.value) {
    webAudioPlayer.value.destroy();
    webAudioPlayer.value = null;
  }

  try {
    console.log('🎵 Initializing Web Audio Player...');

    // 创建Web Audio播放器实例
    webAudioPlayer.value = new WebAudioPlayer();

    // 设置事件回调
    webAudioPlayer.value.onTimeUpdate = handleTimeUpdateInternal;
    webAudioPlayer.value.onEnded = handleAudioEnded;
    webAudioPlayer.value.onLoadingChange = (loading) => {
      uiState.isBuffering = loading;
      emit('loading-change', loading);
    };

    // 加载时间轴数据
    await webAudioPlayer.value.loadTimeline(props.timeline);

    // 设置初始状态
    uiState.duration = webAudioPlayer.value.duration || 0;

    console.log('✅ Web Audio Player initialized successfully');



  } catch (error) {
    console.error('初始化 Web Audio 播放器失败:', error);
    uiState.duration = 0;
    ElMessage.error('初始化播放器失败，请刷新页面重试');
  }
};

// 不再需要显示/隐藏封面的函数，因为我们现在使用统一的内容类型处理

// 检查音频是否已准备好
const checkAudioAvailability = (showWarning = true) => {
  // 检查Web Audio播放器是否存在
  if (!webAudioPlayer.value) {
    if (showWarning) {
      ElMessage.warning('播放器尚未加载完成，请稍候');
    }
    return false;
  }

  // 检查是否有时间轴数据
  if (!props.timeline || props.timeline.length === 0) {
    if (showWarning) {
      ElMessage.warning('播放列表尚未生成完成，请稍候');
    }
    return false;
  }

  // 检查音频是否有效（有时长）
  if (uiState.duration === 0) {
    if (showWarning) {
      ElMessage.warning('播放列表尚未加载完成，请稍候');
    }
    return false;
  }

  return true;
};

// 统一的播放状态管理方法
const updatePlayState = (isPlaying, options = {}) => {
  // 更新播放状态
  uiState.isPlaying = isPlaying;

  // 处理控制区域显示/隐藏
  if (isPlaying) {
    // 播放状态：如果没有特殊指示，则隐藏控制区域
    if (!options.keepControlsVisible) {
      // 立即隐藏控制区域
      controlState.showControls = false;

      // 清除任何可能存在的隐藏定时器
      if (controlState.controlsTimeout) {
        clearTimeout(controlState.controlsTimeout);
        controlState.controlsTimeout = null;
      }
    } else {
      // 如果需要保持控制区域可见一段时间
      resetControlsTimeout();
    }
  } else {
    // 暂停状态：始终显示控制区域
    controlState.showControls = true;

    // 清除任何可能存在的隐藏定时器
    if (controlState.controlsTimeout) {
      clearTimeout(controlState.controlsTimeout);
      controlState.controlsTimeout = null;
    }
  }

  // 返回当前播放状态，方便链式调用
  return isPlaying;
};

// 播放/暂停
const togglePlay = async () => {
  // 检查音频是否可用，显示警告消息
  if (!checkAudioAvailability(true)) {
    return;
  }

  try {
    // 播放/暂停逻辑
    if (uiState.isPlaying) {
      // 如果正在播放，则暂停
      webAudioPlayer.value.pause();
      updatePlayState(false);
      
      // 上报暂停事件
      track(EVENTS.VIDEO_PAUSE, { content_id: getCurrentContentId() });
    } else {
      // 如果已暂停，则开始播放
      await webAudioPlayer.value.play();
      updatePlayState(true);
      console.log('开始播放音频');
      
      // 上报播放事件
      track(EVENTS.VIDEO_PLAY, { content_id: getCurrentContentId() });
    }
  } catch (error) {
    console.error('播放/暂停操作失败:', error);
    if (error.name === 'NotAllowedError') {
      ElMessage.error('请先点击播放按钮启动音频播放');
    } else {
      ElMessage.error('播放操作失败，请刷新页面重试');
    }
    updatePlayState(false);
  }
};

// 开始拖动进度条
const startProgressDrag = () => {
  controlState.isDraggingProgress = true;
  controlState.isUserInteracting = true;
};

// 结束拖动进度条
const endProgressDrag = () => {
  console.log(`🎯 Progress drag ended at ${progressValue.value.toFixed(1)}%`);

  // 立即停止拖动状态
  controlState.isDraggingProgress = false;

  // 延迟恢复自动更新，确保跳转操作完成
  setTimeout(() => {
    controlState.isUserInteracting = false;
    console.log(`🔄 Progress interaction ended, resuming auto-update`);
  }, 500); // 增加延迟时间，确保Web Audio Player跳转完成
};

// 处理进度条变化
const handleProgressChange = (value) => {
  if (!webAudioPlayer.value || !uiState.duration || value === undefined || value === null) return;

  // 标记用户正在交互
  controlState.isUserInteracting = true;

  // 计算新的时间点
  const newTime = (value / 100) * uiState.duration;
  const fromTime = uiState.currentTime;

  // 立即更新进度条值（避免回弹）
  progressValue.value = value;

  // 限制跳转频率，避免过于频繁的操作
  if (!controlState.isDraggingProgress || Date.now() - lastUpdateTime > 150) {
    lastUpdateTime = Date.now();

    try {
      // 跳转到新的时间点
      webAudioPlayer.value.seekTo(newTime);
      uiState.currentTime = newTime;

      // 立即更新索引
      updateCurrentIndex();

      // 移除跳转事件上报

      console.log(`🎯 Progress seek: ${value.toFixed(1)}% → ${newTime.toFixed(2)}s`);
    } catch (error) {
      console.error('进度条跳转失败:', error);
    }
  } else {
    // 即使不跳转，也要更新UI状态
    uiState.currentTime = newTime;
  }
};



// 设置播放速度 - 支持全局倍速调整
const setPlaybackRate = (value) => {
  // 标记用户正在交互
  controlState.isUserInteracting = true;

  uiState.playbackRate = value;

  // 设置Web Audio Player的全局播放速度
  if (webAudioPlayer.value) {
    webAudioPlayer.value.setGlobalPlaybackRate(value);
  }

  console.log('全局播放速度已更新为:', value);

  // 短暂延迟后恢复自动更新
  setTimeout(() => {
    controlState.isUserInteracting = false;
  }, 200);
};

// 全屏功能已移除

// 格式化时间
const formatTime = (time) => {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// 处理文本样式变更
const handleTextStyleChange = (newStyle) => {
  if (newStyle.fontSizePercent !== undefined) {
    // 更新百分比值
    textStyle.fontSizePercent = Math.max(fontSizeRange.min, Math.min(fontSizeRange.max, newStyle.fontSizePercent));
  }

  if (newStyle.color !== undefined) {
    textStyle.color = newStyle.color;
  }
  if (newStyle.textShadow !== undefined) {
    textStyle.textShadow = newStyle.textShadow;
  }

};

// 切换播放列表显示/隐藏
const togglePlaylist = (value) => {
  // 更新本地状态
  uiState.showPlaylist = value;

  // 保存到用户配置
  updateUserConfig('player.showPlaylist', value);

  // 发送切换播放列表事件
  emit('toggle-playlist');
};

// 跳转到指定索引
const jumpToIndex = async (index) => {
  console.log('🎯 VideoPlayerBase: jumpToIndex 被调用', {
    index,
    timelineLength: props.timeline?.length,
    currentIndex: uiState.currentIndex
  });
  
  if (!props.timeline || index < 0 || index >= props.timeline.length) {
    console.warn('跳转索引无效:', index, '时间轴长度:', props.timeline?.length);
    return;
  }

  if (!checkAudioAvailability(false)) {
    console.warn('音频不可用，无法跳转');
    return;
  }

  if (!webAudioPlayer.value) {
    console.warn('Web Audio播放器不存在，无法跳转');
    return;
  }

  const item = props.timeline[index];
  const targetTime = item.startTime || 0;

  console.log('🎯 VideoPlayerBase: 准备跳转到', {
    index,
    targetTime,
    itemContent: item.content?.substring(0, 20) || 'N/A'
  });

  controlState.isUserInteracting = true;

  try {
    // 先更新索引和发送事件
    uiState.currentIndex = index;
    emit('index-change', index);

    // 跳转到目标时间
    webAudioPlayer.value.seekTo(targetTime);
    uiState.currentTime = targetTime;

    // 更新进度条
    if (uiState.duration) {
      progressValue.value = (targetTime / uiState.duration) * 100;
    }

    console.log('🎯 VideoPlayerBase: 跳转完成，开始播放');

    // 尝试开始播放
    try {
      await webAudioPlayer.value.play();
      updatePlayState(true);
      console.log('🎯 VideoPlayerBase: 播放开始成功');
    } catch (playError) {
      if (playError.name === 'NotAllowedError') {
        console.log('🎯 VideoPlayerBase: 需要用户交互才能播放');
        ElMessage.warning('请先点击播放按钮启动音频播放');
        updatePlayState(false);
        return;
      }
      throw playError;
    }

    // 恢复自动更新
    setTimeout(() => {
      controlState.isUserInteracting = false;
      console.log('🎯 VideoPlayerBase: 恢复自动更新');
    }, 500);

  } catch (error) {
    console.error('跳转操作失败:', error);
    updatePlayState(false);
    controlState.isUserInteracting = false;
  }
};

// 跳转到当前句子开头
const jumpToCurrentSentenceStart = () => {
  if (!props.timeline || props.currentIndex < 0 || props.currentIndex >= props.timeline.length) return;

  if (!checkAudioAvailability(true)) {
    return;
  }

  jumpToIndex(props.currentIndex);
};

// 跳转到指定时间
const jumpToTime = async (time) => {
  if (!checkAudioAvailability(true)) {
    return;
  }

  if (!webAudioPlayer.value) {
    console.warn('Web Audio播放器不存在，无法跳转到时间');
    return;
  }

  console.log(`🕐 跳转到时间: ${time.toFixed(3)}s`);

  // 标记用户交互
  controlState.isUserInteracting = true;

  try {
    // 跳转到新的时间点
    webAudioPlayer.value.seekTo(time);
    uiState.currentTime = time;

    // 更新进度条
    if (uiState.duration) {
      progressValue.value = (time / uiState.duration) * 100;
    }

    // 开始播放
    await webAudioPlayer.value.play();
    updatePlayState(true);
    console.log(`✅ 时间跳转成功，开始播放: ${time.toFixed(3)}s`);

    // 短暂延迟后恢复自动更新
    setTimeout(() => {
      controlState.isUserInteracting = false;
      console.log('🔄 时间跳转后恢复自动更新');
    }, 500);

  } catch (error) {
    console.error('时间跳转失败:', error);
    updatePlayState(false);
    controlState.isUserInteracting = false;
  }
};

// 处理音频结束 - 整个播放列表结束的事件
const handleAudioEnded = async () => {
  console.log('🎬 VideoPlayerBase: 音频播放结束，发出 video-end 事件');
  
  // 上报播放完成事件
  track(EVENTS.VIDEO_COMPLETE, { content_id: getCurrentContentId() });
  
  // 发出播放结束事件给父组件
  emit('video-end');
  
  // 只有在循环播放模式下才重置到开头
  if (props.loop && webAudioPlayer.value) {
    console.log('🎬 VideoPlayerBase: 循环模式，重新播放');
    try {
      webAudioPlayer.value.seekTo(0);
      uiState.currentTime = 0;
      await webAudioPlayer.value.play();
      updatePlayState(true);
    } catch (error) {
      console.error('循环播放失败:', error);
      updatePlayState(false);
    }
  } else {
    console.log('🎬 VideoPlayerBase: 非循环模式，停止播放');
    // 非循环模式：停止播放，保持在当前位置
    updatePlayState(false);
  }
};



// 处理音频可以播放事件
const handleCanPlay = () => {
  // 确保进度条位置与当前时间同步
  if (uiState.currentTime > 0 && uiState.duration > 0) {
    progressValue.value = (uiState.currentTime / uiState.duration) * 100;
  } else {
    progressValue.value = 0;
  }

  // 通知父组件可以隐藏加载提示
  emit('loading-change', false);
};

// 全屏功能已移除



// 监听props.currentIndex变化 - 同步父组件状态
watch(() => props.currentIndex, async (newIndex, oldIndex) => {
  console.log('🎯 VideoPlayerBase: props.currentIndex 变化', {
    oldIndex,
    newIndex,
    uiStateCurrentIndex: uiState.currentIndex
  });
  
  if (newIndex !== uiState.currentIndex) {
    console.log('🎯 VideoPlayerBase: 同步索引到 uiState');
    uiState.currentIndex = newIndex;
    
    // 跳转到对应时间点
    if (webAudioPlayer.value && props.timeline[newIndex]) {
      const targetTime = props.timeline[newIndex].startTime || 0;
      console.log('🎯 VideoPlayerBase: 跳转到时间点', targetTime);
      
      controlState.isUserInteracting = true;
      webAudioPlayer.value.seekTo(targetTime);
      uiState.currentTime = targetTime;
      
      // 更新进度条
      if (uiState.duration) {
        progressValue.value = (targetTime / uiState.duration) * 100;
      }
      
      // 恢复自动更新
      setTimeout(() => {
        controlState.isUserInteracting = false;
      }, 300);
    }
  }
});

// 监听加载状态变化
watch(() => props.isLoading, (isLoading) => {
  console.log('加载状态变化:', isLoading);

  // 如果加载完成，尝试初始化 Web Audio 播放器
  if (!isLoading && props.timeline && props.timeline.length > 0) {
    initWebAudioPlayer();
  }
});

// 重置播放器到初始状态的统一方法
const resetToInitialState = () => {
  console.log('🔄 重置播放器到初始状态');

  // 重置播放进度到0
  uiState.currentTime = 0;
  progressValue.value = 0;
  uiState.currentIndex = 0;

  // 如果正在播放，先暂停
  if (webAudioPlayer.value && webAudioPlayer.value.isPlaying) {
    webAudioPlayer.value.pause();
    updatePlayState(false);
  }

  // 向父组件发送索引变化事件，确保所有相关组件同步
  emit('index-change', 0);

  console.log('✅ 播放器状态已重置');
};

// 监听时间轴变化
watch(() => props.timeline, (newTimeline, oldTimeline) => {
  console.log('时间轴变化:', { newLength: newTimeline?.length, oldLength: oldTimeline?.length });

  // 统一重置到初始状态
  resetToInitialState();

  // 如果有新的时间轴且不在加载状态，初始化 Web Audio 播放器
  if (newTimeline && newTimeline.length > 0 && !props.isLoading) {
    initWebAudioPlayer();
  }
});

// 生命周期钩子
onMounted(() => {
  // 加载用户配置
  const userConfig = loadUserConfig();

  // 应用播放列表显示状态
  if (userConfig.player && userConfig.player.showPlaylist !== undefined) {
    uiState.showPlaylist = userConfig.player.showPlaylist;
  }

  // 应用文本样式设置
  if (userConfig.player && userConfig.player.textStyle) {
    // 应用百分比值
    if (userConfig.player.textStyle.fontSizePercent !== undefined) {
      textStyle.fontSizePercent = userConfig.player.textStyle.fontSizePercent;
    }

    textStyle.color = userConfig.player.textStyle.color || textStyle.color;
    textStyle.textShadow = userConfig.player.textStyle.textShadow || textStyle.textShadow;
    textStyle.showAnnotation = userConfig.player.textStyle.showAnnotation !== undefined ?
      userConfig.player.textStyle.showAnnotation : textStyle.showAnnotation;
  }

  // 定时关闭功能不再从本地配置加载

  // 初始显示控制元素
  controlState.showControls = true;

  // 只有在不加载状态时才初始化 Web Audio 播放器并发送加载完成事件
  if (!props.isLoading && props.timeline && props.timeline.length > 0) {
    initWebAudioPlayer();
    emit('loading-change', false);
  }

  // 同步初始索引
  uiState.currentIndex = props.currentIndex;
});

onUnmounted(() => {
  // 清理资源

  // 清理所有定时器
  timers.forEach(timerId => {
    clearTimeout(timerId);
    clearInterval(timerId);
  });
  timers.clear();

  // 停止定时关闭计时器
  if (autoShutdownState.timerId) {
    clearInterval(autoShutdownState.timerId);
    autoShutdownState.timerId = null;
  }

  // 清除控制定时器
  if (controlState.controlsTimeout) {
    clearTimeout(controlState.controlsTimeout);
  }

  // 清理 Web Audio 播放器
  if (webAudioPlayer.value) {
    try {
      webAudioPlayer.value.destroy();
      webAudioPlayer.value = null;
    } catch (error) {
      console.error('销毁 Web Audio 播放器失败:', error);
    }
  }
});

// 显示控制元素（仅在鼠标移动时调用）
const showControlsElements = () => {
  // 显示控制元素
  controlState.showControls = true;

  // 清除之前的定时器
  if (controlState.controlsTimeout) {
    clearTimeout(controlState.controlsTimeout);
    controlState.controlsTimeout = null;
  }

  // 如果正在播放，设置自动隐藏定时器
  if (uiState.isPlaying) {
    controlState.controlsTimeout = setTimeout(() => {
      if (uiState.isPlaying) {
        controlState.showControls = false;
      }
    }, 2000);
  }
};

// 立即隐藏控制元素（鼠标离开时调用）
const hideControlsImmediately = () => {
  // 清除定时器
  if (controlState.controlsTimeout) {
    clearTimeout(controlState.controlsTimeout);
    controlState.controlsTimeout = null;
  }

  // 如果正在播放，立即隐藏控制区域
  if (uiState.isPlaying) {
    controlState.showControls = false;
  }
};

// 重置控制元素隐藏定时器
const resetControlsTimeout = () => {
  // 显示控制元素
  controlState.showControls = true;

  // 清除之前的定时器
  if (controlState.controlsTimeout) {
    clearTimeout(controlState.controlsTimeout);
  }

  // 如果正在播放，设置新的定时器
  if (uiState.isPlaying) {
    controlState.controlsTimeout = setTimeout(() => {
      // 只有在播放状态下才隐藏控制区域
      if (uiState.isPlaying) {
        controlState.showControls = false;
      }
    }, 2000);
  }
};

// 跳转到上一个句子 - 简化逻辑
const jumpToPreviousSentence = () => {
  if (!props.timeline || props.timeline.length === 0) return false;

  const currentIdx = props.currentIndex;
  const currentTime = uiState.currentTime;
  const currentItem = props.timeline[currentIdx];
  
  // 如果不在当前句开头附近，先跳到开头
  if (currentItem.content?.trim()) {
    const currentStartTime = currentItem.startTime || 0;
    const isNearStart = Math.abs(currentTime - currentStartTime) < 0.3;
    
    if (!isNearStart) {
      jumpToIndex(currentIdx);
      return true;
    }
  }
  
  // 查找上一个有内容的项目
  for (let i = currentIdx - 1; i >= 0; i--) {
    const item = props.timeline[i];
    if (item?.type !== 'pause' && item?.type !== 'cover' && item?.content?.trim()) {
      jumpToIndex(i);
      return true;
    }
  }

  emit('boundary-reached', 'start');
  return false;
};



// 直接跳转到上一个句子（用于上键）
const jumpToPreviousDirectly = () => {
  if (!props.timeline || props.timeline.length === 0) return false;

  const currentIdx = props.currentIndex;
  
  for (let i = currentIdx - 1; i >= 0; i--) {
    const item = props.timeline[i];
    if (item?.type !== 'pause' && item?.type !== 'cover' && item?.content?.trim()) {
      jumpToIndex(i);
      return true;
    }
  }

  emit('boundary-reached', 'start');
  return false;
};

// 跳转到下一个句子
const jumpToNextSentence = () => {
  if (!props.timeline || props.timeline.length === 0) return false;

  const currentIdx = props.currentIndex;

  for (let i = currentIdx + 1; i < props.timeline.length; i++) {
    const item = props.timeline[i];
    if (item?.type !== 'pause' && item?.type !== 'cover' && item?.content?.trim()) {
      jumpToIndex(i);
      return true;
    }
  }

  emit('boundary-reached', 'end');
  return false;
};

// 检查是否可以跳转到上一个句子 - 使用父组件状态
const canJumpToPreviousSentence = computed(() => {
  if (!props.timeline || props.timeline.length === 0) return false;
  
  for (let i = props.currentIndex - 1; i >= 0; i--) {
    const item = props.timeline[i];
    if (item?.type !== 'pause' && item?.type !== 'cover' && item?.content?.trim()) {
      return true;
    }
  }
  return false;
});

// 检查是否可以跳转到下一个句子 - 使用父组件状态
const canJumpToNextSentence = computed(() => {
  if (!props.timeline || props.timeline.length === 0) return false;
  
  for (let i = props.currentIndex + 1; i < props.timeline.length; i++) {
    const item = props.timeline[i];
    if (item?.type !== 'pause' && item?.type !== 'cover' && item?.content?.trim()) {
      return true;
    }
  }
  return false;
});

// 启动定时关闭计时器
const startAutoShutdownTimer = () => {
  // 先清除可能存在的定时器
  stopAutoShutdownTimer();

  if (!autoShutdownState.enabled) return;

  // 设置剩余秒数
  autoShutdownState.remainingSeconds = autoShutdownState.minutes * 60;

  // 创建定时器，每秒更新一次
  autoShutdownState.timerId = setInterval(() => {
    // 减少剩余秒数
    autoShutdownState.remainingSeconds--;

    // 如果剩余秒数为0，执行关闭操作
    if (autoShutdownState.remainingSeconds <= 0) {
      executeAutoShutdown();
    }
  }, 1000);

  console.log(`定时关闭计时器已启动，将在${autoShutdownState.minutes}分钟后关闭`);
};

// 停止定时关闭计时器
const stopAutoShutdownTimer = () => {
  if (autoShutdownState.timerId) {
    clearInterval(autoShutdownState.timerId);
    autoShutdownState.timerId = null;
    console.log('定时关闭计时器已停止');
  }
};

// 执行定时关闭操作
const executeAutoShutdown = () => {
  // 停止定时器
  stopAutoShutdownTimer();

  // 暂停播放
  if (webAudioPlayer.value && uiState.isPlaying) {
    webAudioPlayer.value.pause();
    updatePlayState(false);
  }

  // 显示提示
  ElMessage.success('已按设定时间自动停止播放');

  // 禁用定时关闭功能
  autoShutdownState.enabled = false;
};

// 设置定时关闭时间
const setAutoShutdownTime = (minutes) => {
  autoShutdownState.minutes = minutes;

  // 如果定时器已启动，重新启动
  if (autoShutdownState.enabled) {
    startAutoShutdownTimer();
  }
};

// 切换定时关闭状态
const toggleAutoShutdown = () => {
  autoShutdownState.enabled = !autoShutdownState.enabled;

  if (autoShutdownState.enabled) {
    startAutoShutdownTimer();
    ElMessage.success(`已启用定时关闭，将在${autoShutdownState.minutes}分钟后停止播放`);
  } else {
    stopAutoShutdownTimer();
    ElMessage.info('已取消定时关闭');
  }
};

// 切换标注显示状态
const toggleAnnotation = () => {
  textStyle.showAnnotation = !textStyle.showAnnotation;

  // 保存到用户配置
  updateUserConfig('player.textStyle.showAnnotation', textStyle.showAnnotation);
};

// 格式化剩余时间显示
const formatRemainingTime = () => {
  if (!autoShutdownState.enabled) return '';

  const minutes = Math.floor(autoShutdownState.remainingSeconds / 60);
  const seconds = autoShutdownState.remainingSeconds % 60;

  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};


// 处理内容元素点击
const handleContentElementClick = (event) => {
  // 触发父组件的事件
  emit('content-element-click', event);
};

// 获取当前内容ID（用于上报）
const getCurrentContentId = () => {
  // 尝试从路由参数获取内容ID
  if (typeof window !== 'undefined' && window.location) {
    const pathMatch = window.location.pathname.match(/\/player\/(\w+)/);
    if (pathMatch) {
      return pathMatch[1];
    }
  }
  
  // 如果无法获取，返回默认值
  return 'unknown';
};

// 暴露方法给父组件
defineExpose({
  uiState,
  controlState,
  textStyle,
  autoShutdownState,
  togglePlay,
  jumpToIndex,
  jumpToCurrentSentenceStart,
  jumpToTime,
  jumpToPreviousSentence,
  jumpToPreviousDirectly,
  jumpToNextSentence,
  canJumpToPreviousSentence,
  canJumpToNextSentence,
  setPlaybackRate,
  // toggleFullscreen 已移除
  handleProgressChange,
  startProgressDrag,
  endProgressDrag,
  handleTextStyleChange,
  togglePlaylist,
  formatTime,
  progressValue,
  showControlsElements,
  hideControlsImmediately,
  resetControlsTimeout,
  currentItem,
  webAudioPlayer,
  // 定时关闭相关方法
  toggleAutoShutdown,
  setAutoShutdownTime,
  formatRemainingTime,
  startAutoShutdownTimer,
  stopAutoShutdownTimer,
  // 标注相关方法
  toggleAnnotation,
  fontSizeRange,
  colorOptions,
  // 配置相关方法
  updateUserConfig,
  // 重置方法
  resetToInitialState,
  // 内容ID获取方法
  getCurrentContentId
});
</script>

<template>
  <div class="video-player-base">
    <!-- Web Audio Player - 无需DOM元素 -->

    <!-- 内容显示区域 -->
    <div class="content-container">
      <!-- 缓冲loading状态 -->
      <div v-if="uiState.isBuffering" class="buffering-overlay">
        <div class="buffering-spinner">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
          <span class="buffering-text">缓冲中...</span>
        </div>
      </div>

      <!-- 使用ContentDisplay组件显示所有内容 -->
      <ContentDisplay v-if="currentItem" :item="currentItem" :copyright="props.videoConfig.copyright" :textStyle="{
        fontSize: textStyle.fontSize,
        color: textStyle.color,
        textShadow: textStyle.textShadow,
        backgroundColor: 'transparent',
        showAnnotation: textStyle.showAnnotation
      }" @content-element-click="handleContentElementClick" />
    </div>
  </div>
</template>

<style scoped>
.video-player-base {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #000;
}

.content-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  background-color: inherit;
}



/* 缓冲loading样式 */
.buffering-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  backdrop-filter: blur(2px);
}

.buffering-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.8);
  padding: 1.5rem 2rem;
  border-radius: 0.5rem;
  animation: pulse 1.5s ease-in-out infinite;
}

.buffering-spinner .el-icon {
  font-size: 2.5rem;
}

.buffering-text {
  font-size: 1rem;
  font-weight: 500;
}

@keyframes pulse {
  0%, 100% { opacity: 0.9; }
  50% { opacity: 1; }
}

/* 调试信息样式 */
.debug-info {
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 0.5rem;
  font-size: 0.75rem;
  z-index: 10;
  max-width: 100%;
  word-break: break-all;
}
</style>
