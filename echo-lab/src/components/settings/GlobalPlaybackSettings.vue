<template>
  <div class="global-playback-settings">
    <div class="setting-section">
      <h3>学习模式</h3>
      <p class="setting-desc">设置后将自动应用到所有新打开的内容</p>
      
      <div class="current-global-template">
        <div class="template-info">
          <span v-if="currentGlobalTemplate" class="template-name">
            当前学习模式：{{ currentGlobalTemplate.name }}
          </span>
          <span v-else class="no-template">
            未设置学习模式
          </span>
        </div>
        
        <div class="template-actions">
          <el-button @click="showTemplateSelector = true">
            {{ currentGlobalTemplate ? '更换模式' : '设置模式' }}
          </el-button>
          <el-button 
            v-if="currentGlobalTemplate" 
            @click="clearGlobalTemplate"
            type="danger"
            plain
          >
            清除
          </el-button>
        </div>
      </div>

      <div class="setting-note">
        <el-alert
          title="说明"
          type="info"
          :closable="false"
        >
          <ul>
            <li>全局策略会自动应用到所有内容</li>
            <li>模板几个环节就播放几个环节</li>
            <li>手动修改的设置优先级更高</li>
          </ul>
        </el-alert>
      </div>
    </div>

    <!-- 模板选择器 -->
    <el-drawer v-model="showTemplateSelector" title="选择学习模式" size="60%">
      <TemplateSelector 
        :current-template="currentGlobalTemplate"
        @select="setGlobalTemplate"
        @close="showTemplateSelector = false"
      />
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import TemplateSelector from '../template/TemplateSelector.vue';
import globalPlaybackService from '@/services/globalPlaybackService';

const currentGlobalTemplate = ref(null);
const showTemplateSelector = ref(false);

const loadGlobalTemplate = async () => {
  try {
    currentGlobalTemplate.value = await globalPlaybackService.getGlobalTemplate();
    console.log('已加载全局模板:', currentGlobalTemplate.value?.name || 'null');
  } catch (error) {
    console.error('加载全局模板失败:', error);
    currentGlobalTemplate.value = null;
  }
};

const setGlobalTemplate = async (template) => {
  try {
    globalPlaybackService.setGlobalTemplate(template.id);
    currentGlobalTemplate.value = template;
    showTemplateSelector.value = false;
    ElMessage.success('学习模式设置成功');
    console.log('已设置全局模板:', template.name);
  } catch (error) {
    console.error('设置全局模板失败:', error);
    ElMessage.error('设置学习模式失败');
  }
};

const clearGlobalTemplate = async () => {
  try {
    globalPlaybackService.setGlobalTemplate(null);
    currentGlobalTemplate.value = null;
    ElMessage.success('已清除学习模式');
    console.log('已清除全局模板');
  } catch (error) {
    console.error('清除全局模板失败:', error);
    ElMessage.error('清除学习模式失败');
  }
};

onMounted(() => {
  loadGlobalTemplate();
});
</script>

<style scoped>
.global-playback-settings {
  padding: 1rem;
}

.setting-section {
  margin-bottom: 2rem;
}

.setting-section h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.setting-desc {
  color: #606266;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.current-global-template {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e4e7ed;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.template-name {
  color: #409eff;
  font-weight: 500;
}

.no-template {
  color: #909399;
}

.template-actions {
  display: flex;
  gap: 0.5rem;
}

.setting-note ul {
  margin: 0;
  padding-left: 1.5rem;
}

.setting-note li {
  margin-bottom: 0.25rem;
}
</style>