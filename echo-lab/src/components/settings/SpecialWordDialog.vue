<template>
  <StandardDialog v-model="dialogVisible" title="特殊词汇管理" width="800px" :loading="loading" @close="handleClose">
    <div class="special-word-dialog">
      <!-- 服务和语言选择 -->
      <div class="filter-section">
        <el-select v-model="currentService" placeholder="选择TTS服务" @change="handleServiceChange">
          <el-option v-for="service in availableServices" :key="service.id" :label="service.name" :value="service.id" />
        </el-select>

        <el-select v-model="currentLanguage" placeholder="选择语言" @change="handleLanguageChange">
          <el-option v-for="language in availableLanguages" :key="language.code" :label="language.name"
            :value="language.code" />
        </el-select>
      </div>

      <!-- 特殊词汇列表 -->
      <div class="word-list-section">
        <div class="section-header">
          <h3>特殊词汇列表</h3>
          <el-tooltip content="特殊词汇会在音频生成前使用标注中的假名替换原文，确保TTS服务生成正确的发音" placement="top">
            <el-icon>
              <i-ep-question-filled />
            </el-icon>
          </el-tooltip>
        </div>

        <div class="word-list">
          <div class="word-grid">
            <div v-for="word in filteredWords" :key="word.id || word.word" class="word-card">
              <div class="word-card-header">
                <el-tag :type="word.isSystem ? 'success' : 'info'" size="small" class="source-tag">
                  {{ word.isSystem ? '系统' : '用户' }}
                </el-tag>
                <el-button v-if="!word.isSystem" type="danger" size="small" icon="Delete" circle
                  @click="deleteWord(word)" class="delete-btn" />
              </div>
              <div class="word-card-content">
                {{ word.word }}
              </div>
            </div>

            <!-- 添加新词卡片 -->
            <div class="word-card add-card" @click="focusNewWordInput">
              <div class="add-icon">+</div>
              <div class="add-text">添加特殊词汇</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加特殊词汇 -->
      <div class="add-word-section">
        <div class="section-header">
          <h3>添加特殊词汇</h3>
        </div>

        <div class="add-word-form">
          <el-input v-model="newWord" placeholder="输入特殊词汇" ref="newWordInputRef" />
          <el-button type="primary" @click="addWord" :disabled="!newWord">添加</el-button>
        </div>

        <div class="help-text">
          <p>特殊词汇是指在TTS服务中可能有多种读音的词汇，如"紅葉"可读作"こうよう"或"もみじ"。</p>
          <p>添加特殊词汇后，系统会在音频生成前使用标注中的假名替换原文，确保TTS服务生成正确的发音。</p>
          <p class="note">注意：特殊词汇必须与标注数据中的标注单位完全匹配才能成功替换。</p>
          <p>点击"添加"按钮将特殊词汇添加到列表中，点击"保存"按钮将所有更改保存到本地。</p>
        </div>
      </div>
    </div>

    <!-- 添加明确的底部按钮 -->
    <template #footer>
      <div class="special-word-dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="saveSpecialWords">保存</el-button>
      </div>
    </template>
  </StandardDialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import StandardDialog from '../common/StandardDialog.vue';
import {
  saveLocalSpecialWords,
  getSpecialWordsList
} from '@/services/specialWordService';
import { getTtsInfo } from '@/services/ttsInfoService';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'saved']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

// 数据状态
const loading = ref(false);
const ttsInfo = ref(null);
const specialWords = ref({}); // 内存中的词汇列表
const currentService = ref('');
const currentLanguage = ref('');
const newWord = ref('');
const hasChanges = ref(false); // 跟踪是否有未保存的更改

// 添加新词输入框引用
const newWordInputRef = ref(null);

// 计算属性
const availableServices = computed(() => {
  if (!ttsInfo.value) return [];

  // 从TTS信息中提取服务列表
  const services = [];
  const serviceIds = new Set();

  ttsInfo.value.voices.forEach(voice => {
    if (!serviceIds.has(voice.service_id)) {
      serviceIds.add(voice.service_id);
      services.push({
        id: voice.service_id,
        name: voice.service_name
      });
    }
  });

  return services;
});

const availableLanguages = computed(() => {
  if (!ttsInfo.value) return [];

  return Object.entries(ttsInfo.value.languages).map(([code, info]) => ({
    code,
    name: info.name
  }));
});

const filteredWords = computed(() => {
  if (!currentService.value || !currentLanguage.value) return [];

  // 从内存中的特殊词汇列表获取当前服务和语言的词汇
  const serviceWords = specialWords.value[currentService.value] || {};
  return serviceWords[currentLanguage.value] || [];
});

// 方法
function focusNewWordInput() {
  // 滚动到添加词汇区域并聚焦输入框
  document.querySelector('.add-word-form')?.scrollIntoView({ behavior: 'smooth' });
  setTimeout(() => {
    newWordInputRef.value?.focus();
  }, 300);
}

async function fetchData() {
  loading.value = true;
  try {
    console.log('[SpecialWord] 开始加载数据...');

    // 获取TTS服务信息
    ttsInfo.value = await getTtsInfo();
    console.log('[SpecialWord] 已加载TTS服务信息');

    // 设置初始服务和语言
    if (availableServices.value.length > 0) {
      currentService.value = availableServices.value[0].id;
    }

    if (availableLanguages.value.length > 0) {
      currentLanguage.value = availableLanguages.value[0].code;
    }

    // 初始化特殊词汇对象
    specialWords.value = {};

    // 如果已选择服务和语言，加载特殊词汇
    if (currentService.value && currentLanguage.value) {
      console.log(`[SpecialWord] 获取特殊词汇列表 - 服务: ${currentService.value}, 语言: ${currentLanguage.value}`);
      const wordsList = await getSpecialWordsList(currentService.value, currentLanguage.value);

      // 确保数据结构正确
      if (!specialWords.value[currentService.value]) {
        specialWords.value[currentService.value] = {};
      }
      specialWords.value[currentService.value][currentLanguage.value] = wordsList;

      console.log('[SpecialWord] 获取到的特殊词汇:', wordsList);
    }

    // 重置更改标记
    hasChanges.value = false;
    console.log('[SpecialWord] 数据加载完成');
  } catch (error) {
    console.error('[SpecialWord] 加载数据失败:', error);
    ElMessage.error('加载数据失败: ' + error.message);
  } finally {
    loading.value = false;
  }
}

// 当服务或语言改变时重新加载数据
async function handleServiceChange() {
  newWord.value = '';
  if (currentService.value && currentLanguage.value) {
    await fetchData();
  }
}

async function handleLanguageChange() {
  newWord.value = '';
  if (currentService.value && currentLanguage.value) {
    await fetchData();
  }
}

async function addWord() {
  if (!newWord.value || !currentService.value || !currentLanguage.value) {
    return;
  }

  try {
    loading.value = true;
    console.log(`[SpecialWord] 添加词汇: "${newWord.value}" (${currentService.value}, ${currentLanguage.value})`);

    // 检查是否已存在
    const exists = filteredWords.value.some(w => w.word === newWord.value);
    if (exists) {
      ElMessage.warning('该特殊词汇已存在');
      return;
    }

    // 添加到内存中的列表
    if (!specialWords.value[currentService.value]) {
      specialWords.value[currentService.value] = {};
    }

    if (!specialWords.value[currentService.value][currentLanguage.value]) {
      specialWords.value[currentService.value][currentLanguage.value] = [];
    }

    specialWords.value[currentService.value][currentLanguage.value].push({
      id: `local_${Date.now()}`,
      word: newWord.value,
      isSystem: false,
      isLocal: true
    });

    // 标记有未保存的更改
    hasChanges.value = true;
    console.log('[SpecialWord] 词汇已添加到内存');

    // 重置输入
    newWord.value = '';

    ElMessage.success('添加特殊词汇成功');
  } catch (error) {
    console.error('[SpecialWord] 添加词汇失败:', error);
    ElMessage.error('添加特殊词汇失败: ' + error.message);
  } finally {
    loading.value = false;
  }
}

async function deleteWord(word) {
  try {
    await ElMessageBox.confirm(
      `确定要删除特殊词汇 "${word.word}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    loading.value = true;
    console.log(`[SpecialWord] 删除词汇: "${word.word}"`);

    // 从内存中的列表删除
    const wordList = specialWords.value[currentService.value][currentLanguage.value];
    const index = wordList.findIndex(w => w.word === word.word);
    if (index !== -1) {
      wordList.splice(index, 1);
      console.log('[SpecialWord] 词汇已从内存中删除');

      // 标记有未保存的更改
      hasChanges.value = true;

      ElMessage.success('删除特殊词汇成功');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('[SpecialWord] 删除词汇失败:', error);
      ElMessage.error('删除特殊词汇失败: ' + error.message);
    }
  } finally {
    loading.value = false;
  }
}

async function saveSpecialWords() {
  try {
    loading.value = true;
    console.log('[SpecialWord] 开始保存特殊词汇...');

    // 将内存中的词汇列表保存到本地存储
    const localWords = {};

    // 遍历内存中的词汇列表，只保存词汇文本
    Object.entries(specialWords.value).forEach(([serviceId, languages]) => {
      localWords[serviceId] = {};
      Object.entries(languages).forEach(([languageCode, words]) => {
        // 过滤掉可能的空值，只保存词汇文本
        const validWords = words
          .filter(w => w && w.word && typeof w.word === 'string')
          .map(w => w.word);

        if (validWords.length > 0) {
          localWords[serviceId][languageCode] = validWords;
        }
      });
    });

    // 完全覆盖本地存储中的数据
    saveLocalSpecialWords(localWords);
    console.log('[SpecialWord] 特殊词汇已保存到本地存储:', localWords);

    // 重置更改标记
    hasChanges.value = false;

    // 关闭对话框并通知父组件
    dialogVisible.value = false;
    emit('saved');

    ElMessage.success('特殊词汇已保存');
  } catch (error) {
    console.error('[SpecialWord] 保存特殊词汇失败:', error);
    ElMessage.error('保存特殊词汇失败: ' + error.message);
  } finally {
    loading.value = false;
  }
}

// 关闭对话框前确认
async function handleClose() {
  if (hasChanges.value) {
    try {
      await ElMessageBox.confirm(
        '您有未保存的更改，确定要关闭吗？',
        '关闭确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );
      dialogVisible.value = false;
    } catch (error) {
      // 用户取消关闭，不做任何操作
      console.log('[SpecialWord] 用户取消关闭对话框');
    }
  } else {
    dialogVisible.value = false;
  }
}

// 监听对话框可见性
watch(dialogVisible, (newVal) => {
  if (newVal) {
    console.log('[SpecialWord] 对话框打开，重新加载数据');
    fetchData();
  }
});

// 组件挂载时初始化
onMounted(() => {
  console.log('[SpecialWord] 组件挂载，初始化数据');
  fetchData();
});
</script>

<style scoped>
.special-word-dialog {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-section {
  display: flex;
  gap: 1rem;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.section-header h3 {
  margin: 0;
}

.word-list {
  max-height: 350px;
  overflow-y: auto;
  padding: 0.5rem;
}

.word-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(6rem, 1fr));
  gap: 1rem;
}

.word-card {
  position: relative;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  padding: 0.5rem;
  min-height: 5rem;
  display: flex;
  flex-direction: column;
  background-color: #f9f9f9;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.word-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.word-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.source-tag {
  font-size: 0.7rem;
}

.delete-btn {
  padding: 0.2rem;
  font-size: 0.7rem;
  height: 1.5rem;
  width: 1.5rem;
}

.word-card-content {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: bold;
  text-align: center;
  word-break: break-all;
}

.add-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: #f0f9ff;
  border: 1px dashed #a0cfff;
}

.add-card:hover {
  background-color: #ecf5ff;
}

.add-icon {
  font-size: 2rem;
  color: #409eff;
  margin-bottom: 0.5rem;
}

.add-text {
  font-size: 0.8rem;
  color: #409eff;
}

.add-word-form {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.help-text {
  font-size: 0.9rem;
  color: #666;
}

.help-text p {
  margin: 0.5rem 0;
}

.help-text .note {
  color: #e6a23c;
  font-weight: bold;
}

.special-word-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}
</style>
