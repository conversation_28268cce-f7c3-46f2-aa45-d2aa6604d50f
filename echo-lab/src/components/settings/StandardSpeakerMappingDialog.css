/* 整体容器样式 */
.speaker-mapping-container {
  height: 100%;
  border-radius: 0.25rem;
  overflow: hidden;
}

/* 侧边栏样式 */
.language-sidebar {
  border-right: 1px solid #ebeef5;
  background-color: #f8f9fa;
  padding: 0;
}

.sidebar-title {
  margin: 0;
  padding: 1rem;
  font-size: 1rem;
  font-weight: 500;
  color: #303133;
  background-color: #f2f6fc;
  border-bottom: 1px solid #ebeef5;
  text-align: center;
}

.language-menu {
  border-right: none;
}

.language-menu :deep(.el-menu-item) {
  height: 2.5rem;
  line-height: 2.5rem;
  font-size: 0.875rem;
}

.language-menu :deep(.el-menu-item.is-active) {
  background-color: #ecf5ff;
  color: #409eff;
  font-weight: 500;
}

/* 主内容区样式 */
.mapping-main {
  padding: 1.25rem;
  background-color: #fff;
}

/* 标题区域样式 */
.mapping-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #ebeef5;
}

.language-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 500;
  color: #303133;
}

.header-buttons {
  display: flex;
  gap: 0.5rem;
}

.add-button, 
.reset-button {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

/* 映射列表样式 */
.mapping-list {
  margin-top: 1.25rem;
}

.mapping-table {
  width: 100%;
  border-radius: 0.25rem;
  overflow: hidden;
}

.mapping-table :deep(th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 0.75rem 0;
  font-size: 0.875rem;
}

.mapping-table :deep(td) {
  padding: 0.75rem 0;
  font-size: 0.875rem;
}

.mapping-table :deep(.el-table__row) {
  transition: background-color 0.2s;
}

.mapping-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

.speaker-tag {
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
}

.speaker-name {
  font-size: 0.875rem;
  color: #303133;
  font-weight: 500;
}

.voice-name-cell {
  font-size: 0.875rem;
  color: #606266;
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.edit-button {
  padding: 0.375rem;
  transition: all 0.2s;
}

.edit-button:hover {
  background-color: #409eff;
  color: #fff;
}

.delete-button {
  padding: 0.375rem;
  transition: all 0.2s;
}

.delete-button:hover {
  background-color: #f56c6c;
  color: #fff;
}

.edit-button :deep(.el-icon),
.delete-button :deep(.el-icon) {
  font-size: 0.875rem;
}

/* 弹框样式 */
.mapping-dialog-form {
  padding: 0 1rem;
}

.mapping-dialog-form :deep(.el-form-item__label) {
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.625rem;
}

/* 声音选项样式 */
.voice-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.voice-name {
  font-weight: 500;
}

.voice-service {
  color: #909399;
  font-size: 0.75rem;
}

.voice-tag {
  margin-left: auto;
  font-size: 0.625rem;
  padding: 0 0.375rem;
  height: 1.25rem;
  line-height: 1.25rem;
}
