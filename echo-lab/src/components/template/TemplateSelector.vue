<template>
  <div class="template-selector">
    <!-- 加载状态 -->
    <div v-if="templateStore.loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <!-- 模板内容 -->
    <div v-else class="template-content">
      <!-- 快速选择 - 系统模板 -->
      <div class="template-section">
        <div class="section-header">
          <h4>快速选择</h4>
          <span class="section-desc">系统预设的播放策略</span>
        </div>

        <div class="template-grid">
          <div v-for="template in templateStore.systemTemplates" :key="template.id" class="template-card" :class="{
            active: selectedTemplate?.id === template.id || (!selectedTemplate && props.currentTemplate?.id === template.id)
          }" @click="selectTemplate(template)">
            <div class="template-header">
              <div class="template-name">{{ template.name }}</div>
            </div>
            <div class="template-desc">{{ template.description }}</div>
            <div class="template-stats">
              <span>{{ template.config.sections.length }}个环节</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 我的模板 -->
      <div v-if="isLoggedIn && templateStore.hasUserTemplates" class="template-section">
        <div class="section-header">
          <h4>我的模板</h4>
          <span class="section-desc">
            <el-link type="primary" @click="goToTemplateManager">前往模板管理页面创建或编辑模板</el-link>
          </span>
        </div>

        <div class="template-list">
          <div v-for="template in templateStore.userTemplates" :key="template.id" class="template-item"
            :class="{ active: selectedTemplate?.id === template.id || (!selectedTemplate && props.currentTemplate?.id === template.id) }">
            <div class="template-info" @click="selectTemplate(template)">
              <div class="template-name">{{ template.name }}</div>
              <div class="template-meta">
                {{ template.config.sections.length }}个环节 ·
                {{ formatDate(template.createdAt) }}
              </div>
              <div v-if="template.description" class="template-desc">{{ template.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 社区模板 -->
      <div v-if="templateStore.hasPublicTemplates" class="template-section">
        <div class="section-header">
          <h4>社区模板</h4>
          <span class="section-desc">其他用户分享的播放策略</span>
        </div>

        <div class="template-list">
          <div v-for="template in templateStore.publicTemplates" :key="template.id" class="template-item"
            :class="{ active: selectedTemplate?.id === template.id || (!selectedTemplate && props.currentTemplate?.id === template.id) }">
            <div class="template-info" @click="selectTemplate(template)">
              <div class="template-name">{{ template.name }}</div>
              <div class="template-meta">
                {{ template.config.sections.length }}个环节 ·
                by {{ template.creator?.username || '匿名用户' }}
              </div>
              <div v-if="template.description" class="template-desc">{{ template.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!isLoggedIn && !templateStore.hasPublicTemplates" class="empty-state">
        <el-empty description="登录后可以创建和管理自己的播放策略模板">
          <el-button type="primary" @click="$emit('login-required')">立即登录</el-button>
        </el-empty>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="selector-footer">
      <div class="selected-info">
        <span v-if="selectedTemplate">
          已选择：{{ selectedTemplate.name }}
        </span>
        <span v-else class="text-muted">
          请选择一个播放策略
        </span>
      </div>

      <div class="footer-actions">
        <el-button @click="$emit('close')">取消</el-button>
        <el-button type="primary" :disabled="!selectedTemplate" @click="applyTemplate">
          应用策略
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useTemplateStore } from '@/stores/templateStore';
import { useUserStore } from '@/stores/userStore';
import { useRouter } from 'vue-router';

// Props
const props = defineProps({
  currentConfig: {
    type: Object,
    default: () => ({})
  },
  currentTemplate: {
    type: Object,
    default: null
  }
});

// Emits
const emit = defineEmits(['select', 'close', 'login-required']);

// Stores
const templateStore = useTemplateStore();
const userStore = useUserStore();
const router = useRouter();

// State
const selectedTemplate = ref(props.currentTemplate);

// Computed
const isLoggedIn = computed(() => userStore.isLoggedIn);

// Methods
const selectTemplate = (template) => {
  selectedTemplate.value = template;
};

const applyTemplate = async () => {
  if (!selectedTemplate.value) return;

  try {
    // 记录使用次数
    await templateStore.useTemplate(selectedTemplate.value.id);

    // 发送选择事件
    emit('select', selectedTemplate.value);

    ElMessage.success(`已应用播放策略：${selectedTemplate.value.name}`);
  } catch (error) {
    console.error('应用模板失败:', error);
  }
};

const goToTemplateManager = () => {
  router.push('/templates');
  emit('close');
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

// Lifecycle
onMounted(() => {
  templateStore.loadTemplates();
});
</script>

<style scoped>
.template-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 70vh;
}

.loading-container {
  padding: 1.5rem;
}

.template-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.template-section {
  margin-bottom: 2rem;
}

.template-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #303133;
}

.section-desc {
  font-size: 0.875rem;
  color: #909399;
}

/* 模板网格布局 */
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.template-card {
  border: 1px solid rgba(228, 231, 237, 0.6);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #fff 0%, #fafbff 100%);
  position: relative;
  overflow: hidden;
}

.template-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.template-card:hover::before {
  transform: scaleX(1);
}

.template-card:hover {
  border-color: #667eea;
  box-shadow: 0 16px 48px rgba(102, 126, 234, 0.2);
  transform: translateY(-8px);
}

.template-card.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #ecf5ff 0%, #e1f3ff 100%);
  box-shadow: 0 16px 48px rgba(102, 126, 234, 0.3);
  transform: translateY(-4px);
}

.template-card.active::before {
  transform: scaleX(1);
}

.template-card.recommended {
  border-color: #67c23a;
  background: linear-gradient(135deg, #f0f9ff 0%, #e1f3d8 100%);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.template-name {
  font-weight: 700;
  font-size: 1.125rem;
  color: #303133;
}

.template-desc {
  font-size: 1rem;
  color: #606266;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.template-stats {
  font-size: 0.875rem;
  color: #909399;
  padding: 0.5rem;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 8px;
}

/* 模板列表布局 */
.template-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.template-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e4e7ed;
  border-radius: 0.5rem;
  padding: 1rem;
  background: #fff;
  transition: all 0.2s ease;
}

.template-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.template-item.active {
  border-color: #409eff;
  background: #f0f9ff;
}

.template-info {
  flex: 1;
  cursor: pointer;
}

.template-meta {
  font-size: 0.75rem;
  color: #909399;
  margin-top: 0.25rem;
}

.template-actions {
  display: flex;
  gap: 0.5rem;
  margin-left: 1rem;
}

/* 底部操作区 */
.selector-footer {
  border-top: 1px solid #e4e7ed;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.selected-info {
  font-size: 0.875rem;
  color: #606266;
}

.text-muted {
  color: #c0c4cc;
}

.footer-actions {
  display: flex;
  gap: 1rem;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 2rem;
}

/* 移动端适配 */
@media (max-width: 48rem) {
  .template-content {
    padding: 1rem;
  }
  
  .template-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .template-card {
    padding: 1rem;
  }
  
  .template-name {
    font-size: 1rem;
  }
  
  .template-desc {
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
  }
  
  .template-stats {
    font-size: 0.8rem;
    padding: 0.375rem;
  }
  
  .template-list {
    gap: 0.5rem;
  }
  
  .template-item {
    padding: 0.75rem;
  }
  
  .template-name {
    font-size: 0.9rem;
  }
  
  .template-meta {
    font-size: 0.7rem;
  }
  
  .template-desc {
    font-size: 0.8rem;
  }
  
  .selector-footer {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .selected-info {
    text-align: center;
    width: 100%;
  }
  
  .footer-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .empty-state {
    padding: 1.5rem 1rem;
  }
}
</style>
