<!--
  会话管理组件
  显示用户的活跃会话，并允许管理会话
-->
<template>
  <div class="session-manager">
    <div class="session-header">
      <h3>设备登录管理</h3>
      <div class="session-actions">
        <el-button type="primary" size="small" @click="refreshSessions" :loading="loading" :disabled="loading">
          <el-icon>
            <i-ep-refresh />
          </el-icon>
          刷新
        </el-button>
        <el-button type="danger" size="small" @click="confirmDeleteAllOtherSessions"
          :disabled="loading || !hasOtherSessions">
          <el-icon>
            <i-ep-delete />
          </el-icon>
          退出其他设备
        </el-button>
      </div>
    </div>

    <div class="session-info">
      <el-alert type="info" :closable="false" show-icon>
        <template #title>
          您最多可以在 {{ maxSessions }} 个设备上同时登录。超过限制时，最早的登录会被自动退出。
        </template>
      </el-alert>
    </div>

    <div v-if="loading" class="session-loading">
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="error" class="session-error">
      <el-alert type="error" :closable="false" show-icon :title="error" />
    </div>

    <div v-else-if="sessions.length === 0" class="session-empty">
      <el-empty description="没有活跃的会话" />
    </div>

    <div v-else class="session-list">
      <el-card v-for="session in sessions" :key="session.id" class="session-card"
        :class="{ 'current-session': session.isCurrentSession }">
        <div class="session-card-header">
          <div class="session-device">
            <el-icon v-if="session.deviceInfo.device.includes('Mobile')">
              <i-ep-cellphone />
            </el-icon>
            <el-icon v-else-if="session.deviceInfo.device.includes('Tablet')">
              <i-ep-cellphone />
            </el-icon>
            <el-icon v-else>
              <i-ep-monitor />
            </el-icon>
            <span>{{ session.deviceInfo.device || '未知设备' }}</span>
          </div>
          <div class="session-badge" v-if="session.isCurrentSession">
            <el-tag size="small" type="success">当前设备</el-tag>
          </div>
        </div>

        <div class="session-details">
          <div class="session-detail-item">
            <span class="detail-label">浏览器:</span>
            <span class="detail-value">{{ session.deviceInfo.browser || '未知' }}</span>
          </div>
          <div class="session-detail-item">
            <span class="detail-label">操作系统:</span>
            <span class="detail-value">{{ session.deviceInfo.os || '未知' }}</span>
          </div>
          <div class="session-detail-item">
            <span class="detail-label">IP地址:</span>
            <span class="detail-value">{{ session.ipAddress || '未知' }}</span>
          </div>
          <div class="session-detail-item">
            <span class="detail-label">最后活跃:</span>
            <span class="detail-value">{{ formatDate(session.lastActive) }}</span>
          </div>
          <div class="session-detail-item">
            <span class="detail-label">登录时间:</span>
            <span class="detail-value">{{ formatDate(session.createdAt) }}</span>
          </div>
        </div>

        <div class="session-actions">
          <el-button type="danger" size="small" @click="confirmDeleteSession(session)" :disabled="loading">
            {{ session.isCurrentSession ? '退出登录' : '移除' }}
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { getUserSessions, deleteSession, deleteAllOtherSessions } from '@/services/sessionService';

// 状态
const sessions = ref([]);
const loading = ref(false);
const error = ref(null);
const maxSessions = ref(2); // 默认值

// 计算属性
const hasOtherSessions = computed(() => {
  return sessions.value.filter(session => !session.isCurrentSession).length > 0;
});

// 加载会话列表
async function loadSessions() {
  loading.value = true;
  error.value = null;

  try {
    const result = await getUserSessions();
    sessions.value = result.sessions;
    maxSessions.value = result.maxSessions;
  } catch (err) {
    console.error('加载会话失败:', err);
    error.value = err.message || '加载会话失败';
  } finally {
    loading.value = false;
  }
}

// 刷新会话列表
function refreshSessions() {
  loadSessions();
}

// 确认删除会话
function confirmDeleteSession(session) {
  const message = session.isCurrentSession
    ? '确定要退出当前设备的登录吗？'
    : `确定要移除在 ${session.deviceInfo.device || '未知设备'} 上的登录吗？`;

  ElMessageBox.confirm(
    message,
    session.isCurrentSession ? '退出登录' : '移除会话',
    {
      confirmButtonText: session.isCurrentSession ? '退出' : '移除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    handleDeleteSession(session);
  }).catch(() => {
    // 用户取消操作
  });
}

// 处理删除会话
async function handleDeleteSession(session) {
  loading.value = true;

  try {
    await deleteSession(session.tokenId);

    // 如果不是当前会话，从列表中移除
    if (!session.isCurrentSession) {
      sessions.value = sessions.value.filter(s => s.id !== session.id);
    }
    // 如果是当前会话，页面会重定向到登录页，不需要额外处理
  } catch (err) {
    // 错误已在服务中处理
  } finally {
    loading.value = false;
  }
}

// 确认删除所有其他会话
function confirmDeleteAllOtherSessions() {
  ElMessageBox.confirm(
    '确定要退出所有其他设备的登录吗？',
    '退出其他设备',
    {
      confirmButtonText: '退出',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    handleDeleteAllOtherSessions();
  }).catch(() => {
    // 用户取消操作
  });
}

// 处理删除所有其他会话
async function handleDeleteAllOtherSessions() {
  loading.value = true;

  try {
    await deleteAllOtherSessions();
    // 重新加载会话列表
    await loadSessions();
  } catch (err) {
    // 错误已在服务中处理
  } finally {
    loading.value = false;
  }
}

// 格式化日期
function formatDate(dateStr) {
  if (!dateStr) return '未知';

  const date = new Date(dateStr);
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  // 如果是今天
  if (diffDay === 0) {
    if (diffHour === 0) {
      if (diffMin === 0) {
        return '刚刚';
      }
      return `${diffMin}分钟前`;
    }
    return `${diffHour}小时前`;
  }

  // 如果是昨天
  if (diffDay === 1) {
    return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  }

  // 如果是今年
  if (date.getFullYear() === now.getFullYear()) {
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }) +
      ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  }

  // 其他情况
  return date.toLocaleDateString('zh-CN') +
    ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
}

// 组件挂载时加载会话列表
onMounted(() => {
  loadSessions();
});
</script>

<style scoped>
.session-manager {
  width: 100%;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.session-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: #303133;
}

.session-actions {
  display: flex;
  gap: 0.5rem;
}

.session-info {
  margin-bottom: 1.5rem;
}

.session-loading,
.session-error,
.session-empty {
  margin: 2rem 0;
}

.session-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.session-card {
  position: relative;
  border-left: 3px solid transparent;
}

.session-card.current-session {
  border-left-color: #67c23a;
}

.session-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.session-device {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #303133;
}

.session-details {
  margin-bottom: 1rem;
}

.session-detail-item {
  display: flex;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.detail-label {
  width: 5rem;
  color: #909399;
}

.detail-value {
  color: #606266;
}

.session-actions {
  display: flex;
  justify-content: flex-end;
}
</style>
