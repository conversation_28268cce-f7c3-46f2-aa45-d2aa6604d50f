/**
 * 未保存更改检测 Composable
 * 用于检测编辑器中的未保存更改并在页面关闭时提醒用户
 */

import { ref, computed, onMounted, onUnmounted, readonly } from 'vue';
import { ElMessageBox } from 'element-plus';

export function useUnsavedChanges() {
  // 是否有未保存的更改
  const hasUnsavedChanges = ref(false);

  // 是否启用页面关闭提醒
  const enableBeforeUnloadWarning = ref(true);

  // 计算是否有未保存的更改
  const isDirty = computed(() => {
    return hasUnsavedChanges.value;
  });

  // 标记内容已修改
  const markAsModified = () => {
    hasUnsavedChanges.value = true;
  };

  // 标记内容已保存
  const markAsSaved = () => {
    hasUnsavedChanges.value = false;
  };

  // 重置状态
  const reset = () => {
    hasUnsavedChanges.value = false;
  };
  
  // 页面关闭前的处理函数
  const handleBeforeUnload = (event) => {
    if (!enableBeforeUnloadWarning.value || !isDirty.value) {
      return;
    }
    
    // 标准的页面关闭提醒
    const message = '您有未保存的更改，确定要离开吗？';
    event.preventDefault();
    event.returnValue = message;
    return message;
  };
  
  // 路由离开前的确认对话框
  const confirmLeave = async (message = '您有未保存的更改，确定要离开吗？') => {
    if (!isDirty.value) {
      return true;
    }
    
    try {
      await ElMessageBox.confirm(
        message,
        '未保存的更改',
        {
          confirmButtonText: '离开',
          cancelButtonText: '取消',
          type: 'warning',
          distinguishCancelAndClose: true,
        }
      );
      return true;
    } catch (error) {
      return false;
    }
  };
  
  // 启用/禁用页面关闭提醒
  const setBeforeUnloadWarning = (enabled) => {
    enableBeforeUnloadWarning.value = enabled;
  };
  
  // 组件挂载时添加事件监听
  onMounted(() => {
    window.addEventListener('beforeunload', handleBeforeUnload);
  });
  
  // 组件卸载时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('beforeunload', handleBeforeUnload);
  });
  
  return {
    // 状态
    hasUnsavedChanges: readonly(hasUnsavedChanges),
    isDirty: readonly(isDirty),

    // 方法
    markAsModified,
    markAsSaved,
    reset,
    confirmLeave,
    setBeforeUnloadWarning,
  };
}
