/**
 * API配置
 * 定义所有API端点和相关配置
 */

// API基础URL
export const API_BASE_URL = "";

// API端点配置
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    BASE: "/api/auth",
    SEND_CODE: "/api/auth/send-code",
    VERIFY_CODE: "/api/auth/verify-code",
    CURRENT_USER: "/api/auth/me",
    UPDATE_USER: "/api/auth/me",
    LOGOUT: "/api/auth/logout",
  },

  // 内容管理
  CONTENTS: {
    BASE: "/api/contents",
    PUBLIC: "/api/public/contents",
  },

  // 合集管理
  COLLECTIONS: {
    BASE: "/api/collections",
    PUBLIC: "/api/collections/public",
    FAVORITES: "/api/collections/favorites",
    ITEMS: (id) => `/api/collections/${id}/items`,
    ITEM_ORDER: (id) => `/api/collections/${id}/items/order`,
    PUBLISH: (id) => `/api/collections/${id}/publish`,
    UNPUBLISH: (id) => `/api/collections/${id}/unpublish`,
    FAVORITE: (id) => `/api/collections/${id}/favorite`,
    BY_ID: (id) => `/api/collections/${id}`,
  },

  // 用户空间
  USERS: {
    BASE: "/api/users",
    BY_ID: (id) => `/api/users/${id}`,
    CONTENTS: (id) => `/api/users/${id}/contents`,
    COLLECTIONS: (id) => `/api/users/${id}/collections`,
    STATS: (id) => `/api/users/${id}/stats`,
  },

  // 文本处理
  TEXT_PROCESSING: {
    ANNOTATE: "/api/annotate",
    TRANSLATE: "/api/translate",
  },

  // 音频服务
  AUDIO: {
    TTS_BATCH: "/api/tts/batch",
    TTS_INFO: "/api/tts/info",
  },

  // 特殊词汇
  SPECIAL_WORDS: {
    BASE: "/api/special-words",
  },

  // 图片服务
  IMAGES: {
    BASE: "/api/images",
    UPLOAD: "/api/images/upload",
  },

  // 会话管理
  SESSIONS: {
    BASE: "/api/sessions",
  },

  // 功能权限
  PERMISSIONS: {
    FEATURES: "/api/feature-permissions/features",
    CHECK: "/api/feature-permissions/check",
    ME: "/api/permissions/me",
    USAGE: "/api/permissions/usage",
    ADMIN_BASE: "/api/admin/feature-permissions",
  },

  // 用户等级
  USER_LEVELS: {
    BASE: "/api/user-levels",
    ME: "/api/user-levels/me",
    USERS: "/api/user-levels/users",
    SUBSCRIPTIONS: "/api/user-levels/subscriptions",
  },

  // 用户反馈
  FEEDBACK: {
    BASE: "/api/feedback",
    USER: "/api/feedback/user",
    ADMIN: "/api/feedback/admin",
  },

  // 播放策略模板
  TEMPLATES: {
    BASE: "/api/templates",
    USE: (id) => `/api/templates/${id}/use`,
    DUPLICATE: (id) => `/api/templates/${id}/duplicate`,
  },

  // 收藏管理
  FAVORITES: {
    BASE: "/api/favorites",
    CHECK: (id) => `/api/favorites/check/${id}`,
  },

  // 管理员功能
  ADMIN: {
    BASE: "/api/admin",
    USERS: "/api/admin/users",
    STATISTICS: "/api/admin/statistics",
    LEVELS: "/api/admin/user-levels",
    SUBSCRIPTIONS: "/api/admin/subscriptions",
    FILTERS: "/api/admin/filters",
  },

  // 统一过滤器系统
  FILTERS: {
    BASE: "/api/filters",
    TYPES: "/api/filters/types",
    LANGUAGE_LEVELS: "/api/filters/language-levels",
    CONTENT_TYPES: "/api/filters/content-types",
    TOPICS: "/api/filters/topics",
    MATERIALS: "/api/filters/materials",
    CONTENT_FILTERS: (contentId) => `/api/filters/content/${contentId}`,
  },
};

export default {
  API_BASE_URL,
  API_ENDPOINTS,
};
