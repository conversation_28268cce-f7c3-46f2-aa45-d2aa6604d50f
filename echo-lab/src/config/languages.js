/**
 * 语言配置
 * 统一管理系统中使用的语言定义
 */

// 支持的语言列表
export const SUPPORTED_LANGUAGES = [
  {
    value: 'ja',
    label: '日语',
    description: '学习日语，探索日本文化和动漫世界',
    status: 'available', // 可用
    statusText: '内容丰富'
  },
  {
    value: 'en',
    label: '英语',
    description: '提升英语水平，连接全球机会',
    status: 'available', // 英语现已可用
    statusText: '内容丰富'
  },
  {
    value: 'zh-CN',
    label: '中文简体',
    description: '学习中文简体，了解中华文化',
    status: 'hidden', // 暂时隐藏，等有数据时改回 planned
    statusText: '敬请期待'
  },
  {
    value: 'zh-TW',
    label: '中文繁体',
    description: '學習中文繁體，體驗中華文化',
    status: 'hidden', // 暂时隐藏，等有数据时改回 planned
    statusText: '敬请期待'
  }
];

/**
 * 获取语言标签
 * @param {string} langCode 语言代码
 * @returns {string} 语言标签
 */
export function getLanguageLabel(langCode) {
  const lang = SUPPORTED_LANGUAGES.find(l => l.value === langCode);
  return lang ? lang.label : langCode;
}

/**
 * 获取语言描述
 * @param {string} langCode 语言代码
 * @returns {string} 语言描述
 */
export function getLanguageDescription(langCode) {
  const lang = SUPPORTED_LANGUAGES.find(l => l.value === langCode);
  return lang ? lang.description : '开始你的语言学习之旅';
}

/**
 * 获取语言标签类型（用于UI显示）
 * @param {string} langCode 语言代码
 * @returns {string} 标签类型
 */
export function getLanguageTagType(langCode) {
  switch (langCode) {
    case 'zh-CN':
    case 'zh-TW':
      return 'success';
    case 'en':
      return 'info';
    case 'ja':
      return 'warning';
    default:
      return 'info';
  }
}

export default {
  SUPPORTED_LANGUAGES,
  getLanguageLabel,
  getLanguageDescription,
  getLanguageTagType
};
