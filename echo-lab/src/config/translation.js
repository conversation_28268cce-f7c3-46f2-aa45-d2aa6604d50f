/**
 * 翻译服务配置
 * 统一管理翻译相关的配置参数
 */

// 翻译超时配置
export const TRANSLATION_TIMEOUT = {
  // 前端翻译请求超时时间（毫秒）
  // 设置为11分钟，比后端DeepSeek API的10分钟稍长，确保后端有足够时间处理
  FRONTEND_REQUEST: 11 * 60 * 1000, // 660000ms = 11分钟
  
  // 后端DeepSeek API超时时间（毫秒）
  // 这个值在后端配置，这里仅作为参考
  BACKEND_DEEPSEEK_API: 10 * 60 * 1000, // 600000ms = 10分钟
  
  // 用户友好的超时提示时间（毫秒）
  // 在这个时间后显示"翻译可能需要较长时间"的提示
  USER_HINT_TIME: 30 * 1000, // 30秒
};

// 翻译状态配置
export const TRANSLATION_STATUS = {
  IDLE: 'idle',
  TRANSLATING: 'translating',
  SUCCESS: 'success',
  ERROR: 'error',
  TIMEOUT: 'timeout',
};

// 翻译错误消息
export const TRANSLATION_ERROR_MESSAGES = {
  TIMEOUT: '翻译请求超时，请稍后重试或分批处理',
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  SERVER_ERROR: '翻译服务暂时不可用，请稍后重试',
  INVALID_PARAMS: '翻译参数错误，请检查输入内容',
  QUOTA_EXCEEDED: '翻译配额已用完，请稍后重试',
  UNKNOWN_ERROR: '翻译失败，请稍后重试',
};

// 翻译提示消息
export const TRANSLATION_HINT_MESSAGES = {
  LONG_PROCESSING: '翻译正在处理中，大批量内容可能需要较长时间，请耐心等待...',
  BATCH_PROCESSING: '正在批量翻译，请稍候...',
  SINGLE_PROCESSING: '正在翻译，请稍候...',
  RETRY_SUGGESTION: '如果翻译时间过长，建议分批处理或稍后重试',
};

// 翻译性能配置
export const TRANSLATION_PERFORMANCE = {
  // 建议的单次翻译文本数量上限
  MAX_TEXTS_PER_REQUEST: 50,
  
  // 建议的单个文本长度上限（字符数）
  MAX_TEXT_LENGTH: 5000,
  
  // 建议的目标语言数量上限
  MAX_TARGET_LANGUAGES: 5,
  
  // 批量翻译时的分批大小
  BATCH_SIZE: 20,
};

// 导出默认配置
export default {
  TRANSLATION_TIMEOUT,
  TRANSLATION_STATUS,
  TRANSLATION_ERROR_MESSAGES,
  TRANSLATION_HINT_MESSAGES,
  TRANSLATION_PERFORMANCE,
};
