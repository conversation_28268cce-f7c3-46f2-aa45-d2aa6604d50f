/**
 * 视频服务配置
 * 统一管理视频导出相关的配置参数
 */

// 视频超时配置
export const VIDEO_TIMEOUT = {
  // 视频上传超时时间（毫秒）
  // 设置为15分钟，适合大文件上传
  UPLOAD: 15 * 60 * 1000, // 900000ms = 15分钟
  
  // 视频处理等待超时时间（毫秒）
  // 设置为30分钟，确保复杂视频有足够处理时间
  PROCESSING: 30 * 60 * 1000, // 1800000ms = 30分钟
  
  // 视频下载超时时间（毫秒）
  // 设置为15分钟，适合大视频文件下载
  DOWNLOAD: 15 * 60 * 1000, // 900000ms = 15分钟
  
  // 进度查询超时时间（毫秒）
  // 设置为30秒，用于轮询进度
  PROGRESS_QUERY: 30 * 1000, // 30000ms = 30秒
  
  // 取消任务超时时间（毫秒）
  // 设置为10秒，快速响应取消请求
  CANCEL_JOB: 10 * 1000, // 10000ms = 10秒
  
  // 用户提示时间（毫秒）
  // 在这个时间后显示"处理可能需要较长时间"的提示
  USER_HINT_TIME: 60 * 1000, // 60秒
};

// 视频处理状态配置
export const VIDEO_STATUS = {
  IDLE: 'idle',
  UPLOADING: 'uploading',
  PROCESSING: 'processing',
  DOWNLOADING: 'downloading',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  TIMEOUT: 'timeout',
};

// 视频处理阶段配置
export const VIDEO_PHASES = {
  GENERATING_FRAMES: 'generating-frames',
  PREPARING_UPLOAD: 'preparing-upload',
  COMPRESSING: 'compressing',
  UPLOADING: 'uploading',
  PROCESSING_VIDEO: 'processing-video',
  DOWNLOADING: 'downloading',
  COMPLETE: 'complete',
  CANCELLED: 'cancelled',
};

// 视频错误消息
export const VIDEO_ERROR_MESSAGES = {
  UPLOAD_TIMEOUT: '视频上传超时，请检查网络连接或稍后重试',
  PROCESSING_TIMEOUT: '视频处理超时，可能是视频内容较复杂，请稍后重试',
  DOWNLOAD_TIMEOUT: '视频下载超时，请检查网络连接或稍后重试',
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  SERVER_ERROR: '服务器错误，请稍后重试',
  FILE_TOO_LARGE: '文件过大，请减少视频内容或降低质量',
  INVALID_FORMAT: '文件格式不支持，请检查输入内容',
  QUOTA_EXCEEDED: '导出配额已用完，请稍后重试',
  PERMISSION_DENIED: '权限不足，无法执行此操作',
  UNKNOWN_ERROR: '未知错误，请稍后重试',
};

// 视频提示消息
export const VIDEO_HINT_MESSAGES = {
  UPLOAD_PROGRESS: '正在上传视频数据，请稍候...',
  PROCESSING_PROGRESS: '正在处理视频，复杂内容可能需要较长时间，请耐心等待...',
  DOWNLOAD_PROGRESS: '正在下载生成的视频，请稍候...',
  LONG_PROCESSING: '视频处理时间较长，系统正在努力为您生成高质量视频...',
  RETRY_SUGGESTION: '如果处理时间过长，建议稍后重试或联系客服',
  CANCEL_WARNING: '取消操作将丢失当前进度，确定要取消吗？',
};

// 视频性能配置
export const VIDEO_PERFORMANCE = {
  // 文件大小限制（字节）
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
  
  // 建议的最大帧数
  MAX_FRAME_COUNT: 3000, // 约100秒@30fps
  
  // 建议的最大音频时长（秒）
  MAX_AUDIO_DURATION: 300, // 5分钟
  
  // 轮询间隔配置
  POLL_INTERVALS: {
    BASE: 5000, // 基础轮询间隔5秒
    MAX: 20000, // 最大轮询间隔20秒
    STEP: 2000, // 每次增加2秒
  },
  
  // 进度阶段分配（总和为100）
  PHASE_ALLOCATION: {
    'generating-frames': 30,    // 生成帧占30%
    'preparing-upload': 5,      // 准备上传占5%
    'compressing': 5,           // 压缩文件占5%
    'uploading': 10,            // 上传文件占10%
    'processing-video': 40,     // 服务器处理视频占40%
    'downloading': 10           // 下载文件占10%
  },
  
  // 质量配置
  QUALITY_PRESETS: {
    low: { width: 854, height: 480, bitrate: '1000k' },
    medium: { width: 1280, height: 720, bitrate: '2500k' },
    high: { width: 1920, height: 1080, bitrate: '5000k' },
  },
};

// 视频格式配置
export const VIDEO_FORMATS = {
  SUPPORTED_INPUT: ['.zip'], // 支持的输入格式
  SUPPORTED_OUTPUT: ['.mp4'], // 支持的输出格式
  DEFAULT_OUTPUT: 'mp4', // 默认输出格式
  
  // 编码配置
  ENCODING: {
    VIDEO_CODEC: 'libx264',
    AUDIO_CODEC: 'aac',
    FRAME_RATE: 30,
    PIXEL_FORMAT: 'yuv420p',
  },
};

// 导出默认配置
export default {
  VIDEO_TIMEOUT,
  VIDEO_STATUS,
  VIDEO_PHASES,
  VIDEO_ERROR_MESSAGES,
  VIDEO_HINT_MESSAGES,
  VIDEO_PERFORMANCE,
  VIDEO_FORMATS,
};
