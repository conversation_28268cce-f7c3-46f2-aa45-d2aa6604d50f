/**
 * 核心模块入口
 * 导出所有核心模块
 */

import nodeFactory from './factories/NodeFactory';
import { registerNodeProcessors } from './processors';
import { useNodeStore } from './stores/nodeStore';
import * as nodeUtils from './utils/nodeUtils';

// 注册节点处理器
registerNodeProcessors(nodeFactory);

// 导出核心模块
export {
  nodeFactory,
  useNodeStore,
  nodeUtils
};

export default {
  nodeFactory,
  useNodeStore,
  nodeUtils
};
