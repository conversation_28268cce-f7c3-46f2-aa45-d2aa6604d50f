import { defineStore } from "pinia";
import contentService from "@/services/contentService";

export const useContentStore = defineStore("content", {
  state: () => ({
    loading: false,
    error: null,
    contents: [],
    pagination: {
      page: 1,
      pageSize: 20,
      total: 0,
    },
    filters: {
      search: "",
      status: "all",
      learningLanguage: "all",
      sortBy: "updatedAt",
      sortOrder: "desc",
    },
  }),

  getters: {
    // 由于改用后端分页，直接返回contents
    filteredContents(state) {
      return state.contents;
    },
  },

  actions: {
    // 获取内容列表
    async fetchContents(resetPage = false) {
      this.loading = true;
      try {
        if (resetPage) {
          this.pagination.page = 1;
        }

        // 构建查询参数
        const params = {
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          search: this.filters.search,
          status: this.filters.status,
          sortBy: this.filters.sortBy,
          sortOrder: this.filters.sortOrder,

        };

        const response = await contentService.getContentWithPagination(params);
        if (response && response.success) {
          this.contents = response.data || [];
          this.pagination = { ...this.pagination, ...response.pagination };
        } else {
          throw new Error(response?.error || "获取内容列表失败");
        }
      } catch (err) {
        console.error("获取内容列表失败:", err);
        this.error = err.message;
      } finally {
        this.loading = false;
      }
    },

    // 删除内容
    async deleteContent(id) {
      try {
        const response = await contentService.deleteContent(id);
        if (response && response.success) {
          this.contents = this.contents.filter((item) => item.id !== id);
        } else {
          throw new Error(response?.error || "删除失败");
        }
      } catch (err) {
        console.error("删除内容失败:", err);
        throw err;
      }
    },

    // 发布内容（上线）
    async publishContent(id) {
      try {
        const response = await contentService.publishContent(id);
        if (response && response.success) {
          // 更新内容状态
          const index = this.contents.findIndex((item) => item.id === id);
          if (index !== -1) {
            this.contents[index].status = "published";
          }
          return response.content;
        } else {
          throw new Error(response?.error || "发布失败");
        }
      } catch (err) {
        console.error("发布内容失败:", err);
        throw err;
      }
    },

    // 下架内容
    async unpublishContent(id) {
      try {
        const response = await contentService.unpublishContent(id);
        if (response && response.success) {
          // 更新内容状态
          const index = this.contents.findIndex((item) => item.id === id);
          if (index !== -1) {
            this.contents[index].status = "draft";
          }
          return response.content;
        } else {
          throw new Error(response?.error || "下架失败");
        }
      } catch (err) {
        console.error("下架内容失败:", err);
        throw err;
      }
    },

    // 更新过滤器
    updateFilters(filters) {
      this.filters = { ...this.filters, ...filters };
      this.fetchContents(true); // 重置到第一页并重新获取数据
    },

    // 更新分页
    updatePagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
      this.fetchContents(); // 重新获取数据
    },
  },
});
