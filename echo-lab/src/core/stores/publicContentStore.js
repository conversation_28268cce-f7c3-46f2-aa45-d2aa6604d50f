import { defineStore } from "pinia";
import publicContentService from "@/services/publicContentService";

/**
 * 公开内容存储
 * 管理公开内容的状态
 */
export const usePublicContentStore = defineStore("publicContent", {
  state: () => ({
    loading: false,
    error: null,
    contents: [],
    pagination: {
      total: 0,
      page: 1,
      pageSize: 20, // 标准分页大小
    },

  }),

  getters: {
    /**
     * 获取过滤后的内容
     */
    filteredContents(state) {
      let result = [...state.contents];

      // 搜索过滤
      if (state.filters.search) {
        const searchQuery = state.filters.search.toLowerCase();
        result = result.filter(
          (item) =>
            item.name.toLowerCase().includes(searchQuery) ||
            (item.description &&
              item.description.toLowerCase().includes(searchQuery))
        );
      }



      // 排序
      result.sort((a, b) => {
        const key = state.filters.sortBy;
        const orderFactor = state.filters.sortOrder === "desc" ? -1 : 1;
        return orderFactor * (a[key] < b[key] ? -1 : 1);
      });

      // 更新总数
      this.pagination.total = result.length;

      return result;
    },
  },

  actions: {
    /**
     * 获取公开内容列表
     */
    async fetchPublicContents(resetPage = true, filters = null) {
      this.loading = true;
      try {
        if (resetPage) {
          this.pagination.page = 1;
        }

        const params = {
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          sortBy: "createdAt",
          sortOrder: "DESC",
        };

        // 添加过滤参数
        if (filters) {
          // 添加搜索关键词
          if (filters.search && filters.search.trim()) {
            params.search = filters.search.trim();
          }

          // 使用按类型分组的过滤器
          if (filters.filtersByType && typeof filters.filtersByType === 'object') {
            const allFilterIds = [];
            Object.values(filters.filtersByType).forEach(typeFilters => {
              if (Array.isArray(typeFilters)) {
                allFilterIds.push(...typeFilters);
              }
            });
            if (allFilterIds.length > 0) {
              params.filters = allFilterIds.join(',');
            }
          }
          // 如果没有按类型分组的过滤器，使用过滤器ID数组
          else if (filters.filterIds && Array.isArray(filters.filterIds)) {
            params.filters = filters.filterIds.join(',');
          }
        }



        const response = await publicContentService.getPublicContents(params);

        if (response && response.success) {
          if (resetPage) {
            this.contents = response.contents || [];
          } else {
            this.contents = [...this.contents, ...(response.contents || [])];
          }
          
          if (response.pagination) {
            this.pagination = {
              ...this.pagination,
              ...response.pagination,
            };
          }
        } else {
          throw new Error(response?.error || "获取公开内容列表失败");
        }
      } catch (err) {
        console.error("获取公开内容列表失败:", err);
        this.error = err.message;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 加载更多内容
     */
    async loadMoreContents(filters = null) {
      if (this.loading || this.contents.length >= this.pagination.total) {
        return;
      }

      this.pagination.page += 1;
      await this.fetchPublicContents(false, filters); // 不重置页面，追加内容
    },

    /**
     * 更新分页
     */
    updatePagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
      this.fetchPublicContents();
    },


  },
});
