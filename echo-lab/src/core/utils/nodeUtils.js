/**
 * 节点工具函数
 * 提供节点相关的工具函数
 */

/**
 * 获取节点显示名称
 * @param {Object} node - 节点对象
 * @param {Object} nodeTypes - 节点类型配置
 * @returns {string} 节点显示名称
 */
export function getNodeDisplayName(node, nodeTypes) {
  if (!node) return "";

  // 如果有自定义名称，优先使用
  if (node.customName) {
    return node.customName;
  }

  // 获取节点类型标签
  const typeLabel = nodeTypes[node.type]?.label || node.type;

  // 使用节点编号
  const nodeNumber = node.number || 1;

  return `${typeLabel} #${nodeNumber}`;
}

/**
 * 获取节点类型颜色
 * @param {Object} node - 节点对象
 * @param {Object} nodeTypes - 节点类型配置
 * @returns {string} 节点类型颜色
 */
export function getNodeTypeColor(node, nodeTypes) {
  if (!node) return "#909399";

  return nodeTypes[node.type]?.color || "#909399";
}

/**
 * 检查节点是否有错误
 * @param {Object} node - 节点对象
 * @param {Array} sourceResults - 源节点结果数组
 * @returns {boolean} 是否有错误
 */
export function hasNodeError(node, sourceResults) {
  if (!node) return false;

  // 检查源节点结果是否有错误
  if (sourceResults.some((result) => result === null || result === undefined)) {
    return true;
  }

  // 检查节点参数是否有效
  switch (node.type) {
    case "text":
      return !node.params.text || node.params.text.trim() === "";
    case "textSequence":
      return sourceResults.length === 0;
    case "resource":
      return sourceResults.length === 0;
    case "videoConfig":
      return sourceResults.length === 0;
    default:
      return false;
  }
}

/**
 * 获取节点错误消息
 * @param {Object} node - 节点对象
 * @param {Array} sourceResults - 源节点结果数组
 * @returns {string} 错误消息
 */
export function getNodeErrorMessage(node, sourceResults) {
  if (!node) return "";

  // 检查源节点结果是否有错误
  if (sourceResults.some((result) => result === null || result === undefined)) {
    return "源节点处理失败";
  }

  // 检查节点参数是否有效
  switch (node.type) {
    case "text":
      return !node.params.text || node.params.text.trim() === ""
        ? "请输入文本内容"
        : "";
    case "textSequence":
      return sourceResults.length === 0 ? "请连接文本内容节点" : "";
    case "resource":
      return sourceResults.length === 0 ? "请连接文本内容节点" : "";
    case "videoConfig":
      return sourceResults.length === 0 ? "请连接文本序列节点" : "";
    default:
      return "";
  }
}
