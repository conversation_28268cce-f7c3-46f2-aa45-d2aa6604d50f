// 确保Vue核心首先加载
import { createApp, h } from "vue";

// Element Plus 中文语言包
import ElementPlus from "element-plus";
import zhCn from "element-plus/dist/locale/zh-cn.mjs";

// 按需引入Element Plus样式
import "@/styles/element-plus.scss";
import "@/assets/styles/element-overrides.css";

// 其他核心导入
import { createPinia } from "pinia";
import App from "./App.vue";
import router from "./router";
// 节点组件改为按需加载，不在全局注册
// import { registerNodeComponents } from "@/components/nodes";
import "@/core";
import { useFavoriteStore } from "@/stores/favoriteStore";
import { useUserStore } from "@/stores/userStore";
import { registerSW } from "virtual:pwa-register";
import { hideInitialLoading } from "./utils/hideLoading";
import { setUserId } from "./utils/analytics";

// 创建应用实例
const app = createApp(App);

// 创建 Pinia 实例
const pinia = createPinia();

// 使用插件（确保顺序正确）
app.use(pinia);
app.use(router);
app.use(ElementPlus, { locale: zhCn });

// 初始化用户状态和收藏状态
const initUserAndFavorites = async () => {
  const userStore = useUserStore();
  const favoriteStore = useFavoriteStore();

  // 如果本地存储中有登录状态，从服务器获取最新的用户信息
  if (userStore.isLoggedIn) {
    // 获取最新用户信息
    await userStore.fetchCurrentUser();

    // 同步用户ID到GrowingIO
    if (userStore.user) {
      setUserId(userStore.user.id, {
        email: userStore.user.email,
      });
    }

    // 获取用户收藏
    await favoriteStore.fetchFavorites();
  }
};

// 同步挂载应用，避免时序问题
app.mount("#app");

// 异步初始化用户状态
async function initializeUserData() {
  try {
    // 初始化用户状态和收藏状态
    await initUserAndFavorites();
  } catch (error) {
    console.error("应用初始化失败:", error);
  } finally {
    // 无论成功还是失败，都隐藏loading
    hideInitialLoading();
  }
}

// 启动用户数据初始化
initializeUserData();

// 注册 Service Worker
registerSW({
  onNeedRefresh() {
    ElMessageBox.confirm("发现新版本，是否立即更新？", "更新提示", {
      confirmButtonText: "立即更新",
      cancelButtonText: "稍后再说",
      type: "info",
    })
      .then(() => {
        window.location.reload();
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "您可以稍后刷新页面以获取更新",
        });
      });
  },
  onOfflineReady() {
    ElMessage({
      message: "应用已准备好离线使用",
      type: "success",
      duration: 2000,
    });
  },
  onRegistered(registration) {
    console.log("Service Worker 注册成功:", registration);
  },
  onRegisterError(error) {
    console.error("Service Worker 注册失败:", error);
  },
});
