/**
 * 管理后台路由配置
 * 所有管理后台相关的路由
 */
import { useUserStore } from "@/stores/userStore";

// 管理后台布局组件
const AdminLayout = () => import("@/components/admin/AdminLayout.vue");

// 管理后台页面
const AdminDashboard = () => import("@/views/admin/AdminDashboard.vue");
const UserManagement = () => import("@/views/admin/UserManagement.vue");
const UserDetail = () => import("@/views/admin/UserDetail.vue");
const FeaturePermissions = () => import("@/views/admin/FeaturePermissions.vue");
const SpecialWordManagement = () =>
  import("@/views/admin/SpecialWordManagement.vue");

const FeedbackManagement = () => import("@/views/admin/FeedbackManagement.vue");
const SecurityManagement = () => import("@/views/admin/SecurityManagement.vue");
const UserLevelManagement = () =>
  import("@/views/admin/UserLevelManagement.vue");
const UserSubscriptionManagement = () =>
  import("@/views/admin/UserSubscriptionManagement.vue");
const UsageStatistics = () => import("@/views/admin/UsageStatistics.vue");
const SEOManagement = () => import("@/views/admin/SEOManagement.vue");

const FilterManagement = () => import("@/views/admin/FilterManagement.vue");
const DimensionManagement = () =>
  import("@/components/admin/DimensionManagement.vue");
const AdminContentManagement = () =>
  import("@/views/admin/AdminContentManagement.vue");

/**
 * 管理员路由守卫
 * 检查用户是否是管理员
 */
export function adminGuard(to, from, next) {
  // 在函数内部调用 store，而不是在模块级别
  const userStore = useUserStore();

  // 检查用户是否登录且是管理员
  if (!userStore.isLoggedIn) {
    // 未登录，重定向到登录页
    next({
      path: "/login",
      query: { redirect: to.fullPath },
    });
  } else if (!userStore.isAdmin) {
    // 已登录但不是管理员，显示无权限页面
    next({ path: "/unauthorized" });
  } else {
    // 是管理员，允许访问
    next();
  }
}

/**
 * 管理后台路由配置
 */
const adminRoutes = [
  {
    path: "/admin",
    component: AdminLayout,
    beforeEnter: adminGuard,
    children: [
      {
        path: "",
        name: "AdminDashboard",
        component: AdminDashboard,
        meta: { title: "控制台" },
      },
      {
        path: "users",
        name: "UserManagement",
        component: UserManagement,
        meta: { title: "用户管理" },
      },
      {
        path: "users/:id",
        name: "UserDetail",
        component: UserDetail,
        props: true,
        meta: { title: "用户详情" },
      },
      {
        path: "contents",
        name: "AdminContentManagement",
        component: AdminContentManagement,
        meta: { title: "内容管理" },
      },
      {
        path: "feature-permissions",
        name: "FeaturePermissions",
        component: FeaturePermissions,
        meta: { title: "功能权限管理" },
      },
      {
        path: "special-words",
        name: "SpecialWordManagement",
        component: SpecialWordManagement,
        meta: { title: "特殊词管理" },
      },

      {
        path: "feedback",
        name: "FeedbackManagement",
        component: FeedbackManagement,
        meta: { title: "用户反馈管理" },
      },
      {
        path: "security",
        name: "SecurityManagement",
        component: SecurityManagement,
        meta: { title: "安全管理" },
      },
      {
        path: "user-levels",
        name: "UserLevelManagement",
        component: UserLevelManagement,
        meta: { title: "用户等级管理" },
      },
      {
        path: "subscriptions",
        name: "UserSubscriptionManagement",
        component: UserSubscriptionManagement,
        meta: { title: "用户订阅管理" },
      },
      {
        path: "usage-statistics",
        name: "UsageStatistics",
        component: UsageStatistics,
        meta: { title: "使用统计" },
      },
      {
        path: "seo",
        name: "SEOManagement",
        component: SEOManagement,
        meta: { title: "SEO管理" },
      },

      {
        path: "filters",
        name: "FilterManagement",
        component: FilterManagement,
        meta: { title: "过滤器管理" },
      },
      {
        path: "dimensions",
        name: "DimensionManagement",
        component: DimensionManagement,
        meta: { title: "维度管理" },
      },
    ],
  },
];

export default adminRoutes;
