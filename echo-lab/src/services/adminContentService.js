/**
 * 管理员内容服务
 * 用于管理员获取和管理所有用户的内容
 */
import httpClient from "@/utils/httpClient";

export default {
  /**
   * 获取所有用户的内容列表（管理员权限）
   * @param {Object} params 查询参数
   */
  async getAllContents(params = {}) {
    try {
      const response = await httpClient.get("/api/admin/contents", params);
      return response;
    } catch (error) {
      console.error("获取所有内容列表失败:", error);
      throw error;
    }
  },

  /**
   * 删除指定内容（管理员权限）
   * @param {string} contentId 内容ID
   */
  async deleteContent(contentId) {
    try {
      const response = await httpClient.delete(`/api/admin/contents/${contentId}`);
      return response;
    } catch (error) {
      console.error("删除内容失败:", error);
      throw error;
    }
  },

  /**
   * 更新内容状态（管理员权限）
   * @param {string} contentId 内容ID
   * @param {string} status 新状态
   */
  async updateContentStatus(contentId, status) {
    try {
      const response = await httpClient.patch(`/api/admin/contents/${contentId}/status`, {
        status
      });
      return response;
    } catch (error) {
      console.error("更新内容状态失败:", error);
      throw error;
    }
  },

  /**
   * 获取内容详情（管理员权限）
   * @param {string} contentId 内容ID
   */
  async getContentDetail(contentId) {
    try {
      const response = await httpClient.get(`/api/admin/contents/${contentId}`);
      return response;
    } catch (error) {
      console.error("获取内容详情失败:", error);
      throw error;
    }
  },

  /**
   * 批量删除内容（管理员权限）
   * @param {Array} contentIds 内容ID数组
   */
  async batchDeleteContents(contentIds) {
    try {
      const response = await httpClient.post("/api/admin/contents/batch-delete", {
        contentIds
      });
      return response;
    } catch (error) {
      console.error("批量删除内容失败:", error);
      throw error;
    }
  },

  /**
   * 批量更新内容状态（管理员权限）
   * @param {Array} contentIds 内容ID数组
   * @param {string} status 新状态
   */
  async batchUpdateContentStatus(contentIds, status) {
    try {
      const response = await httpClient.post("/api/admin/contents/batch-update-status", {
        contentIds,
        status
      });
      return response;
    } catch (error) {
      console.error("批量更新内容状态失败:", error);
      throw error;
    }
  }
};
