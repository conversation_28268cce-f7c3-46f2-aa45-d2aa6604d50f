/**
 * API服务
 * 提供业务相关的API调用方法
 * 使用统一的HTTP客户端
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";
import { TRANSLATION_TIMEOUT } from "@/config/translation";

// 翻译服务
export const translateService = {
  /**
   * 批量翻译文本（支持分批次处理）
   * @param {Array} texts - 要翻译的文本数组，每个元素包含 id, content
   * @param {Array} targetLanguages - 目标语言数组
   * @returns {Promise} 翻译结果
   */
  async translateTexts(texts, targetLanguages) {
    try {
      console.log("发送翻译请求:", {
        texts: texts.length,
        targetLanguages,
      });

      // 如果文本数量较少，直接处理
      if (texts.length <= 10) {
        const response = await httpClient.post(
          API_ENDPOINTS.TEXT_PROCESSING.TRANSLATE,
          {
            texts,
            targetLanguages,
          },
          {
            timeout: TRANSLATION_TIMEOUT.FRONTEND_REQUEST,
          }
        );
        console.log("翻译响应:", response);
        return response;
      }

      // 分批处理，每批最多10个
      const BATCH_SIZE = 10;
      const batches = [];
      
      for (let i = 0; i < texts.length; i += BATCH_SIZE) {
        batches.push(texts.slice(i, i + BATCH_SIZE));
      }

      console.log(`翻译任务分为 ${batches.length} 批，每批最多 ${BATCH_SIZE} 个文本`);

      const allTranslations = [];
      let successCount = 0;
      let failureCount = 0;

      // 处理每一批
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        
        try {
          console.log(`处理第 ${batchIndex + 1}/${batches.length} 批，包含 ${batch.length} 个文本`);
          
          const response = await httpClient.post(
            API_ENDPOINTS.TEXT_PROCESSING.TRANSLATE,
            {
              texts: batch,
              targetLanguages,
            },
            {
              timeout: TRANSLATION_TIMEOUT.FRONTEND_REQUEST,
            }
          );

          if (response.success && response.translations) {
            allTranslations.push(...response.translations);
            successCount += response.translations.length;
            console.log(`第 ${batchIndex + 1} 批处理成功，翻译了 ${response.translations.length} 个文本`);
          } else {
            console.error(`第 ${batchIndex + 1} 批处理失败:`, response.error);
            failureCount += batch.length;
            
            // 为失败的批次创建空翻译结果
            batch.forEach(text => {
              const emptyTranslation = { id: text.id, translations: {} };
              targetLanguages.forEach(lang => {
                emptyTranslation.translations[lang] = '';
              });
              allTranslations.push(emptyTranslation);
            });
          }
        } catch (error) {
          console.error(`第 ${batchIndex + 1} 批处理出错:`, error);
          failureCount += batch.length;
          
          // 为出错的批次创建空翻译结果
          batch.forEach(text => {
            const emptyTranslation = { id: text.id, translations: {} };
            targetLanguages.forEach(lang => {
              emptyTranslation.translations[lang] = '';
            });
            allTranslations.push(emptyTranslation);
          });
        }


      }

      console.log(`翻译完成: 成功 ${successCount} 个，失败 ${failureCount} 个`);

      // 返回合并后的结果
      return {
        success: successCount > 0,
        translations: allTranslations,
        error: failureCount > 0 ? `部分翻译失败: ${failureCount} 个` : null
      };
    } catch (error) {
      console.error("翻译请求失败:", error);
      throw error;
    }
  },
};

// 标注服务
export const annotateService = {
  /**
   * 批量标注文本
   * @param {Array} segments - 要标注的文本段落数组，每个元素包含 id, content, language
   * @param {String} language - 文本语言
   * @returns {Promise} 标注结果
   */
  async annotateTexts(segments, language) {
    try {
      console.log("发送标注请求:", {
        segments,
        language,
      });
      const response = await httpClient.post(
        API_ENDPOINTS.TEXT_PROCESSING.ANNOTATE,
        {
          segments,
          language,
        }
      );
      console.log("标注响应:", response);
      return response;
    } catch (error) {
      console.error("标注请求失败:", error);
      throw error;
    }
  },
};

// 音频服务
export const audioService = {
  /**
   * 批量生成音频
   * @param {Array} items - 要生成的音频项数组，每个元素包含 id, text, language, speaker, speed
   * @returns {Promise} 生成结果
   */
  async generateAudios(items) {
    try {
      console.log("发送音频生成请求:", items);
      const response = await httpClient.post(API_ENDPOINTS.AUDIO.TTS_BATCH, {
        items: items,
      });
      console.log("音频生成响应:", response);
      return response;
    } catch (error) {
      console.error("音频生成请求失败:", error);
      throw error;
    }
  },

  /**
   * 生成单个音频
   * @param {Object} item - 要生成的音频项，包含 id, text, language, speaker, speed
   * @returns {Promise} 生成结果
   */
  async generateAudio(item) {
    try {
      console.log("发送单个音频生成请求:", item);
      const response = await httpClient.post(API_ENDPOINTS.AUDIO.TTS_BATCH, {
        items: [item],
      });
      console.log("单个音频生成响应:", response);
      if (response.success && response.results && response.results.length > 0) {
        return response.results[0];
      }
      throw new Error("音频生成失败");
    } catch (error) {
      console.error("单个音频生成请求失败:", error);
      throw error;
    }
  },

  /**
   * 获取TTS服务信息
   * @returns {Promise} TTS服务信息，包括支持的语言、服务和声音ID
   */
  async getTtsServiceInfo() {
    try {
      const response = await httpClient.get(API_ENDPOINTS.AUDIO.TTS_INFO);
      console.log("获取TTS服务信息响应:", response);
      if (response.success && response.data) {
        return response.data;
      }
      throw new Error("获取TTS服务信息失败");
    } catch (error) {
      console.error("获取TTS服务信息失败:", error);
      throw error;
    }
  },
};

// 导出统一的HTTP客户端和业务服务
export default {
  // HTTP客户端方法
  get: httpClient.get,
  post: httpClient.post,
  put: httpClient.put,
  delete: httpClient.delete,
  upload: httpClient.upload,

  // 业务服务
  translateService,
  annotateService,
  audioService,
};
