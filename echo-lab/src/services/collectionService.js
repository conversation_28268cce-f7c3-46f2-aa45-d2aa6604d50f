/**
 * 合集服务
 * 管理合集的创建、更新等操作
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

export default {
  /**
   * 获取用户的合集列表
   * @param {Object} params 查询参数
   */
  async getUserCollections(params = {}) {
    return await httpClient.get(API_ENDPOINTS.COLLECTIONS.BASE, params);
  },

  /**
   * 获取公开合集列表
   * @param {Object} params 查询参数
   */
  async getPublicCollections(params = {}) {
    return await httpClient.get(API_ENDPOINTS.COLLECTIONS.PUBLIC, params);
  },

  /**
   * 根据ID获取合集详情
   * @param {string} id 合集ID
   */
  async getCollectionById(id) {
    return await httpClient.get(API_ENDPOINTS.COLLECTIONS.BY_ID(id));
  },

  /**
   * 创建新合集
   * @param {Object} collection 合集数据
   */
  async createCollection(collection) {
    // 确保 tags 是字符串，如果是数组则转换为逗号分隔的字符串
    const tags = Array.isArray(collection.tags)
      ? collection.tags.join(",")
      : collection.tags || "";

    return await httpClient.post(API_ENDPOINTS.COLLECTIONS.BASE, {
      name: collection.name,
      description: collection.description || "",
      coverImageUrl: collection.coverImageUrl || "",
      tags: tags,
      isPublic: collection.isPublic || false,
      learningLanguage: collection.learningLanguage, // 添加语言参数
    });
  },

  /**
   * 更新合集
   * @param {string} id 合集ID
   * @param {Object} updateData 更新数据
   */
  async updateCollection(id, updateData) {
    // 确保 tags 是字符串，如果是数组则转换为逗号分隔的字符串
    if (updateData.tags && Array.isArray(updateData.tags)) {
      updateData.tags = updateData.tags.join(",");
    }

    return await httpClient.put(
      API_ENDPOINTS.COLLECTIONS.BY_ID(id),
      updateData
    );
  },

  /**
   * 删除合集
   * @param {string} id 合集ID
   */
  async deleteCollection(id) {
    return await httpClient.delete(API_ENDPOINTS.COLLECTIONS.BY_ID(id));
  },

  /**
   * 发布合集
   * @param {string} id 合集ID
   */
  async publishCollection(id) {
    return await this.updateCollection(id, { status: "published" });
  },

  /**
   * 下架合集
   * @param {string} id 合集ID
   */
  async unpublishCollection(id) {
    return await this.updateCollection(id, { status: "draft" });
  },

  /**
   * 添加内容到合集
   * @param {string} collectionId 合集ID
   * @param {Array} contentIds 内容ID数组
   */
  async addContentToCollection(collectionId, contentIds) {
    return await httpClient.post(
      API_ENDPOINTS.COLLECTIONS.ITEMS(collectionId),
      {
        contentIds,
      }
    );
  },

  /**
   * 从合集中移除内容
   * @param {string} collectionId 合集ID
   * @param {string} contentId 内容ID
   */
  async removeContentFromCollection(collectionId, contentId) {
    return await httpClient.delete(
      `${API_ENDPOINTS.COLLECTIONS.ITEMS(collectionId)}/${contentId}`
    );
  },

  /**
   * 更新合集内容排序
   * @param {string} collectionId 合集ID
   * @param {Array} itemOrders 排序数组 [{contentId, sortOrder}]
   */
  async updateCollectionItemOrder(collectionId, itemOrders) {
    return await httpClient.put(
      API_ENDPOINTS.COLLECTIONS.ITEM_ORDER(collectionId),
      {
        itemOrders,
      }
    );
  },

  /**
   * 发布合集
   * @param {string} id 合集ID
   */
  async publishCollection(id) {
    return await httpClient.post(API_ENDPOINTS.COLLECTIONS.PUBLISH(id));
  },

  /**
   * 下架合集
   * @param {string} id 合集ID
   */
  async unpublishCollection(id) {
    return await httpClient.post(API_ENDPOINTS.COLLECTIONS.UNPUBLISH(id));
  },

  /**
   * 收藏/取消收藏合集
   * @param {string} collectionId 合集ID
   */
  async toggleCollectionFavorite(collectionId) {
    return await httpClient.post(
      API_ENDPOINTS.COLLECTIONS.FAVORITE(collectionId)
    );
  },

  /**
   * 获取用户收藏的合集列表
   * @param {Object} params 查询参数
   */
  async getUserFavoriteCollections(params = {}) {
    return await httpClient.get(API_ENDPOINTS.COLLECTIONS.FAVORITES, params);
  },

  /**
   * 复制合集
   * @param {string} sourceCollectionId 源合集ID
   * @param {Object} newCollectionData 新合集数据
   */
  async duplicateCollection(sourceCollectionId, newCollectionData) {
    // 获取源合集详情
    const sourceCollection = await this.getCollectionById(sourceCollectionId);

    if (!sourceCollection.success) {
      throw new Error("源合集不存在");
    }

    // 创建新合集
    const newCollection = await this.createCollection({
      name:
        newCollectionData.name || `${sourceCollection.collection.name} - 副本`,
      description:
        newCollectionData.description ||
        sourceCollection.collection.description,
      coverImageUrl:
        newCollectionData.coverImageUrl ||
        sourceCollection.collection.coverImageUrl,
      tags: newCollectionData.tags || sourceCollection.collection.tags,
      isPublic: newCollectionData.isPublic || false,
    });

    if (!newCollection.success) {
      throw new Error("创建新合集失败");
    }

    // 复制内容
    const sourceItems = sourceCollection.collection.items || [];
    const contentIds = sourceItems
      .sort((a, b) => a.sortOrder - b.sortOrder)
      .map((item) => item.contentId);

    if (contentIds.length > 0) {
      await this.addContentToCollection(
        newCollection.collection.id,
        contentIds
      );
    }

    return newCollection;
  },
};
