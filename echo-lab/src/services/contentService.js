/**
 * 内容服务
 * 管理内容的创建、更新等操作
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

export default {
  /**
   * 创建新内容
   * @param {Object} content 内容数据
   */
  async createContent(content) {
    return await httpClient.post(API_ENDPOINTS.CONTENTS.BASE, {
      name: content.name,
      description: content.description || "",
      configJson: content.configJson,
      thumbnailUrl: content.thumbnailUrl || "",
      learningLanguage: content.learningLanguage,
      filterIds: content.filterIds || [],
      keywords: content.keywords || []
    });
  },

  /**
   * 更新内容
   * @param {string|number} id 内容ID
   * @param {Object} content 内容数据
   */
  async updateContent(id, content) {
    return await httpClient.put(`${API_ENDPOINTS.CONTENTS.BASE}/${id}`, {
      name: content.name,
      description: content.description || "",
      configJson: content.configJson,
      thumbnailUrl: content.thumbnailUrl || "",
      learningLanguage: content.learningLanguage,
      filterIds: content.filterIds || [],
      keywords: content.keywords || []
    });
  },

  /**
   * 获取所有内容
   */
  async getAllContent() {
    return await httpClient.get(API_ENDPOINTS.CONTENTS.BASE);
  },

  /**
   * 获取内容列表（带分页）
   * @param {Object} params 查询参数
   */
  async getContentWithPagination(params) {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page);
    if (params.pageSize) queryParams.append('pageSize', params.pageSize);
    if (params.search) queryParams.append('search', params.search);
    if (params.status && params.status !== 'all') queryParams.append('status', params.status);

    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);


    const url = `${API_ENDPOINTS.CONTENTS.BASE}?${queryParams.toString()}`;
    return await httpClient.get(url);
  },

  /**
   * 获取单个内容
   * @param {string|number} id 内容ID
   */
  async getContent(id) {
    return await httpClient.get(`${API_ENDPOINTS.CONTENTS.BASE}/${id}`);
  },

  /**
   * 删除内容
   * @param {string|number} id 内容ID
   */
  async deleteContent(id) {
    return await httpClient.delete(`${API_ENDPOINTS.CONTENTS.BASE}/${id}`);
  },

  /**
   * 发布内容（上线）
   * @param {string|number} id 内容ID
   */
  async publishContent(id) {
    return await httpClient.put(`${API_ENDPOINTS.CONTENTS.BASE}/${id}/publish`);
  },

  /**
   * 下架内容
   * @param {string|number} id 内容ID
   */
  async unpublishContent(id) {
    return await httpClient.put(
      `${API_ENDPOINTS.CONTENTS.BASE}/${id}/unpublish`
    );
  },

  /**
   * 记录内容观看
   * @param {string} id 内容ID
   * @param {number} viewDuration 观看时长（秒）
   */
  async recordView(id, viewDuration = 0) {
    return await httpClient.post(`${API_ENDPOINTS.CONTENTS.BASE}/${id}/views`, {
      viewDuration,
    });
  },
};
