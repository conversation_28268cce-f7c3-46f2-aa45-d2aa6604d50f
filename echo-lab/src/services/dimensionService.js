/**
 * 简化的维度管理服务
 * 只提供基本的查看和创建功能
 */
import httpClient from '@/utils/httpClient'

class DimensionService {
  constructor() {
    this.baseUrl = '/api/admin/dimensions'
  }

  /**
   * 获取所有维度类型
   */
  async getAllDimensions() {
    try {
      const response = await httpClient.get(this.baseUrl)
      return response
    } catch (error) {
      console.error('获取维度列表失败:', error)
      throw error
    }
  }

  /**
   * 创建新维度类型
   */
  async createDimension(dimensionData) {
    try {
      const response = await httpClient.post(this.baseUrl, dimensionData)
      return response
    } catch (error) {
      console.error('创建维度失败:', error)
      throw error
    }
  }

  /**
   * 删除维度类型
   */
  async deleteDimension(type) {
    try {
      const response = await httpClient.delete(`${this.baseUrl}/${type}`)
      return response
    } catch (error) {
      console.error('删除维度失败:', error)
      throw error
    }
  }

  /**
   * 编辑维度类型
   */
  async updateDimension(type, updateData) {
    try {
      const response = await httpClient.put(`${this.baseUrl}/${type}`, updateData)
      return response
    } catch (error) {
      console.error('更新维度失败:', error)
      throw error
    }
  }

  /**
   * 格式化维度类型（转换为有效格式）
   */
  formatDimensionType(input) {
    if (!input || typeof input !== 'string') {
      return ''
    }

    // 常见中文到英文的映射
    const chineseToEnglish = {
      '难度': 'difficulty',
      '等级': 'level',
      '类型': 'type',
      '主题': 'topic',
      '技能': 'skill',
      '格式': 'format',
      '时长': 'duration',
      '复杂度': 'complexity',
      '年龄': 'age',
      '场景': 'scenario',
      '风格': 'style',
      '模式': 'mode'
    }

    let result = input.toLowerCase()

    // 先尝试中文映射
    for (const [chinese, english] of Object.entries(chineseToEnglish)) {
      if (result.includes(chinese)) {
        result = result.replace(chinese, english)
      }
    }

    // 然后进行格式化
    result = result
      .replace(/\s+/g, '_')  // 空格转下划线
      .replace(/[^a-z0-9_]/g, '')  // 移除无效字符
      .replace(/_{2,}/g, '_')  // 多个下划线合并
      .replace(/^_+|_+$/g, '')  // 移除首尾下划线

    return result
  }
}

// 创建单例实例
const dimensionService = new DimensionService()

export default dimensionService
