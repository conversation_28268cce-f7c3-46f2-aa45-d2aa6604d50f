/**
 * 功能权限服务
 * 提供功能权限相关的API调用
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

/**
 * 获取所有功能特性
 * @returns {Promise<Array>} 功能特性列表
 */
export async function getAllFeatures() {
  try {
    const response = await httpClient.get(API_ENDPOINTS.PERMISSIONS.FEATURES);

    if (response && response.success) {
      return response.features;
    }

    return [];
  } catch (error) {
    console.error("获取功能特性失败:", error);
    return [];
  }
}

/**
 * 获取等级功能映射
 * @returns {Promise<Object>} 等级功能映射
 */
export async function getLevelFeatureMapping() {
  try {
    const response = await httpClient.get(
      "/api/feature-permissions/level-mapping"
    );

    if (response && response.success) {
      return response.levelFeatures;
    }

    return {};
  } catch (error) {
    console.error("获取等级功能映射失败:", error);
    return {};
  }
}

/**
 * 检查用户是否有权限访问特定功能
 * @param {string} featureKey - 功能标识符
 * @returns {Promise<boolean>} 是否有权限
 */
export async function checkFeaturePermission(featureKey) {
  try {
    const response = await httpClient.get(
      `${API_ENDPOINTS.PERMISSIONS.CHECK}/${featureKey}`
    );

    if (response && response.success) {
      return response.hasPermission;
    }

    return false;
  } catch (error) {
    console.error(`检查功能权限失败 (${featureKey}):`, error);
    return false;
  }
}

/**
 * 获取所有功能标志
 * @returns {Promise<Array>} 功能标志列表
 */
export async function getAllFeatureFlags() {
  try {
    const response = await httpClient.get(
      `${API_ENDPOINTS.PERMISSIONS.ADMIN_BASE}/flags`
    );

    if (response && response.success) {
      return response.flags;
    }

    return [];
  } catch (error) {
    console.error("获取功能标志失败:", error);
    return [];
  }
}

/**
 * 更新功能标志
 * @param {number} id - 功能标志ID
 * @param {Object} data - 更新数据
 * @returns {Promise<Object>} 更新后的功能标志
 */
export async function updateFeatureFlag(id, data) {
  try {
    const response = await httpClient.put(
      `${API_ENDPOINTS.PERMISSIONS.ADMIN_BASE}/flags/${id}`,
      data
    );

    if (response && response.success) {
      return response.flag;
    }

    throw new Error(response?.error || "更新功能标志失败");
  } catch (error) {
    console.error(`更新功能标志失败 (ID: ${id}):`, error);
    throw error;
  }
}

/**
 * 创建新功能权限
 * @param {string} featureKey - 功能标识符
 * @param {string} displayName - 显示名称
 * @param {string} description - 描述
 * @param {boolean} isEnabled - 是否全局启用
 * @returns {Promise<Object>} 创建的功能权限
 */
export async function createFeature(
  featureKey,
  displayName,
  description,
  isEnabled = false
) {
  try {
    const response = await httpClient.post(API_ENDPOINTS.PERMISSIONS.FEATURES, {
      featureKey,
      displayName,
      description,
      isEnabled,
    });

    if (response && response.success) {
      return response.feature;
    }

    throw new Error(response?.error || "创建功能权限失败");
  } catch (error) {
    console.error("创建功能权限失败:", error);
    throw error;
  }
}

/**
 * 获取所有用户功能权限
 * @returns {Promise<Array>} 用户功能权限列表
 */
export async function getAllUserPermissions() {
  try {
    const response = await httpClient.get(
      `${API_ENDPOINTS.PERMISSIONS.ADMIN_BASE}/users`
    );

    if (response && response.success) {
      return response.permissions;
    }

    return [];
  } catch (error) {
    console.error("获取用户功能权限失败:", error);
    return [];
  }
}

/**
 * 添加用户功能权限
 * @param {string} userId - 用户ID
 * @param {string} featureKey - 功能标识符
 * @returns {Promise<Object>} 创建的权限
 */
export async function addUserPermission(userId, featureKey) {
  try {
    const response = await httpClient.post(
      `${API_ENDPOINTS.PERMISSIONS.ADMIN_BASE}/users`,
      { userId, featureKey }
    );

    if (response && response.success) {
      return response.permission;
    }

    throw new Error(response?.error || "添加用户功能权限失败");
  } catch (error) {
    console.error("添加用户功能权限失败:", error);
    throw error;
  }
}

/**
 * 删除用户功能权限
 * @param {number} id - 权限ID
 * @returns {Promise<boolean>} 是否成功
 */
export async function deleteUserPermission(id) {
  try {
    const response = await httpClient.delete(
      `${API_ENDPOINTS.PERMISSIONS.ADMIN_BASE}/users/${id}`
    );

    return response && response.success;
  } catch (error) {
    console.error(`删除用户功能权限失败 (ID: ${id}):`, error);
    throw error;
  }
}

/**
 * 获取功能使用情况
 * @param {string} featureKey - 功能标识符
 * @returns {Promise<Object>} 使用情况
 */
export async function getFeatureUsage(featureKey) {
  try {
    const response = await httpClient.get(
      `/api/permissions/usage/${featureKey}`
    );

    if (response && response.success) {
      return response;
    }

    return {
      hasLimit: false,
      dailyLimit: null,
      monthlyLimit: null,
      dailyUsage: 0,
      monthlyUsage: 0,
    };
  } catch (error) {
    console.error(`获取功能使用情况失败 (${featureKey}):`, error);
    return {
      hasLimit: false,
      dailyLimit: null,
      monthlyLimit: null,
      dailyUsage: 0,
      monthlyUsage: 0,
    };
  }
}

export default {
  getAllFeatures,
  getLevelFeatureMapping,
  checkFeaturePermission,
  getAllFeatureFlags,
  updateFeatureFlag,
  createFeature,
  getAllUserPermissions,
  addUserPermission,
  deleteUserPermission,
  getFeatureUsage,
};
