/**
 * 过滤器服务
 * 提供过滤器管理功能的API调用
 */

import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

class FilterService {
  constructor() {
    this.baseUrl = "/api/filters";
    this.adminUrl = API_ENDPOINTS.ADMIN.FILTERS || "/api/admin/filters";
  }

  /**
   * 获取所有过滤器（按类型分组）- 首页筛选使用
   * @param {Object} options 查询选项
   * @returns {Promise} API响应
   */
  async getFilters(options = {}) {
    const params = new URLSearchParams();

    if (options.language) {
      params.append("language", options.language);
    }
    if (options.type) {
      params.append("type", options.type);
    }

    const url = `${this.baseUrl}${
      params.toString() ? "?" + params.toString() : ""
    }`;
    return await httpClient.get(url);
  }

  /**
   * 获取所有过滤器（包括禁用的，用于管理）
   * @returns {Promise} API响应
   */
  async getAllFilters() {
    return await httpClient.get(`${this.adminUrl}/all`);
  }

  /**
   * 获取所有过滤器类型 - 编辑页使用
   * @returns {Promise} API响应
   */
  async getFilterTypes() {
    return await httpClient.get(`${this.baseUrl}/types`);
  }

  /**
   * 创建过滤器
   * @param {Object} filterData 过滤器数据
   * @returns {Promise} API响应
   */
  async createFilter(filterData) {
    return await httpClient.post(this.adminUrl, filterData);
  }

  /**
   * 更新过滤器
   * @param {number} filterId 过滤器ID
   * @param {Object} filterData 过滤器数据
   * @returns {Promise} API响应
   */
  async updateFilter(filterId, filterData) {
    return await httpClient.put(`${this.adminUrl}/${filterId}`, filterData);
  }

  /**
   * 删除过滤器
   * @param {number} filterId 过滤器ID
   * @returns {Promise} API响应
   */
  async deleteFilter(filterId) {
    return await httpClient.delete(`${this.adminUrl}/${filterId}`);
  }


}

export default new FilterService();