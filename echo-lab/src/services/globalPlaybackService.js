/**
 * 学习模式服务
 */
import { useTemplateStore } from '@/stores/templateStore';

class GlobalPlaybackService {
  // 获取学习模式 ID
  getGlobalTemplateId() {
    return localStorage.getItem('globalPlaybackTemplate');
  }

  // 设置学习模式
  setGlobalTemplate(templateId) {
    if (templateId) {
      localStorage.setItem('globalPlaybackTemplate', templateId);
    } else {
      localStorage.removeItem('globalPlaybackTemplate');
    }
  }

  // 获取全局模板详情
  async getGlobalTemplate() {
    const templateId = this.getGlobalTemplateId();
    if (!templateId) {
      return null;
    }

    try {
      const templateStore = useTemplateStore();
      
      // 确保模板数据已加载
      if (templateStore.systemTemplates.length === 0) {
        await templateStore.loadTemplates();
      }
      
      const template = templateStore.templateById(templateId);
      return template;
    } catch (error) {
      console.error('获取全局模板失败:', error);
      return null;
    }
  }

  // 判断是否应该应用全局策略
  shouldApplyGlobalTemplate(contentId) {
    // 检查该内容是否有特定设置
    const hasCustomSettings = localStorage.getItem(`ps_${contentId}`);
    return !hasCustomSettings;
  }

  // 应用全局模板到服务器配置
  applyGlobalTemplate(serverConfig, globalTemplate) {
    if (!globalTemplate?.config?.sections || !serverConfig?.sections) {
      return serverConfig;
    }

    const templateSections = globalTemplate.config.sections;
    const serverSections = serverConfig.sections;
    const resultSections = [];

    // 按模板环节数量生成配置
    templateSections.forEach((templateSection, index) => {
      const serverSection = serverSections[index];
      
      if (serverSection) {
        // 有对应的服务器环节，保留服务器字段，应用模板参数
        resultSections.push({
          ...serverSection,
          pauseDuration: templateSection.pauseDuration,
          repeatCount: templateSection.repeatCount,
          repeatSpeeds: templateSection.repeatSpeeds || Array(templateSection.repeatCount).fill(1.0),
          repeatPauses: templateSection.repeatPauses || Array(templateSection.repeatCount).fill(templateSection.pauseDuration),
          enableTranslation: templateSection.enableTranslation || false,
          translationLanguage: templateSection.translationLanguage || '',
          translationPosition: templateSection.translationPosition || 2,
          enableKeywords: templateSection.enableKeywords || false,
          keywordPosition: templateSection.keywordPosition || 2,
          keywordRepeatCount: templateSection.keywordRepeatCount || 2
        });
      } else {
        // 没有对应的服务器环节，创建新环节
        resultSections.push({
          id: `section_global_${Date.now()}_${index}`,
          title: `环节 ${index + 1}`,
          description: '全局模板生成的环节',
          processingMode: 'sequence',
          userEditable: true,
          sourceIndex: 0,
          pauseDuration: templateSection.pauseDuration,
          repeatCount: templateSection.repeatCount,
          repeatSpeeds: templateSection.repeatSpeeds || Array(templateSection.repeatCount).fill(1.0),
          repeatPauses: templateSection.repeatPauses || Array(templateSection.repeatCount).fill(templateSection.pauseDuration),
          enableTranslation: templateSection.enableTranslation || false,
          translationLanguage: templateSection.translationLanguage || '',
          translationPosition: templateSection.translationPosition || 2,
          enableKeywords: templateSection.enableKeywords || false,
          keywordPosition: templateSection.keywordPosition || 2,
          keywordRepeatCount: templateSection.keywordRepeatCount || 2
        });
      }
    });

    return { sections: resultSections };
  }
}

export default new GlobalPlaybackService();