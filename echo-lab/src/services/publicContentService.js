/**
 * 公开内容服务
 * 用于获取和管理公开的视频内容
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

export default {
  /**
   * 获取所有公开内容
   * @param {Object} params 查询参数
   * @returns {Promise} 请求结果
   */
  async getPublicContents(params = {}) {
    return await httpClient.get(API_ENDPOINTS.CONTENTS.PUBLIC, params);
  },

  /**
   * 获取单个公开内容
   * @param {string} id 内容ID
   * @returns {Promise} 请求结果
   */
  async getPublicContent(id) {
    return await httpClient.get(`${API_ENDPOINTS.CONTENTS.PUBLIC}/${id}`);
  },


};
