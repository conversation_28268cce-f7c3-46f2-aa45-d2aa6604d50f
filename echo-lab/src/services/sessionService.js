/**
 * 会话管理服务
 * 提供用户会话的查询和管理功能
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";
import { ElMessage } from "element-plus";
import { useUserStore } from "@/stores/userStore";
import router from "@/router";

/**
 * 获取当前用户的所有活跃会话
 * @returns {Promise<Array>} 会话列表
 */
export async function getUserSessions() {
  try {
    const response = await httpClient.get(API_ENDPOINTS.SESSIONS.BASE);

    if (response && response.success) {
      return {
        sessions: response.sessions,
        maxSessions: response.maxSessions,
      };
    }

    throw new Error(response?.error || "获取会话列表失败");
  } catch (error) {
    console.error("获取会话列表失败:", error);
    throw error;
  }
}

/**
 * 删除会话（登出）
 * @param {string} tokenId - 会话ID
 * @returns {Promise<Object>} 删除结果
 */
export async function deleteSession(tokenId) {
  try {
    const response = await httpClient.delete(
      `${API_ENDPOINTS.SESSIONS.BASE}/${tokenId}`
    );

    if (response && response.success) {
      // 如果是当前会话，执行登出操作
      if (response.isCurrentSession) {
        const userStore = useUserStore();
        userStore.logout();
        router.push("/login");
        ElMessage.success("您已成功登出");
      } else {
        ElMessage.success("会话已删除");
      }

      return response;
    }

    throw new Error(response?.error || "删除会话失败");
  } catch (error) {
    console.error("删除会话失败:", error);
    ElMessage.error(`删除会话失败: ${error.message}`);
    throw error;
  }
}

/**
 * 删除所有其他会话
 * @returns {Promise<Object>} 删除结果
 */
export async function deleteAllOtherSessions() {
  try {
    const response = await httpClient.delete(API_ENDPOINTS.SESSIONS.BASE);

    if (response && response.success) {
      ElMessage.success(`已删除 ${response.deletedCount} 个其他设备的会话`);
      return response;
    }

    throw new Error(response?.error || "删除其他会话失败");
  } catch (error) {
    console.error("删除其他会话失败:", error);
    ElMessage.error(`删除其他会话失败: ${error.message}`);
    throw error;
  }
}

export default {
  getUserSessions,
  deleteSession,
  deleteAllOtherSessions,
};
