/**
 * 播放策略模板服务
 * 提供模板相关的API调用功能
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

class TemplateService {
  /**
   * 获取模板列表
   * @param {Object} params 查询参数
   * @param {string} params.type 模板类型 (system|user)
   * @param {boolean} params.isPublic 是否公开
   * @returns {Promise<Object>} 模板列表
   */
  async getTemplates(params = {}) {
    try {
      const response = await httpClient.get(
        API_ENDPOINTS.TEMPLATES.BASE,
        params
      );
      return response;
    } catch (error) {
      console.error("获取模板列表失败:", error);
      throw error;
    }
  }

  /**
   * 根据ID获取模板
   * @param {string} id 模板ID
   * @returns {Promise<Object>} 模板对象
   */
  async getTemplateById(id) {
    try {
      const response = await httpClient.get(
        `${API_ENDPOINTS.TEMPLATES.BASE}/${id}`
      );
      return response;
    } catch (error) {
      console.error("获取模板失败:", error);
      throw error;
    }
  }

  /**
   * 创建模板
   * @param {Object} templateData 模板数据
   * @param {string} templateData.name 模板名称
   * @param {string} templateData.description 模板描述
   * @param {Object} templateData.config 模板配置
   * @param {boolean} templateData.isPublic 是否公开
   * @returns {Promise<Object>} 创建的模板
   */
  async createTemplate(templateData) {
    try {
      const response = await httpClient.post(
        API_ENDPOINTS.TEMPLATES.BASE,
        templateData
      );
      return response;
    } catch (error) {
      console.error("创建模板失败:", error);
      throw error;
    }
  }

  /**
   * 更新模板
   * @param {string} id 模板ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<Object>} 更新后的模板
   */
  async updateTemplate(id, updateData) {
    try {
      const response = await httpClient.put(
        `${API_ENDPOINTS.TEMPLATES.BASE}/${id}`,
        updateData
      );
      return response;
    } catch (error) {
      console.error("更新模板失败:", error);
      throw error;
    }
  }

  /**
   * 删除模板
   * @param {string} id 模板ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  async deleteTemplate(id) {
    try {
      await httpClient.delete(`${API_ENDPOINTS.TEMPLATES.BASE}/${id}`);
      return true;
    } catch (error) {
      console.error("删除模板失败:", error);
      throw error;
    }
  }

  /**
   * 使用模板（增加使用次数）
   * @param {string} id 模板ID
   * @returns {Promise<boolean>} 是否成功
   */
  async useTemplate(id) {
    try {
      await httpClient.post(API_ENDPOINTS.TEMPLATES.USE(id));
      return true;
    } catch (error) {
      console.error("更新使用记录失败:", error);
      throw error;
    }
  }

  /**
   * 复制模板
   * @param {string} id 原模板ID
   * @param {string} newName 新模板名称
   * @returns {Promise<Object>} 新模板
   */
  async duplicateTemplate(id, newName) {
    try {
      const response = await httpClient.post(
        API_ENDPOINTS.TEMPLATES.DUPLICATE(id),
        {
          name: newName,
        }
      );
      return response;
    } catch (error) {
      console.error("复制模板失败:", error);
      throw error;
    }
  }

  /**
   * 验证模板配置格式
   * @param {Object} config 配置对象
   * @returns {Object} 验证结果
   */
  validateTemplateConfig(config) {
    const errors = [];

    if (!config || typeof config !== "object") {
      errors.push("配置必须是一个对象");
      return { valid: false, errors };
    }

    if (!config.sections || !Array.isArray(config.sections)) {
      errors.push("配置必须包含sections数组");
      return { valid: false, errors };
    }

    if (config.sections.length === 0) {
      errors.push("至少需要一个环节配置");
      return { valid: false, errors };
    }

    // 验证每个环节的配置
    config.sections.forEach((section, index) => {
      const requiredFields = [
        "pauseDuration",
        "repeatCount",
        "enableTranslation",
        "translationLanguage",
        "translationPosition",
        "enableKeywords",
        "keywordPosition",
        "keywordRepeatCount",
        "repeatSpeeds",
        "repeatPauses",
      ];

      requiredFields.forEach((field) => {
        if (section[field] === undefined) {
          errors.push(`环节${index + 1}缺少字段: ${field}`);
        }
      });

      // 验证数组长度
      if (
        section.repeatSpeeds &&
        section.repeatCount &&
        section.repeatSpeeds.length !== section.repeatCount
      ) {
        errors.push(
          `环节${index + 1}的repeatSpeeds数组长度与repeatCount不匹配`
        );
      }

      if (
        section.repeatPauses &&
        section.repeatCount &&
        section.repeatPauses.length !== section.repeatCount
      ) {
        errors.push(
          `环节${index + 1}的repeatPauses数组长度与repeatCount不匹配`
        );
      }

      // 验证数值范围
      if (
        section.repeatCount !== undefined &&
        (section.repeatCount < 1 || section.repeatCount > 10)
      ) {
        errors.push(`环节${index + 1}的repeatCount值应在1-10之间`);
      }

      // 验证停顿时长范围
      if (
        section.pauseDuration !== undefined &&
        (section.pauseDuration < 0 || section.pauseDuration > 5000)
      ) {
        errors.push(`环节${index + 1}的pauseDuration值应在0-5000之间`);
      }
    });

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 创建默认模板配置
   * @param {number} sectionCount 环节数量
   * @returns {Object} 默认配置
   */
  createDefaultConfig(sectionCount = 1) {
    const sections = [];

    for (let i = 0; i < sectionCount; i++) {
      sections.push({
        pauseDuration: 2500,
        repeatCount: 4,
        enableTranslation: false,
        translationLanguage: "",
        translationPosition: 2,
        enableKeywords: false,
        keywordRepeatCount: 2,
        keywordPosition: 2,
        repeatSpeeds: [1.0, 1.0, 1.0, 1.0],
        repeatPauses: [2500, 2500, 2500, 2500],
      });
    }

    return { sections };
  }

  /**
   * 应用模板到内容配置（修复后的版本）
   * @param {Object} serverConfig 服务器内容配置
   * @param {Object} template 模板对象
   * @returns {Object} 应用模板后的配置
   */
  applyTemplateToContent(serverConfig, template) {
    if (
      !serverConfig ||
      !serverConfig.sections ||
      !template ||
      !template.config
    ) {
      throw new Error("无效的配置或模板");
    }

    return {
      sections: serverConfig.sections.map((serverSection, index) => {
        const templateSection = template.config.sections[index];
        if (!templateSection) return serverSection;

        return {
          // 保留服务器控制字段
          id: serverSection.id,
          title: serverSection.title,
          description: serverSection.description,
          processingMode: serverSection.processingMode,
          userEditable: serverSection.userEditable,
          sourceIndex: serverSection.sourceIndex,
          sourceNodeIds: serverSection.sourceNodeIds,

          // 只应用用户可编辑字段
          pauseDuration: templateSection.pauseDuration,
          repeatCount: templateSection.repeatCount,
          repeatSpeeds: templateSection.repeatSpeeds,
          repeatPauses: templateSection.repeatPauses,
          enableTranslation: templateSection.enableTranslation,
          translationLanguage: templateSection.translationLanguage,
          translationPosition: templateSection.translationPosition,
          enableKeywords: templateSection.enableKeywords,
          keywordPosition: templateSection.keywordPosition,
          keywordRepeatCount: templateSection.keywordRepeatCount
        };
      })
    };
  }

  /**
   * 从当前配置创建模板（修复后的版本）
   * @param {Object} currentConfig 当前播放配置
   * @returns {Object} 模板配置
   */
  createTemplateFromConfig(currentConfig) {
    if (!currentConfig || !currentConfig.sections) {
      throw new Error("无效的配置");
    }

    const templateConfig = {
      sections: currentConfig.sections.map((section) => {
        // 只提取用户可编辑字段
        return {
          pauseDuration: section.pauseDuration,
          repeatCount: section.repeatCount,
          repeatSpeeds: section.repeatSpeeds,
          repeatPauses: section.repeatPauses,
          enableTranslation: section.enableTranslation,
          translationLanguage: section.translationLanguage,
          translationPosition: section.translationPosition,
          enableKeywords: section.enableKeywords,
          keywordPosition: section.keywordPosition,
          keywordRepeatCount: section.keywordRepeatCount
        };
      }),
    };

    return templateConfig;
  }
}

export default new TemplateService();
