/**
 * 用户等级服务
 * 提供用户等级和订阅相关的API调用
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";
import { useUserStore } from "@/stores/userStore";

/**
 * 获取所有用户等级
 * @returns {Promise<Array>} 用户等级列表
 */
export async function getAllLevels() {
  try {
    const response = await httpClient.get("/api/user-levels");

    if (response && response.success) {
      return response.levels;
    }

    return [];
  } catch (error) {
    console.error("获取用户等级失败:", error);
    return [];
  }
}

/**
 * 获取当前用户的等级信息
 * @returns {Promise<Object>} 用户等级信息
 */
export async function getUserLevelInfo() {
  try {
    const response = await httpClient.get(API_ENDPOINTS.USER_LEVELS.ME);

    if (response && response.success) {
      return {
        level: response.level,
        levelName: response.levelName,
        levelDescription: response.levelDescription,
        subscription: response.subscription,
      };
    }

    throw new Error(response?.error || "获取用户等级信息失败");
  } catch (error) {
    console.error("获取用户等级信息失败:", error);
    throw error;
  }
}

/**
 * 检查用户是否达到所需等级
 * @param {number} requiredLevel - 所需等级
 * @returns {Promise<boolean>} 是否达到所需等级
 */
export async function checkUserLevel(requiredLevel) {
  try {
    const userStore = useUserStore();

    // 如果用户未登录，返回false
    if (!userStore.isLoggedIn) {
      return false;
    }

    // 如果用户是管理员，始终返回true
    if (userStore.isAdmin) {
      return true;
    }

    // 如果用户等级信息已加载，直接比较
    if (userStore.userLevel !== undefined) {
      return userStore.userLevel >= requiredLevel;
    }

    // 否则，从服务器获取用户等级信息
    const levelInfo = await getUserLevelInfo();

    // 更新用户等级信息
    userStore.setUserLevel(levelInfo.level);

    return levelInfo.level >= requiredLevel;
  } catch (error) {
    console.error("检查用户等级失败:", error);
    return false;
  }
}

/**
 * 获取功能使用情况
 * @param {string} featureKey - 功能标识符
 * @returns {Promise<Object>} 使用情况
 */
export async function getFeatureUsage(featureKey) {
  try {
    const response = await httpClient.get(
      `${API_ENDPOINTS.PERMISSIONS.USAGE}/${featureKey}`
    );

    if (response && response.success) {
      return {
        hasLimit: response.hasLimit,
        dailyLimit: response.dailyLimit,
        monthlyLimit: response.monthlyLimit,
        dailyUsage: response.dailyUsage,
        monthlyUsage: response.monthlyUsage,
        dailyRemaining: response.dailyRemaining,
        monthlyRemaining: response.monthlyRemaining,
      };
    }

    throw new Error(response?.error || "获取功能使用情况失败");
  } catch (error) {
    console.error(`获取功能使用情况失败 (${featureKey}):`, error);
    throw error;
  }
}

/**
 * 获取用户可用的所有功能
 * @returns {Promise<Array<string>>} 功能标识符列表
 */
export async function getUserFeatures() {
  try {
    const response = await httpClient.get(API_ENDPOINTS.PERMISSIONS.ME);

    if (response && response.success) {
      return response.features;
    }

    return [];
  } catch (error) {
    console.error("获取用户功能失败:", error);
    return [];
  }
}

export default {
  getAllLevels,
  getUserLevelInfo,
  checkUserLevel,
  getFeatureUsage,
  getUserFeatures,
};
