/**
 * 用户服务
 * 管理用户空间相关的API调用
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

export default {
  /**
   * 获取用户基本信息
   * @param {string} userId 用户ID
   */
  async getUserInfo(userId) {
    return await httpClient.get(API_ENDPOINTS.USERS.BY_ID(userId));
  },

  /**
   * 获取用户的公开内容列表
   * @param {string} userId 用户ID
   * @param {Object} params 查询参数
   */
  async getUserContents(userId, params = {}) {
    return await httpClient.get(API_ENDPOINTS.USERS.CONTENTS(userId), params);
  },

  /**
   * 获取用户的公开合集列表
   * @param {string} userId 用户ID
   * @param {Object} params 查询参数
   */
  async getUserCollections(userId, params = {}) {
    return await httpClient.get(API_ENDPOINTS.USERS.COLLECTIONS(userId), params);
  },

  /**
   * 获取用户统计信息
   * @param {string} userId 用户ID
   */
  async getUserStats(userId) {
    return await httpClient.get(API_ENDPOINTS.USERS.STATS(userId));
  },
};
