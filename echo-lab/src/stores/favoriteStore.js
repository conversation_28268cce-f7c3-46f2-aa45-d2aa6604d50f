/**
 * 收藏状态存储
 * 管理用户收藏内容的状态
 */
import { defineStore } from "pinia";
import favoriteService from "@/services/favoriteService";
import { useUserStore } from "@/stores/userStore";

export const useFavoriteStore = defineStore("favorite", {
  state: () => ({
    loading: false,
    error: null,
    favorites: [],
    favoriteIds: new Set(), // 使用Set存储已收藏的内容ID，方便快速查询
  }),

  getters: {
    /**
     * 检查内容是否已收藏
     * @param {string} contentId 内容ID
     * @returns {boolean} 是否已收藏
     */
    isFavorite: (state) => (contentId) => {
      return state.favoriteIds.has(contentId);
    },

    /**
     * 获取收藏数量
     * @returns {number} 收藏数量
     */
    favoriteCount: (state) => {
      return state.favorites.length;
    },
  },

  actions: {
    /**
     * 获取用户收藏列表
     */
    async fetchFavorites() {
      const userStore = useUserStore();
      if (!userStore.isLoggedIn) {
        return;
      }

      this.loading = true;
      try {
        const response = await favoriteService.getFavorites();
        if (response && response.success) {
          this.favorites = response.favorites || [];
          // 更新收藏ID集合
          this.favoriteIds = new Set(this.favorites.map((item) => item.id));
        } else {
          throw new Error(response?.error || "获取收藏列表失败");
        }
      } catch (err) {
        console.error("获取收藏列表失败:", err);
        this.error = err.message;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 添加收藏
     * @param {string} contentId 内容ID
     * @param {Object} contentData 内容数据（可选，用于立即更新UI）
     */
    async addFavorite(contentId, contentData = null) {
      const userStore = useUserStore();
      if (!userStore.isLoggedIn) {
        return { success: false, error: "请先登录" };
      }

      try {
        const response = await favoriteService.addFavorite(contentId);
        if (response && response.success) {
          // 如果提供了内容数据，直接添加到收藏列表
          if (contentData) {
            this.favorites.unshift(contentData);
          }
          // 更新收藏ID集合
          this.favoriteIds.add(contentId);
          return { success: true };
        } else {
          throw new Error(response?.error || "添加收藏失败");
        }
      } catch (err) {
        console.error("添加收藏失败:", err);
        return { success: false, error: err.message };
      }
    },

    /**
     * 删除收藏
     * @param {string} contentId 内容ID
     */
    async removeFavorite(contentId) {
      const userStore = useUserStore();
      if (!userStore.isLoggedIn) {
        return { success: false, error: "请先登录" };
      }

      try {
        const response = await favoriteService.removeFavorite(contentId);
        if (response && response.success) {
          // 从收藏列表中移除
          this.favorites = this.favorites.filter(
            (item) => item.id !== contentId
          );
          // 更新收藏ID集合
          this.favoriteIds.delete(contentId);
          return { success: true };
        } else {
          throw new Error(response?.error || "取消收藏失败");
        }
      } catch (err) {
        console.error("取消收藏失败:", err);
        return { success: false, error: err.message };
      }
    },

    /**
     * 检查内容是否已收藏
     * @param {string} contentId 内容ID
     */
    async checkFavorite(contentId) {
      const userStore = useUserStore();
      if (!userStore.isLoggedIn) {
        return false;
      }

      try {
        const response = await favoriteService.checkFavorite(contentId);
        if (response && response.success) {
          const isFavorite = response.isFavorite;
          // 更新收藏ID集合
          if (isFavorite) {
            this.favoriteIds.add(contentId);
          } else {
            this.favoriteIds.delete(contentId);
          }
          return isFavorite;
        }
        return false;
      } catch (err) {
        console.error("检查收藏状态失败:", err);
        return false;
      }
    },

    /**
     * 切换收藏状态
     * @param {string} contentId 内容ID
     * @param {Object} contentData 内容数据（可选，用于立即更新UI）
     */
    async toggleFavorite(contentId, contentData = null) {
      if (this.isFavorite(contentId)) {
        return this.removeFavorite(contentId);
      } else {
        return this.addFavorite(contentId, contentData);
      }
    },
  },
});
