/**
 * 语言状态管理
 * 管理用户当前学习的语言
 */

import { defineStore } from "pinia";
import { SUPPORTED_LANGUAGES, getLanguageLabel } from "@/config/languages";
import {
  getLearningLanguage,
  setLearningLanguage,
  isFirstVisit,
  markAsVisited,
  shouldShowOnboarding
} from "@/utils/userSettings";

export const useLanguageStore = defineStore("language", {
  state: () => ({
    // 当前学习的语言
    currentLearningLanguage: getLearningLanguage(), // 从统一设置中获取

    // 支持的语言列表
    supportedLanguages: SUPPORTED_LANGUAGES,
  }),

  getters: {
    /**
     * 获取当前学习语言的标签
     */
    currentLanguageLabel(state) {
      return getLanguageLabel(state.currentLearningLanguage);
    },

    /**
     * 获取当前学习语言的配置对象
     */
    currentLanguageConfig(state) {
      return state.supportedLanguages.find(
        (lang) => lang.value === state.currentLearningLanguage
      );
    },

    /**
     * 检查是否已设置学习语言
     */
    hasLanguageSet(state) {
      return !!state.currentLearningLanguage;
    },



    /**
     * 检查是否需要显示引导流程
     */
    needsOnboarding() {
      return shouldShowOnboarding();
    },

    /**
     * 检查是否首次访问
     */
    isFirstVisit() {
      return isFirstVisit();
    },
  },

  actions: {
    /**
     * 设置当前学习语言
     * @param {string} languageCode 语言代码
     */
    setLearningLanguage(languageCode) {
      if (!languageCode) return false;

      // 验证语言代码是否支持
      const isSupported = this.supportedLanguages.some(
        (lang) => lang.value === languageCode
      );

      if (!isSupported) {
        console.warn(`不支持的语言代码: ${languageCode}`);
        return false;
      }



      this.currentLearningLanguage = languageCode;

      // 使用统一的设置管理
      const success = setLearningLanguage(languageCode);

      if (success) {
        // 语言切换成功
      }

      return success;
    },





    /**
     * 从本地存储加载学习语言和等级
     */
    loadLearningLanguage() {
      try {
        // 加载语言设置
        const savedLanguage = getLearningLanguage();
        if (savedLanguage) {
          // 验证保存的语言是否仍然支持
          const isSupported = this.supportedLanguages.some(
            (lang) => lang.value === savedLanguage
          );

          if (isSupported) {
            this.currentLearningLanguage = savedLanguage;
          } else {
            console.warn(`保存的语言代码不再支持: ${savedLanguage}，使用默认语言`);
            this.setLearningLanguage("ja"); // 使用默认日语
          }
        }


      } catch (error) {
        console.error("加载学习语言失败:", error);
        this.setLearningLanguage("ja"); // 出错时使用默认日语
      }
    },



    /**
     * 重新加载所有状态
     */
    reloadFromStorage() {
      this.loadLearningLanguage();
    },

    /**
     * 标记首次访问完成
     */
    markAsVisited() {
      return markAsVisited();
    },

    /**
     * 重置为默认语言
     */
    resetToDefault() {
      this.setLearningLanguage("ja");
    },

    /**
     * 切换学习语言（带确认）
     * @param {string} languageCode 新的语言代码
     * @returns {Promise<boolean>} 是否成功切换
     */
    async switchLanguage(languageCode) {
      if (languageCode === this.currentLearningLanguage) {
        return true; // 相同语言，无需切换
      }

      try {
        this.setLearningLanguage(languageCode);

        // 可以在这里添加切换后的处理逻辑
        // 比如清除相关缓存、重新加载内容等

        return true;
      } catch (error) {
        console.error("切换学习语言失败:", error);
        return false;
      }
    },

    /**
     * 获取语言选项列表（用于UI显示）
     */
    getLanguageOptions() {
      return this.supportedLanguages.map((lang) => ({
        value: lang.value,
        label: lang.label,
        isActive: lang.value === this.currentLearningLanguage,
      }));
    },
  },
});

export default useLanguageStore;
