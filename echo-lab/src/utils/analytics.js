/**
 * GrowingIO 数据上报工具
 */
import { EVENTS } from '@/constants/events';

/**
 * 通用事件上报
 */
export function track(eventName, properties = {}) {
  if (!window.gio) return;
  window.gio('track', eventName, properties);
}

/**
 * 页面访问上报
 */
export function trackPageView(pageName, properties = {}) {
  track(EVENTS.PAGE_VIEW, {
    page_name: pageName,
    page_path: location.pathname,
    ...properties
  });
}

/**
 * 设置用户ID
 */
export function setUserId(userId, userProperties = {}) {
  if (!window.gio) return;
  window.gio('setUserId', userId);
  window.gio('setUserAttributes', userProperties);
}

/**
 * 清除用户ID
 */
export function clearUserId() {
  if (!window.gio) return;
  window.gio('clearUserId');
}