/**
 * 深拷贝工具函数
 * 提供安全的深拷贝功能，处理可能包含不可克隆对象的数据
 */

/**
 * 将 Proxy 对象转换为普通对象
 * @param {any} obj - Proxy 对象
 * @returns {any} 普通对象
 */
function unproxy(obj) {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  // 对于数组
  if (Array.isArray(obj)) {
    return obj.map((item) => unproxy(item));
  }

  // 对于普通对象，创建一个新的普通对象
  const plain = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      try {
        const value = obj[key];
        if (typeof value === "function" || typeof value === "symbol") {
          continue;
        }
        plain[key] = unproxy(value);
      } catch (error) {
        console.warn(`跳过无法访问的属性: ${key}`, error);
      }
    }
  }
  return plain;
}

/**
 * 安全的深拷贝函数，处理可能包含不可克隆对象的数据
 * @param {any} obj - 要克隆的对象
 * @returns {any} 克隆后的对象
 */
export function safeCloneDeep(obj) {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  // 对于Date对象
  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }

  // 对于RegExp对象
  if (obj instanceof RegExp) {
    return new RegExp(obj);
  }

  // 对于数组
  if (Array.isArray(obj)) {
    return obj.map((item) => safeCloneDeep(item));
  }

  // 对于普通对象
  const cloned = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      try {
        // 跳过函数、Symbol等不可序列化的属性
        const value = obj[key];
        if (typeof value === "function" || typeof value === "symbol") {
          continue;
        }
        cloned[key] = safeCloneDeep(value);
      } catch (error) {
        // 如果某个属性无法克隆，跳过它
        console.warn(`跳过无法克隆的属性: ${key}`, error);
      }
    }
  }
  return cloned;
}

/**
 * 尝试使用原生 structuredClone，失败时回退到安全克隆
 * @param {any} obj - 要克隆的对象
 * @returns {any} 克隆后的对象
 */
export function cloneDeep(obj) {
  try {
    // 优先使用原生 structuredClone（性能更好）
    return structuredClone(obj);
  } catch (error) {
    // 如果 structuredClone 失败，说明对象包含不可克隆的内容（如 Proxy）
    console.log("structuredClone 失败，可能包含 Proxy 对象，使用安全克隆");
    console.log("错误信息:", error.message);

    // 直接使用安全克隆，先转换 Proxy 为普通对象
    return safeCloneDeep(unproxy(obj));
  }
}

/**
 * JSON 序列化深拷贝（仅适用于可序列化的数据）
 * @param {any} obj - 要克隆的对象
 * @returns {any} 克隆后的对象
 */
export function jsonCloneDeep(obj) {
  try {
    return JSON.parse(JSON.stringify(obj));
  } catch (error) {
    console.warn("JSON 深拷贝失败，使用安全深拷贝:", error.message);
    return safeCloneDeep(obj);
  }
}

export default {
  safeCloneDeep,
  cloneDeep,
  jsonCloneDeep,
};
