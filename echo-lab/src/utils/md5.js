/**
 * MD5 哈希函数
 *
 * 使用 crypto-js 库计算 MD5 哈希值，用于生成内容的唯一标识符。
 */
import CryptoJS from "crypto-js";

/**
 * 将字符串转换为 MD5 哈希
 * @param {string} str 要哈希的字符串
 * @returns {string} MD5 哈希值
 */
export function md5(str) {
  // 如果输入不是字符串，转换为字符串
  if (typeof str !== "string") {
    str = String(str);
  }

  // 使用 crypto-js 计算 MD5
  return CryptoJS.MD5(str).toString();
}

/**
 * 为内容生成唯一 ID
 * @param {string} content 内容
 * @param {string} prefix ID 前缀
 * @returns {string} 唯一 ID
 */
export function generateId(content, prefix = "") {
  const hash = md5(content);
  return `${prefix}${hash}`;
}
