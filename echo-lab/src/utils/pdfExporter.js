/**
 * 原文内容导出工具
 * 用于将原文内容导出为PDF文件，支持中文和日文字符
 */

/**
 * 创建HTML内容并转换为PDF
 * @param {Array} formattedLines - 格式化后的内容行
 * @param {string} title - 标题
 * @returns {Promise<Blob>} PDF Blob
 */
async function createPDFFromHTML(formattedLines, title) {
  // 创建HTML内容
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <style>
        body {
          font-family: "Microsoft YaHei", "SimSun", "Arial Unicode MS", sans-serif;
          font-size: 14px;
          line-height: 1.6;
          margin: 20px;
          color: #333;
        }
        .title {
          font-size: 18px;
          font-weight: bold;
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 2px solid #333;
          padding-bottom: 10px;
        }
        .content-table {
          width: 100%;
          border-collapse: collapse;
          margin-top: 20px;
        }
        .content-table th,
        .content-table td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }
        .content-table th {
          background-color: #f5f5f5;
          font-weight: bold;
          text-align: center;
        }
        .content-table .index-col {
          width: 50px;
          text-align: center;
        }
        .content-table .content-col {
          word-wrap: break-word;
          word-break: break-all;
        }
      </style>
    </head>
    <body>
      <div class="title">${title}</div>
      <table class="content-table">
        <thead>
          <tr>
            <th class="index-col">序号</th>
            <th class="content-col">内容</th>
          </tr>
        </thead>
        <tbody>
          ${formattedLines
            .map(
              (line, index) => `
            <tr>
              <td class="index-col">${index + 1}</td>
              <td class="content-col">${line}</td>
            </tr>
          `
            )
            .join("")}
        </tbody>
      </table>
    </body>
    </html>
  `;

  return htmlContent;
}

/**
 * 从configJson中提取原始内容
 * @param {Object} configJson - 内容配置JSON
 * @returns {Array} 原始内容数组
 */
export function extractOriginalContent(configJson) {
  const originalContent = [];

  if (!configJson || !configJson.nodes) {
    console.error("无效的配置JSON:", configJson);
    return originalContent;
  }

  // 遍历所有textContent节点
  Object.values(configJson.nodes)
    .filter((node) => node.type === "textContent")
    .forEach((node) => {
      if (node.params?.segments) {
        node.params.segments.forEach((segment) => {
          // 检查是否有有效的角色信息
          const hasValidSpeaker =
            segment.speaker &&
            segment.speaker !== "default" &&
            segment.speaker.trim() !== "";

          originalContent.push({
            content: segment.content,
            speaker: hasValidSpeaker ? segment.speaker : null,
            language: segment.language || "ja",
          });
        });
      }
    });

  return originalContent;
}

/**
 * 格式化内容为PDF文本
 * @param {Array} originalContent - 原始内容数组
 * @returns {Array} 格式化后的文本行数组
 */
export function formatContentForPDF(originalContent) {
  return originalContent.map((item) => {
    if (item.speaker) {
      return `${item.speaker}：${item.content}`;
    } else {
      return item.content;
    }
  });
}

/**
 * 导出原文内容为PDF文件
 * @param {Object} configJson - 内容配置JSON
 * @param {string} contentTitle - 内容标题
 */
export async function exportOriginalContentToPDF(configJson, contentTitle) {
  try {
    // 提取原始内容
    const originalContent = extractOriginalContent(configJson);

    if (originalContent.length === 0) {
      throw new Error("没有找到可导出的原文内容");
    }

    // 格式化内容
    const formattedLines = formatContentForPDF(originalContent);
    const title = contentTitle || "原文内容";

    // 创建HTML内容
    const htmlContent = await createPDFFromHTML(formattedLines, title);

    // 使用浏览器的打印功能来生成PDF
    // 创建一个新窗口来显示内容并打印
    const printWindow = window.open("", "_blank");
    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // 等待内容加载完成
    await new Promise((resolve) => {
      printWindow.onload = resolve;
      setTimeout(resolve, 1000); // 备用超时
    });

    // 触发打印对话框
    printWindow.print();

    // 关闭窗口（用户可以选择取消）
    setTimeout(() => {
      printWindow.close();
    }, 1000);

    return {
      success: true,
      message: "请在打印对话框中选择'另存为PDF'来保存文件",
      fileName: `${title}.pdf`,
    };
  } catch (error) {
    console.error("PDF导出失败:", error);
    throw new Error(`PDF导出失败: ${error.message}`);
  }
}
