/**
 * 原文内容导出工具
 * 用于将原文内容导出为PDF文件，支持中文和日文字符
 */

/**
 * 从configJson中提取原始内容
 * @param {Object} configJson - 内容配置JSON
 * @returns {Array} 原始内容数组
 */
export function extractOriginalContent(configJson) {
  const originalContent = [];

  if (!configJson || !configJson.nodes) {
    console.error("无效的配置JSON:", configJson);
    return originalContent;
  }

  // 遍历所有textContent节点
  Object.values(configJson.nodes)
    .filter((node) => node.type === "textContent")
    .forEach((node) => {
      if (node.params?.segments) {
        node.params.segments.forEach((segment) => {
          // 检查是否有有效的角色信息
          const hasValidSpeaker =
            segment.speaker &&
            segment.speaker !== "default" &&
            segment.speaker.trim() !== "";

          originalContent.push({
            content: segment.content,
            speaker: hasValidSpeaker ? segment.speaker : null,
            language: segment.language || "ja",
          });
        });
      }
    });

  return originalContent;
}

/**
 * 格式化内容为PDF文本
 * @param {Array} originalContent - 原始内容数组
 * @returns {Array} 格式化后的文本行数组
 */
export function formatContentForPDF(originalContent) {
  return originalContent.map((item) => {
    if (item.speaker) {
      return `${item.speaker}：${item.content}`;
    } else {
      return item.content;
    }
  });
}

/**
 * 导出原文内容为PDF文件
 * @param {Object} configJson - 内容配置JSON
 * @param {string} contentTitle - 内容标题
 */
export async function exportOriginalContentToPDF(configJson, contentTitle) {
  try {
    // 动态导入html2pdf
    const html2pdf = (await import("html2pdf.js")).default;

    // 提取原始内容
    const originalContent = extractOriginalContent(configJson);

    if (originalContent.length === 0) {
      throw new Error("没有找到可导出的原文内容");
    }

    // 格式化内容
    const formattedLines = formatContentForPDF(originalContent);
    const title = contentTitle || "原文内容";

    // 创建临时DOM元素
    const tempDiv = document.createElement("div");
    tempDiv.style.position = "absolute";
    tempDiv.style.left = "-9999px";
    tempDiv.style.top = "-9999px";
    tempDiv.style.width = "210mm"; // A4宽度
    tempDiv.style.backgroundColor = "white";
    tempDiv.style.fontFamily =
      '"Microsoft YaHei", "SimSun", "Arial Unicode MS", sans-serif';
    tempDiv.style.fontSize = "14px";
    tempDiv.style.lineHeight = "1.6";
    tempDiv.style.padding = "20px";
    tempDiv.style.color = "#333";

    // 创建标题
    const titleDiv = document.createElement("div");
    titleDiv.style.fontSize = "18px";
    titleDiv.style.fontWeight = "bold";
    titleDiv.style.textAlign = "center";
    titleDiv.style.marginBottom = "30px";
    titleDiv.style.borderBottom = "2px solid #333";
    titleDiv.style.paddingBottom = "10px";
    titleDiv.textContent = title;
    tempDiv.appendChild(titleDiv);

    // 创建表格
    const table = document.createElement("table");
    table.style.width = "100%";
    table.style.borderCollapse = "collapse";
    table.style.marginTop = "20px";

    // 创建表头
    const thead = document.createElement("thead");
    const headerRow = document.createElement("tr");

    const indexHeader = document.createElement("th");
    indexHeader.textContent = "序号";
    indexHeader.style.border = "1px solid #ddd";
    indexHeader.style.padding = "8px";
    indexHeader.style.backgroundColor = "#f5f5f5";
    indexHeader.style.fontWeight = "bold";
    indexHeader.style.textAlign = "center";
    indexHeader.style.width = "50px";

    const contentHeader = document.createElement("th");
    contentHeader.textContent = "内容";
    contentHeader.style.border = "1px solid #ddd";
    contentHeader.style.padding = "8px";
    contentHeader.style.backgroundColor = "#f5f5f5";
    contentHeader.style.fontWeight = "bold";
    contentHeader.style.textAlign = "center";

    headerRow.appendChild(indexHeader);
    headerRow.appendChild(contentHeader);
    thead.appendChild(headerRow);
    table.appendChild(thead);

    // 创建表体
    const tbody = document.createElement("tbody");
    formattedLines.forEach((line, index) => {
      const row = document.createElement("tr");

      const indexCell = document.createElement("td");
      indexCell.textContent = (index + 1).toString();
      indexCell.style.border = "1px solid #ddd";
      indexCell.style.padding = "8px";
      indexCell.style.textAlign = "center";
      indexCell.style.width = "50px";

      const contentCell = document.createElement("td");
      contentCell.textContent = line;
      contentCell.style.border = "1px solid #ddd";
      contentCell.style.padding = "8px";
      contentCell.style.wordWrap = "break-word";
      contentCell.style.wordBreak = "break-all";

      row.appendChild(indexCell);
      row.appendChild(contentCell);
      tbody.appendChild(row);
    });

    table.appendChild(tbody);
    tempDiv.appendChild(table);
    document.body.appendChild(tempDiv);

    // 等待一下让DOM完全渲染
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 配置html2pdf选项
    const options = {
      margin: 0.5,
      filename: `${title}.pdf`,
      image: { type: "jpeg", quality: 0.98 },
      html2canvas: {
        scale: 1,
        useCORS: true,
        allowTaint: true,
        letterRendering: true,
      },
      jsPDF: {
        unit: "in",
        format: "a4",
        orientation: "portrait",
      },
    };

    // 生成并下载PDF
    await html2pdf().set(options).from(tempDiv).save();

    // 清理临时元素
    if (document.body.contains(tempDiv)) {
      document.body.removeChild(tempDiv);
    }

    return {
      success: true,
      message: "PDF导出成功",
      fileName: `${title}.pdf`,
    };
  } catch (error) {
    console.error("PDF导出失败:", error);
    throw new Error(`PDF导出失败: ${error.message}`);
  }
}
