/**
 * PDF导出工具
 * 用于将原文内容导出为PDF文件
 */

/**
 * 从configJson中提取原始内容
 * @param {Object} configJson - 内容配置JSON
 * @returns {Array} 原始内容数组
 */
export function extractOriginalContent(configJson) {
  const originalContent = [];
  
  if (!configJson || !configJson.nodes) {
    console.error('无效的配置JSON:', configJson);
    return originalContent;
  }

  // 遍历所有textContent节点
  Object.values(configJson.nodes)
    .filter(node => node.type === 'textContent')
    .forEach(node => {
      if (node.params?.segments) {
        node.params.segments.forEach(segment => {
          // 检查是否有有效的角色信息
          const hasValidSpeaker = segment.speaker && 
                                 segment.speaker !== 'default' && 
                                 segment.speaker.trim() !== '';
          
          originalContent.push({
            content: segment.content,
            speaker: hasValidSpeaker ? segment.speaker : null,
            language: segment.language || 'ja'
          });
        });
      }
    });
  
  return originalContent;
}

/**
 * 格式化内容为PDF文本
 * @param {Array} originalContent - 原始内容数组
 * @returns {Array} 格式化后的文本行数组
 */
export function formatContentForPDF(originalContent) {
  return originalContent.map(item => {
    if (item.speaker) {
      return `${item.speaker}：${item.content}`;
    } else {
      return item.content;
    }
  });
}

/**
 * 导出原文内容为PDF
 * @param {Object} configJson - 内容配置JSON
 * @param {string} contentTitle - 内容标题
 */
export async function exportOriginalContentToPDF(configJson, contentTitle) {
  try {
    // 动态导入jsPDF
    const { jsPDF } = await import('jspdf');
    
    // 提取原始内容
    const originalContent = extractOriginalContent(configJson);
    
    if (originalContent.length === 0) {
      throw new Error('没有找到可导出的原文内容');
    }
    
    // 格式化内容
    const formattedLines = formatContentForPDF(originalContent);
    
    // 创建PDF文档
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });
    
    // 设置字体（支持中文）
    // 注意：jsPDF默认不支持中文，这里先使用默认字体
    // 如果需要中文支持，需要额外配置字体文件
    doc.setFont('helvetica');
    doc.setFontSize(12);
    
    // 添加标题
    doc.setFontSize(16);
    doc.text(contentTitle || '原文内容', 20, 20);
    
    // 添加内容
    doc.setFontSize(12);
    let yPosition = 40;
    const lineHeight = 8;
    const pageHeight = 280; // A4页面高度减去边距
    const leftMargin = 20;
    const rightMargin = 190; // A4宽度减去右边距
    
    formattedLines.forEach((line, index) => {
      // 检查是否需要换页
      if (yPosition > pageHeight) {
        doc.addPage();
        yPosition = 20;
      }
      
      // 处理长文本换行
      const splitText = doc.splitTextToSize(line, rightMargin - leftMargin);
      
      // 检查分割后的文本是否会超出页面
      if (yPosition + (splitText.length * lineHeight) > pageHeight) {
        doc.addPage();
        yPosition = 20;
      }
      
      // 添加文本
      doc.text(splitText, leftMargin, yPosition);
      yPosition += splitText.length * lineHeight + 2; // 额外间距
    });
    
    // 生成文件名
    const fileName = `${contentTitle || '原文内容'}.pdf`;
    
    // 下载PDF
    doc.save(fileName);
    
    return {
      success: true,
      message: 'PDF导出成功',
      fileName
    };
    
  } catch (error) {
    console.error('PDF导出失败:', error);
    throw new Error(`PDF导出失败: ${error.message}`);
  }
}
