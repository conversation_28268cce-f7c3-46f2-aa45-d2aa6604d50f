/**
 * 原文内容导出工具
 * 用于将原文内容导出为文本文件（由于字体支持问题，暂时使用文本格式而非PDF）
 */

/**
 * 从configJson中提取原始内容
 * @param {Object} configJson - 内容配置JSON
 * @returns {Array} 原始内容数组
 */
export function extractOriginalContent(configJson) {
  const originalContent = [];

  if (!configJson || !configJson.nodes) {
    console.error("无效的配置JSON:", configJson);
    return originalContent;
  }

  // 遍历所有textContent节点
  Object.values(configJson.nodes)
    .filter((node) => node.type === "textContent")
    .forEach((node) => {
      if (node.params?.segments) {
        node.params.segments.forEach((segment) => {
          // 检查是否有有效的角色信息
          const hasValidSpeaker =
            segment.speaker &&
            segment.speaker !== "default" &&
            segment.speaker.trim() !== "";

          originalContent.push({
            content: segment.content,
            speaker: hasValidSpeaker ? segment.speaker : null,
            language: segment.language || "ja",
          });
        });
      }
    });

  return originalContent;
}

/**
 * 格式化内容为PDF文本
 * @param {Array} originalContent - 原始内容数组
 * @returns {Array} 格式化后的文本行数组
 */
export function formatContentForPDF(originalContent) {
  return originalContent.map((item) => {
    if (item.speaker) {
      return `${item.speaker}：${item.content}`;
    } else {
      return item.content;
    }
  });
}

/**
 * 导出原文内容为文本文件
 * @param {Object} configJson - 内容配置JSON
 * @param {string} contentTitle - 内容标题
 */
export async function exportOriginalContentToPDF(configJson, contentTitle) {
  try {
    // 动态导入jsPDF
    const { jsPDF } = await import("jspdf");

    // 提取原始内容
    const originalContent = extractOriginalContent(configJson);

    if (originalContent.length === 0) {
      throw new Error("没有找到可导出的原文内容");
    }

    // 格式化内容
    const formattedLines = formatContentForPDF(originalContent);

    // 创建PDF文档
    const doc = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    });

    // 由于jsPDF默认不支持中文和日文，我们使用简单的文本导出方式
    // 创建一个简单的文本内容，然后提示用户复制
    const textContent = [
      contentTitle || "原文内容",
      "",
      ...formattedLines,
    ].join("\n");

    // 创建一个临时的文本文件下载
    const blob = new Blob([textContent], { type: "text/plain;charset=utf-8" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `${contentTitle || "原文内容"}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    return {
      success: true,
      message: "原文内容已导出为文本文件",
      fileName: `${contentTitle || "原文内容"}.txt`,
    };
  } catch (error) {
    console.error("文本导出失败:", error);
    throw new Error(`文本导出失败: ${error.message}`);
  }
}
