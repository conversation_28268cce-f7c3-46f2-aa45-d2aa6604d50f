/**
 * 原文内容导出工具
 * 用于将原文内容导出为PDF文件，支持中文和日文字符
 */

/**
 * 从configJson中提取原始内容
 * @param {Object} configJson - 内容配置JSON
 * @returns {Array} 原始内容数组
 */
export function extractOriginalContent(configJson) {
  const originalContent = [];

  if (!configJson || !configJson.nodes) {
    console.error("无效的配置JSON:", configJson);
    return originalContent;
  }

  // 遍历所有textContent节点
  Object.values(configJson.nodes)
    .filter((node) => node.type === "textContent")
    .forEach((node) => {
      if (node.params?.segments) {
        node.params.segments.forEach((segment) => {
          // 检查是否有有效的角色信息
          const hasValidSpeaker =
            segment.speaker &&
            segment.speaker !== "default" &&
            segment.speaker.trim() !== "";

          originalContent.push({
            content: segment.content,
            speaker: hasValidSpeaker ? segment.speaker : null,
            language: segment.language || "ja",
          });
        });
      }
    });

  return originalContent;
}

/**
 * 格式化内容为PDF文本
 * @param {Array} originalContent - 原始内容数组
 * @returns {Array} 格式化后的文本行数组
 */
export function formatContentForPDF(originalContent) {
  return originalContent.map((item) => {
    if (item.speaker) {
      return `${item.speaker}：${item.content}`;
    } else {
      return item.content;
    }
  });
}

/**
 * 导出原文内容为PDF文件
 * @param {Object} configJson - 内容配置JSON
 * @param {string} contentTitle - 内容标题
 */
export async function exportOriginalContentToPDF(configJson, contentTitle) {
  try {
    // 动态导入html2pdf
    const html2pdf = (await import("html2pdf.js")).default;

    // 提取原始内容
    const originalContent = extractOriginalContent(configJson);

    if (originalContent.length === 0) {
      throw new Error("没有找到可导出的原文内容");
    }

    // 格式化内容
    const formattedLines = formatContentForPDF(originalContent);

    const title = contentTitle || "原文内容";

    // 创建HTML内容字符串
    const htmlContent = `
      <div style="
        position: absolute;
        left: -9999px;
        top: -9999px;
        width: 210mm;
        background-color: white;
        font-family: 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', sans-serif;
        font-size: 14px;
        line-height: 1.6;
        padding: 20px;
        color: #333;
        z-index: -1;
      ">
        <div style="
          font-size: 18px;
          font-weight: bold;
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 2px solid #333;
          padding-bottom: 10px;
        ">${title}</div>

        <table style="
          width: 100%;
          border-collapse: collapse;
          margin-top: 20px;
        ">
          <thead>
            <tr>
              <th style="
                border: 1px solid #ddd;
                padding: 8px;
                background-color: #f5f5f5;
                font-weight: bold;
                text-align: center;
                width: 50px;
              ">序号</th>
              <th style="
                border: 1px solid #ddd;
                padding: 8px;
                background-color: #f5f5f5;
                font-weight: bold;
                text-align: center;
              ">内容</th>
            </tr>
          </thead>
          <tbody>
            ${formattedLines
              .map(
                (line, index) => `
              <tr>
                <td style="
                  border: 1px solid #ddd;
                  padding: 6px;
                  text-align: center;
                  width: 50px;
                  font-size: 12px;
                ">${index + 1}</td>
                <td style="
                  border: 1px solid #ddd;
                  padding: 6px;
                  word-wrap: break-word;
                  word-break: break-all;
                  font-size: 12px;
                  line-height: 1.4;
                ">${line}</td>
              </tr>
            `
              )
              .join("")}
          </tbody>
        </table>
      </div>
    `;

    // 创建临时DOM元素
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = htmlContent;
    document.body.appendChild(tempDiv);

    // 等待DOM完全渲染
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 配置html2pdf选项 - 优化自动分页
    const options = {
      margin: [0.4, 0.4, 0.4, 0.4], // 上右下左边距
      filename: `${title}.pdf`,
      image: {
        type: "jpeg",
        quality: 0.9,
      },
      html2canvas: {
        scale: 1,
        useCORS: true,
        allowTaint: true,
        letterRendering: true,
        scrollX: 0,
        scrollY: 0,
      },
      jsPDF: {
        unit: "in",
        format: "a4",
        orientation: "portrait",
      },
      pagebreak: {
        mode: ["avoid-all"],
        before: ".page-break-before",
        after: ".page-break-after",
        avoid: "tr",
      },
    };

    // 生成并下载PDF
    const targetElement = tempDiv.firstElementChild;
    await html2pdf()
      .set(options)
      .from(targetElement || tempDiv)
      .save();

    // 清理临时元素
    if (document.body.contains(tempDiv)) {
      document.body.removeChild(tempDiv);
    }

    return {
      success: true,
      message: "PDF导出成功",
      fileName: `${title}.pdf`,
    };
  } catch (error) {
    console.error("PDF导出失败:", error);
    throw new Error(`PDF导出失败: ${error.message}`);
  }
}
