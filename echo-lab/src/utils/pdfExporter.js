/**
 * 原文内容导出工具
 * 用于将原文内容导出为PDF文件，支持中文和日文字符
 */

/**
 * 创建进度弹框
 * @returns {Object} 包含弹框元素和更新方法的对象
 */
function createProgressDialog() {
  // 创建遮罩层
  const overlay = document.createElement("div");
  overlay.style.position = "fixed";
  overlay.style.top = "0";
  overlay.style.left = "0";
  overlay.style.width = "100%";
  overlay.style.height = "100%";
  overlay.style.backgroundColor = "rgba(0, 0, 0, 0.5)";
  overlay.style.zIndex = "99999";
  overlay.style.display = "flex";
  overlay.style.alignItems = "center";
  overlay.style.justifyContent = "center";

  // 创建弹框
  const dialog = document.createElement("div");
  dialog.style.backgroundColor = "white";
  dialog.style.borderRadius = "12px";
  dialog.style.padding = "30px";
  dialog.style.minWidth = "300px";
  dialog.style.textAlign = "center";
  dialog.style.boxShadow = "0 4px 20px rgba(0, 0, 0, 0.3)";
  dialog.style.fontFamily = "Arial, sans-serif";

  // 创建标题
  const title = document.createElement("div");
  title.style.fontSize = "18px";
  title.style.fontWeight = "bold";
  title.style.marginBottom = "20px";
  title.style.color = "#333";
  title.textContent = "正在生成PDF文件";

  // 创建进度条容器
  const progressContainer = document.createElement("div");
  progressContainer.style.width = "100%";
  progressContainer.style.height = "8px";
  progressContainer.style.backgroundColor = "#f0f0f0";
  progressContainer.style.borderRadius = "4px";
  progressContainer.style.marginBottom = "15px";
  progressContainer.style.overflow = "hidden";

  // 创建进度条
  const progressBar = document.createElement("div");
  progressBar.style.height = "100%";
  progressBar.style.backgroundColor = "#1890ff";
  progressBar.style.borderRadius = "4px";
  progressBar.style.width = "0%";
  progressBar.style.transition = "width 0.3s ease";

  // 创建进度文本
  const progressText = document.createElement("div");
  progressText.style.fontSize = "14px";
  progressText.style.color = "#666";
  progressText.textContent = "准备中...";

  // 组装弹框
  progressContainer.appendChild(progressBar);
  dialog.appendChild(title);
  dialog.appendChild(progressContainer);
  dialog.appendChild(progressText);
  overlay.appendChild(dialog);
  document.body.appendChild(overlay);

  return {
    element: overlay,
    updateProgress: (current, total, message) => {
      const percentage = Math.round((current / total) * 100);
      progressBar.style.width = `${percentage}%`;
      progressText.textContent =
        message || `正在处理第 ${current} 页，共 ${total} 页...`;
    },
    hide: () => {
      if (document.body.contains(overlay)) {
        document.body.removeChild(overlay);
      }
    },
  };
}

/**
 * 从configJson中提取原始内容
 * @param {Object} configJson - 内容配置JSON
 * @returns {Array} 原始内容数组
 */
export function extractOriginalContent(configJson) {
  const originalContent = [];

  if (!configJson || !configJson.nodes) {
    console.error("无效的配置JSON:", configJson);
    return originalContent;
  }

  // 遍历所有textContent节点
  Object.values(configJson.nodes)
    .filter((node) => node.type === "textContent")
    .forEach((node) => {
      if (node.params?.segments) {
        node.params.segments.forEach((segment) => {
          // 检查是否有有效的角色信息
          const hasValidSpeaker =
            segment.speaker &&
            segment.speaker !== "default" &&
            segment.speaker.trim() !== "";

          originalContent.push({
            content: segment.content,
            speaker: hasValidSpeaker ? segment.speaker : null,
            language: segment.language || "ja",
          });
        });
      }
    });

  return originalContent;
}

/**
 * 格式化内容为PDF文本
 * @param {Array} originalContent - 原始内容数组
 * @returns {Array} 格式化后的文本行数组
 */
export function formatContentForPDF(originalContent) {
  return originalContent.map((item) => {
    if (item.speaker) {
      return `${item.speaker}：${item.content}`;
    } else {
      return item.content;
    }
  });
}

/**
 * 导出原文内容为PDF文件
 * @param {Object} configJson - 内容配置JSON
 * @param {string} contentTitle - 内容标题
 */
export async function exportOriginalContentToPDF(configJson, contentTitle) {
  // 创建进度弹框
  const progressDialog = createProgressDialog();

  try {
    // 动态导入jsPDF和html2canvas
    const [{ jsPDF }, html2canvas] = await Promise.all([
      import("jspdf"),
      import("html2canvas"),
    ]);

    // 提取原始内容
    const originalContent = extractOriginalContent(configJson);

    if (originalContent.length === 0) {
      throw new Error("没有找到可导出的原文内容");
    }

    // 格式化内容
    const formattedLines = formatContentForPDF(originalContent);
    const title = contentTitle || "原文内容";

    // 创建临时DOM元素 - 隐藏显示
    const tempDiv = document.createElement("div");
    tempDiv.style.position = "fixed";
    tempDiv.style.left = "-9999px"; // 隐藏在屏幕外
    tempDiv.style.top = "-9999px";
    tempDiv.style.width = "800px"; // 固定像素宽度
    tempDiv.style.backgroundColor = "white";
    tempDiv.style.fontFamily = "Arial, sans-serif"; // 使用通用字体
    tempDiv.style.fontSize = "14px";
    tempDiv.style.lineHeight = "1.6";
    tempDiv.style.padding = "20px";
    tempDiv.style.color = "#333";
    tempDiv.style.zIndex = "-1"; // 确保不显示

    // 设置HTML内容
    tempDiv.innerHTML = `
      <div style="
        font-size: 18px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #333;
        padding-bottom: 10px;
      ">${title}</div>

      <table style="
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
      ">
        <thead>
          <tr>
            <th style="
              border: 1px solid #ddd;
              padding: 8px;
              background-color: #f5f5f5;
              font-weight: bold;
              text-align: center;
              width: 50px;
            ">序号</th>
            <th style="
              border: 1px solid #ddd;
              padding: 8px;
              background-color: #f5f5f5;
              font-weight: bold;
              text-align: center;
            ">内容</th>
          </tr>
        </thead>
        <tbody>
          ${formattedLines
            .map(
              (line, index) => `
            <tr>
              <td style="
                border: 1px solid #ddd;
                padding: 6px;
                text-align: center;
                width: 50px;
                font-size: 12px;
              ">${index + 1}</td>
              <td style="
                border: 1px solid #ddd;
                padding: 6px;
                word-wrap: break-word;
                word-break: break-all;
                font-size: 12px;
                line-height: 1.4;
              ">${line}</td>
            </tr>
          `
            )
            .join("")}
        </tbody>
      </table>
    `;

    document.body.appendChild(tempDiv);

    // 等待DOM完全渲染
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 在生成PDF前将元素移到可见位置但保持隐藏
    tempDiv.style.left = "0px";
    tempDiv.style.top = "0px";
    tempDiv.style.zIndex = "10000";
    tempDiv.style.opacity = "0"; // 完全透明

    // 创建jsPDF实例
    const doc = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    });

    // 计算分页参数
    const pageHeight = 257; // A4高度减去上下边距 (297-20-20)
    const pageWidth = 190; // A4宽度减去左右边距
    const pixelPageHeight = (pageHeight * 800) / pageWidth; // 转换为像素高度

    // 获取所有表格行，用于智能分页
    const tableRows = tempDiv.querySelectorAll("tbody tr");
    const titleElement = tempDiv.querySelector("div"); // 标题元素
    const titleHeight = titleElement ? titleElement.offsetHeight : 0;
    const tableHeaderHeight = tempDiv.querySelector("thead")
      ? tempDiv.querySelector("thead").offsetHeight
      : 0;

    // 计算每行的累积高度
    let currentPageStartRow = 0;
    let currentY = titleHeight + tableHeaderHeight + 40; // 标题 + 表头 + 边距
    const pages = [];

    for (let i = 0; i < tableRows.length; i++) {
      const rowHeight = tableRows[i].offsetHeight;

      // 如果添加这一行会超出页面高度，则开始新页面
      if (currentY + rowHeight > pixelPageHeight && i > currentPageStartRow) {
        pages.push({
          startRow: currentPageStartRow,
          endRow: i - 1,
          height: currentY - rowHeight,
        });
        currentPageStartRow = i;
        currentY = tableHeaderHeight + rowHeight + 40; // 新页面从表头开始
      } else {
        currentY += rowHeight;
      }
    }

    // 添加最后一页
    if (currentPageStartRow < tableRows.length) {
      pages.push({
        startRow: currentPageStartRow,
        endRow: tableRows.length - 1,
        height: currentY,
      });
    }

    // 更新进度：开始处理
    progressDialog.updateProgress(0, pages.length, "开始处理页面...");

    // 逐页渲染
    for (let pageIndex = 0; pageIndex < pages.length; pageIndex++) {
      // 更新进度
      progressDialog.updateProgress(pageIndex + 1, pages.length);

      if (pageIndex > 0) {
        doc.addPage();
      }

      const page = pages[pageIndex];

      // 创建当前页面的临时容器 - 隐藏显示
      const pageDiv = document.createElement("div");
      pageDiv.style.position = "fixed";
      pageDiv.style.left = "-9999px"; // 隐藏在屏幕外
      pageDiv.style.top = "-9999px";
      pageDiv.style.width = "800px";
      pageDiv.style.backgroundColor = "white";
      pageDiv.style.fontFamily = "Arial, sans-serif";
      pageDiv.style.fontSize = "14px";
      pageDiv.style.lineHeight = "1.6";
      pageDiv.style.padding = "20px";
      pageDiv.style.color = "#333";
      pageDiv.style.zIndex = "-1"; // 确保不显示

      // 创建页面内容
      let pageContent = "";

      // 第一页包含标题
      if (pageIndex === 0) {
        pageContent += `
          <div style="
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
          ">${title}</div>
        `;
      }

      // 添加表格
      pageContent += `
        <table style="
          width: 100%;
          border-collapse: collapse;
          margin-top: ${pageIndex === 0 ? "20px" : "0px"};
        ">
          <thead>
            <tr>
              <th style="
                border: 1px solid #ddd;
                padding: 8px;
                background-color: #f5f5f5;
                font-weight: bold;
                text-align: center;
                width: 50px;
              ">序号</th>
              <th style="
                border: 1px solid #ddd;
                padding: 8px;
                background-color: #f5f5f5;
                font-weight: bold;
                text-align: center;
              ">内容</th>
            </tr>
          </thead>
          <tbody>
      `;

      // 添加当前页面的行
      for (let rowIndex = page.startRow; rowIndex <= page.endRow; rowIndex++) {
        const line = formattedLines[rowIndex];
        pageContent += `
          <tr>
            <td style="
              border: 1px solid #ddd;
              padding: 6px;
              text-align: center;
              width: 50px;
              font-size: 12px;
            ">${rowIndex + 1}</td>
            <td style="
              border: 1px solid #ddd;
              padding: 6px;
              word-wrap: break-word;
              word-break: break-all;
              font-size: 12px;
              line-height: 1.4;
            ">${line}</td>
          </tr>
        `;
      }

      pageContent += `
          </tbody>
        </table>
      `;

      pageDiv.innerHTML = pageContent;
      document.body.appendChild(pageDiv);

      // 等待渲染
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 渲染当前页面
      const pageCanvas = await html2canvas.default(pageDiv, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
        width: 800,
        height: pageDiv.scrollHeight,
      });

      // 添加到PDF
      const imgData = pageCanvas.toDataURL("image/jpeg", 0.9);
      const imgHeight = (pageCanvas.height * pageWidth) / (800 * 2); // 考虑scale=2
      doc.addImage(imgData, "JPEG", 10, 20, pageWidth, imgHeight);

      // 清理临时元素
      document.body.removeChild(pageDiv);
    }

    // 更新进度：保存文件
    progressDialog.updateProgress(
      pages.length,
      pages.length,
      "正在保存PDF文件..."
    );

    // 保存PDF
    doc.save(`${title}.pdf`);

    // 清理临时元素
    if (document.body.contains(tempDiv)) {
      document.body.removeChild(tempDiv);
    }

    // 隐藏进度弹框
    progressDialog.hide();

    return {
      success: true,
      message: "PDF导出成功",
      fileName: `${title}.pdf`,
    };
  } catch (error) {
    // 隐藏进度弹框
    progressDialog.hide();

    console.error("PDF导出失败:", error);
    throw new Error(`PDF导出失败: ${error.message}`);
  }
}
