/**
 * 原文内容导出工具
 * 用于将原文内容导出为PDF文件，支持中文和日文字符
 */

/**
 * 从configJson中提取原始内容
 * @param {Object} configJson - 内容配置JSON
 * @returns {Array} 原始内容数组
 */
export function extractOriginalContent(configJson) {
  const originalContent = [];

  if (!configJson || !configJson.nodes) {
    console.error("无效的配置JSON:", configJson);
    return originalContent;
  }

  // 遍历所有textContent节点
  Object.values(configJson.nodes)
    .filter((node) => node.type === "textContent")
    .forEach((node) => {
      if (node.params?.segments) {
        node.params.segments.forEach((segment) => {
          // 检查是否有有效的角色信息
          const hasValidSpeaker =
            segment.speaker &&
            segment.speaker !== "default" &&
            segment.speaker.trim() !== "";

          originalContent.push({
            content: segment.content,
            speaker: hasValidSpeaker ? segment.speaker : null,
            language: segment.language || "ja",
          });
        });
      }
    });

  return originalContent;
}

/**
 * 格式化内容为PDF文本
 * @param {Array} originalContent - 原始内容数组
 * @returns {Array} 格式化后的文本行数组
 */
export function formatContentForPDF(originalContent) {
  return originalContent.map((item) => {
    if (item.speaker) {
      return `${item.speaker}：${item.content}`;
    } else {
      return item.content;
    }
  });
}

/**
 * 导出原文内容为PDF文件
 * @param {Object} configJson - 内容配置JSON
 * @param {string} contentTitle - 内容标题
 */
export async function exportOriginalContentToPDF(configJson, contentTitle) {
  try {
    // 动态导入jsPDF和html2canvas
    const [{ jsPDF }, html2canvas] = await Promise.all([
      import("jspdf"),
      import("html2canvas"),
    ]);

    // 提取原始内容
    const originalContent = extractOriginalContent(configJson);

    if (originalContent.length === 0) {
      throw new Error("没有找到可导出的原文内容");
    }

    // 格式化内容
    const formattedLines = formatContentForPDF(originalContent);
    const title = contentTitle || "原文内容";

    // 创建临时DOM元素 - 使用html2canvas方案
    const tempDiv = document.createElement("div");
    tempDiv.style.position = "fixed";
    tempDiv.style.left = "0px";
    tempDiv.style.top = "0px";
    tempDiv.style.width = "800px"; // 固定像素宽度
    tempDiv.style.backgroundColor = "white";
    tempDiv.style.fontFamily = "Arial, sans-serif"; // 使用通用字体
    tempDiv.style.fontSize = "14px";
    tempDiv.style.lineHeight = "1.6";
    tempDiv.style.padding = "20px";
    tempDiv.style.color = "#333";
    tempDiv.style.zIndex = "10000";
    tempDiv.style.opacity = "0.1";

    // 设置HTML内容
    tempDiv.innerHTML = `
      <div style="
        font-size: 18px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #333;
        padding-bottom: 10px;
      ">${title}</div>

      <table style="
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
      ">
        <thead>
          <tr>
            <th style="
              border: 1px solid #ddd;
              padding: 8px;
              background-color: #f5f5f5;
              font-weight: bold;
              text-align: center;
              width: 50px;
            ">序号</th>
            <th style="
              border: 1px solid #ddd;
              padding: 8px;
              background-color: #f5f5f5;
              font-weight: bold;
              text-align: center;
            ">内容</th>
          </tr>
        </thead>
        <tbody>
          ${formattedLines
            .map(
              (line, index) => `
            <tr>
              <td style="
                border: 1px solid #ddd;
                padding: 6px;
                text-align: center;
                width: 50px;
                font-size: 12px;
              ">${index + 1}</td>
              <td style="
                border: 1px solid #ddd;
                padding: 6px;
                word-wrap: break-word;
                word-break: break-all;
                font-size: 12px;
                line-height: 1.4;
              ">${line}</td>
            </tr>
          `
            )
            .join("")}
        </tbody>
      </table>
    `;

    document.body.appendChild(tempDiv);

    // 等待DOM完全渲染
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 在生成PDF前将元素设为完全可见
    tempDiv.style.opacity = "1";

    // 创建jsPDF实例
    const doc = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    });

    // 计算分页参数
    const pageHeight = 257; // A4高度减去上下边距 (297-20-20)
    const pageWidth = 190; // A4宽度减去左右边距
    const pixelPageHeight = (pageHeight * 800) / pageWidth; // 转换为像素高度

    // 获取总内容高度
    const totalHeight = tempDiv.scrollHeight;
    const totalPages = Math.ceil(totalHeight / pixelPageHeight);

    // 逐页渲染
    for (let pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      if (pageIndex > 0) {
        doc.addPage();
      }

      // 计算当前页面的渲染区域
      const startY = pageIndex * pixelPageHeight;
      const endY = Math.min(startY + pixelPageHeight, totalHeight);
      const currentPageHeight = endY - startY;

      // 创建当前页面的临时容器
      const pageDiv = document.createElement("div");
      pageDiv.style.position = "fixed";
      pageDiv.style.left = "0px";
      pageDiv.style.top = "0px";
      pageDiv.style.width = "800px";
      pageDiv.style.height = `${currentPageHeight}px`;
      pageDiv.style.backgroundColor = "white";
      pageDiv.style.overflow = "hidden";
      pageDiv.style.zIndex = "10001";

      // 复制内容并调整位置
      const clonedContent = tempDiv.cloneNode(true);
      clonedContent.style.position = "relative";
      clonedContent.style.top = `-${startY}px`;
      clonedContent.style.left = "0px";
      pageDiv.appendChild(clonedContent);
      document.body.appendChild(pageDiv);

      // 等待渲染
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 渲染当前页面
      const pageCanvas = await html2canvas.default(pageDiv, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
        width: 800,
        height: currentPageHeight,
      });

      // 添加到PDF
      const imgData = pageCanvas.toDataURL("image/jpeg", 0.9);
      const imgHeight = (currentPageHeight * pageWidth) / 800;
      doc.addImage(imgData, "JPEG", 10, 20, pageWidth, imgHeight);

      // 清理临时元素
      document.body.removeChild(pageDiv);
    }

    // 保存PDF
    doc.save(`${title}.pdf`);

    // 清理临时元素
    setTimeout(() => {
      if (document.body.contains(tempDiv)) {
        document.body.removeChild(tempDiv);
      }
    }, 100);

    return {
      success: true,
      message: "PDF导出成功",
      fileName: `${title}.pdf`,
    };
  } catch (error) {
    console.error("PDF导出失败:", error);
    throw new Error(`PDF导出失败: ${error.message}`);
  }
}
