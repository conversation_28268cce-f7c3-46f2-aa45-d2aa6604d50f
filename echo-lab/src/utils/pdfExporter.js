/**
 * 原文内容导出工具
 * 用于将原文内容导出为PDF文件，支持中文和日文字符
 */

/**
 * 从configJson中提取原始内容
 * @param {Object} configJson - 内容配置JSON
 * @returns {Array} 原始内容数组
 */
export function extractOriginalContent(configJson) {
  const originalContent = [];

  if (!configJson || !configJson.nodes) {
    console.error("无效的配置JSON:", configJson);
    return originalContent;
  }

  // 遍历所有textContent节点
  Object.values(configJson.nodes)
    .filter((node) => node.type === "textContent")
    .forEach((node) => {
      if (node.params?.segments) {
        node.params.segments.forEach((segment) => {
          // 检查是否有有效的角色信息
          const hasValidSpeaker =
            segment.speaker &&
            segment.speaker !== "default" &&
            segment.speaker.trim() !== "";

          originalContent.push({
            content: segment.content,
            speaker: hasValidSpeaker ? segment.speaker : null,
            language: segment.language || "ja",
          });
        });
      }
    });

  return originalContent;
}

/**
 * 格式化内容为PDF文本
 * @param {Array} originalContent - 原始内容数组
 * @returns {Array} 格式化后的文本行数组
 */
export function formatContentForPDF(originalContent) {
  return originalContent.map((item) => {
    if (item.speaker) {
      return `${item.speaker}：${item.content}`;
    } else {
      return item.content;
    }
  });
}

/**
 * 导出原文内容为PDF文件
 * @param {Object} configJson - 内容配置JSON
 * @param {string} contentTitle - 内容标题
 */
export async function exportOriginalContentToPDF(configJson, contentTitle) {
  try {
    // 动态导入jsPDF和autoTable
    const { jsPDF } = await import("jspdf");
    const { default: autoTable } = await import("jspdf-autotable");

    // 提取原始内容
    const originalContent = extractOriginalContent(configJson);

    if (originalContent.length === 0) {
      throw new Error("没有找到可导出的原文内容");
    }

    // 格式化内容
    const formattedLines = formatContentForPDF(originalContent);

    // 创建PDF文档
    const doc = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    });

    // 设置字体 - 使用内置字体支持基本的中文显示
    doc.setFont("helvetica");

    // 添加标题
    doc.setFontSize(16);
    const title = contentTitle || "原文内容";
    doc.text(title, 20, 20);

    // 准备表格数据 - 使用autoTable来更好地处理文本换行和多语言
    const tableData = formattedLines.map((line, index) => [
      (index + 1).toString(),
      line,
    ]);

    // 使用autoTable生成表格
    autoTable(doc, {
      startY: 35,
      head: [["序号", "内容"]],
      body: tableData,
      styles: {
        font: "helvetica",
        fontSize: 10,
        cellPadding: 3,
        overflow: "linebreak",
        cellWidth: "wrap",
      },
      headStyles: {
        fillColor: [240, 240, 240],
        textColor: [0, 0, 0],
        fontStyle: "bold",
      },
      columnStyles: {
        0: { cellWidth: 15, halign: "center" }, // 序号列
        1: { cellWidth: 160 }, // 内容列
      },
      margin: { left: 20, right: 20 },
      theme: "grid",
    });

    // 生成文件名
    const fileName = `${contentTitle || "原文内容"}.pdf`;

    // 下载PDF
    doc.save(fileName);

    return {
      success: true,
      message: "PDF导出成功",
      fileName,
    };
  } catch (error) {
    console.error("PDF导出失败:", error);
    throw new Error(`PDF导出失败: ${error.message}`);
  }
}
