/**
 * Robots.txt生成器
 * 用于生成网站的robots.txt文件
 */

// 网站基础URL
const BASE_URL = 'https://echolab.club';

/**
 * 生成robots.txt内容
 * @param {Object} options - 配置选项
 * @returns {string} robots.txt内容
 */
export function generateRobotsTxt(options = {}) {
  const {
    allowAll = true,
    disallowPaths = [
      '/admin/*',
      '/api/*',
      '/editor/*',
      '/content',
      '/profile',
      '/settings',
      '/feedback',
      '/favorites',
      '/upgrade',
      '/unauthorized',
      '/mobile-tip'
    ],
    crawlDelay = null,
    sitemapUrl = `${BASE_URL}/sitemap.xml`
  } = options;

  let robotsTxt = '';

  // 主要爬虫规则
  robotsTxt += 'User-agent: *\n';
  
  if (allowAll) {
    // 允许访问根目录
    robotsTxt += 'Allow: /\n';
    
    // 禁止访问特定路径
    disallowPaths.forEach(path => {
      robotsTxt += `Disallow: ${path}\n`;
    });
  } else {
    // 禁止所有访问
    robotsTxt += 'Disallow: /\n';
  }

  // 添加爬取延迟（如果指定）
  if (crawlDelay && crawlDelay > 0) {
    robotsTxt += `Crawl-delay: ${crawlDelay}\n`;
  }

  robotsTxt += '\n';

  // 特定搜索引擎的规则
  
  // Google Bot - 允许访问公开内容
  robotsTxt += 'User-agent: Googlebot\n';
  robotsTxt += 'Allow: /\n';
  robotsTxt += 'Allow: /player/*\n';
  robotsTxt += 'Allow: /content-info\n';
  robotsTxt += 'Allow: /install-guide\n';
  disallowPaths.forEach(path => {
    robotsTxt += `Disallow: ${path}\n`;
  });
  robotsTxt += '\n';

  // Bing Bot
  robotsTxt += 'User-agent: Bingbot\n';
  robotsTxt += 'Allow: /\n';
  robotsTxt += 'Allow: /player/*\n';
  robotsTxt += 'Allow: /content-info\n';
  robotsTxt += 'Allow: /install-guide\n';
  disallowPaths.forEach(path => {
    robotsTxt += `Disallow: ${path}\n`;
  });
  robotsTxt += '\n';

  // 百度爬虫
  robotsTxt += 'User-agent: Baiduspider\n';
  robotsTxt += 'Allow: /\n';
  robotsTxt += 'Allow: /player/*\n';
  robotsTxt += 'Allow: /content-info\n';
  robotsTxt += 'Allow: /install-guide\n';
  disallowPaths.forEach(path => {
    robotsTxt += `Disallow: ${path}\n`;
  });
  robotsTxt += '\n';

  // 禁止一些已知的恶意爬虫
  const badBots = [
    'AhrefsBot',
    'MJ12bot',
    'DotBot',
    'SemrushBot',
    'BLEXBot',
    'MegaIndex',
    'PetalBot'
  ];

  badBots.forEach(bot => {
    robotsTxt += `User-agent: ${bot}\n`;
    robotsTxt += 'Disallow: /\n\n';
  });

  // 添加sitemap链接
  if (sitemapUrl) {
    robotsTxt += `Sitemap: ${sitemapUrl}\n`;
  }

  return robotsTxt;
}

/**
 * 生成开发环境的robots.txt（禁止所有爬虫）
 * @returns {string} robots.txt内容
 */
export function generateDevRobotsTxt() {
  return generateRobotsTxt({
    allowAll: false,
    sitemapUrl: null
  });
}

/**
 * 生成生产环境的robots.txt
 * @returns {string} robots.txt内容
 */
export function generateProdRobotsTxt() {
  return generateRobotsTxt({
    allowAll: true,
    crawlDelay: 1, // 1秒延迟，避免过度爬取
    sitemapUrl: `${BASE_URL}/sitemap.xml`
  });
}

/**
 * 下载robots.txt文件
 * @param {boolean} isProduction - 是否为生产环境
 */
export function downloadRobotsTxt(isProduction = true) {
  const robotsTxt = isProduction ? generateProdRobotsTxt() : generateDevRobotsTxt();
  
  const blob = new Blob([robotsTxt], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'robots.txt';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

/**
 * 验证robots.txt格式
 * @param {string} robotsTxt - robots.txt内容
 * @returns {Object} 验证结果
 */
export function validateRobotsTxt(robotsTxt) {
  const lines = robotsTxt.split('\n');
  const errors = [];
  const warnings = [];
  
  let currentUserAgent = null;
  let hasUserAgent = false;
  let hasSitemap = false;
  
  lines.forEach((line, index) => {
    const trimmedLine = line.trim();
    const lineNumber = index + 1;
    
    // 跳过空行和注释
    if (!trimmedLine || trimmedLine.startsWith('#')) {
      return;
    }
    
    // 检查User-agent指令
    if (trimmedLine.toLowerCase().startsWith('user-agent:')) {
      hasUserAgent = true;
      currentUserAgent = trimmedLine.substring(11).trim();
      
      if (!currentUserAgent) {
        errors.push(`第${lineNumber}行: User-agent值不能为空`);
      }
      return;
    }
    
    // 检查Disallow指令
    if (trimmedLine.toLowerCase().startsWith('disallow:')) {
      if (!currentUserAgent) {
        errors.push(`第${lineNumber}行: Disallow指令必须在User-agent指令之后`);
      }
      return;
    }
    
    // 检查Allow指令
    if (trimmedLine.toLowerCase().startsWith('allow:')) {
      if (!currentUserAgent) {
        errors.push(`第${lineNumber}行: Allow指令必须在User-agent指令之后`);
      }
      return;
    }
    
    // 检查Crawl-delay指令
    if (trimmedLine.toLowerCase().startsWith('crawl-delay:')) {
      if (!currentUserAgent) {
        errors.push(`第${lineNumber}行: Crawl-delay指令必须在User-agent指令之后`);
      } else {
        const delay = trimmedLine.substring(12).trim();
        if (isNaN(delay) || parseFloat(delay) < 0) {
          errors.push(`第${lineNumber}行: Crawl-delay值必须是非负数`);
        }
      }
      return;
    }
    
    // 检查Sitemap指令
    if (trimmedLine.toLowerCase().startsWith('sitemap:')) {
      hasSitemap = true;
      const sitemapUrl = trimmedLine.substring(8).trim();
      
      if (!sitemapUrl) {
        errors.push(`第${lineNumber}行: Sitemap URL不能为空`);
      } else {
        try {
          new URL(sitemapUrl);
        } catch (e) {
          errors.push(`第${lineNumber}行: Sitemap URL格式无效`);
        }
      }
      return;
    }
    
    // 未识别的指令
    warnings.push(`第${lineNumber}行: 未识别的指令 "${trimmedLine}"`);
  });
  
  // 基本检查
  if (!hasUserAgent) {
    errors.push('robots.txt必须包含至少一个User-agent指令');
  }
  
  if (!hasSitemap) {
    warnings.push('建议添加Sitemap指令');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    stats: {
      hasUserAgent,
      hasSitemap,
      lineCount: lines.length
    }
  };
}

/**
 * 获取robots.txt统计信息
 * @param {string} robotsTxt - robots.txt内容
 * @returns {Object} 统计信息
 */
export function getRobotsTxtStats(robotsTxt) {
  const lines = robotsTxt.split('\n');
  const stats = {
    totalLines: lines.length,
    userAgents: [],
    disallowRules: 0,
    allowRules: 0,
    crawlDelays: 0,
    sitemaps: []
  };
  
  let currentUserAgent = null;
  
  lines.forEach(line => {
    const trimmedLine = line.trim().toLowerCase();
    
    if (trimmedLine.startsWith('user-agent:')) {
      currentUserAgent = line.substring(11).trim();
      if (currentUserAgent && !stats.userAgents.includes(currentUserAgent)) {
        stats.userAgents.push(currentUserAgent);
      }
    } else if (trimmedLine.startsWith('disallow:')) {
      stats.disallowRules++;
    } else if (trimmedLine.startsWith('allow:')) {
      stats.allowRules++;
    } else if (trimmedLine.startsWith('crawl-delay:')) {
      stats.crawlDelays++;
    } else if (trimmedLine.startsWith('sitemap:')) {
      const sitemapUrl = line.substring(8).trim();
      if (sitemapUrl && !stats.sitemaps.includes(sitemapUrl)) {
        stats.sitemaps.push(sitemapUrl);
      }
    }
  });
  
  return stats;
}
