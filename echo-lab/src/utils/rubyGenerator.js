/**
 * Ruby标签生成工具
 * 用于生成带有假名标注的HTML内容
 */

/**
 * 判断字符是否需要显示标注
 * @param {Object} charObj - 字符对象，包含char和reading字段
 * @returns {boolean} - 是否需要显示标注
 */
function needsAnnotation(charObj) {
  // 有reading字段且不等于字符本身，就需要标注
  return charObj.reading && charObj.reading !== charObj.char;
}

/**
 * 生成带有Ruby标签的HTML内容
 * @param {string} content - 原始文本内容
 * @param {Object} annotation - 标注对象，包含reading和characters字段
 * @returns {string} - 带有Ruby标签的HTML内容
 */
export function generateRubyHTML(content, annotation) {
  // 如果没有内容或标注，直接返回加粗的原始内容
  if (!content || !annotation) {
    return `<b>${content}</b>`;
  }

  // 如果没有字符级标注，直接返回加粗的原始内容
  if (!annotation.characters || annotation.characters.length === 0) {
    return `<b>${content}</b>`;
  }

  // 使用字符级标注生成Ruby标签
  const characters = annotation.characters;
  let html = "<b>"; // 整段话开始加粗

  for (let i = 0; i < characters.length; i++) {
    const char = characters[i];

    // 有标注信息就显示ruby标签，没有就显示原字符
    if (needsAnnotation(char)) {
      html += `<ruby>${char.char}<rt>${char.reading}</rt></ruby>`;
    } else {
      html += char.char;
    }
  }

  html += "</b>"; // 整段话结束加粗
  return html;
}
