import { md5 } from './md5';

/**
 * 解析说话人标记
 * @param {string} content - 包含说话人标记的内容
 * @returns {Object} 解析结果
 */
function parseSpeaker(content) {
  if (!content) {
    return { speaker: null, content: '' };
  }

  // 匹配说话人标记：甲：、乙：、李：等
  const speakerMatch = content.match(/^([^：:]+)[：:]\s*/);
  
  if (speakerMatch) {
    const speaker = speakerMatch[1].trim();
    const pureContent = content.substring(speakerMatch[0].length);
    return { speaker, content: pureContent };
  }
  
  return { speaker: null, content };
}

/**
 * 生成唯一的分句ID
 * @param {string} content - 分句内容
 * @param {Array} existingIds - 已存在的ID列表
 * @returns {string} 唯一的分句ID
 */
export function generateUniqueSegmentId(content, existingIds = []) {
  const { content: pureContent } = parseSpeaker(content);
  const baseId = `seg_${md5(pureContent)}`;
  
  // 如果基础ID不重复，直接使用
  if (!existingIds.includes(baseId)) {
    return baseId;
  }
  
  // 生成唯一ID（4位随机字符串后缀）
  let uniqueId;
  do {
    const randomSuffix = Math.random().toString(36).substring(2, 6); // 4位随机字符
    uniqueId = `${baseId}_${randomSuffix}`;
  } while (existingIds.includes(uniqueId));
  
  return uniqueId;
}

/**
 * 确保分句列表中的ID都是唯一的
 * @param {Array} segments - 分句列表
 * @returns {Array} 具有唯一ID的分句列表
 */
export function ensureUniqueSegmentIds(segments) {
  const existingIds = [];
  
  return segments.map(segment => {
    // 如果已有ID且不重复，保持不变
    if (segment.id && !existingIds.includes(segment.id)) {
      existingIds.push(segment.id);
      return segment;
    }
    
    // 生成唯一ID
    const uniqueId = generateUniqueSegmentId(segment.content, existingIds);
    existingIds.push(uniqueId);
    
    return {
      ...segment,
      id: uniqueId
    };
  });
}
