/**
 * 静音音频生成工具
 * 用于在音频获取失败时生成等长的静音音频
 */

/**
 * 创建指定时长的静音音频缓冲区
 * @param {AudioContext} audioContext - 音频上下文
 * @param {number} duration - 静音时长（秒）
 * @returns {AudioBuffer} 静音音频缓冲区
 */
export function createSilenceBuffer(audioContext, duration = 2) {
  // 确保时长有效
  const validDuration = Math.max(0.1, Math.min(duration, 30));

  // 计算采样数（采样率 * 时长）
  const sampleRate = audioContext.sampleRate;
  const frameCount = Math.ceil(sampleRate * validDuration);

  // 创建音频缓冲区（单声道，24kHz）
  const audioBuffer = audioContext.createBuffer(1, frameCount, sampleRate);

  // 填充静音数据（全部为0）
  const channelData = audioBuffer.getChannelData(0);
  for (let i = 0; i < frameCount; i++) {
    channelData[i] = 0;
  }

  return audioBuffer;
}

/**
 * 创建静音音频的ArrayBuffer
 * @param {number} duration - 静音时长（秒）
 * @param {number} sampleRate - 采样率，默认24000
 * @returns {ArrayBuffer} 静音音频的ArrayBuffer
 */
export function createSilenceArrayBuffer(duration = 2, sampleRate = 24000) {
  // 确保时长有效
  const validDuration = Math.max(0.1, Math.min(duration, 30));

  // 计算采样数（采样率 * 时长）
  const frameCount = Math.ceil(sampleRate * validDuration);

  // 创建WAV文件头
  const wavHeader = createWavHeader(frameCount, sampleRate);

  // 创建音频数据（全部为0）
  const audioData = new Float32Array(frameCount);

  // 将Float32Array转换为Int16Array（WAV格式要求）
  const pcmData = new Int16Array(frameCount);
  for (let i = 0; i < frameCount; i++) {
    pcmData[i] = audioData[i] * 0x7fff; // 转换为16位整数
  }

  // 合并文件头和音频数据
  const wavFile = new Uint8Array(wavHeader.length + pcmData.byteLength);
  wavFile.set(new Uint8Array(wavHeader), 0);
  wavFile.set(new Uint8Array(pcmData.buffer), wavHeader.length);

  return wavFile.buffer;
}

/**
 * 创建WAV文件头
 * @param {number} frameCount - 采样数
 * @param {number} sampleRate - 采样率
 * @returns {ArrayBuffer} WAV文件头
 */
function createWavHeader(frameCount, sampleRate = 24000) {
  const numChannels = 1; // 单声道
  const bitsPerSample = 16; // 16位
  const byteRate = (sampleRate * numChannels * bitsPerSample) / 8;
  const blockAlign = (numChannels * bitsPerSample) / 8;
  const dataSize = frameCount * blockAlign;
  const buffer = new ArrayBuffer(44);
  const view = new DataView(buffer);

  // RIFF标识
  writeString(view, 0, "RIFF");
  // 文件长度
  view.setUint32(4, 36 + dataSize, true);
  // WAVE标识
  writeString(view, 8, "WAVE");
  // fmt子块标识
  writeString(view, 12, "fmt ");
  // fmt子块长度
  view.setUint32(16, 16, true);
  // 音频格式（1表示PCM）
  view.setUint16(20, 1, true);
  // 通道数
  view.setUint16(22, numChannels, true);
  // 采样率
  view.setUint32(24, sampleRate, true);
  // 字节率
  view.setUint32(28, byteRate, true);
  // 块对齐
  view.setUint16(32, blockAlign, true);
  // 位深度
  view.setUint16(34, bitsPerSample, true);
  // data子块标识
  writeString(view, 36, "data");
  // data子块长度
  view.setUint32(40, dataSize, true);

  return buffer;
}

/**
 * 在DataView中写入字符串
 * @param {DataView} view - DataView对象
 * @param {number} offset - 偏移量
 * @param {string} string - 要写入的字符串
 */
function writeString(view, offset, string) {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
}
