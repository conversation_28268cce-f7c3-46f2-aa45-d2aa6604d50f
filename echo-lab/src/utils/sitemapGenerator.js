/**
 * Sitemap生成器
 * 用于生成网站的sitemap.xml文件
 */

// 网站基础URL
const BASE_URL = "https://echolab.club";

// 静态页面配置函数
function getStaticPages(latestContentUpdate) {
  const lastmod = latestContentUpdate || new Date().toISOString().split("T")[0];
  
  return [
    {
      url: "/",
      changefreq: "daily",
      priority: 1.0,
      lastmod,
    },
    {
      url: "/content-info",
      changefreq: "monthly",
      priority: 0.8,
      lastmod,
    },
    {
      url: "/install-guide",
      changefreq: "monthly",
      priority: 0.7,
      lastmod,
    },
    {
      url: "/login",
      changefreq: "monthly",
      priority: 0.5,
      lastmod,
    },
  ];
}

/**
 * 生成单个URL的XML条目
 * @param {Object} page - 页面信息
 * @returns {string} XML字符串
 */
function generateUrlEntry(page) {
  return `  <url>
    <loc>${BASE_URL}${page.url}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`;
}

/**
 * 生成内容页面的URL条目
 * @param {Object} content - 内容对象
 * @returns {string} XML字符串
 */
function generateContentUrlEntry(content) {
  const lastmod = content.updatedAt
    ? new Date(content.updatedAt).toISOString().split("T")[0]
    : new Date().toISOString().split("T")[0];

  return `  <url>
    <loc>${BASE_URL}/player/${content.id}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>`;
}

/**
 * 生成完整的sitemap XML
 * @param {Array} contents - 公开内容列表
 * @returns {string} 完整的sitemap XML
 */
export function generateSitemap(contents = []) {
  const header = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

  const footer = `</urlset>`;

  // 获取最新内容更新时间
  const publishedContents = contents.filter((content) => content.status === "published");
  const latestContentUpdate = publishedContents.length > 0
    ? new Date(publishedContents[0].updatedAt).toISOString().split("T")[0]
    : null;

  // 生成静态页面条目
  const staticPages = getStaticPages(latestContentUpdate);
  const staticEntries = staticPages.map(generateUrlEntry).join("\n");

  // 生成内容页面条目
  const contentEntries = publishedContents
    .map(generateContentUrlEntry)
    .join("\n");

  // 组合完整的sitemap
  const sitemap = [header, staticEntries, contentEntries, footer].join("\n");

  return sitemap;
}

/**
 * 获取所有公开内容并生成sitemap
 * @returns {Promise<string>} sitemap XML字符串
 */
export async function generateSitemapFromAPI() {
  try {
    // 直接调用后端的sitemap生成API
    const response = await fetch("/api/seo/sitemap.xml");

    if (!response.ok) {
      throw new Error("Failed to fetch sitemap from server");
    }

    const sitemapXML = await response.text();
    return sitemapXML;
  } catch (error) {
    console.error("从服务器获取sitemap时出错:", error);

    // 如果服务器API失败，尝试客户端生成
    try {
      const response = await fetch("/api/public/contents?limit=1000");

      if (!response.ok) {
        throw new Error("Failed to fetch contents");
      }

      const data = await response.json();
      const contents = data.success ? data.contents : [];

      return generateSitemap(contents);
    } catch (fallbackError) {
      console.error("客户端生成sitemap也失败:", fallbackError);
      // 如果都失败，至少返回静态页面的sitemap
      return generateSitemap([]);
    }
  }
}

/**
 * 下载sitemap文件
 */
export function downloadSitemap() {
  generateSitemapFromAPI().then((sitemapXML) => {
    const blob = new Blob([sitemapXML], { type: "application/xml" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "sitemap.xml";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  });
}

/**
 * 验证sitemap格式
 * @param {string} sitemapXML - sitemap XML字符串
 * @returns {boolean} 是否有效
 */
export function validateSitemap(sitemapXML) {
  try {
    // 基本的XML格式检查
    const parser = new DOMParser();
    const doc = parser.parseFromString(sitemapXML, "application/xml");

    // 检查是否有解析错误
    const parseError = doc.querySelector("parsererror");
    if (parseError) {
      console.error("Sitemap XML解析错误:", parseError.textContent);
      return false;
    }

    // 检查根元素
    const urlset = doc.querySelector("urlset");
    if (!urlset) {
      console.error("Sitemap缺少urlset根元素");
      return false;
    }

    // 检查URL条目
    const urls = doc.querySelectorAll("url");
    if (urls.length === 0) {
      console.warn("Sitemap中没有URL条目");
    }

    // 检查每个URL条目的必需字段
    for (const url of urls) {
      const loc = url.querySelector("loc");
      if (!loc || !loc.textContent.trim()) {
        console.error("发现缺少loc元素的URL条目");
        return false;
      }

      // 验证URL格式
      try {
        new URL(loc.textContent);
      } catch (e) {
        console.error("发现无效的URL:", loc.textContent);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error("验证sitemap时出错:", error);
    return false;
  }
}

/**
 * 获取sitemap统计信息
 * @param {string} sitemapXML - sitemap XML字符串
 * @returns {Object} 统计信息
 */
export function getSitemapStats(sitemapXML) {
  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(sitemapXML, "application/xml");

    const urls = doc.querySelectorAll("url");
    const staticUrls = [];
    const contentUrls = [];

    urls.forEach((url) => {
      const loc = url.querySelector("loc");
      if (loc) {
        const urlPath = loc.textContent.replace(BASE_URL, "");
        if (urlPath.startsWith("/player/")) {
          contentUrls.push(urlPath);
        } else {
          staticUrls.push(urlPath);
        }
      }
    });

    return {
      total: urls.length,
      static: staticUrls.length,
      content: contentUrls.length,
      staticUrls,
      contentUrls,
    };
  } catch (error) {
    console.error("获取sitemap统计信息时出错:", error);
    return {
      total: 0,
      static: 0,
      content: 0,
      staticUrls: [],
      contentUrls: [],
    };
  }
}
