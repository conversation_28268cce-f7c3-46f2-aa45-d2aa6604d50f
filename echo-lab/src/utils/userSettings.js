/**
 * 统一的用户设置管理
 * 整合语言、等级、引导状态等所有用户相关设置
 */

// 统一的存储键名
const STORAGE_KEY = 'echolab_user_settings';

// 设置版本（用于数据迁移）
const SETTINGS_VERSION = '1.0.0';

// 默认设置
const DEFAULT_SETTINGS = {
  version: SETTINGS_VERSION,
  language: {
    learning: null // 学习语言 - 默认为null，首次访问时引导选择
  },
  // 用户选择的多个水平等级
  selectedLevels: [],
  // 简化引导状态，只记录是否首次访问
  firstVisit: true,
  preferences: {
    // 其他用户偏好设置
    theme: 'light',
    autoPlay: true,
    showSubtitles: true
  },
  updatedAt: null
};

/**
 * 获取完整的用户设置
 * @returns {Object} 用户设置对象
 */
export function getUserSettings() {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) {
      return { ...DEFAULT_SETTINGS };
    }

    const settings = JSON.parse(stored);
    
    // 检查版本并进行数据迁移
    if (!settings.version || settings.version !== SETTINGS_VERSION) {
      return migrateSettings(settings);
    }

    // 合并默认设置，确保新增字段有默认值
    return mergeWithDefaults(settings);
  } catch (error) {
    console.error('获取用户设置失败:', error);
    return { ...DEFAULT_SETTINGS };
  }
}

/**
 * 保存用户设置
 * @param {Object} updates 要更新的设置
 * @returns {boolean} 是否保存成功
 */
export function saveUserSettings(updates) {
  try {
    const current = getUserSettings();
    const updated = deepMerge(current, updates);
    updated.updatedAt = new Date().toISOString();

    localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
    return true;
  } catch (error) {
    console.error('保存用户设置失败:', error);
    return false;
  }
}

/**
 * 获取学习语言
 * @returns {string} 语言代码
 */
export function getLearningLanguage() {
  const settings = getUserSettings();
  return settings.language.learning;
}

/**
 * 设置学习语言
 * @param {string} languageCode 语言代码
 * @returns {boolean} 是否设置成功
 */
export function setLearningLanguage(languageCode) {
  return saveUserSettings({
    language: {
      learning: languageCode
    }
  });
}

/**
 * 获取用户等级 (已废弃，返回null)
 * @returns {null}
 */
export function getUserLevel() {
  return null;
}

/**
 * 设置用户等级 (已废弃，不执行任何操作)
 * @param {string} level 等级代码
 * @returns {boolean} 总是返回true
 */
export function setUserLevel(level) {
  return true;
}

/**
 * 检查是否首次访问
 * @returns {boolean} 是否首次访问
 */
export function isFirstVisit() {
  const settings = getUserSettings();
  return settings.firstVisit;
}

/**
 * 获取用户选择的多个水平等级
 * @returns {Array} 等级数组
 */
export function getSelectedLevels() {
  const settings = getUserSettings();
  return settings.selectedLevels || [];
}

/**
 * 设置用户选择的多个水平等级
 * @param {Array} levels 等级数组
 * @returns {boolean} 是否设置成功
 */
export function setSelectedLevels(levels) {
  return saveUserSettings({
    selectedLevels: Array.isArray(levels) ? levels : []
  });
}

/**
 * 标记首次访问完成
 * @returns {boolean} 是否设置成功
 */
export function setFirstVisitCompleted() {
  return saveUserSettings({
    firstVisit: false
  });
}

/**
 * 标记已访问
 * @returns {boolean} 是否设置成功
 */
export function markAsVisited() {
  return setFirstVisitCompleted();
}

/**
 * 检查是否需要显示引导
 * @returns {boolean} 是否需要显示引导
 */
export function shouldShowOnboarding() {
  const settings = getUserSettings();
  // 只要没有设置学习语言，就需要显示引导
  return !settings.language.learning;
}

/**
 * 重置用户设置（用于测试或重新引导）
 * @param {boolean} clearLanguageAndLevel 是否同时清除语言和等级设置
 * @returns {boolean} 是否重置成功
 */
export function resetUserSettings(clearLanguageAndLevel = false) {
  const updates = {
    firstVisit: true
  };

  // 如果需要，同时清除语言和等级设置
  if (clearLanguageAndLevel) {
    updates.language = {
      learning: null
    };
    updates.level = {
      current: null,
      setAt: null
    };
  }

  return saveUserSettings(updates);
}

/**
 * 获取用户偏好设置
 * @returns {Object} 偏好设置
 */
export function getUserPreferences() {
  const settings = getUserSettings();
  return settings.preferences;
}

/**
 * 设置用户偏好
 * @param {Object} preferences 偏好设置
 * @returns {boolean} 是否设置成功
 */
export function setUserPreferences(preferences) {
  return saveUserSettings({
    preferences
  });
}

/**
 * 清除所有用户设置
 * @returns {boolean} 是否清除成功
 */
export function clearUserSettings() {
  try {
    localStorage.removeItem(STORAGE_KEY);
    return true;
  } catch (error) {
    console.error('清除用户设置失败:', error);
    return false;
  }
}

/**
 * 导出用户设置（用于备份）
 * @returns {string} JSON字符串
 */
export function exportUserSettings() {
  const settings = getUserSettings();
  return JSON.stringify(settings, null, 2);
}

/**
 * 导入用户设置（用于恢复）
 * @param {string} settingsJson JSON字符串
 * @returns {boolean} 是否导入成功
 */
export function importUserSettings(settingsJson) {
  try {
    const settings = JSON.parse(settingsJson);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
    return true;
  } catch (error) {
    console.error('导入用户设置失败:', error);
    return false;
  }
}



// ========== 内部辅助函数 ==========

/**
 * 深度合并对象
 */
function deepMerge(target, source) {
  const result = { ...target };
  
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = deepMerge(result[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  }
  
  return result;
}

/**
 * 与默认设置合并
 */
function mergeWithDefaults(settings) {
  return deepMerge(DEFAULT_SETTINGS, settings);
}

/**
 * 数据迁移（从旧版本迁移到新版本）
 * 简化版本，只保留核心迁移逻辑
 */
function migrateSettings(oldSettings) {
  const newSettings = { ...DEFAULT_SETTINGS };

  try {
    // 只迁移核心的语言和等级设置
    const oldLanguage = localStorage.getItem('echolab_learning_language');
    const oldUserPreferences = localStorage.getItem('user_preferences');

    if (oldLanguage) {
      newSettings.language.learning = oldLanguage;
      newSettings.firstVisit = false;
    }

    if (oldUserPreferences) {
      try {
        const preferences = JSON.parse(oldUserPreferences);
        if (preferences.level) {
          newSettings.level.current = preferences.level;
          newSettings.level.setAt = new Date().toISOString();
        }
      } catch (e) {
        console.warn('解析旧用户偏好失败:', e);
      }
    }

    // 保存迁移后的设置
    localStorage.setItem(STORAGE_KEY, JSON.stringify(newSettings));

    // 清理旧数据
    ['echolab_learning_language', 'echolab_onboarding_completed', 'echolab_onboarding_completed_at', 'user_preferences']
      .forEach(key => localStorage.removeItem(key));

    return newSettings;
  } catch (error) {
    console.error('用户设置迁移失败:', error);
    return newSettings;
  }
}

export default {
  getUserSettings,
  saveUserSettings,
  getLearningLanguage,
  setLearningLanguage,
  getUserLevel,
  setUserLevel,
  isFirstVisit,
  setFirstVisitCompleted,
  shouldShowOnboarding,
  resetUserSettings,
  getUserPreferences,
  setUserPreferences,
  clearUserSettings,
  exportUserSettings,
  importUserSettings
};
