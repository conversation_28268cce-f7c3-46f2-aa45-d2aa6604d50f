/**
 * 视频导出工具
 * 基于时间线数据生成视频帧和字幕文件
 */

/**
 * 将DOM元素渲染为Canvas
 * @param {HTMLElement} element - 要渲染的DOM元素
 * @param {Object} options - 渲染选项
 * @returns {Promise<HTMLCanvasElement>} 渲染后的Canvas
 */
export async function renderElementToCanvas(element, options = {}) {
  // 临时添加的元素引用，用于清理
  let wasAppended = false;

  try {
    // 初始检查是否已取消
    if (options.signal && options.signal.aborted) {
      console.log("渲染元素到Canvas操作已被取消（初始阶段）");
      throw new DOMException("渲染元素到Canvas操作已被取消", "AbortError");
    }

    // 创建一个Canvas元素
    const canvas = document.createElement("canvas");

    // 获取目标尺寸（最终输出尺寸）
    const targetWidth = options.width || 1280;
    const targetHeight = options.height || 720;

    // 设置Canvas大小为目标尺寸
    canvas.width = targetWidth;
    canvas.height = targetHeight;

    // 获取绘图上下文
    const ctx = canvas.getContext("2d");

    // 设置背景色
    if (options.bgcolor) {
      ctx.fillStyle = options.bgcolor;
      ctx.fillRect(0, 0, targetWidth, targetHeight);
    }

    // 检查是否已取消
    if (options.signal && options.signal.aborted) {
      console.log("渲染元素到Canvas操作已被取消（设置背景后）");
      throw new DOMException("渲染元素到Canvas操作已被取消", "AbortError");
    }

    // 将元素添加到文档中以确保能够正确渲染
    if (!element.parentNode) {
      document.body.appendChild(element);
      wasAppended = true;
    }

    // 检查是否包含图片元素，如果有则等待加载
    const images = element.querySelectorAll('img');
    if (images.length > 0) {
      await Promise.all(Array.from(images).map(img => {
        if (img.complete) return Promise.resolve();
        return new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            resolve(); // 超时也继续
          }, 3000);
          img.onload = () => {
            clearTimeout(timeout);
            resolve();
          };
          img.onerror = () => {
            clearTimeout(timeout);
            resolve(); // 错误也继续
          };
        });
      }));
    }

    // 添加延迟确保渲染完成
    await new Promise(resolve => setTimeout(resolve, 200));

    // 再次检查是否已取消
    if (options.signal && options.signal.aborted) {
      console.log("渲染元素到Canvas操作已被取消（渲染延迟后）");
      throw new DOMException("渲染元素到Canvas操作已被取消", "AbortError");
    }
    // 使用html2canvas库渲染DOM元素
    const html2canvas = await import("html2canvas");

    // 获取元素的实际尺寸（从DOM中测量）
    // 优先使用clientWidth/clientHeight，这些是元素的实际可见尺寸
    const actualWidth =
      element.clientWidth ||
      parseInt(element.style.width) ||
      element.offsetWidth;
    const actualHeight =
      element.clientHeight ||
      parseInt(element.style.height) ||
      element.offsetHeight;

    if (!actualWidth || !actualHeight) {
      console.warn("无法获取元素的实际尺寸，使用目标尺寸作为后备");
    }

    // 如果提供了容器尺寸，优先使用它
    // 否则使用从元素测量的实际尺寸
    const containerWidth = options.containerWidth || actualWidth || targetWidth;
    const containerHeight =
      options.containerHeight || actualHeight || targetHeight;

    // 计算宽高比例
    const widthRatio = targetWidth / containerWidth;
    const heightRatio = targetHeight / containerHeight;

    // 使用较大的比例，确保内容完全覆盖目标区域
    // 这样可以保证文本不会被裁剪，同时保持清晰度
    const scale = Math.max(widthRatio, heightRatio);

    // 检查是否已取消
    if (options.signal && options.signal.aborted) {
      console.log("渲染元素到Canvas操作已被取消（html2canvas前）");
      throw new DOMException("渲染元素到Canvas操作已被取消", "AbortError");
    }

    // 创建一个可取消的Promise
    const renderedCanvas = await new Promise((resolve, reject) => {
      // 如果已经取消，直接拒绝
      if (options.signal && options.signal.aborted) {
        reject(new DOMException("渲染元素到Canvas操作已被取消", "AbortError"));
        return;
      }

      const renderProcess = html2canvas.default(element, {
        width: containerWidth, // 使用实际容器宽度
        height: containerHeight, // 使用实际容器高度
        backgroundColor: options.bgcolor || null,
        scale: scale, // 使用计算的缩放比例
        logging: false,
        allowTaint: false, // 防止 canvas 污染
        useCORS: true,
      });
      // 处理渲染结果
      renderProcess.then(resolve).catch(reject);

      // 如果有取消信号，监听取消事件
      if (options.signal) {
        options.signal.addEventListener(
          "abort",
          () => {
            // html2canvas没有提供取消机制，但我们可以拒绝Promise
            reject(
              new DOMException("渲染元素到Canvas操作已被取消", "AbortError")
            );
          },
          { once: true }
        );
      }
    });

    // 检查是否已取消
    if (options.signal && options.signal.aborted) {
      console.log("渲染元素到Canvas操作已被取消（html2canvas后）");
      throw new DOMException("渲染元素到Canvas操作已被取消", "AbortError");
    }

    // 将渲染结果绘制到我们的Canvas上，保持比例
    ctx.drawImage(renderedCanvas, 0, 0, targetWidth, targetHeight);

    return canvas;
  } catch (error) {
    // 如果是取消错误，直接重新抛出
    if (error.name === "AbortError") {
      throw error;
    }

    console.error("渲染元素到Canvas失败:", error);

    // 创建一个空白Canvas作为后备
    const canvas = document.createElement("canvas");
    canvas.width = options.width || 1280;
    canvas.height = options.height || 720;

    // 设置背景色
    const ctx = canvas.getContext("2d");
    ctx.fillStyle = options.bgcolor || "#000000";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 如果有错误但有文本内容，尝试渲染文本
    if (element && element.textContent) {
      ctx.font = "24px sans-serif";
      ctx.fillStyle = "#FFFFFF";
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillText(element.textContent, canvas.width / 2, canvas.height / 2);
    }

    return canvas;
  } finally {
    // 如果元素是临时添加的，确保移除它
    if (wasAppended && element && element.parentNode) {
      try {
        document.body.removeChild(element);
      } catch (e) {
        console.warn("清理临时添加的DOM元素失败:", e);
      }
    }
  }
}

/**
 * 判断时间线项目是否需要生成视频帧
 * @param {Object} item - 时间线项目
 * @returns {boolean} 是否需要生成帧
 */
function shouldGenerateVideoFrame(item) {
  // 有内容或图片就生成帧
  return Boolean(item.content || item.imageUrl);
}

/**
 * 从时间线数据生成视频帧
 * @param {Array} timeline - 时间线数据
 * @param {Function} renderContent - 渲染内容的函数，接收内容项并返回DOM元素
 * @param {Object} options - 渲染选项
 * @returns {Promise<Array>} 帧数据数组
 */
export async function generateFramesFromTimeline(
  timeline,
  renderContent,
  options = {}
) {
  const frames = [];

  // 设置默认选项
  const defaultOptions = {
    width: 1280,
    height: 720,
    containerWidth: null, // 容器宽度
    containerHeight: null, // 容器高度
    playerToOutputRatio: null, // 播放器到输出的比例
    frameRate: 30,
    backgroundColor: "#000000",
    signal: null, // AbortController信号
  };

  const config = { ...defaultOptions, ...options };

  // 检查是否已取消
  if (config.signal && config.signal.aborted) {
    console.log("生成帧操作已被取消（初始化阶段）");
    throw new DOMException("生成帧操作已被取消", "AbortError");
  }

  // 记录配置信息
  console.log("帧生成配置:", {
    targetDimensions: `${config.width}x${config.height}`,
    containerDimensions:
      config.containerWidth && config.containerHeight
        ? `${config.containerWidth}x${config.containerHeight}`
        : "未指定",
    playerToOutputRatio: config.playerToOutputRatio
      ? `宽度=${config.playerToOutputRatio.width.toFixed(
          2
        )}, 高度=${config.playerToOutputRatio.height.toFixed(2)}`
      : "未指定",
    hasAbortSignal: config.signal ? "是" : "否",
  });

  // 创建进度回调
  const progressCallback = options.onProgress || (() => {});

  try {
    // 遍历时间线项目
    for (let i = 0; i < timeline.length; i++) {
      // 检查是否已取消
      if (config.signal && config.signal.aborted) {
        console.log(
          `生成帧操作已被取消（处理第${i + 1}/${timeline.length}帧前）`
        );
        throw new DOMException("生成帧操作已被取消", "AbortError");
      }

      const item = timeline[i];

      // 更新进度
      progressCallback({
        phase: "generating-frames",
        current: i + 1,
        total: timeline.length,
        percentage: Math.round(((i + 1) / timeline.length) * 100),
      });

      try {
        // 渲染内容项 - 支持异步renderContent函数
        const contentElement = await Promise.resolve(renderContent(item));

        // 将内容渲染为Canvas，传递所有相关尺寸信息
        const canvas = await renderElementToCanvas(contentElement, {
          width: config.width,
          height: config.height,
          bgcolor: config.backgroundColor,
          // 传递容器尺寸信息，如果有的话
          containerWidth: config.containerWidth,
          containerHeight: config.containerHeight,
          // 传递取消信号
          signal: config.signal,
        });

        // 再次检查是否已取消（渲染Canvas后）
        if (config.signal && config.signal.aborted) {
          console.log(
            `生成帧操作已被取消（渲染第${i + 1}/${timeline.length}帧后）`
          );
          throw new DOMException("生成帧操作已被取消", "AbortError");
        }

        // 判断是否需要为此项目生成视频帧
        if (shouldGenerateVideoFrame(item)) {
          const frameDuration = item.displayDuration || item.duration;

          // 检查帧是否有有效的时长和开始时间
          if (typeof frameDuration !== "number" || frameDuration <= 0) {
            console.error(`错误: 时间线项目 ${i} 缺少有效的时长`, item);
            throw new Error(`时间线项目 ${i} 缺少有效的时长`);
          }

          if (typeof item.startTime !== "number") {
            console.error(`错误: 时间线项目 ${i} 缺少有效的开始时间`, item);
            throw new Error(`时间线项目 ${i} 缺少有效的开始时间`);
          }

          const frameStartTime = item.startTime;

          console.log(
            `帧 ${i}: 持续时间 ${frameDuration.toFixed(
              3
            )}秒, 开始时间 ${frameStartTime.toFixed(3)}秒`
          );

          // 保存帧数据
          frames.push({
            canvas,
            duration: frameDuration,
            startTime: frameStartTime,
          });
        }
      } catch (error) {
        // 如果是取消错误，直接重新抛出
        if (error.name === "AbortError") {
          throw error;
        }
        // 其他错误也重新抛出
        throw new Error(`处理帧 ${i} 时出错: ${error.message}`);
      }
    }

    // 最终检查是否已取消
    if (config.signal && config.signal.aborted) {
      console.log("生成帧操作已被取消（所有帧处理完成后）");
      throw new DOMException("生成帧操作已被取消", "AbortError");
    }

    return frames;
  } catch (error) {
    // 如果是取消错误，直接重新抛出
    if (error.name === "AbortError") {
      throw error;
    }
    // 其他错误也重新抛出，但添加更多上下文信息
    console.error("生成帧过程中出错:", error);
    throw new Error(`生成帧失败: ${error.message}`);
  }
}

/**
 * 生成SRT格式字幕
 * @param {Array} timeline - 时间线数据
 * @param {string} language - 字幕语言
 * @param {Object} configJson - 配置JSON数据，包含翻译信息
 * @returns {string} SRT格式字幕内容
 */
export function generateSRTSubtitles(
  timeline,
  language = "ja",
  configJson = null
) {
  console.log("🎬 开始生成字幕文件...");

  // 筛选有效的字幕项目（与视频帧生成逻辑一致）
  // 只处理有内容的项目，跳过停顿项目，封面项目通常不需要字幕
  const subtitleItems = timeline
    .filter((item) => {
      // 只为有文本内容的项目生成字幕
      return Boolean(item.content);
    })
    .map((item, index) => {
      // 使用displayDuration生成字幕时间
      const startTime = item.startTime;
      const endTime = startTime + item.displayDuration;

      console.log(
        `字幕 ${index + 1}: ${startTime.toFixed(3)}s → ${endTime.toFixed(3)}s`
      );
      console.log(`  内容: "${item.content?.substring(0, 30)}..."`);

      // 显示时长延长信息
      if (item.displayDuration !== item.duration) {
        const extension = item.displayDuration - item.duration;
        console.log(
          `  时长: 音频${item.duration.toFixed(3)}s + 延长${extension.toFixed(
            3
          )}s = 显示${item.displayDuration.toFixed(3)}s`
        );
      }

      // 获取对应语言的内容
      let content = "";

      // 如果字幕语言与内容语言相同，直接使用内容
      if (language === item.language) {
        content = item.content;
      }
      // 如果是关键词且字幕语言与内容语言不同，且没有翻译资源，跳过该项
      else if (item.isKeyword && language !== item.language) {
        console.log(
          `关键词 "${item.content}" 语言不匹配且没有翻译资源，将跳过该项`
        );
        content = ""; // 设置为空内容，后续会被过滤掉
      }
      // 否则从翻译中获取内容
      else if (
        configJson &&
        configJson.resources &&
        configJson.resources.translations &&
        configJson.resources.translations[language]
      ) {
        // 使用原始ID查找翻译
        const idToUse = item.originalId || item.id;

        // 从翻译中查找内容
        if (configJson.resources.translations[language][idToUse]) {
          content = configJson.resources.translations[language][idToUse];
        }
        // 如果是关键词且没有找到翻译，跳过该项（返回空内容）
        else if (item.isKeyword) {
          console.log(
            `关键词 "${item.content}" 没有 ${language} 翻译，将跳过该项`
          );
          content = ""; // 设置为空内容，后续会被过滤掉
        }
      }

      // 清理HTML标签
      content = stripHtml(content);

      return {
        index: index + 1,
        startTime,
        endTime,
        content,
      };
    })
    // 过滤掉没有内容的字幕项
    .filter((item) => item.content);

  // 转换为SRT格式
  return subtitleItems
    .map((sub) => {
      const startTime = formatSRTTime(sub.startTime);
      const endTime = formatSRTTime(sub.endTime);

      return `${sub.index}\n${startTime} --> ${endTime}\n${sub.content}\n`;
    })
    .join("\n");
}

/**
 * 格式化时间为SRT时间格式
 * @param {number} seconds - 秒数
 * @returns {string} 格式化的时间字符串 (00:00:00,000)
 */
function formatSRTTime(seconds) {
  const date = new Date(seconds * 1000);
  const hours = date.getUTCHours().toString().padStart(2, "0");
  const minutes = date.getUTCMinutes().toString().padStart(2, "0");
  const secs = date.getUTCSeconds().toString().padStart(2, "0");
  const ms = date.getUTCMilliseconds().toString().padStart(3, "0");

  return `${hours}:${minutes}:${secs},${ms}`;
}

/**
 * 移除HTML标签
 * @param {string} html - 包含HTML标签的字符串
 * @returns {string} 纯文本内容
 */
function stripHtml(html) {
  if (!html) return "";
  const temp = document.createElement("div");
  temp.innerHTML = html;
  return temp.textContent || temp.innerText || "";
}
