import { Howl } from 'howler';

export class WebAudioPlayer {
  constructor() {
    this.timeline = [];
    this.currentTime = 0;
    this.duration = 0;
    this.isPlaying = false;
    this.currentIndex = 0;
    this.audioList = [];
    this.currentSound = null;
    this.onTimeUpdate = null;
    this.onEnded = null;
    this.onLoadingChange = null;
    this.timeUpdateTimer = null;
    this.playbackRate = 1.0;
  }

  async loadTimeline(timeline) {
    this.timeline = timeline;
    this.duration = timeline.reduce((total, item) => total + (item.duration || 0), 0);
    this.currentIndex = 0;
    this.currentTime = 0;

    this.audioList = timeline.map((item, index) => {
      if (!item.audioUrl?.trim()) return null;
      
      return new Howl({ 
        src: [item.audioUrl], 
        format: ['mp3', 'wav'],
        preload: index < 3, // 前3个预加载
        volume: 1,
        rate: this.playbackRate
      });
    });
  }

  async play() {
    if (this.isPlaying) return;
    this.isPlaying = true;
    this.startTimeUpdateTimer();
    this.playCurrentIndex();
  }

  pause() {
    if (!this.isPlaying) return;
    this.isPlaying = false;
    this.currentSound?.pause();
    this.stopTimeUpdateTimer();
  }

  // 统一的跳转方法 - 所有跳转操作都通过这里
  seekTo(time) {
    this.stopCurrentAudio();
    this.currentTime = Math.max(0, Math.min(time, this.duration));
    this.currentIndex = this.findIndexByTime(time);
    
    if (this.isPlaying) {
      this.playCurrentIndex();
    }
  }

  // 直接跳转到指定索引 - 快捷键和列表点击使用
  jumpToIndex(index) {
    if (index < 0 || index >= this.timeline.length) return;
    
    this.stopCurrentAudio();
    this.currentIndex = index;
    this.currentTime = this.timeline[index].startTime;
    
    if (this.isPlaying) {
      const sound = this.audioList[this.currentIndex];
      if (sound && sound.state() === 'loading') {
        this.onLoadingChange?.(true);
      }
      this.playCurrentIndex();
    }
  }

  // 跳转到下一个项目（用于快捷键，自动跳过停顿）
  jumpToNext() {
    let nextIndex = this.currentIndex + 1;
    while (nextIndex < this.timeline.length && this.timeline[nextIndex].isPause) {
      nextIndex++;
    }
    if (nextIndex < this.timeline.length) {
      this.jumpToIndex(nextIndex);
    }
  }

  // 跳转到上一个项目（用于快捷键，自动跳过停顿）
  jumpToPrevious() {
    let prevIndex = this.currentIndex - 1;
    while (prevIndex >= 0 && this.timeline[prevIndex].isPause) {
      prevIndex--;
    }
    if (prevIndex >= 0) {
      this.jumpToIndex(prevIndex);
    }
  }

  // 设置全局播放速度
  setGlobalPlaybackRate(rate) {
    this.playbackRate = rate;
    // 更新所有音频的播放速度
    this.audioList.forEach(sound => {
      if (sound) {
        sound.rate(rate);
      }
    });
    // 如果当前有音频在播放，立即应用新速度
    if (this.currentSound && this.currentSound.playing()) {
      this.currentSound.rate(rate);
    }
  }

  destroy() {
    this.pause();
    this.audioList.forEach(sound => sound?.unload());
    this.audioList = [];
  }

  // 私有方法：播放当前索引的音频
  playCurrentIndex() {
    const sound = this.audioList[this.currentIndex];
    
    if (!sound) {
      this.playNext();
      return;
    }

    this.currentSound = sound;
    this.setupAudioEvents(sound);
    
    const state = sound.state();
    if (state === 'unloaded') {
      this.onLoadingChange?.(true);
      sound.load();
    } else if (state === 'loading') {
      this.onLoadingChange?.(true);
    } else {
      sound.play();
    }
    
    this.preloadNext();
  }

  // 预加载接下来的几个音频
  preloadNext() {
    for (let i = 1; i <= 3; i++) {
      const nextIndex = this.currentIndex + i;
      if (nextIndex < this.audioList.length) {
        const nextSound = this.audioList[nextIndex];
        if (nextSound && nextSound.state() === 'unloaded') {
          nextSound.load();
        }
      }
    }
  }

  // 设置音频事件监听
  setupAudioEvents(sound) {
    sound.once('load', () => {
      this.onLoadingChange?.(false);
      if (this.currentSound === sound && this.isPlaying) {
        sound.play();
      }
    });
    
    sound.once('loaderror', () => {
      this.onLoadingChange?.(false);
      if (this.currentSound === sound && this.isPlaying) {
        this.playNext();
      }
    });
    
    sound.once('end', () => {
      if (this.currentSound === sound && this.isPlaying) {
        this.playNext();
      }
    });
  }

  // 播放下一个音频
  playNext() {
    this.currentIndex++;
    if (this.currentIndex < this.audioList.length) {
      this.playCurrentIndex();
    } else {
      this.handlePlaybackEnd();
    }
  }

  // 私有方法：停止当前音频
  stopCurrentAudio() {
    if (this.currentSound) {
      this.currentSound.off();
      this.currentSound.stop();
      this.currentSound = null;
    }
    this.onLoadingChange?.(false);
  }

  // 私有方法：处理播放结束
  handlePlaybackEnd() {
    console.log('🎵 WebAudioPlayer: 播放结束');
    this.isPlaying = false;
    this.stopTimeUpdateTimer();
    if (this.onEnded) {
      console.log('🎵 WebAudioPlayer: 调用 onEnded 回调');
      this.onEnded();
    } else {
      console.log('🎵 WebAudioPlayer: onEnded 回调未设置');
    }
  }

  findIndexByTime(time) {
    let currentTime = 0;
    for (let i = 0; i < this.timeline.length; i++) {
      const item = this.timeline[i];
      if (time >= currentTime && time < currentTime + item.duration) {
        return i;
      }
      currentTime += item.duration;
    }
    return this.timeline.length - 1;
  }

  startTimeUpdateTimer() {
    this.stopTimeUpdateTimer();
    this.timeUpdateTimer = setInterval(() => {
      if (this.isPlaying && this.onTimeUpdate) {
        this.updateCurrentTime();
        this.onTimeUpdate(this.currentTime);
      }
    }, 100);
  }

  stopTimeUpdateTimer() {
    if (this.timeUpdateTimer) {
      clearInterval(this.timeUpdateTimer);
      this.timeUpdateTimer = null;
    }
  }

  updateCurrentTime() {
    if (this.currentIndex >= this.timeline.length) return;
    
    const currentItem = this.timeline[this.currentIndex];
    let time = currentItem.startTime;
    
    if (this.currentSound?.playing()) {
      time += this.currentSound.seek();
    }
    
    this.currentTime = time;
  }
}