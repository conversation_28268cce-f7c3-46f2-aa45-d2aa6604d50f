<!--
  签到页面
  展示签到卡片和日历
-->
<template>
  <div class="check-in-page">
    <!-- 顶部导航栏 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="$router.back()" link class="back-btn">
          <el-icon><i-ep-arrow-left /></el-icon>
          返回
        </el-button>
      </div>
      <div class="header-center">
        <h1>每日签到</h1>
      </div>
      <div class="header-right"></div>
    </div>
    
    <div class="check-in-content">
      <div class="check-in-main">
        <CheckInCard />
      </div>
      
      <CheckInCalendar />
    </div>
  </div>
</template>

<script setup>
import CheckInCard from '@/components/common/CheckInCard.vue';
import CheckInCalendar from '@/components/common/CheckInCalendar.vue';
</script>

<style scoped>
.check-in-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.page-header {
  background: white;
  box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.1);
  padding: 1rem;
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left,
.header-right {
  flex: 1;
}

.header-center {
  flex: 2;
  text-align: center;
}

.header-center h1 {
  font-size: 1.25rem;
  color: #303133;
  margin: 0;
  font-weight: 600;
}

.back-btn {
  color: #007bff;
  font-size: 0.875rem;
  padding: 0.5rem;
}

.check-in-content {
  max-width: 50rem;
  margin: 0 auto;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.check-in-main {
  max-width: 30rem;
  margin: 0 auto;
}

.check-in-calendar-section h2 {
  font-size: 1.25rem;
  color: #303133;
  margin-bottom: 0.5rem;
  font-weight: 600;
  text-align: center;
}

@media (max-width: 48rem) {
  .page-header {
    padding: 0.75rem 1rem;
  }
  
  .header-center h1 {
    font-size: 1.125rem;
  }
  
  .check-in-content {
    padding: 1rem 0.5rem;
    gap: 1.5rem;
  }
}
</style>