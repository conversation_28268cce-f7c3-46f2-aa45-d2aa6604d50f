<template>
  <div class="help-page">
    <!-- 固定头部 -->
    <div class="help-header">
      <div class="header-content">
        <div class="header-left">
          <el-button @click="goBack" type="default" size="default">
            <el-icon class="el-icon--left">
              <i-ep-arrow-left />
            </el-icon>
            返回
          </el-button>
          <h1 class="help-title">{{ helpConfig.title }}</h1>
        </div>

      </div>
    </div>

    <!-- 帮助内容 -->
    <div class="help-content">
      <div class="help-container">
        <!-- 侧边栏导航 -->
        <div class="help-sidebar">
          <el-menu
            :default-active="activeSection"
            @select="handleSectionSelect"
            class="help-menu"
          >
            <el-menu-item
              v-for="section in helpConfig.sections"
              :key="section.id"
              :index="section.id"
            >
              <span>{{ section.title }}</span>
            </el-menu-item>
          </el-menu>
        </div>

        <!-- 主要内容区域 -->
        <div class="help-main">
          <div v-if="currentSection" class="section-content">
            <div class="section-header">
              <h2>{{ currentSection.title }}</h2>
              <p class="section-description">{{ currentSection.description }}</p>
            </div>

            <div class="section-items">
              <div
                v-for="item in currentSection.items"
                :key="item.id"
                class="help-item"
              >
                <div class="item-header" @click="toggleItem(item.id)">
                  <h3>{{ item.title }}</h3>
                  <el-icon class="toggle-icon" :class="{ 'expanded': expandedItems.includes(item.id) }">
                    <i-ep-arrow-down />
                  </el-icon>
                </div>
                <p class="item-description">{{ item.description }}</p>
                
                <div v-if="expandedItems.includes(item.id)" class="item-details">
                  <div
                    v-for="detail in item.details"
                    :key="detail.id"
                    class="detail-section"
                  >
                    <h4>{{ detail.title }}</h4>
                    <p class="detail-content">{{ detail.content }}</p>
                    
                    <div v-if="detail.steps" class="detail-steps">
                      <h5>操作步骤：</h5>
                      <ol>
                        <li v-for="step in detail.steps" :key="step">{{ step }}</li>
                      </ol>
                    </div>
                    
                    <div v-if="detail.tips" class="detail-tips">
                      <h5>小贴士：</h5>
                      <ul>
                        <li v-for="tip in detail.tips" :key="tip">{{ tip }}</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  PLAYER_HELP_CONFIG,
  EDITOR_HELP_CONFIG,
  CONTENT_HELP_CONFIG
} from '@/config/helpContent';

const route = useRoute();
const router = useRouter();

// Props
const props = defineProps({
  pageType: {
    type: String,
    default: 'editor'
  }
});

// 响应式数据
const activeSection = ref('');
const expandedItems = ref([]);

// 计算属性
const helpConfig = computed(() => {
  switch (props.pageType) {
    case 'player':
      return PLAYER_HELP_CONFIG;
    case 'content':
      return CONTENT_HELP_CONFIG;
    case 'editor':
    default:
      return EDITOR_HELP_CONFIG;
  }
});

const currentSection = computed(() => {
  return helpConfig.value.sections.find(section => section.id === activeSection.value);
});



// 方法
const goBack = () => {
  router.go(-1);
};



const handleSectionSelect = (sectionId) => {
  activeSection.value = sectionId;
  expandedItems.value = []; // 切换章节时收起所有项目
};

const toggleItem = (itemId) => {
  const index = expandedItems.value.indexOf(itemId);
  if (index > -1) {
    expandedItems.value.splice(index, 1);
  } else {
    expandedItems.value.push(itemId);
  }
};

// 生命周期
onMounted(() => {
  // 设置默认选中第一个章节
  if (helpConfig.value.sections.length > 0) {
    activeSection.value = helpConfig.value.sections[0].id;
  }
});
</script>

<style scoped>
.help-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.help-header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 16px 24px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.help-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.help-content {
  flex: 1;
  overflow: hidden;
}

.help-container {
  display: flex;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.help-sidebar {
  width: 240px;
  border-right: 1px solid #e4e7ed;
  background: #fafafa;
}

.help-menu {
  border: none;
  background: transparent;
}

.help-main {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.section-header {
  margin-bottom: 24px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.section-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.help-item {
  margin-bottom: 24px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.item-header {
  padding: 16px;
  background: #f8f9fa;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s;
}

.item-header:hover {
  background: #ecf5ff;
}

.item-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.toggle-icon {
  transition: transform 0.3s;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.item-description {
  padding: 0 16px 16px 16px;
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.item-details {
  border-top: 1px solid #e4e7ed;
  background: white;
}

.detail-section {
  padding: 16px;
  border-bottom: 1px solid #f5f7fa;
}

.detail-section:last-child {
  border-bottom: none;
}

.detail-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #409eff;
}

.detail-content {
  margin: 0 0 12px 0;
  color: #606266;
  line-height: 1.6;
}

.detail-steps h5,
.detail-tips h5 {
  margin: 12px 0 8px 0;
  font-size: 13px;
  color: #909399;
}

.detail-steps ol,
.detail-tips ul {
  margin: 0;
  padding-left: 20px;
}

.detail-steps li,
.detail-tips li {
  margin-bottom: 4px;
  color: #606266;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .help-container {
    flex-direction: column;
  }
  
  .help-sidebar {
    width: 100%;
    height: auto;
  }
  
  .help-menu {
    display: flex;
    overflow-x: auto;
  }
  
  .help-main {
    padding: 16px;
  }
}
</style>
