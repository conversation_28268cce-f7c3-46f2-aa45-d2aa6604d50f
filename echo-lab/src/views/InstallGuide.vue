<!--
  安装指南页面
  提供将应用添加到桌面的详细指南
-->
<template>
  <div class="install-guide-container" :class="{ 'mobile-device': isMobile }">
    <!-- 页面标题栏 -->
    <SmartPageHeader :title="`添加到${isMobile ? '主屏幕' : '桌面'}`" :force-show-back="true" />

    <div class="install-guide-content">
      <div class="guide-header">
        <p class="subtitle">按照以下步骤将 Echo Lab 添加到{{ isMobile ? '主屏幕' : '桌面' }}，获得更好的使用体验</p>
      </div>

      <div class="guide-content">
        <!-- 已安装提示 -->
        <div v-if="pwaInstalled" class="already-installed">
          <el-alert title="应用已安装" type="success" description="Echo Lab 已经成功安装到您的设备上，您可以直接从桌面/主屏幕启动应用。" show-icon
            :closable="false" />
          <div class="installed-actions">
            <el-button type="primary" @click="goBack">返回使用</el-button>
          </div>
        </div>

        <!-- 未安装时显示安装指南 -->
        <template v-else>
          <!-- iOS 设备指南 -->
          <div v-if="isIOS" class="guide-section">
            <h2>iOS Safari 浏览器</h2>
            <div class="steps">
              <div class="step">
                <div class="step-number">1</div>
                <div class="step-text">点击底部的<strong>分享按钮</strong> <span class="icon">⎅</span></div>
              </div>
              <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">在弹出的菜单中，滚动并点击<strong>添加到主屏幕</strong></div>
              </div>
              <div class="step">
                <div class="step-number">3</div>
                <div class="step-text">点击右上角的<strong>添加</strong>按钮完成</div>
              </div>
            </div>
          </div>
          <!-- Android 设备指南 -->
          <div v-else-if="isAndroid" class="guide-section">
            <h2>Android Chrome 浏览器</h2>
            <div class="steps">
              <div class="step">
                <div class="step-number">1</div>
                <div class="step-text">点击右上角的<strong>菜单按钮</strong> <span class="icon">⋮</span></div>
              </div>
              <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">选择<strong>添加到主屏幕</strong>选项</div>
              </div>
              <div class="step">
                <div class="step-number">3</div>
                <div class="step-text">点击<strong>添加</strong>按钮完成</div>
              </div>
            </div>
          </div>

          <!-- Chrome 浏览器指南 -->
          <div v-else-if="isChrome" class="guide-section">
            <h2>Chrome 浏览器</h2>
            <div class="steps">
              <div class="step">
                <div class="step-number">1</div>
                <div class="step-text">点击右上角的<strong>菜单按钮</strong> <span class="icon">⋮</span></div>
              </div>
              <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">选择<strong>更多工具</strong> > <strong>创建快捷方式</strong></div>
              </div>
              <div class="step">
                <div class="step-number">3</div>
                <div class="step-text">勾选<strong>在窗口中打开</strong>选项</div>
              </div>
              <div class="step">
                <div class="step-number">4</div>
                <div class="step-text">点击<strong>创建</strong>按钮完成</div>
              </div>
            </div>
          </div>

          <!-- Edge 浏览器指南 -->
          <div v-else-if="isEdge" class="guide-section">
            <h2>Edge 浏览器</h2>
            <div class="steps">
              <div class="step">
                <div class="step-number">1</div>
                <div class="step-text">点击右上角的<strong>菜单按钮</strong> <span class="icon">⋯</span></div>
              </div>
              <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">选择<strong>应用</strong> > <strong>安装网站为应用</strong></div>
              </div>
              <div class="step">
                <div class="step-number">3</div>
                <div class="step-text">点击<strong>安装</strong>按钮完成</div>
              </div>
            </div>
          </div>

          <!-- 其他浏览器指南 -->
          <div v-else class="guide-section">
            <h2>其他浏览器</h2>
            <div class="steps">
              <div class="step">
                <div class="step-number">1</div>
                <div class="step-text">在浏览器菜单中查找<strong>添加到主屏幕</strong>或<strong>创建快捷方式</strong>选项</div>
              </div>
              <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">按照浏览器提示完成添加</div>
              </div>
            </div>
          </div>

          <!-- 一键安装按钮 -->
          <div v-if="installable" class="one-click-install">
            <h2>一键安装</h2>
            <p>您的浏览器支持一键安装功能，点击下方按钮直接安装：</p>
            <el-button type="success" @click="installPWA" size="large">
              一键安装到{{ isMobile ? '主屏幕' : '桌面' }}
            </el-button>
          </div>
        </template>
      </div>
    </div>

    <!-- 不再需要底部返回按钮 -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import SmartPageHeader from '@/components/common/SmartPageHeader.vue';
import { isMobileDevice, getBrowserInfo, isPWAInstallable, isPWAInstalled } from '@/utils/deviceDetector';


// 路由
const router = useRouter();

const installable = ref(false);
let deferredPrompt = null;

// 计算属性
const isMobile = computed(() => isMobileDevice());
const browserInfo = computed(() => getBrowserInfo());
const isIOS = computed(() => browserInfo.value.isIOS);
const isAndroid = computed(() => browserInfo.value.isAndroid);
const isChrome = computed(() => browserInfo.value.name === 'Chrome');
const isEdge = computed(() => browserInfo.value.name === 'Edge');
const pwaInstalled = computed(() => isPWAInstalled());



// 安装PWA
const installPWA = async () => {
  if (!deferredPrompt) {
    if (window.matchMedia('(display-mode: standalone)').matches) {
      ElMessage.info('应用已经安装到您的设备上');
    } else if (!isPWAInstallable()) {
      ElMessage.info('您的浏览器不支持PWA功能，请按照指引手动添加到主屏幕');
    } else {
      ElMessage.info('浏览器暂时不允许安装，可能是因为您需要先使用网站一段时间，请按照指引手动添加到主屏幕');
    }
    return;
  }

  try {
    console.log('尝试显示安装提示...');

    // 显示安装提示 - 这必须由用户手势触发
    deferredPrompt.prompt();

    // 等待用户响应
    const { outcome } = await deferredPrompt.userChoice;
    console.log('用户选择结果:', outcome);

    // 根据用户选择显示消息
    if (outcome === 'accepted') {
      ElMessage.success('安装成功！');
      installable.value = false;
      // 安装成功后返回首页
      router.push('/');
    } else {
      ElMessage.info('您取消了安装');
    }

    // 清除提示，因为它只能使用一次
    deferredPrompt = null;
  } catch (error) {
    console.error('安装PWA时出错:', error);
    ElMessage.error('安装过程中出现错误，请尝试手动安装');
  }
};

onMounted(() => {
  // 检测是否支持PWA
  const isPwaSupported = isPWAInstallable();
  installable.value = isPwaSupported;
  console.log('PWA 支持状态:', isPwaSupported);

  // 监听 beforeinstallprompt 事件
  window.addEventListener('beforeinstallprompt', (e) => {
    // 阻止 Chrome 67 及更早版本自动显示安装提示
    e.preventDefault();
    console.log('beforeinstallprompt 事件已触发!');
    // 保存事件，以便稍后触发
    deferredPrompt = e;
    installable.value = true;
  });
});
</script>

<style scoped>
.install-guide-container {
  width: 100%;
}

.install-guide-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 1.5rem 1rem;
}

/* 移动端优化 */
.mobile-device .install-guide-content {
  padding: 1rem 0.75rem;
}

.guide-header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

.subtitle {
  color: #606266;
  font-size: 1rem;
  margin: 0;
}

.guide-content {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

/* 移动端内容区域优化 */
.mobile-device .guide-content {
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.guide-section {
  margin-bottom: 2rem;
}

/* 移动端指南区域优化 */
.mobile-device .guide-section {
  margin-bottom: 1.5rem;
}

.guide-section h2 {
  font-size: 1.25rem;
  color: #303133;
  margin-bottom: 1rem;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 0.5rem;
}

.steps {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

/* 移动端步骤间距优化 */
.mobile-device .step {
  gap: 0.75rem;
}

.step-number {
  width: 2rem;
  height: 2rem;
  background-color: #67c23a;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  flex-shrink: 0;
}

/* 移动端步骤优化 */
.mobile-device .step-number {
  width: 1.75rem;
  height: 1.75rem;
  font-size: 0.875rem;
}

.step-text {
  flex: 1;
  padding-top: 0.25rem;
}

.icon {
  font-size: 1.25rem;
  vertical-align: middle;
}

.one-click-install {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px dashed #ebeef5;
  text-align: center;
}

.guide-footer {
  text-align: center;
}

/* 已安装提示样式 */
.already-installed {
  text-align: center;
  padding: 1rem;
}

.installed-actions {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
}

/* 使用rem单位，移除媒体查询 */
</style>
