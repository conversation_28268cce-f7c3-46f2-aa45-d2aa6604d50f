<!--
  语言选择页面
  强制用户选择学习语言
-->
<template>
  <div class="language-selection-page">
    <div class="selection-container">
      <div class="header">
        <h1 class="title">{{ isChangingLanguage ? '切换学习语言' : '选择您的学习语言' }}</h1>
        <p class="subtitle">{{ isChangingLanguage ? '选择您想要切换到的语言' : '请选择您想要学习的语言，以获得个性化的学习体验' }}</p>
      </div>

      <div class="language-grid">
        <div
          v-for="lang in availableLanguages"
          :key="lang.value"
          class="language-card"
          :class="{ 
            selected: selectedLanguage === lang.value,
            disabled: lang.status !== 'available'
          }"
          @click="selectLanguage(lang.value)"
        >
          <div class="language-icon">
            {{ lang.flag || '🌐' }}
          </div>
          <div class="language-info">
            <h3 class="language-name">{{ lang.label }}</h3>
            <p class="language-desc">{{ lang.description }}</p>
            <el-tag
              v-if="lang.status !== 'available'"
              :type="getStatusTagType(lang.status)"
              size="small"
              class="status-tag"
            >
              {{ lang.statusText }}
            </el-tag>
          </div>
          <div v-if="selectedLanguage === lang.value" class="selected-icon">
            <el-icon><Check /></el-icon>
          </div>
        </div>
      </div>

      <div class="action-section">
        <el-button
          type="primary"
          size="large"
          :disabled="!selectedLanguage"
          @click="confirmSelection"
          class="confirm-btn"
        >
          {{ isChangingLanguage ? '确认切换' : '确认选择' }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Check } from '@element-plus/icons-vue'
import { SUPPORTED_LANGUAGES } from '@/config/languages'
import { useLanguageStore } from '@/stores/languageStore'

const router = useRouter()
const route = useRoute()
const languageStore = useLanguageStore()

const selectedLanguage = ref(null)

// 检查是否是切换语言（已有语言设置）
const isChangingLanguage = computed(() => !!languageStore.currentLearningLanguage)

// 可用语言列表
const availableLanguages = computed(() => {
  return SUPPORTED_LANGUAGES.filter(lang => lang.status !== 'hidden')
})

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case 'coming-soon':
      return 'warning'
    case 'planned':
      return 'info'
    default:
      return 'info'
  }
}

// 选择语言
const selectLanguage = (langValue) => {
  const lang = SUPPORTED_LANGUAGES.find(l => l.value === langValue)
  
  if (lang.status !== 'available') {
    if (lang.status === 'coming-soon') {
      ElMessage.info(`${lang.label}内容正在制作中，敬请期待！`)
    } else if (lang.status === 'planned') {
      ElMessage.info(`${lang.label}内容计划中，敬请期待！`)
    }
    return
  }

  selectedLanguage.value = langValue
}

// 确认选择
const confirmSelection = async () => {
  if (!selectedLanguage.value) {
    ElMessage.warning('请选择一种语言')
    return
  }

  try {
    // 保存语言设置
    languageStore.setLearningLanguage(selectedLanguage.value)

    ElMessage.success(isChangingLanguage.value ? '语言切换成功' : '语言设置成功')

    // 获取重定向地址
    const redirectTo = route.query.redirect || '/'

    // 跳转到原始页面或首页
    router.replace(redirectTo)
  } catch (error) {
    console.error('保存语言设置失败:', error)
    ElMessage.error('设置失败，请重试')
  }
}

// 页面挂载时初始化
onMounted(() => {
  // 如果已经有语言设置，预选中当前语言
  if (languageStore.currentLearningLanguage) {
    selectedLanguage.value = languageStore.currentLearningLanguage
  }

  // 注意：不再自动跳转，允许用户重新选择语言
})
</script>

<style scoped>
.language-selection-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.selection-container {
  background: white;
  border-radius: 1rem;
  padding: 3rem;
  max-width: 800px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1rem;
  color: #666;
  line-height: 1.5;
}

.language-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 3rem;
}

.language-card {
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
}

.language-card:hover:not(.disabled) {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.language-card.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.language-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.language-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.language-info {
  flex: 1;
}

.language-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.language-desc {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.status-tag {
  margin-top: 0.5rem;
}

.selected-icon {
  color: #3b82f6;
  font-size: 1.5rem;
}

.action-section {
  text-align: center;
}

.confirm-btn {
  padding: 0.75rem 2rem;
  font-size: 1rem;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .language-selection-page {
    padding: 1rem;
  }

  .selection-container {
    padding: 2rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .language-grid {
    grid-template-columns: 1fr;
  }

  .language-card {
    padding: 1rem;
  }
}
</style>
