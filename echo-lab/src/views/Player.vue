<script setup>
import { ref, onMounted, onUnmounted, computed, watch, nextTick, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import DesktopPlayerLayout from '../components/player/DesktopPlayerLayout.vue';
import MobilePlayerLayout from '../components/player/MobilePlayerLayout.vue';
import PlaybackSettingsPanel from '../components/player/PlaybackSettingsPanel.vue';
import FavoriteButton from '../components/common/FavoriteButton.vue';
import FloatingHelpButton from '@/components/help/FloatingHelpButton.vue';
import contentService from '../services/contentService';
import { generateTimeline, extractSequencesFromNodes } from '../utils/timelineGenerator';
// Web Audio API直接使用时间轴数据
// import { precacheSpeedAdjustedAudio } from '../utils/speedAudioCache';
import { isMobileDevice } from '../utils/deviceDetector';
import { loadUserConfig, updateUserConfig } from '../services/userConfigService';

import { useFavoriteStore } from '@/stores/favoriteStore';
import { useUserStore } from '@/stores/userStore';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { useTemplateStore } from '@/stores/templateStore';
import collectionService from '@/services/collectionService';
import { usePlayerConfig } from '@/composables/usePlayerConfig';
import { KeyboardHandler } from '@/utils/keyboardHandler';
import { track } from '@/utils/analytics';
import { EVENTS } from '@/constants/events';
import { decryptJSON } from '../utils/cryptoService';
// 移除不必要的音频处理工具
// import { collectAllAudioUrls, validateAudioUrls } from '@/utils/audioUrlCollector';
// import { createProgressManager } from '@/utils/progressManager';
// import { comparePlaybackConfigs } from '@/utils/playbackStrategy';

// 路由参数
const route = useRoute();
const router = useRouter();
// 从HTML data属性或SSR数据获取contentId
const contentId = computed(() => {
  // 优先从 SSR 数据中的content获取
  if (window.__SSR_DATA__?.content?.id) {
    return window.__SSR_DATA__.content.id;
  }
  // 其次从 data 属性获取
  const dataContentId = document.getElementById('app')?.dataset?.contentId;
  return dataContentId || route.params.id;
});
const collectionId = computed(() => route.query.collection);
// 根据contentId自动计算在合集中的位置
const collectionIndex = computed(() => {
  if (!collection.value?.items?.length || !contentId.value) {
    console.log('🔍 计算collectionIndex: 数据不存在', {
      hasCollection: !!collection.value,
      itemsLength: collection.value?.items?.length || 0,
      contentId: contentId.value
    });
    return -1;
  }
  
  const sortedItems = [...collection.value.items].sort((a, b) => a.sortOrder - b.sortOrder);
  const index = sortedItems.findIndex(item => item.content?.id === contentId.value);
  
  console.log('🔍 计算collectionIndex:', {
    contentId: contentId.value,
    合集项目数: collection.value.items.length,
    排序后项目数: sortedItems.length,
    找到的索引: index,
    当前项目: sortedItems[index]?.content?.name || '未找到'
  });
  
  return index;
});
const playModeFromQuery = computed(() => {
  const mode = route.query.playMode || 'sequence';
  return ['sequence', 'reverse', 'random'].includes(mode) ? mode : 'sequence';
});

// 状态管理
const favoriteStore = useFavoriteStore();
const userStore = useUserStore();
const templateStore = useTemplateStore();



// 状态
const content = ref(null);
const timeline = ref([]);
const isLoading = ref(false);
const showSettings = ref(false);
const showContextPanel = ref(true);
const currentIndex = ref(0);
const collection = ref(null);

// 播放模式状态
const collectionPlayMode = ref('sequence'); // sequence, reverse, random, off
const videoLoopEnabled = ref(false); // 当前视频是否开启循环
const playedIndexes = ref(new Set()); // 随机播放时记录已播放的索引


// 使用配置管理 composable
const configManager = usePlayerConfig(contentId);
const { settings, currentTemplate, serverConfig, configState } = configManager;

// 收藏状态
const isFavorited = computed(() => favoriteStore.isFavorite(contentId.value));
const isLoggedIn = computed(() => userStore.isLoggedIn);


// 视频配置信息
const videoConfig = ref({
  cover: {
    imageUrl: "",
    duration: 3
  },
  copyright: {
    text: "",
    position: "bottomRight"
  }
});

// 文本样式设置
const textStyle = ref({
  fontSize: 2.2, // rem，增加到2.2rem更适合电脑端
  color: '#FFFFFF',
  textShadow: '0.0625rem 0.0625rem 0.125rem rgba(0, 0, 0, 0.7)'
});

// 计算属性
const isMobile = computed(() => {
  return isMobileDevice();
});

// 加载内容
// 设置面板引用
const settingsPanelRef = ref(null);
// 布局组件引用
const layoutRef = ref(null);

// 加载用户配置
const loadUserTextStyleConfig = () => {
  try {
    const userConfig = loadUserConfig();
    if (userConfig.player && userConfig.player.textStyle) {
      textStyle.value = {
        ...textStyle.value,
        ...userConfig.player.textStyle
      };
      console.log('已加载用户文本样式配置:', textStyle.value);
    }
  } catch (error) {
    console.error('加载用户文本样式配置失败:', error);
  }
};

// 处理文本样式变更
const handleTextStyleChange = (newTextStyle) => {
  console.log('文本样式已更新:', newTextStyle);

  // 更新本地文本样式
  textStyle.value = { ...newTextStyle };

  // 更新VideoPlayer组件的文本样式
  const videoPlayer = getVideoPlayer();
  if (videoPlayer) {
    // 如果VideoPlayer组件有textStyle属性，直接更新
    if (videoPlayer.textStyle) {
      videoPlayer.textStyle.fontSize = newTextStyle.fontSize;
      videoPlayer.textStyle.color = newTextStyle.color;
      videoPlayer.textStyle.textShadow = newTextStyle.textShadow;
    }
  }

  // 保存到用户配置
  try {
    const userConfig = loadUserConfig();
    userConfig.player = {
      ...userConfig.player,
      textStyle: { ...newTextStyle }
    };
    updateUserConfig('player.textStyle', newTextStyle);
    console.log('文本样式配置已保存');

    // 设置变更已保存
  } catch (error) {
    console.error('保存文本样式配置失败:', error);
    // 错误已经有自己的上报机制，不需要通过统计系统跟踪
  }
};

// 获取VideoPlayer实例的辅助函数
const getVideoPlayer = () => {
  return layoutRef.value?.videoPlayerRef;
};

const loadContent = async () => {
  // 检查是否有预加载数据
  if (window.__SSR_DATA__?.content) {
    console.log('使用预加载的内容数据');
    const contentData = window.__SSR_DATA__.content;
    
    // 处理configJson解密
    if (contentData.configJson && typeof contentData.configJson === 'string') {
      try {
        contentData.configJson = decryptJSON(contentData.configJson);
      } catch (parseError) {
        console.error('解析/解密configJson失败:', parseError);
      }
    }
    
    // 提取视频配置信息
    if (contentData.configJson?.nodes) {
      const videoConfigNode = Object.values(contentData.configJson.nodes).find(node => node.type === 'videoConfig');
      if (videoConfigNode?.params) {
        if (videoConfigNode.params.cover) {
          videoConfig.value.cover = { ...videoConfigNode.params.cover };
        }
        if (videoConfigNode.params.copyright) {
          videoConfig.value.copyright = { ...videoConfigNode.params.copyright };
        }
      }
    }
    
    content.value = contentData;
    generateServerConfig();
    await loadAndApplyConfiguration();
    await processAudioFiles();
    
    if (shouldAutoplay.value) {
      setTimeout(() => {
        const videoPlayer = getVideoPlayer();
        if (videoPlayer) {
          videoPlayer.togglePlay();
        }
      }, 1000);
    }
  } else {
    // 没有content数据，显示404状态
    console.log('没有content数据，显示404状态');
    content.value = null;
    isLoading.value = false;
    return;
  }
};

// 生成服务器配置（从内容数据生成基础环节结构）
const generateServerConfig = () => {
  if (!content.value || !content.value.configJson) {
    console.error('无法生成服务器配置，内容数据格式不正确:', content.value);
    serverConfig.value = { sections: [] };
    return;
  }

  const sections = [];

  // 从节点中提取序列数据
  const sequences = extractSequencesFromNodes(content.value.configJson);

  // 检查是否有文本序列节点
  const textSequenceNodes = Object.values(content.value.configJson.nodes || {})
    .filter(node => node.type === 'textSequence');

  // 如果有文本序列节点，检查它们的环节设置
  if (textSequenceNodes.length > 0) {
    const firstSequenceNode = textSequenceNodes[0];

    // 检查节点是否有环节设置
    if (firstSequenceNode && firstSequenceNode.params && firstSequenceNode.params.sections && firstSequenceNode.params.sections.length > 0) {
      console.log('使用节点中的环节设置:', firstSequenceNode.params.sections);

      // 直接使用完整的环节配置，保留所有原始字段包括播放参数
      firstSequenceNode.params.sections.forEach((section, index) => {
        const newSection = {
          ...section, // 保留所有原有字段，包括repeatCount等播放参数
          id: section.id || `section_default_${Date.now() + index}`,
          title: section.title || section.name || `环节 ${index + 1}`,
          description: section.description || '',
          processingMode: section.processingMode || (section.sourceNodeId ? 'source' : 'sequence'),
          userEditable: section.userEditable !== false
        };

        // 根据处理模式设置正确的属性
        if (newSection.processingMode === 'sequence') {
          newSection.sourceIndex = newSection.sourceIndex ?? 0; // 默认使用第一个序列
        } else if (newSection.processingMode === 'source') {
          // 如果是基于源节点，设置源节点ID
          if (section.sourceNodeIds && section.sourceNodeIds.length > 0) {
            newSection.sourceNodeIds = [...section.sourceNodeIds];
          } else if (section.sourceNodeId) {
            newSection.sourceNodeIds = [section.sourceNodeId];
          }
        }

        sections.push(newSection);
      });
    } else {
      console.log('节点中没有环节设置，创建默认环节');

      // 只创建一个默认的通读环节（基于序列）
      if (sequences.length > 0) {
        sections.push({
          id: `section_seq_${Date.now()}`,
          title: `环节: 基于序列`,
          description: '默认环节',
          processingMode: 'sequence',
          sourceIndex: 0, // 使用第一个序列
          userEditable: true
        });
      } else {
        // 如果没有序列，尝试创建基于源节点的通读环节
        const textContentNodes = Object.values(content.value.configJson.nodes || {})
          .filter(node => node.type === 'textContent');

        if (textContentNodes.length > 0) {
          sections.push({
            id: `section_src_${Date.now()}`,
            title: `环节: 基于源节点`,
            description: '默认环节',
            processingMode: 'source',
            sourceNodeIds: [textContentNodes[0].id],
            userEditable: true
          });
        }
      }
    }
  } else {
    console.log('未找到文本序列节点，创建默认环节');

    // 创建通读环节（基于序列）
    if (sequences.length > 0) {
      sections.push({
        id: `section_seq_${Date.now()}`,
        title: `环节: 基于序列`,
        description: '默认环节',
        processingMode: 'sequence',
        sourceIndex: 0, // 使用第一个序列
        userEditable: true
      });
    } else {
      // 如果没有序列，尝试创建基于源节点的通读环节
      const textContentNodes = Object.values(content.value.configJson.nodes || {})
        .filter(node => node.type === 'textContent');

      if (textContentNodes.length > 0) {
        sections.push({
          id: `section_src_${Date.now()}`,
          title: `环节: 基于源节点`,
          description: '默认环节',
          processingMode: 'source',
          sourceNodeIds: [textContentNodes[0].id],
          userEditable: true
        });
      }
    }
  }

  // 确保至少有一个环节
  if (sections.length === 0) {
    console.log('没有找到任何有效的环节配置，创建最基本的默认环节');
    sections.push({
      id: `section_fallback_${Date.now()}`,
      title: '默认播放',
      description: '基础播放环节',
      processingMode: 'sequence',
      sourceIndex: 0,
      userEditable: true
    });
  }

  serverConfig.value = {
    sections
  };

  console.log('生成的服务器配置:', serverConfig.value);
};

// 智能加载配置（新的核心逻辑）
const loadAndApplyConfiguration = async () => {
  try {
    console.log('开始智能加载配置...');

    // 2. 在有了内容数据和服务器配置后，加载模板数据
    console.log('加载模板数据...');
    if (templateStore.systemTemplates.length === 0) {
      await templateStore.loadTemplates();
    }

    // 3. 加载播放配置（基于服务器配置和模板数据）
    console.log('加载播放配置...');
    const success = await configManager.smartLoadConfig();

    if (success) {
      generateTimelineFromSettings();
    } else {
      console.log('配置加载被跳过，等待内容加载完成');
      // 如果配置加载失败（通常是因为serverConfig未准备好），使用默认配置
      if (serverConfig.value && serverConfig.value.sections && serverConfig.value.sections.length > 0) {
        configManager.settings.value = JSON.parse(JSON.stringify(serverConfig.value));
        generateTimelineFromSettings();
      }
    }

  } catch (error) {
    console.error('智能加载配置失败:', error);
    ElMessage.error('加载播放策略失败，使用默认配置');

    // 只有在有服务器配置时才使用服务器配置
    if (serverConfig.value && serverConfig.value.sections && serverConfig.value.sections.length > 0) {
      configManager.settings.value = JSON.parse(JSON.stringify(serverConfig.value));
      generateTimelineFromSettings();
    }
  }
};

// 应用模板（统一逻辑）
const applyTemplate = async (template) => {
  try {
    console.log('开始应用模板:', template.name);

    const success = await configManager.applyTemplate(template);
    if (success) {
      // 应用模板只是保存配置，不重新生成时间线
      // 时间线的重新生成在用户点击"应用设置"时进行
      console.log('模板应用成功:', template.name);
      return true;
    } else {
      throw new Error('应用模板失败');
    }
  } catch (error) {
    console.error('应用模板失败:', error);
    ElMessage.error('应用模板失败，使用服务器配置');
    configManager.settings.value = JSON.parse(JSON.stringify(serverConfig.value));
    return false;
  }
};



// 生成时间线
const generateTimelineFromSettings = () => {
  if (content.value && content.value.configJson) {
    timeline.value = generateTimeline(content.value.configJson, settings.value);

    // 检查是否生成了时间线
    if (timeline.value.length === 0) {
      console.error('未能生成时间线，请检查内容配置');
      ElMessage.error('未找到有效的序列数据，请确保内容中包含文本序列节点');
    }
  } else {
    console.error('内容数据格式不正确:', content.value);
    ElMessage.error('内容数据格式不正确');
    timeline.value = [];
  }
};

// 简化的音频处理 - Web Audio API直接使用时间轴
const processAudioFiles = async () => {
  try {
    const startTime = performance.now();
    console.log('🎵 准备Web Audio播放器数据...');

    // 基本验证
    if (!timeline.value || timeline.value.length === 0) {
      console.warn('时间线数据为空');
      isLoading.value = false;
      return;
    }
    // 计算时间轴的总时长
    const totalDuration = timeline.value.reduce((sum, item) => sum + (item.duration || 0), 0);
    console.log(`📊 时间轴总时长: ${totalDuration.toFixed(3)}s\n`);

    // 完成加载
    isLoading.value = false;

    const totalTime = performance.now();
    console.log(`⏱️ 总处理耗时: ${(totalTime - startTime).toFixed(2)}ms`);
    console.log('✅ Web Audio播放器数据准备完成');

  } catch (error) {
    console.error('准备播放器数据失败:', error);
    ElMessage.error('准备播放器数据失败，请稍后重试');

    // 重置加载状态
    isLoading.value = false;
  }
};

// 处理设置保存（来自MobileSettingsPanel的save事件）
const saveSettings = async (newSettings) => {
  try {
    // 验证和优化配置
    const optimizedConfig = configManager.validateAndOptimizeConfig(newSettings);
    if (!optimizedConfig) {
      ElMessage.error('设置格式不正确或验证失败');
      return;
    }

    // 更新设置（不调用updateTemplateState，由saveConfigAndTemplate统一处理）
    configManager.settings.value = optimizedConfig;

    console.log('设置已保存:', optimizedConfig);
  } catch (error) {
    console.error('保存设置失败:', error);
    ElMessage.error('保存设置失败');
  }
};

// 处理设置保存（来自底部按钮）
const handleSettingsSave = async () => {
  if (settingsPanelRef.value) {
    try {
      // 保存设置
      await settingsPanelRef.value.saveSettings();

      // 统一保存逻辑（配置和模板状态同步保存）
      configManager.saveConfigAndTemplate();

      // 重新生成时间线
      generateTimelineFromSettings();

      // 重新处理音频
      await processAudioFiles();

      // 关闭设置面板
      showSettings.value = false;

      // 上报播放设置保存
      track(EVENTS.PLAYBACK_SETTINGS_SAVE);

      ElMessage.success('设置已保存');
    } catch (error) {
      console.error('保存设置失败:', error);
      ElMessage.error('保存设置失败');
    }
  }
};

// 处理模板状态检测（实时检测，不保存设置）
const handleTemplateStateCheck = (newSettings) => {
  console.log('接收到模板状态检测事件', {
    hasCurrentTemplate: !!currentTemplate.value,
    templateName: currentTemplate.value?.name,
    configStateType: configState.value.type
  });
  
  // 直接调用 configManager 的更新方法
  configManager.updateTemplateState();
};

// 切换设置面板
const toggleSettings = () => {
  showSettings.value = !showSettings.value;
  // 上报播放策略设置点击
  if (showSettings.value) {
    track(EVENTS.PLAYBACK_SETTINGS_CLICK);
  }
};

// 处理重置为已保存配置
const handleResetToSaved = async () => {
  try {
    // 重新加载保存的配置和模板状态
    await configManager.loadConfigAndTemplate();
    console.log('已重置为保存的配置');
  } catch (error) {
    console.error('重置配置失败:', error);
  }
};

// 监听设置面板打开状态
watch(showSettings, (isOpen) => {
  if (isOpen && settingsPanelRef.value) {
    // 只重置本地设置为当前props，不重新加载localStorage
    nextTick(() => {
      settingsPanelRef.value.resetLocalSettings?.();
    });
  }
});

// 处理设置重置（重置为视频数据本身的配置）
const handleSettingsReset = async () => {
  try {
    // 直接重置为服务器配置（视频数据本身的配置）
    const success = configManager.resetToServerConfig();
    if (!success) {
      ElMessage.error('无法获取服务器端配置信息');
      return;
    }
    
    generateTimelineFromSettings();

    // 重新处理音频
    processAudioFiles().catch(error => {
      console.error('重新处理音频失败:', error);
    });

    ElMessage.success('已重置为内容原始配置');
  } catch (error) {
    console.error('重置设置失败:', error);
    ElMessage.error('重置设置失败');
  }
};

// 选择模板（统一逻辑）
const selectTemplate = async (template) => {
  try {
    const success = await applyTemplate(template);
    if (success) {
      ElMessage.success(`已应用模板：${template.name}，点击"应用设置"保存`);
    }
  } catch (error) {
    console.error('选择模板失败:', error);
    ElMessage.error('应用模板失败');
  }
};

// 返回首页
const goToHomePage = () => {
  router.replace('/');
};

// 切换收藏状态
const toggleFavorite = async () => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录后再收藏内容');
    router.push('/login');
    return;
  }

  try {
    const result = await favoriteStore.toggleFavorite(contentId.value, content.value);
    if (result.success) {
      const action = favoriteStore.isFavorite(contentId.value) ? 'add' : 'remove';
      
      // 移除收藏事件上报
      
      if (action === 'add') {
        ElMessage.success('收藏成功');
      } else {
        ElMessage.success('已取消收藏');
      }
    } else {
      ElMessage.error(result.error || '操作失败');
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    ElMessage.error('操作失败，请稍后重试');
  }
};

// 切换上下文面板
const toggleContextPanel = () => {
  showContextPanel.value = !showContextPanel.value;

  // 如果有布局组件引用，同步状态
  if (layoutRef.value && layoutRef.value.videoPlayerRef) {
    layoutRef.value.videoPlayerRef.uiState.showPlaylist = showContextPanel.value;
  }
};

// 跳转到指定索引
const jumpToIndex = (index) => {
  console.log('🎯 Player: jumpToIndex 被调用', {
    index,
    timelineLength: timeline.value.length,
    currentIndex: currentIndex.value
  });
  
  if (index >= 0 && index < timeline.value.length) {
    console.log('🎯 Player: 更新 currentIndex 从', currentIndex.value, '到', index);
    currentIndex.value = index;
  } else {
    console.warn('🎯 Player: 无效的索引', index);
  }
};

// 处理加载状态变化
const handleLoadingChange = (loading) => {
  // 只有在内容已加载完成时才处理音频缓冲状态
  // 避免与内容加载loading重复
  if (content.value && content.value.configJson) {
    // 内容已加载，这是音频缓冲状态，不影响主loading
    return;
  }
  // 内容未加载时，更新主loading状态
  isLoading.value = loading;
};

// 键盘事件处理器
let keyboardHandler = null;



// 键盘快捷键处理
const handleKeyDown = (event) => {
  if (!keyboardHandler) return;
  
  const player = getVideoPlayer();
  if (!player) return;
  
  keyboardHandler.handleKeyDown(event);
};





// 加载合集信息
const loadCollectionInfo = async () => {
  if (!collectionId.value) return;
  
  try {
    const response = await collectionService.getCollectionById(collectionId.value);
    if (response.success) {
      collection.value = response.collection;
      console.log('合集信息加载成功:', collection.value);
    }
  } catch (error) {
    console.error('加载合集信息失败:', error);
  }
};

// 处理合集内容选择
const handleCollectionSelect = (index, playMode = null) => {
  console.log('🎥 Player: handleCollectionSelect 被调用', {
    index,
    playMode,
    collectionItemsLength: collection.value?.items?.length || 0
  });
  
  if (!collection.value?.items?.length) {
    console.error('🎥 Player: 合集数据不存在');
    return;
  }
  
  // 获取排序后的合集项目
  const sortedItems = [...collection.value.items].sort((a, b) => a.sortOrder - b.sortOrder);
  
  // 验证索引有效性
  if (index < 0 || index >= sortedItems.length) {
    console.error('🎥 Player: 无效的合集索引', {
      index,
      sortedItemsLength: sortedItems.length
    });
    return;
  }
  
  const item = sortedItems[index];
  if (!item?.content?.id) {
    console.error('🎥 Player: 合集项目数据无效', item);
    return;
  }
  
  console.log('🎥 Player: 准备跳转到内容', {
    targetContentId: item.content.id,
    targetContentName: item.content.name,
    currentContentId: contentId.value
  });
  
  // 如果是当前内容，不需要跳转
  if (item.content.id === contentId.value) {
    console.log('🎥 Player: 已经是当前内容，无需跳转');
    return;
  }
  
  // 停止当前内容的观看统计
  stopViewTracking();
  
  const query = {
    collection: collectionId.value,
    autoplay: 'true'
  };
  
  // 如果指定了播放模式，携带播放模式参数
  if (playMode) {
    query.playMode = playMode;
  } else if (collectionPlayMode.value !== 'sequence') {
    // 如果当前不是顺序播放模式，携带当前播放模式
    query.playMode = collectionPlayMode.value;
  }
  
  console.log('🎥 Player: 跳转到新内容', {
    path: `/player/${item.content.id}`,
    query
  });
  
  router.replace({
    path: `/player/${item.content.id}`,
    query
  });
};

// 自动播放下一个内容
const playNextInCollection = () => {
  console.log('🎥 Player: playNextInCollection 被调用');
  if (!collection.value?.items?.length) {
    console.log('🎥 Player: 没有合集数据');
    return false;
  }
  
  const sortedItems = [...collection.value.items].sort((a, b) => a.sortOrder - b.sortOrder);
  const currentIdx = collectionIndex.value;
  const totalItems = sortedItems.length;
  
  // 验证当前索引有效性
  if (currentIdx < 0 || currentIdx >= totalItems) {
    console.log('🎥 Player: 当前索引无效', currentIdx);
    return false;
  }
  
  const nextIndex = currentIdx + 1;
  console.log('🎥 Player: 当前索引', currentIdx, '下一个索引', nextIndex, '总数', totalItems);
  
  if (nextIndex < totalItems && sortedItems[nextIndex]?.content) {
    console.log('🎥 Player: 跳转到下一个内容');
    handleCollectionSelect(nextIndex);
    return true;
  }
  console.log('🎥 Player: 已是最后一个内容，合集播放完毕');
  ElMessage.info('合集播放完毕');
  return false;
};

// 处理视频播放结束
const handleVideoEnd = () => {
  console.log('🎥 Player: 接收到 video-end 事件', {
    collectionId: collectionId.value,
    collectionPlayMode: collectionPlayMode.value,
    videoLoopEnabled: videoLoopEnabled.value,
    collectionIndex: collectionIndex.value
  });
  
  // 优先级1: 如果当前视频开启了循环，重新播放当前视频
  if (videoLoopEnabled.value) {
    console.log('🎥 Player: 视频循环播放');
    const videoPlayer = getVideoPlayer();
    if (videoPlayer) {
      videoPlayer.restart();
    }
    return;
  }
  
  // 优先级2: 如果在合集中且合集播放模式不是关闭，按合集模式播放
  if (collectionId.value && collectionPlayMode.value !== 'off') {
    let hasNext = false;
    switch (collectionPlayMode.value) {
      case 'sequence':
        hasNext = playNextInCollection();
        break;
      case 'reverse':
        hasNext = playPrevInCollection();
        break;
      case 'random':
        hasNext = playRandomInCollection();
        break;
    }
    
    if (!hasNext) {
      console.log('🎥 Player: 合集播放完毕');
    }
    return;
  }
  
  // 默认行为 - 播放结束
  console.log('🎥 Player: 视频播放结束');
};

// 随机播放合集中的内容
const playRandomInCollection = () => {
  console.log('🎥 Player: playRandomInCollection 被调用');
  if (!collection.value?.items?.length) {
    console.log('🎥 Player: 没有合集数据');
    return false;
  }
  
  const sortedItems = [...collection.value.items].sort((a, b) => a.sortOrder - b.sortOrder);
  const totalItems = sortedItems.length;
  const currentIdx = collectionIndex.value;
  
  console.log('🎥 Player: 总内容数', totalItems, '已播放数', playedIndexes.value.size);
  
  // 只有当前索引有效时才记录
  if (currentIdx >= 0 && currentIdx < totalItems) {
    playedIndexes.value.add(currentIdx);
  }
  
  // 如果所有内容都播放过了，清空记录重新开始
  if (playedIndexes.value.size >= totalItems) {
    playedIndexes.value.clear();
    if (currentIdx >= 0 && currentIdx < totalItems) {
      playedIndexes.value.add(currentIdx);
    }
    console.log('🎥 Player: 所有内容播放完毕，重新开始随机播放');
    ElMessage.info('合集随机播放完毕，重新开始');
  }
  
  // 找到未播放且有效的内容
  const unplayedIndexes = [];
  for (let i = 0; i < totalItems; i++) {
    if (!playedIndexes.value.has(i) && sortedItems[i]?.content) {
      unplayedIndexes.push(i);
    }
  }
  
  console.log('🎥 Player: 未播放的内容索引', unplayedIndexes);
  
  if (unplayedIndexes.length === 0) {
    console.log('🎥 Player: 没有未播放的内容，播放完毕');
    return false;
  }
  
  // 随机选择一个未播放的内容
  const randomIndex = unplayedIndexes[Math.floor(Math.random() * unplayedIndexes.length)];
  console.log('🎥 Player: 随机选择索引', randomIndex);
  handleCollectionSelect(randomIndex);
  return true;
};

// 倒序播放上一个内容
const playPrevInCollection = () => {
  console.log('🎥 Player: playPrevInCollection 被调用');
  if (!collection.value?.items?.length) {
    console.log('🎥 Player: 没有合集数据');
    return false;
  }
  
  const sortedItems = [...collection.value.items].sort((a, b) => a.sortOrder - b.sortOrder);
  const currentIdx = collectionIndex.value;
  const totalItems = sortedItems.length;
  
  // 验证当前索引有效性
  if (currentIdx < 0 || currentIdx >= totalItems) {
    console.log('🎥 Player: 当前索引无效', currentIdx);
    return false;
  }
  
  const prevIndex = currentIdx - 1;
  console.log('🎥 Player: 当前索引', currentIdx, '上一个索引', prevIndex);
  
  if (prevIndex >= 0 && sortedItems[prevIndex]?.content) {
    console.log('🎥 Player: 跳转到上一个内容');
    handleCollectionSelect(prevIndex);
    return true;
  }
  console.log('🎥 Player: 已是第一个内容，倒序播放完毕');
  ElMessage.info('合集倒序播放完毕');
  return false;
};

// 处理视频循环状态变化
const handleVideoLoopChange = (enabled) => {
  videoLoopEnabled.value = enabled;
};

// 设置合集播放模式
const setCollectionPlayMode = (mode) => {
  console.log('🎵 Player: 设置播放模式', mode);
  collectionPlayMode.value = mode;
  // 切换模式时清空播放记录
  playedIndexes.value.clear();
  console.log('🎵 Player: 合集播放模式已更新:', mode);
};

// 观看统计状态
const viewTracking = reactive({
  timer: null,
  recorded: false,
});

// 记录观看开始
const startViewTracking = () => {
  if (!viewTracking.timer && contentId.value) {
    viewTracking.recorded = false;
    
    // 移除开始学习事件上报
    
    // 30秒后记录观看
    viewTracking.timer = setTimeout(async () => {
      if (!viewTracking.recorded) {
        try {
          await contentService.recordView(contentId.value, 30);
          viewTracking.recorded = true;
          console.log('记录观看: 30秒');
        } catch (error) {
          console.error('记录观看失败:', error);
        }
      }
    }, 30000);
  }
};

// 停止观看统计
const stopViewTracking = () => {
  if (viewTracking.timer) {
    clearTimeout(viewTracking.timer);
    viewTracking.timer = null;
    viewTracking.recorded = false;
  }
};

// 初始化内容数据
const initializeContent = async () => {
  // 1. 首先加载内容数据（获取视频基础信息）
  await loadContent();
  // 2. 如果有合集参数，加载合集信息
  if (collectionId.value) {
    await loadCollectionInfo();
  }
  // 3. 设置播放模式
  collectionPlayMode.value = playModeFromQuery.value;
  // 4. 开始观看统计
  startViewTracking();
};

// 检查是否需要自动播放
const shouldAutoplay = computed(() => route.query.autoplay === 'true');

// 生命周期钩子
onMounted(async () => {
  // 加载用户文本样式配置
  loadUserTextStyleConfig();

  // 初始化键盘处理器
  keyboardHandler = new KeyboardHandler(
    getVideoPlayer,
    currentIndex,
    timeline,
    (newIndex) => {
      currentIndex.value = newIndex;
    }
  );

  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeyDown);

  // 检查收藏状态
  if (userStore.isLoggedIn && contentId.value) {
    favoriteStore.checkFavorite(contentId.value);
  }

  // 移除内容查看事件上报

  // 在组件挂载后初始化内容
  await initializeContent();
});

onUnmounted(() => {
  // 停止观看统计
  stopViewTracking();
  
  // 清理键盘处理器
  if (keyboardHandler) {
    keyboardHandler.cleanup();
    keyboardHandler = null;
  }

  // 移除事件监听
  window.removeEventListener('keydown', handleKeyDown);

  // Web Audio Player会自动清理资源

  // 清理音频缓存（可选，根据需要决定是否清理）
  // 注意：这会清理所有缓存，可能影响其他页面的性能
  // import { clearSpeedAdjustedCache } from '@/utils/speedAudioCache.js';
  // import { clearSilenceCache } from '@/utils/silenceCache.js';
  // clearSpeedAdjustedCache();
  // clearSilenceCache();
});

// 监听路由变化
watch(() => route.params.id, (newId, oldId) => {
  console.log('路由参数变化:', { newId, oldId });
  if (newId !== oldId) {
    // 停止上一个内容的观看统计
    stopViewTracking();
    // 初始化新内容
    initializeContent();
  }
});

// 监听合集相关查询参数变化
watch(() => [route.query.collection, route.query.index, route.query.playMode], ([newCollection, newIndex, newPlayMode], [oldCollection, oldIndex, oldPlayMode]) => {
  const hasCollectionChange = newCollection !== oldCollection;
  const hasIndexChange = newIndex !== oldIndex;
  const hasPlayModeChange = newPlayMode !== oldPlayMode;
  
  if (hasCollectionChange) {
    console.log('合集参数变化:', { newCollection, oldCollection });
    loadCollectionInfo();
  }
  
  if (hasPlayModeChange) {
    console.log('🎵 Player: URL播放模式变化:', { newPlayMode, oldPlayMode });
    setCollectionPlayMode(newPlayMode || 'sequence');
  }
});


</script>

<template>
  <div class="player-page" :class="{ 'mobile-device': isMobile }">
    <!-- AI 生成内容提示 -->
    <div class="ai-disclaimer">
      视频内容使用 AI 辅助生成文本和 TTS 合成音频，在语音表达上可能有所欠缺，建议作为学习辅助工具使用。
    </div>

    <!-- 调试信息 -->
    <!-- content: {{ content ? 'exists' : 'null' }}, configJson: {{ content?.configJson ? 'exists' : 'null' }} -->

    <!-- 404状态 -->
    <template v-if="content === null && !isLoading">
      <div class="error-container">
        <div class="error-content">
          <h2>内容不可用</h2>
          <p>该内容可能已被删除、下架或您没有访问权限</p>
          <div class="error-actions">
            <el-button type="primary" @click="goToHomePage">
              <el-icon>
                <i-ep-house />
              </el-icon>
              返回首页
            </el-button>
            <el-button @click="router.back()">
              <el-icon>
                <i-ep-arrow-left />
              </el-icon>
              返回上页
            </el-button>
          </div>
        </div>
      </div>
    </template>

    <!-- 只有在内容数据准备好之后才渲染播放器组件 -->
    <template v-else-if="content && content.configJson">
      <!-- 桌面端布局 -->
      <DesktopPlayerLayout v-if="!isMobile" ref="layoutRef" :timeline="timeline" :currentIndex="currentIndex"
        :isLoading="isLoading" :showContextPanel="showContextPanel" :videoConfig="videoConfig"
        :configJson="content.configJson" :contentData="content" :collection="collection" :collection-index="collectionIndex"
        :collection-play-mode="collectionPlayMode"
        @index-change="currentIndex = $event" @loading-change="handleLoadingChange" @open-settings="toggleSettings" 
        @toggle-playlist="toggleContextPanel" @select-item="jumpToIndex" @go-home="goToHomePage" 
        @collection-select="handleCollectionSelect" @video-end="handleVideoEnd" @video-loop-change="handleVideoLoopChange" @play-mode-change="setCollectionPlayMode">
        <!-- 收藏按钮 -->
        <template #actions>
          <FavoriteButton :content-id="contentId" :content-data="content" :in-player-controls="true" />
        </template>
      </DesktopPlayerLayout>

      <!-- 移动端布局 -->
      <MobilePlayerLayout v-else ref="layoutRef" :timeline="timeline" :currentIndex="currentIndex"
        :isLoading="isLoading" :showContextPanel="showContextPanel" :videoConfig="videoConfig"
        :configJson="content.configJson" :contentData="content" :collection="collection" :collection-index="collectionIndex"
        :collection-play-mode="collectionPlayMode"
        @index-change="currentIndex = $event" @loading-change="handleLoadingChange" @open-settings="toggleSettings"
        @toggle-playlist="toggleContextPanel" @select-item="jumpToIndex" @go-home="goToHomePage"
        @collection-select="handleCollectionSelect" @video-end="handleVideoEnd" @play-mode-change="setCollectionPlayMode">
        <!-- 收藏按钮 -->
        <template #actions>
          <FavoriteButton :content-id="contentId" :content-data="content" size="small" :in-player-controls="true" />
        </template>
      </MobilePlayerLayout>
    </template>

    <!-- 内容加载中的占位符 -->
    <div v-else class="loading-placeholder" v-loading="true" element-loading-text="正在加载内容...">
    </div>

    <!-- 统一使用抽屉组件作为设置面板 -->
    <el-drawer v-model="showSettings" :direction="isMobile ? 'btt' : 'rtl'" :size="isMobile ? '75%' : '70%'"
      :with-header="false" class="settings-drawer">
      <div class="drawer-header">
        <h3>播放策略设置</h3>
        <el-button link @click="showSettings = false">关闭</el-button>
      </div>
      <div class="drawer-content">
        <PlaybackSettingsPanel ref="settingsPanelRef" :settings="settings" :server-config="serverConfig"
          :current-template="currentTemplate" :config-state="configState" :content="content" @save="saveSettings" @select-template="selectTemplate"
          @check-template-state="handleTemplateStateCheck" @reset-to-saved="handleResetToSaved" />
      </div>
      <div class="drawer-footer">
        <div class="footer-buttons">
          <el-button @click="handleSettingsReset" type="warning">重置为原始配置</el-button>
          <el-button type="primary" @click="handleSettingsSave">应用设置</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 浮动帮助按钮 -->
    <FloatingHelpButton page-type="player" />
  </div>
</template>

<style scoped>
.player-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background-color: #f0f2f5;
}

/* AI 生成内容提示样式 */
.ai-disclaimer {
  background-color: #f8f9fa;
  color: #6c757d;
  text-align: center;
  padding: 0.25rem 1rem;
  font-size: 0.75rem;
  line-height: 1.3;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}



/* 移动端AI提示样式调整 */
.mobile-device .ai-disclaimer {
  padding: 0.1875rem 0.75rem;
  font-size: 0.6875rem;
}

.loading-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #000;
}

/* 自定义 Element Plus 按钮样式 */
:deep(.el-button) {
  border-radius: 0.25rem;
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.el-button:hover) {
  transform: translateY(-0.125rem);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

:deep(.el-dialog__header) {
  margin-bottom: 0;
  padding: 1rem;
  border-bottom: 1px solid #e6e6e6;
}

:deep(.el-dialog__body) {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

/* 移动设备样式 - 不使用媒体查询 */
.mobile-device :deep(.el-dialog) {
  width: 95% !important;
  margin-top: 1rem !important;
}

.mobile-device :deep(.el-dialog__body) {
  max-height: 80vh;
  padding: 0;
}

.mobile-device :deep(.el-dialog__footer) {
  padding: 0.5rem;
}

.mobile-device :deep(.el-dialog__footer .el-button) {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

/* 统一抽屉式设置面板样式 */
:deep(.settings-drawer .el-drawer) {
  overflow: hidden;
}

:deep(.settings-drawer .el-drawer__body) {
  padding: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100vh;
  /* 确保抽屉内容不超出视口高度 */
}

/* 移动端特有样式 */
:deep(.settings-drawer .el-drawer[rtl="false"]) {
  border-radius: 0.75rem 0.75rem 0 0;
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 0.0625rem solid #ebeef5;
  background-color: #ffffff;
}

.drawer-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: #303133;
}

.drawer-content {
  flex: 1;
  overflow-y: auto;
  height: calc(100% - 6rem);
  /* 减去头部和底部的高度 */
  display: flex;
  flex-direction: column;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0.75rem 1rem;
  border-top: 0.0625rem solid #ebeef5;
  background-color: #fff;
}

.footer-buttons {
  display: flex;
  gap: 0.5rem;
}

/* 设置对话框样式 */
.settings-dialog {
  display: flex;
}

.settings-dialog :deep(.el-dialog__header) {
  background-color: #f5f7fa;
}

.settings-dialog :deep(.el-dialog__headerbtn) {
  top: 1rem;
}

/* 错误页面样式 */
.error-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.error-content {
  text-align: center;
  max-width: 400px;
  padding: 2rem;
}

.error-content h2 {
  font-size: 1.5rem;
  color: #303133;
  margin: 0 0 1rem 0;
}

.error-content p {
  color: #606266;
  margin: 0 0 2rem 0;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* 移动端适配 */
.mobile-device .error-actions {
  flex-direction: column;
}
</style>
