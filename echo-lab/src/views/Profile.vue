<template>
  <div class="profile-container">
    <!-- 页面标题栏 -->
    <SmartPageHeader title="个人信息" :force-show-back="true">
      <template #actions>
        <el-button type="primary" @click="handleSave" :loading="saving" :disabled="!hasChanges" size="small">
          保存修改
        </el-button>
      </template>
    </SmartPageHeader>

    <div class="profile-content">
      <el-card class="profile-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>

        <el-form label-position="top" :model="userForm" ref="formRef">
          <el-form-item label="用户ID">
            <div class="input-with-button">
              <el-input v-model="userId" disabled></el-input>
              <el-button type="primary" @click="copyUserId" icon="Document">
                复制
              </el-button>
            </div>
            <div class="form-hint">用户唯一标识符</div>
          </el-form-item>

          <el-form-item label="邮箱">
            <el-input v-model="userForm.email" disabled></el-input>
            <div class="form-hint">邮箱地址不可修改</div>
          </el-form-item>

          <el-form-item label="用户名" prop="username">
            <el-input v-model="userForm.username" placeholder="请输入用户名"></el-input>
            <div class="form-hint">用户名将显示在个人资料中</div>
          </el-form-item>

          <el-form-item label="最后登录时间">
            <el-input v-model="lastLoginTime" disabled></el-input>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 会话管理 -->
      <el-card class="profile-card">
        <template #header>
          <div class="card-header">
            <span>设备登录管理</span>
          </div>
        </template>

        <SessionManager />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useUserStore } from '@/stores/userStore';
import { ElMessage } from 'element-plus';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import SmartPageHeader from '@/components/common/SmartPageHeader.vue';
import SessionManager from '@/components/user/SessionManager.vue';

// 用户状态存储
const userStore = useUserStore();

// 表单引用
const formRef = ref(null);

// 保存状态
const saving = ref(false);

// 用户表单数据
const userForm = ref({
  email: '',
  username: '',
});

// 计算属性：用户ID
const userId = computed(() => {
  if (!userStore.user || !userStore.user.id) {
    return '未知';
  }
  return userStore.user.id;
});

// 计算属性：最后登录时间
const lastLoginTime = computed(() => {
  if (!userStore.user || !userStore.user.lastLoginAt) {
    return '未知';
  }
  return formatDate(userStore.user.lastLoginAt);
});

// 计算属性：是否有修改
const hasChanges = computed(() => {
  if (!userStore.user) return false;
  return userForm.value.username !== (userStore.user.username || '');
});



// 格式化日期
function formatDate(dateString) {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return '未知';
    }
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  } catch (error) {
    console.error('日期格式化失败:', error);
    return '未知';
  }
}

// 复制用户ID
function copyUserId() {
  if (!userId.value || userId.value === '未知') {
    ElMessage.warning('没有可复制的用户ID');
    return;
  }

  // 尝试使用现代Clipboard API
  if (navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard.writeText(userId.value)
      .then(() => {
        ElMessage.success('用户ID已复制到剪贴板');
      })
      .catch(err => {
        console.error('使用Clipboard API复制失败:', err);
        // 如果Clipboard API失败，尝试备用方法
        fallbackCopyUserId();
      });
  } else {
    // 浏览器不支持Clipboard API，使用备用方法
    fallbackCopyUserId();
  }
}

// 备用复制方法（使用临时文本区域）
function fallbackCopyUserId() {
  try {
    // 创建临时文本区域
    const textArea = document.createElement('textarea');
    textArea.value = userId.value;

    // 设置样式使其不可见
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);

    // 选择并复制文本
    textArea.focus();
    textArea.select();
    const successful = document.execCommand('copy');

    // 移除临时元素
    document.body.removeChild(textArea);

    if (successful) {
      ElMessage.success('用户ID已复制到剪贴板');
    } else {
      ElMessage.warning('复制失败，请手动选择并复制');
    }
  } catch (err) {
    console.error('备用复制方法失败:', err);
    ElMessage.error('复制失败，请手动选择并复制');
  }
}

// 保存修改
async function handleSave() {
  if (!hasChanges.value) return;

  saving.value = true;
  try {
    const result = await userStore.updateUser({
      username: userForm.value.username,
    });

    if (result.success) {
      ElMessage.success('个人信息更新成功');
    } else {
      ElMessage.error(result.error || '更新失败');
    }
  } catch (error) {
    console.error('更新用户信息失败:', error);
    ElMessage.error('更新失败，请稍后重试');
  } finally {
    saving.value = false;
  }
}

// 初始化表单数据
function initFormData() {
  if (userStore.user) {
    userForm.value.email = userStore.user.email || '';
    userForm.value.username = userStore.user.username || '';
  }
}

// 组件挂载时初始化数据
onMounted(async () => {
  // 获取最新的用户信息
  await userStore.fetchCurrentUser();

  // 初始化表单数据
  initFormData();
});
</script>

<style scoped>
.profile-container {
  width: 100%;
  padding: 0;
}

.profile-content {
  padding: 2rem;
}



.profile-card {
  margin-bottom: 2rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-hint {
  font-size: 0.75rem;
  color: #909399;
  margin-top: 0.25rem;
}

.input-with-button {
  display: flex;
  gap: 0.5rem;
}

.input-with-button .el-input {
  flex: 1;
}
</style>
