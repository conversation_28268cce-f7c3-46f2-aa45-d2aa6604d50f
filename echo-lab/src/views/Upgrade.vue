<template>
  <div class="upgrade-container">
    <!-- 页面标题栏 -->
    <SmartPageHeader title="升级会员" :force-show-back="true" />

    <div class="upgrade-header">
      <h1 class="upgrade-title">升级会员</h1>
      <p class="upgrade-subtitle">解锁更多功能，提升您的使用体验</p>
    </div>

    <div class="upgrade-content">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>
      <template v-else>
        <!-- 当前等级信息 -->
        <div class="current-level-info">
          <h2 class="section-title">当前等级</h2>
          <div class="level-card current">
            <div class="level-header">
              <h3>{{ userStore.userLevelName }}</h3>
              <div class="level-badge current">当前</div>
            </div>
            <div v-if="userStore.hasActiveSubscription" class="subscription-info">
              <p>
                订阅状态: <span class="active">有效</span>
              </p>
              <p v-if="userStore.subscription && userStore.subscription.endDate">
                到期时间: {{ formatDate(userStore.subscription.endDate) }}
              </p>
              <p v-else-if="userStore.subscription">
                订阅类型: <span class="lifetime">终身会员</span>
              </p>
            </div>
          </div>
        </div>

        <!-- 等级列表 -->
        <div class="levels-container">
          <h2 class="section-title">可用等级</h2>
          <div class="levels-grid">
            <div v-for="level in levels" :key="level.level" class="level-card" :class="{
              'current': userStore.userLevel === level.level,
              'recommended': level.level === recommendedLevel,
              'disabled': userStore.userLevel > level.level
            }">
              <div class="level-header">
                <h3>{{ level.name }}</h3>
                <div v-if="level.level === recommendedLevel" class="level-badge recommended">推荐</div>
              </div>
              <div class="level-price" v-if="level.price">
                <span class="price">¥{{ level.price }}</span>
                <span class="period" v-if="level.period">/{{ level.period }}</span>
              </div>
              <div class="level-description">
                <p>{{ level.description }}</p>
              </div>
              <div class="level-features">
                <h4>包含功能</h4>
                <ul>
                  <li v-for="feature in getLevelFeatures(level.level)" :key="feature.key">
                    <el-icon class="feature-icon">
                      <i-ep-check />
                    </el-icon>
                    <span>{{ feature.name }}</span>
                  </li>
                </ul>
              </div>
              <div class="level-action">
                <el-button v-if="userStore.userLevel < level.level" type="primary" @click="upgradeToLevel(level.level)"
                  class="upgrade-button">
                  升级到{{ level.name }}
                </el-button>
                <el-button v-else-if="userStore.userLevel === level.level" type="info" disabled class="current-button">
                  当前等级
                </el-button>
                <el-button v-else type="info" disabled>
                  已超过此等级
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 功能对比表 -->
        <div class="features-comparison">
          <h2 class="section-title">功能对比</h2>
          <div class="comparison-table-container">
            <el-table :data="featuresList" border stripe class="comparison-table">
              <el-table-column prop="name" label="功能" min-width="180" />
              <el-table-column v-for="level in levels" :key="level.level" :label="level.name" align="center"
                min-width="120">
                <template #default="scope">
                  <el-icon v-if="hasFeature(level.level, scope.row.key)" color="#67C23A" class="feature-check">
                    <i-ep-check />
                  </el-icon>
                  <el-icon v-else color="#F56C6C" class="feature-close">
                    <i-ep-close />
                  </el-icon>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/stores/userStore';
import { getAllLevels } from '@/services/userLevelService';
import { getAllFeatures, getLevelFeatureMapping } from '@/services/featurePermissionService';
import SmartPageHeader from '@/components/common/SmartPageHeader.vue';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { ElMessage } from 'element-plus';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const loading = ref(true);
const levels = ref([]);
const redirect = computed(() => route.query.redirect || '/');
const requiredLevel = computed(() => parseInt(route.query.requiredLevel) || 1);
const recommendedLevel = computed(() => Math.max(requiredLevel.value, (userStore.userLevel || 0) + 1));

// 功能列表 - 从服务器获取
const featuresList = ref([]);

// 等级功能映射 - 从服务器获取
const levelFeatures = ref({});

// 获取所有等级
async function fetchLevels() {
  loading.value = true;
  try {
    const result = await getAllLevels();
    levels.value = result;
  } catch (error) {
    console.error('获取等级信息失败:', error);
    ElMessage.error('获取等级信息失败');
  } finally {
    loading.value = false;
  }
}

// 获取等级拥有的功能
function getLevelFeatures(level) {
  const features = levelFeatures.value[level] || [];
  return featuresList.value.filter(f => features.includes(f.key));
}

// 检查等级是否拥有特定功能
function hasFeature(level, featureKey) {
  const features = levelFeatures.value[level] || [];
  return features.includes(featureKey);
}

// 格式化日期
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

// 升级到指定等级
function upgradeToLevel(level) {
  // 这里应该跳转到支付页面或显示支付弹窗
  // 目前仅显示提示信息
  ElMessage.info(`即将升级到${level}级会员，支付功能尚未实现`);

  // 如果是管理员，直接模拟升级成功
  if (userStore.isAdmin) {
    userStore.setUserLevel(level);
    ElMessage.success('管理员模式：升级成功');

    // 如果有重定向地址，跳转回去
    if (redirect.value) {
      router.push(redirect.value);
    }
  }
}

// 获取功能列表
async function fetchFeatures() {
  try {
    const features = await getAllFeatures();
    if (features && features.length > 0) {
      featuresList.value = features;
    }

    const mapping = await getLevelFeatureMapping();
    if (mapping && Object.keys(mapping).length > 0) {
      levelFeatures.value = mapping;
    }
  } catch (error) {
    console.error('获取功能列表失败:', error);
  }
}

onMounted(async () => {
  // 获取用户等级信息
  if (userStore.isLoggedIn && userStore.userLevel === undefined) {
    await userStore.fetchUserLevel();
  }

  // 获取功能列表和等级映射
  await fetchFeatures();

  // 获取所有等级
  await fetchLevels();
});
</script>

<style scoped>
.upgrade-container {
  max-width: 75rem;
  /* 匹配Home.vue中的max-width */
  margin: 0 auto;
  padding: 2rem 1rem;
  color: var(--el-text-color-primary);
  background-color: #f5f7fa;
  /* 匹配Home.vue中的背景色 */
}

.upgrade-header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  background: linear-gradient(135deg, #4a90e2, #8e54e9);
  /* 匹配Home.vue中的渐变色 */
  border-radius: 0.5rem;
  padding: 2rem;
  color: white;
}

.upgrade-title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: white;
  font-weight: 600;
}

.upgrade-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 36rem;
  margin: 0 auto;
  line-height: 1.6;
}

.loading-container {
  padding: 2rem;
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #303133;
  font-weight: 600;
}

.current-level-info {
  margin-bottom: 2rem;
}

.level-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
  margin-bottom: 1rem;
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.level-card:hover {
  transform: translateY(-0.125rem);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.12);
}

.level-card.current {
  border-color: var(--el-color-primary);
  background-color: rgba(var(--el-color-primary-rgb), 0.05);
}

.level-card.recommended {
  border-color: var(--el-color-success);
  background-color: rgba(var(--el-color-success-rgb), 0.05);
  position: relative;
  z-index: 1;
}

.level-card.disabled {
  opacity: 0.7;
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.level-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: #303133;
}

.level-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.8rem;
  font-weight: bold;
}

.level-badge.current {
  background-color: var(--el-color-primary);
  color: white;
}

.level-badge.recommended {
  background-color: var(--el-color-success);
  color: white;
}

.level-price {
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--el-color-primary);
}

.level-price .period {
  font-size: 0.875rem;
  color: var(--el-text-color-secondary);
  font-weight: normal;
}

.subscription-info {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--el-border-color-lighter);
  line-height: 1.5;
}

.subscription-info .active {
  color: var(--el-color-success);
  font-weight: bold;
}

.subscription-info .lifetime {
  color: var(--el-color-warning);
  font-weight: bold;
}

.levels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.level-description {
  margin-bottom: 1.5rem;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.level-features {
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.level-features h4 {
  font-size: 1rem;
  margin-bottom: 0.75rem;
  color: #303133;
  font-weight: 600;
}

.level-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.level-features li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.level-features li .feature-icon {
  margin-right: 0.5rem;
  color: var(--el-color-success);
  font-size: 1rem;
  margin-top: 0.1rem;
  flex-shrink: 0;
}

.level-action {
  margin-top: auto;
  padding-top: 1rem;
}

.upgrade-button {
  width: 100%;
  height: 2.25rem;
  font-size: 0.875rem;
  font-weight: 600;
  border-radius: 0.25rem;
}

.current-button {
  width: 100%;
  height: 2.25rem;
  font-size: 0.875rem;
  font-weight: 600;
  border-radius: 0.25rem;
}

.features-comparison {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--el-border-color-light);
}

.comparison-table-container {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
}

.comparison-table {
  width: 100%;
}

.feature-check,
.feature-close {
  font-size: 1.125rem;
}

/* 移动端样式 - 使用类名而非媒体查询 */
.mobile-device .levels-grid {
  grid-template-columns: 1fr;
}

.mobile-device .upgrade-title {
  font-size: 1.5rem;
}

.mobile-device .upgrade-subtitle {
  font-size: 1rem;
}

.mobile-device .section-title {
  font-size: 1.25rem;
}
</style>
