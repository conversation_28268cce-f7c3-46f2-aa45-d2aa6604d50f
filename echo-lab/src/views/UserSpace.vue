<!--
  用户空间页面
  展示用户的基本信息、公开内容和公开合集
-->
<template>
  <div class="user-space" :class="{ 'mobile-layout': isMobile }">
    <!-- 页面标题栏 -->
    <SmartPageHeader :title="getUserDisplayName(userInfo)" :force-show-back="true" />

    <div class="user-space-content">
      <!-- 用户信息头部 -->
      <div class="user-header">
        <div class="user-info">
          <div class="user-avatar">
            <el-icon :size="isMobile ? 48 : 64">
              <i-ep-user />
            </el-icon>
          </div>
          <div class="user-details">
            <h1 class="user-name">{{ getUserDisplayName(userInfo) }}</h1>
            <p class="user-join-date">注册时间：{{ formatDate(userInfo?.created_at) }}</p>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="user-stats">
          <div class="stat-item">
            <span class="stat-number">{{ stats?.contentCount || 0 }}</span>
            <span class="stat-label">内容</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ stats?.collectionCount || 0 }}</span>
            <span class="stat-label">合集</span>
          </div>
        </div>
      </div>

      <!-- 内容标签页 -->
      <el-tabs v-model="activeTab" class="user-tabs" @tab-change="handleTabChange" :stretch="true">
        <el-tab-pane label="内容" name="contents">
          <div class="tab-content">
            <!-- 内容网格 -->
            <div v-if="contents.length > 0" class="content-grid">
              <PublicContentCard v-for="content in contents" :key="content.id" :content="content" class="grid-item" :show-author="false" />
            </div>

            <!-- 空状态 -->
            <div v-else-if="!contentsLoading" class="empty-state">
              <el-empty description="该用户还没有发布任何内容" :image-size="100" />
            </div>

            <!-- 加载状态 -->
            <div v-if="contentsLoading" class="loading-state">
              <el-skeleton :rows="3" animated />
            </div>

            <!-- 分页 -->
            <!-- 加载更多 -->
            <div v-if="hasMoreContents" class="load-more-section">
              <el-button type="primary" size="large" :loading="contentsLoading" @click="loadMoreContents"
                class="load-more-btn">
                <span v-if="!contentsLoading">加载更多</span>
                <span v-else>加载中...</span>
              </el-button>
            </div>

            <!-- 没有更多数据提示 -->
            <div v-if="!hasMoreContents && contents.length > 0" class="no-more-tip">
              <el-divider>
                <span class="no-more-text">已显示全部内容</span>
              </el-divider>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="合集" name="collections">
          <div class="tab-content">
            <!-- 合集网格 -->
            <CollectionGrid :collections="collections" :loading="collectionsLoading && collections.length === 0"
              :show-actions="false" :show-author="false" :show-view-count="true" :show-favorite-count="true"
              :show-favorite-button="false" :show-status="false" :show-pagination="false" empty-text="该用户还没有发布任何合集" />

            <!-- 加载更多合集 -->
            <div v-if="hasMoreCollections" class="load-more-section">
              <el-button type="primary" size="large" :loading="collectionsLoading" @click="loadMoreCollections"
                class="load-more-btn">
                <span v-if="!collectionsLoading">加载更多</span>
                <span v-else>加载中...</span>
              </el-button>
            </div>

            <!-- 没有更多数据提示 -->
            <div v-if="!hasMoreCollections && collections.length > 0" class="no-more-tip">
              <el-divider>
                <span class="no-more-text">已显示全部合集</span>
              </el-divider>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { useRoute } from 'vue-router';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { ElMessage } from 'element-plus';
import userService from '@/services/userService';
import SmartPageHeader from '@/components/common/SmartPageHeader.vue';
import PublicContentCard from '@/components/content/PublicContentCard.vue';
import CollectionGrid from '@/components/collection/CollectionGrid.vue';
import { isMobileDevice } from '@/utils/deviceDetector';

const route = useRoute();

// 设备检测
const isMobile = ref(isMobileDevice());

// 响应式数据
const userInfo = ref(null);
const stats = ref(null);
const activeTab = ref('contents');

// 内容相关数据
const contents = ref([]);
const contentsLoading = ref(false);
const contentsPagination = ref({
  page: 1,
  pageSize: 20,
  total: 0,
});

// 计算是否还有更多内容
const hasMoreContents = computed(() => {
  return contents.value.length < contentsPagination.value.total;
});

// 合集相关数据
const collections = ref([]);
const collectionsLoading = ref(false);
const collectionsPagination = ref({
  page: 1,
  pageSize: 20,
  total: 0,
});

// 计算是否还有更多合集
const hasMoreCollections = computed(() => {
  return collections.value.length < collectionsPagination.value.total;
});

// 获取用户ID
const userId = ref(route.params.id);

// 监听路由变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    userId.value = newId;
    loadUserData();
  }
});

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// 获取用户显示名称
const getUserDisplayName = (user) => {
  if (!user) return '未知用户';

  // 优先显示用户名
  if (user.username && user.username.trim()) {
    return user.username;
  }

  // 显示用户ID
  if (user.id) {
    return user.id;
  }

  return '未知用户';
};

// 加载用户数据
const loadUserData = async () => {
  try {
    // 并行加载用户信息和统计数据
    const [userResponse, statsResponse] = await Promise.all([
      userService.getUserInfo(userId.value),
      userService.getUserStats(userId.value)
    ]);

    if (userResponse.success) {
      userInfo.value = userResponse.user;
    } else {
      throw new Error(userResponse.error || '获取用户信息失败');
    }

    if (statsResponse.success) {
      stats.value = statsResponse.stats;
    }

    // 加载默认标签页的内容
    if (activeTab.value === 'contents') {
      loadUserContents();
    } else {
      loadUserCollections();
    }
  } catch (error) {
    console.error('加载用户数据失败:', error);
    ElMessage.error(error.message || '加载用户数据失败');
  }
};

// 加载用户内容
const loadUserContents = async (append = false) => {
  contentsLoading.value = true;
  try {
    const response = await userService.getUserContents(userId.value, {
      page: append ? contentsPagination.value.page + 1 : 1,
      pageSize: contentsPagination.value.pageSize,
      sortBy: 'updated_at',
      sortOrder: 'DESC',
    });

    if (response.success) {
      if (append) {
        // 追加模式：将新内容添加到现有列表
        contents.value = [...contents.value, ...(response.contents || [])];
        contentsPagination.value.page += 1;
      } else {
        // 重新加载模式：替换现有内容
        contents.value = response.contents || [];
        contentsPagination.value.page = 1;
      }

      if (response.pagination) {
        contentsPagination.value = {
          ...contentsPagination.value,
          total: response.pagination.total,
        };
      }
    } else {
      throw new Error(response.error || '获取用户内容失败');
    }
  } catch (error) {
    console.error('加载用户内容失败:', error);
    ElMessage.error(error.message || '加载用户内容失败');
  } finally {
    contentsLoading.value = false;
  }
};

// 加载更多内容
const loadMoreContents = async () => {
  if (contentsLoading.value || !hasMoreContents.value) return;
  await loadUserContents(true);
};

// 加载用户合集
const loadUserCollections = async (append = false) => {
  collectionsLoading.value = true;
  try {
    const response = await userService.getUserCollections(userId.value, {
      page: append ? collectionsPagination.value.page + 1 : 1,
      pageSize: collectionsPagination.value.pageSize,
      sortBy: 'updated_at',
      sortOrder: 'DESC',
    });

    if (response.success) {
      if (append) {
        // 追加模式：将新合集添加到现有列表
        collections.value = [...collections.value, ...(response.collections || [])];
        collectionsPagination.value.page += 1;
      } else {
        // 重新加载模式：替换现有合集
        collections.value = response.collections || [];
        collectionsPagination.value.page = 1;
      }

      if (response.pagination) {
        collectionsPagination.value = {
          ...collectionsPagination.value,
          total: response.pagination.total,
        };
      }
    } else {
      throw new Error(response.error || '获取用户合集失败');
    }
  } catch (error) {
    console.error('加载用户合集失败:', error);
    ElMessage.error(error.message || '加载用户合集失败');
  } finally {
    collectionsLoading.value = false;
  }
};

// 加载更多合集
const loadMoreCollections = async () => {
  if (collectionsLoading.value || !hasMoreCollections.value) return;
  await loadUserCollections(true);
};

// 处理标签页切换
const handleTabChange = (tabName) => {
  if (tabName === 'contents') {
    // 切换到内容标签页时，如果没有内容则加载
    if (contents.value.length === 0) {
      resetContents();
      loadUserContents();
    }
  } else if (tabName === 'collections') {
    // 切换到合集标签页时，如果没有合集则加载
    if (collections.value.length === 0) {
      resetCollections();
      loadUserCollections();
    }
  }
};

// 重置内容列表（切换标签页时使用）
const resetContents = () => {
  contents.value = [];
  contentsPagination.value.page = 1;
  contentsPagination.value.total = 0;
};

// 重置合集列表（切换标签页时使用）
const resetCollections = () => {
  collections.value = [];
  collectionsPagination.value.page = 1;
  collectionsPagination.value.total = 0;
};



// 初始化
onMounted(() => {
  if (userId.value) {
    loadUserData();
  }
});
</script>

<style scoped>
.user-space {
  width: 100%;
  padding: 0;
}

.user-space-content {
  padding: 2rem;
}

/* 手机端布局调整 */
.user-space.mobile-layout .user-space-content {
  padding: 1rem;
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding: 2rem;
  background: white;
  border-radius: 0.75rem;
  /* 12px ÷ 16 */
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1);
  /* 2px, 8px ÷ 16 */
}

/* 手机端头部布局 */
.mobile-layout .user-header {
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1.5rem;
  margin-bottom: 1rem;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

/* 手机端用户信息布局 */
.mobile-layout .user-info {
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

.user-avatar {
  width: 5rem;
  /* 80px ÷ 16 */
  height: 5rem;
  /* 80px ÷ 16 */
  border-radius: 50%;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
}

/* 手机端头像调整 */
.mobile-layout .user-avatar {
  width: 4rem;
  height: 4rem;
}

.user-details {
  flex: 1;
}

.user-name {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #303133;
}

/* 手机端用户名调整 */
.mobile-layout .user-name {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
}

.user-email {
  margin: 0 0 0.25rem 0;
  color: #606266;
  font-size: 0.875rem;
}

.user-join-date {
  margin: 0;
  color: #909399;
  font-size: 0.75rem;
}

.user-stats {
  display: flex;
  gap: 2rem;
}

/* 手机端统计信息布局 */
.mobile-layout .user-stats {
  gap: 3rem;
  justify-content: center;
  width: 100%;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 600;
  color: #409eff;
}

/* 手机端统计数字调整 */
.mobile-layout .stat-number {
  font-size: 1.25rem;
}

.stat-label {
  display: block;
  font-size: 0.75rem;
  color: #909399;
  margin-top: 0.25rem;
}

.user-tabs {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 标签页样式 */
.user-tabs :deep(.el-tabs__header) {
  margin: 0 0 1.5rem 0;
}

.user-tabs :deep(.el-tabs__item) {
  font-size: 1rem;
  font-weight: 500;
  padding: 0 1.5rem;
  height: 2.5rem;
  line-height: 2.5rem;
}

.tab-content {
  padding: 1.5rem;
}

/* 手机端标签页内容调整 */
.mobile-layout .tab-content {
  padding: 1rem;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(14rem, 1fr));
  gap: 1.5rem;
}

/* 手机端内容网格 - 双列紧凑布局 */
.mobile-layout .content-grid {
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.grid-item {
  width: 100%;
}

.empty-state,
.loading-state {
  padding: 3rem 1rem;
  text-align: center;
}

/* 手机端空状态调整 */
.mobile-layout .empty-state,
.mobile-layout .loading-state {
  padding: 2rem 0.5rem;
}

/* 加载更多按钮样式 */
.load-more-section {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
}

.load-more-btn {
  min-width: 8rem;
  border-radius: 2rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.load-more-btn:hover {
  transform: translateY(-0.125rem);
  box-shadow: 0 0.5rem 1rem rgba(64, 158, 255, 0.3);
}

/* 没有更多数据提示 */
.no-more-tip {
  margin-top: 2rem;
  text-align: center;
}

.no-more-text {
  color: #909399;
  font-size: 0.875rem;
}

/* 手机端调整 */
.mobile-layout .load-more-section {
  margin-top: 1.5rem;
}

.mobile-layout .load-more-btn {
  min-width: 7rem;
  font-size: 0.875rem;
}

/* 手机端标签页样式调整 */
.mobile-layout .user-tabs :deep(.el-tabs__header) {
  margin: 0 0 1rem 0;
}

.mobile-layout .user-tabs :deep(.el-tabs__item) {
  font-size: 0.875rem;
  padding: 0 1rem;
  height: 2rem;
  line-height: 2rem;
}

/* 统一的响应式设计，不使用媒体查询 */
</style>
