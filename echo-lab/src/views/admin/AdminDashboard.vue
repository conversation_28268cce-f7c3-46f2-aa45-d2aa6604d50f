<!--
  管理后台控制台页面
  显示系统概览数据
-->
<template>
  <div class="admin-dashboard">
    <!-- 数据卡片 -->
    <div class="stat-cards">
      <el-card class="stat-card">
        <template #header>
          <div class="card-header">
            <span>用户统计</span>
            <el-button link @click="refreshStats">
              <el-icon>
                <i-ep-refresh />
              </el-icon>
            </el-button>
          </div>
        </template>
        <div class="card-content">
          <div class="stat-item">
            <div class="stat-value">{{ stats.userStats.total }}</div>
            <div class="stat-label">总用户数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.userStats.active }}</div>
            <div class="stat-label">活跃用户</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.userStats.admin }}</div>
            <div class="stat-label">管理员</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <template #header>
          <div class="card-header">
            <span>内容统计</span>
          </div>
        </template>
        <div class="card-content">
          <div class="stat-item">
            <div class="stat-value">{{ stats.contentStats.total }}</div>
            <div class="stat-label">总内容数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.contentStats.published }}</div>
            <div class="stat-label">已发布</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 最近数据 -->
    <div class="recent-data">
      <el-card class="recent-card">
        <template #header>
          <div class="card-header">
            <span>最近注册用户</span>
            <el-button type="primary" size="small" @click="goToUsers">查看全部</el-button>
          </div>
        </template>
        <div class="card-content">
          <el-table :data="stats.recentUsers" style="width: 100%" v-loading="loading">
            <el-table-column prop="username" label="用户名">
              <template #default="scope">
                <router-link :to="`/admin/users/${scope.row.id}`" class="user-link">
                  {{ scope.row.username || scope.row.email }}
                </router-link>
              </template>
            </el-table-column>
            <el-table-column prop="role" label="角色">
              <template #default="scope">
                <el-tag :type="scope.row.role === 'admin' ? 'danger' : 'info'" size="small">
                  {{ scope.row.role === 'admin' ? '管理员' : '用户' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)" size="small">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="注册时间">
              <template #default="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>

      <el-card class="recent-card">
        <template #header>
          <div class="card-header">
            <span>最近创建内容</span>
            <el-button type="primary" size="small" @click="goToContents">查看全部</el-button>
          </div>
        </template>
        <div class="card-content">
          <el-table :data="stats.recentContents" style="width: 100%" v-loading="loading">
            <el-table-column prop="name" label="内容名称">
              <template #default="scope">
                <router-link :to="`/admin/contents/${scope.row.id}`" class="content-link">
                  {{ scope.row.name }}
                </router-link>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <el-tag :type="getContentStatusType(scope.row.status)" size="small">
                  {{ getContentStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间">
              <template #default="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { ElMessage } from 'element-plus';
import httpClient from '@/utils/httpClient';
import { API_ENDPOINTS } from '@/config/api';

const router = useRouter();

// 加载状态
const loading = ref(false);

// 统计数据
const stats = ref({
  userStats: {
    total: 0,
    active: 0,
    admin: 0
  },
  contentStats: {
    total: 0,
    published: 0
  },
  recentUsers: [],
  recentContents: []
});

// 获取统计数据
async function fetchStats() {
  loading.value = true;
  try {
    const response = await httpClient.get(`${API_ENDPOINTS.ADMIN.BASE}/dashboard`);
    if (response.success) {
      stats.value = response.data;
    } else {
      ElMessage.error(response.error || '获取统计数据失败');
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
    ElMessage.error('获取统计数据失败: ' + error.message);
  } finally {
    loading.value = false;
  }
}

// 刷新统计数据
function refreshStats() {
  fetchStats();
}

// 跳转到用户列表
function goToUsers() {
  router.push('/admin/users');
}

// 跳转到内容列表
function goToContents() {
  router.push('/admin/contents');
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// 获取用户状态类型
function getStatusType(status) {
  switch (status) {
    case 'active': return 'success';
    case 'inactive': return 'warning';
    case 'banned': return 'danger';
    default: return 'info';
  }
}

// 获取用户状态文本
function getStatusText(status) {
  switch (status) {
    case 'active': return '活跃';
    case 'inactive': return '未激活';
    case 'banned': return '已禁用';
    default: return '未知';
  }
}

// 获取内容状态类型
function getContentStatusType(status) {
  switch (status) {
    case 'published': return 'success';
    case 'draft': return 'info';
    case 'archived': return 'warning';
    default: return 'info';
  }
}

// 获取内容状态文本
function getContentStatusText(status) {
  switch (status) {
    case 'published': return '已发布';
    case 'draft': return '草稿';
    case 'archived': return '已归档';
    default: return '未知';
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchStats();
});
</script>

<style scoped>
.admin-dashboard {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.stat-cards {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.stat-card {
  flex: 1;
  min-width: 15rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  display: flex;
  justify-content: space-around;
  padding: 1rem 0;
}

.stat-item {
  text-align: center;
  padding: 0.5rem;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #606266;
}

.recent-data {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.recent-card {
  flex: 1;
  min-width: 30rem;
}

.user-link,
.content-link {
  color: #409eff;
  text-decoration: none;
}

.user-link:hover,
.content-link:hover {
  text-decoration: underline;
}

/* 响应式布局 - 使用类名而非媒体查询 */
.mobile-device .stat-cards,
.mobile-device .recent-data {
  flex-direction: column;
}

.mobile-device .stat-card,
.mobile-device .recent-card {
  width: 100%;
  min-width: auto;
}
</style>
