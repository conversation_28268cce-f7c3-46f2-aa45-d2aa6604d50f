<template>
  <div class="feedback-management">
    <div class="page-header">
      <h2>用户反馈管理</h2>
      <p class="page-description">管理用户提交的反馈，回复并更新处理状态</p>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-toolbar">
      <el-select v-model="filterType" placeholder="反馈类型" clearable @change="handleFilterChange">
        <el-option label="功能建议（改进现有功能）" value="suggestion" />
        <el-option label="问题反馈（报告错误）" value="bug" />
        <el-option label="新功能请求（添加全新功能）" value="feature" />
        <el-option label="其他" value="other" />
      </el-select>

      <el-select v-model="filterStatus" placeholder="处理状态" clearable @change="handleFilterChange">
        <el-option label="待处理" value="pending" />
        <el-option label="处理中" value="processing" />
        <el-option label="已解决" value="resolved" />
        <el-option label="已拒绝" value="rejected" />
      </el-select>
    </div>

    <!-- 反馈列表 -->
    <el-table v-loading="loading" :data="feedbacks" style="width: 100%" empty-text="暂无反馈记录">
      <!-- 反馈ID -->
      <el-table-column prop="id" label="ID" width="80" />

      <!-- 反馈类型 -->
      <el-table-column prop="type" label="类型" width="120">
        <template #default="{ row }">
          <el-tag :type="getFeedbackTypeTag(row.type)">
            {{ getFeedbackTypeLabel(row.type) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 反馈内容 -->
      <el-table-column prop="content" label="内容" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="feedback-content">{{ row.content }}</div>
        </template>
      </el-table-column>

      <!-- 用户信息 -->
      <el-table-column label="用户" width="150">
        <template #default="{ row }">
          <div v-if="row.user">
            {{ row.user.username || row.user.email }}
          </div>
          <div v-else-if="row.contact">
            {{ row.contact }}
          </div>
          <div v-else class="text-muted">匿名用户</div>
        </template>
      </el-table-column>

      <!-- 处理状态 -->
      <el-table-column prop="status" label="状态" width="120">
        <template #default="{ row }">
          <el-tag :type="getFeedbackStatusTag(row.status)">
            {{ getFeedbackStatusLabel(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 提交时间 -->
      <el-table-column prop="created_at" label="提交时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>

      <!-- 操作 -->
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleFeedback(row)">
            处理
          </el-button>
          <el-button type="info" size="small" @click="viewFeedbackDetail(row)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next" :total="total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>

    <!-- 反馈处理对话框 -->
    <el-dialog v-model="handleDialogVisible" title="处理反馈" width="600px" :destroy-on-close="true">
      <div v-if="selectedFeedback" class="feedback-handle-form">
        <div class="feedback-content-preview">
          <div class="preview-header">
            <el-tag :type="getFeedbackTypeTag(selectedFeedback.type)">
              {{ getFeedbackTypeLabel(selectedFeedback.type) }}
            </el-tag>
            <span class="preview-date">{{ formatDate(selectedFeedback.created_at) }}</span>
          </div>
          <div class="preview-content">{{ selectedFeedback.content }}</div>
          <div v-if="selectedFeedback.contact" class="preview-contact">
            联系方式: {{ selectedFeedback.contact }}
          </div>
        </div>

        <el-form :model="handleForm" label-position="top">
          <el-form-item label="处理状态">
            <el-select v-model="handleForm.status" class="w-100">
              <el-option label="待处理" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已解决" value="resolved" />
              <el-option label="已拒绝" value="rejected" />
            </el-select>
          </el-form-item>

          <el-form-item label="回复内容">
            <el-input v-model="handleForm.adminReply" type="textarea" :rows="5" placeholder="请输入回复内容..." />
          </el-form-item>
        </el-form>

        <div class="dialog-footer">
          <el-button @click="handleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitFeedbackHandle" :loading="submitting">
            保存
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 反馈详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="反馈详情" width="700px" :destroy-on-close="true">
      <div v-if="selectedFeedback" class="feedback-detail">
        <div class="detail-section">
          <h3>基本信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <div class="detail-label">反馈ID：</div>
              <div class="detail-value">{{ selectedFeedback.id }}</div>
            </div>

            <div class="detail-item">
              <div class="detail-label">反馈类型：</div>
              <div class="detail-value">
                <el-tag :type="getFeedbackTypeTag(selectedFeedback.type)">
                  {{ getFeedbackTypeLabel(selectedFeedback.type) }}
                </el-tag>
              </div>
            </div>

            <div class="detail-item">
              <div class="detail-label">处理状态：</div>
              <div class="detail-value">
                <el-tag :type="getFeedbackStatusTag(selectedFeedback.status)">
                  {{ getFeedbackStatusLabel(selectedFeedback.status) }}
                </el-tag>
              </div>
            </div>

            <div class="detail-item">
              <div class="detail-label">提交时间：</div>
              <div class="detail-value">
                {{ formatDate(selectedFeedback.created_at) }}
              </div>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h3>用户信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <div class="detail-label">用户ID：</div>
              <div class="detail-value">
                {{ selectedFeedback.userId || '匿名用户' }}
              </div>
            </div>

            <div class="detail-item">
              <div class="detail-label">联系方式：</div>
              <div class="detail-value">
                {{ selectedFeedback.contact || '未提供' }}
              </div>
            </div>

            <div class="detail-item">
              <div class="detail-label">页面URL：</div>
              <div class="detail-value">
                {{ selectedFeedback.pageUrl || '未记录' }}
              </div>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h3>设备信息</h3>
          <div class="detail-grid">
            <div class="detail-item full-width" v-if="selectedFeedback.deviceInfo">
              <pre class="device-info">{{ formatDeviceInfo(selectedFeedback.deviceInfo) }}</pre>
            </div>
            <div class="detail-item full-width" v-else>
              <div class="text-muted">未记录设备信息</div>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h3>反馈内容</h3>
          <div class="content-box">
            {{ selectedFeedback.content }}
          </div>
        </div>

        <div class="detail-section" v-if="selectedFeedback.adminReply">
          <h3>管理员回复</h3>
          <div class="content-box admin-reply">
            {{ selectedFeedback.adminReply }}
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getAllFeedbacks, updateFeedbackStatus } from "@/services/feedbackService";
import { ElMessage } from "element-plus";

// 加载状态
const loading = ref(false);
const submitting = ref(false);

// 反馈列表
const feedbacks = ref([]);

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 筛选
const filterType = ref("");
const filterStatus = ref("");

// 对话框
const handleDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const selectedFeedback = ref(null);

// 处理表单
const handleForm = ref({
  status: "pending",
  adminReply: ""
});

// 获取反馈列表
async function fetchFeedbacks() {
  loading.value = true;
  try {
    const result = await getAllFeedbacks({
      page: currentPage.value,
      pageSize: pageSize.value,
      type: filterType.value,
      status: filterStatus.value
    });

    if (result.success) {
      feedbacks.value = result.feedbacks;
      total.value = result.pagination.total;

      // 调试信息：检查返回的数据
      console.log('反馈数据:', result.feedbacks);
      if (result.feedbacks.length > 0) {
        console.log('第一条反馈的时间字段:', {
          createdAt: result.feedbacks[0].createdAt,
          created_at: result.feedbacks[0].created_at,
          updatedAt: result.feedbacks[0].updatedAt,
          updated_at: result.feedbacks[0].updated_at
        });
      }
    } else {
      ElMessage.error(result.error || "获取反馈列表失败");
    }
  } catch (error) {
    console.error("获取反馈列表失败:", error);
    ElMessage.error("获取反馈列表失败，请稍后重试");
  } finally {
    loading.value = false;
  }
}

// 处理筛选变化
function handleFilterChange() {
  currentPage.value = 1;
  fetchFeedbacks();
}

// 处理页码变化
function handleCurrentChange(page) {
  currentPage.value = page;
  fetchFeedbacks();
}

// 处理每页数量变化
function handleSizeChange(size) {
  pageSize.value = size;
  currentPage.value = 1;
  fetchFeedbacks();
}

// 处理反馈
function handleFeedback(feedback) {
  selectedFeedback.value = feedback;
  handleForm.value.status = feedback.status;
  handleForm.value.adminReply = feedback.adminReply || "";
  handleDialogVisible.value = true;
}

// 查看反馈详情
function viewFeedbackDetail(feedback) {
  selectedFeedback.value = feedback;
  detailDialogVisible.value = true;
}

// 提交反馈处理
async function submitFeedbackHandle() {
  if (!selectedFeedback.value) return;

  submitting.value = true;
  try {
    const result = await updateFeedbackStatus(selectedFeedback.value.id, {
      status: handleForm.value.status,
      adminReply: handleForm.value.adminReply
    });

    if (result.success) {
      ElMessage.success("反馈处理成功");
      handleDialogVisible.value = false;
      fetchFeedbacks();
    } else {
      ElMessage.error(result.error || "反馈处理失败");
    }
  } catch (error) {
    console.error("反馈处理失败:", error);
    ElMessage.error("反馈处理失败，请稍后重试");
  } finally {
    submitting.value = false;
  }
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit"
  });
}

// 格式化设备信息
function formatDeviceInfo(deviceInfoStr) {
  try {
    if (!deviceInfoStr) return "未记录设备信息";
    const deviceInfo = JSON.parse(deviceInfoStr);
    return JSON.stringify(deviceInfo, null, 2);
  } catch (error) {
    return deviceInfoStr;
  }
}

// 获取反馈类型标签
function getFeedbackTypeLabel(type) {
  const typeMap = {
    suggestion: "功能建议",
    bug: "问题反馈",
    feature: "新功能请求",
    other: "其他"
  };
  return typeMap[type] || type;
}

// 获取反馈类型标签样式
function getFeedbackTypeTag(type) {
  const typeMap = {
    suggestion: "success",
    bug: "danger",
    feature: "warning",
    other: "info"
  };
  return typeMap[type] || "";
}

// 获取反馈状态标签
function getFeedbackStatusLabel(status) {
  const statusMap = {
    pending: "待处理",
    processing: "处理中",
    resolved: "已解决",
    rejected: "已拒绝"
  };
  return statusMap[status] || status;
}

// 获取反馈状态标签样式
function getFeedbackStatusTag(status) {
  const statusMap = {
    pending: "info",
    processing: "warning",
    resolved: "success",
    rejected: "danger"
  };
  return statusMap[status] || "";
}

// 组件挂载时获取反馈列表
onMounted(() => {
  fetchFeedbacks();
});
</script>

<style scoped>
.feedback-management {
  padding: 1rem;
}

.page-header {
  margin-bottom: 2rem;
}

.page-description {
  color: #606266;
  margin-top: 0.5rem;
}

.filter-toolbar {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.pagination-container {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}

.feedback-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.text-muted {
  color: #909399;
}

.w-100 {
  width: 100%;
}

.feedback-content-preview {
  background-color: #f5f7fa;
  padding: 1rem;
  border-radius: 0.25rem;
  margin-bottom: 1.5rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.preview-date {
  color: #909399;
  font-size: 0.875rem;
}

.preview-content {
  margin: 1rem 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.preview-contact {
  font-size: 0.875rem;
  color: #606266;
  border-top: 1px solid #dcdfe6;
  padding-top: 0.5rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 1rem;
}

.feedback-detail {
  padding: 1rem;
}

.detail-section {
  margin-bottom: 1.5rem;
}

.detail-section h3 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1rem;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 0.5rem;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.detail-item {
  display: flex;
}

.full-width {
  grid-column: span 2;
}

.detail-label {
  font-weight: bold;
  width: 6rem;
  flex-shrink: 0;
}

.detail-value {
  flex: 1;
}

.content-box {
  background-color: #f5f7fa;
  padding: 1rem;
  border-radius: 0.25rem;
  white-space: pre-wrap;
  word-break: break-word;
}

.admin-reply {
  background-color: #ecf5ff;
  border-left: 0.25rem solid #409eff;
}

.device-info {
  background-color: #f5f7fa;
  padding: 1rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  overflow: auto;
  max-height: 10rem;
  margin: 0;
}
</style>
