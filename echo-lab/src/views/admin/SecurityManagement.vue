<!--
  安全管理页面
  用于管理IP黑白名单和查看安全警报
-->
<template>
  <div class="security-management">
    <div class="page-header">
      <h2>安全管理</h2>
      <p class="page-description">管理IP黑白名单，查看安全警报，保护系统安全</p>
    </div>

    <el-tabs v-model="activeTab" class="security-tabs">
      <!-- IP黑名单管理 -->
      <el-tab-pane label="IP黑名单" name="blacklist">
        <el-card class="blacklist-card">
          <template #header>
            <div class="card-header">
              <span>IP黑名单</span>
              <div class="header-actions">
                <el-button type="primary" @click="showAddBlacklistDialog">
                  添加IP
                </el-button>
                <el-button @click="refreshBlacklist">
                  <el-icon>
                    <i-ep-refresh />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </template>

          <div v-loading="blacklistLoading">
            <el-tabs v-model="blacklistType">
              <el-tab-pane label="静态黑名单" name="static">
                <div v-if="staticBlacklist.length === 0" class="empty-state">
                  <el-empty description="暂无静态黑名单IP" />
                </div>
                <el-table v-else :data="staticBlacklist" style="width: 100%">
                  <el-table-column prop="ip" label="IP地址" />
                  <el-table-column prop="reason" label="原因" />
                  <el-table-column prop="created_at" label="添加时间">
                    <template #default="scope">
                      {{ formatDate(scope.row.created_at) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="created_by" label="添加者" />
                  <el-table-column label="操作" width="150">
                    <template #default="scope">
                      <el-button type="danger" size="small" @click="removeFromBlacklist(scope.row.ip, 'static')">
                        移除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>

              <el-tab-pane label="动态黑名单" name="dynamic">
                <div v-if="dynamicBlacklist.length === 0" class="empty-state">
                  <el-empty description="暂无动态黑名单IP" />
                </div>
                <el-table v-else :data="dynamicBlacklist" style="width: 100%">
                  <el-table-column prop="ip" label="IP地址" />
                  <el-table-column prop="reason" label="原因" />
                  <el-table-column prop="expires" label="过期时间">
                    <template #default="scope">
                      {{ formatDate(scope.row.expires) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150">
                    <template #default="scope">
                      <el-button type="danger" size="small" @click="removeFromBlacklist(scope.row.ip, 'dynamic')">
                        移除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>
      </el-tab-pane>

      <!-- IP白名单管理 -->
      <el-tab-pane label="IP白名单" name="whitelist">
        <el-card class="whitelist-card">
          <template #header>
            <div class="card-header">
              <span>IP白名单</span>
              <div class="header-actions">
                <el-button type="primary" @click="showAddWhitelistDialog">
                  添加IP
                </el-button>
                <el-button @click="refreshWhitelist">
                  <el-icon>
                    <i-ep-refresh />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </template>

          <div v-loading="whitelistLoading">
            <div v-if="whitelist.length === 0" class="empty-state">
              <el-empty description="暂无白名单IP" />
            </div>
            <el-table v-else :data="whitelist" style="width: 100%">
              <el-table-column prop="ip" label="IP地址" />
              <el-table-column prop="comment" label="备注" />
              <el-table-column prop="created_at" label="添加时间">
                <template #default="scope">
                  {{ formatDate(scope.row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column prop="created_by" label="添加者" />
              <el-table-column label="操作" width="150">
                <template #default="scope">
                  <el-button type="danger" size="small" @click="removeFromWhitelist(scope.row.ip)">
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 安全警报 -->
      <el-tab-pane label="安全警报" name="alerts">
        <el-card class="alerts-card">
          <template #header>
            <div class="card-header">
              <span>安全警报</span>
              <div class="header-actions">
                <el-button @click="refreshAlerts">
                  <el-icon>
                    <i-ep-refresh />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </template>

          <div class="filter-container">
            <el-select v-model="alertStatus" placeholder="状态" clearable @change="fetchAlerts">
              <el-option label="全部" value="" />
              <el-option label="新警报" value="new" />
              <el-option label="已审核" value="reviewed" />
              <el-option label="已解决" value="resolved" />
              <el-option label="误报" value="false_positive" />
            </el-select>
            <el-select v-model="alertType" placeholder="类型" clearable @change="fetchAlerts">
              <el-option label="全部" value="" />
              <el-option label="可疑活动" value="suspicious_activity" />
              <el-option label="登录失败" value="login_failure" />
              <el-option label="请求频率超限" value="rate_limit_exceeded" />
            </el-select>
            <el-input v-model="alertIp" placeholder="IP地址" clearable @clear="fetchAlerts">
              <template #append>
                <el-button @click="fetchAlerts">搜索</el-button>
              </template>
            </el-input>
          </div>

          <div v-loading="alertsLoading">
            <div v-if="alerts.length === 0" class="empty-state">
              <el-empty description="暂无安全警报" />
            </div>
            <el-table v-else :data="alerts" style="width: 100%">
              <el-table-column prop="ip" label="IP地址" width="150" />
              <el-table-column prop="type" label="类型" width="150" />
              <el-table-column prop="details" label="详情" show-overflow-tooltip />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="getAlertStatusType(scope.row.status)">
                    {{ getAlertStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createdAt" label="时间" width="180">
                <template #default="scope">
                  {{ formatDate(scope.row.createdAt) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template #default="scope">
                  <el-button type="primary" size="small" @click="handleAlert(scope.row)">
                    处理
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <div class="pagination-container">
              <el-pagination v-model:current-page="alertsCurrentPage" v-model:page-size="alertsPageSize"
                :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="alertsTotal"
                @size-change="handleAlertsSizeChange" @current-change="handleAlertsCurrentChange" />
            </div>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加黑名单IP对话框 -->
    <el-dialog v-model="addBlacklistDialogVisible" title="添加IP到黑名单" width="500px">
      <el-form :model="blacklistForm" label-width="100px">
        <el-form-item label="IP地址" required>
          <el-input v-model="blacklistForm.ip" placeholder="输入IP地址或CIDR格式 (例如: *********** 或 ***********/24)" />
        </el-form-item>
        <el-form-item label="黑名单类型" required>
          <el-radio-group v-model="blacklistForm.type">
            <el-radio label="static">静态黑名单（永久）</el-radio>
            <el-radio label="dynamic">动态黑名单（临时）</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="blacklistForm.type === 'dynamic'" label="持续时间">
          <el-input-number v-model="blacklistForm.duration" :min="1" :max="720" />
          <span class="form-hint">小时</span>
        </el-form-item>
        <el-form-item label="原因">
          <el-input v-model="blacklistForm.reason" type="textarea" placeholder="添加到黑名单的原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addBlacklistDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addToBlacklist" :loading="submitting">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加白名单IP对话框 -->
    <el-dialog v-model="addWhitelistDialogVisible" title="添加IP到白名单" width="500px">
      <el-form :model="whitelistForm" label-width="100px">
        <el-form-item label="IP地址" required>
          <el-input v-model="whitelistForm.ip" placeholder="输入IP地址或CIDR格式 (例如: *********** 或 ***********/24)" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="whitelistForm.comment" type="textarea" placeholder="添加备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addWhitelistDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addToWhitelist" :loading="submitting">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 处理安全警报对话框 -->
    <el-dialog v-model="alertDialogVisible" title="处理安全警报" width="600px">
      <div v-if="currentAlert" class="alert-details">
        <div class="alert-info">
          <p><strong>IP地址:</strong> {{ currentAlert.ip }}</p>
          <p><strong>类型:</strong> {{ currentAlert.type }}</p>
          <p><strong>详情:</strong> {{ currentAlert.details }}</p>
          <p><strong>时间:</strong> {{ formatDate(currentAlert.createdAt) }}</p>
          <p><strong>当前状态:</strong> {{ getAlertStatusText(currentAlert.status) }}</p>
        </div>

        <el-divider />

        <el-form :model="alertForm" label-width="100px">
          <el-form-item label="更新状态">
            <el-select v-model="alertForm.status">
              <el-option label="已审核" value="reviewed" />
              <el-option label="已解决" value="resolved" />
              <el-option label="误报" value="false_positive" />
            </el-select>
          </el-form-item>
          <el-form-item label="处理备注">
            <el-input v-model="alertForm.notes" type="textarea" rows="4" placeholder="添加处理备注" />
          </el-form-item>
          <el-form-item label="操作">
            <el-checkbox v-model="alertForm.addToBlacklist">添加到黑名单</el-checkbox>
            <el-select v-if="alertForm.addToBlacklist" v-model="alertForm.blacklistType" style="margin-left: 10px">
              <el-option label="静态黑名单（永久）" value="static" />
              <el-option label="动态黑名单（24小时）" value="dynamic" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="alertDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateAlertStatus" :loading="submitting">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import httpClient from '@/utils/httpClient';
import { API_ENDPOINTS } from '@/config/api';

// 选项卡
const activeTab = ref('blacklist');
const blacklistType = ref('static');

// 加载状态
const blacklistLoading = ref(false);
const whitelistLoading = ref(false);
const alertsLoading = ref(false);
const submitting = ref(false);

// 数据
const staticBlacklist = ref([]);
const dynamicBlacklist = ref([]);
const whitelist = ref([]);
const alerts = ref([]);

// 分页
const alertsCurrentPage = ref(1);
const alertsPageSize = ref(20);
const alertsTotal = ref(0);

// 筛选
const alertStatus = ref('');
const alertType = ref('');
const alertIp = ref('');

// 对话框
const addBlacklistDialogVisible = ref(false);
const addWhitelistDialogVisible = ref(false);
const alertDialogVisible = ref(false);
const currentAlert = ref(null);

// 表单
const blacklistForm = ref({
  ip: '',
  type: 'static',
  duration: 24,
  reason: ''
});

const whitelistForm = ref({
  ip: '',
  comment: ''
});

const alertForm = ref({
  status: 'reviewed',
  notes: '',
  addToBlacklist: false,
  blacklistType: 'static'
});

// 获取黑名单
async function fetchBlacklist() {
  blacklistLoading.value = true;
  try {
    const response = await httpClient.get(`${API_ENDPOINTS.ADMIN.BASE}/security/blacklist`);
    if (response.success) {
      // 处理静态黑名单
      staticBlacklist.value = response.blacklist.static;

      // 处理动态黑名单
      dynamicBlacklist.value = response.blacklist.dynamic;
    } else {
      ElMessage.error(response.error || '获取黑名单失败');
    }
  } catch (error) {
    console.error('获取黑名单失败:', error);
    ElMessage.error('获取黑名单失败: ' + error.message);
  } finally {
    blacklistLoading.value = false;
  }
}

// 获取白名单
async function fetchWhitelist() {
  whitelistLoading.value = true;
  try {
    const response = await httpClient.get(`${API_ENDPOINTS.ADMIN.BASE}/security/whitelist`);
    if (response.success) {
      whitelist.value = response.whitelist;
    } else {
      ElMessage.error(response.error || '获取白名单失败');
    }
  } catch (error) {
    console.error('获取白名单失败:', error);
    ElMessage.error('获取白名单失败: ' + error.message);
  } finally {
    whitelistLoading.value = false;
  }
}

// 获取安全警报
async function fetchAlerts() {
  alertsLoading.value = true;
  try {
    const params = {
      page: alertsCurrentPage.value,
      limit: alertsPageSize.value,
      status: alertStatus.value,
      type: alertType.value,
      ip: alertIp.value
    };

    const response = await httpClient.get(`${API_ENDPOINTS.ADMIN.BASE}/security/alerts`, params);
    if (response.success) {
      alerts.value = response.alerts;
      alertsTotal.value = response.pagination.total;
    } else {
      ElMessage.error(response.error || '获取安全警报失败');
    }
  } catch (error) {
    console.error('获取安全警报失败:', error);
    ElMessage.error('获取安全警报失败: ' + error.message);
  } finally {
    alertsLoading.value = false;
  }
}

// 刷新数据
function refreshBlacklist() {
  fetchBlacklist();
}

function refreshWhitelist() {
  fetchWhitelist();
}

function refreshAlerts() {
  fetchAlerts();
}

// 显示添加对话框
function showAddBlacklistDialog() {
  blacklistForm.value = {
    ip: '',
    type: 'static',
    duration: 24,
    reason: ''
  };
  addBlacklistDialogVisible.value = true;
}

function showAddWhitelistDialog() {
  whitelistForm.value = {
    ip: '',
    comment: ''
  };
  addWhitelistDialogVisible.value = true;
}

// 添加到黑名单
async function addToBlacklist() {
  if (!blacklistForm.value.ip) {
    ElMessage.warning('请输入IP地址');
    return;
  }

  submitting.value = true;
  try {
    const response = await httpClient.post(`${API_ENDPOINTS.ADMIN.BASE}/security/blacklist`, {
      ip: blacklistForm.value.ip,
      type: blacklistForm.value.type,
      duration: blacklistForm.value.type === 'dynamic' ? blacklistForm.value.duration : undefined,
      reason: blacklistForm.value.reason
    });

    if (response.success) {
      ElMessage.success(response.message || 'IP已添加到黑名单');
      addBlacklistDialogVisible.value = false;
      fetchBlacklist();
    } else {
      ElMessage.error(response.error || '添加到黑名单失败');
    }
  } catch (error) {
    console.error('添加到黑名单失败:', error);
    ElMessage.error('添加到黑名单失败: ' + error.message);
  } finally {
    submitting.value = false;
  }
}

// 添加到白名单
async function addToWhitelist() {
  if (!whitelistForm.value.ip) {
    ElMessage.warning('请输入IP地址');
    return;
  }

  submitting.value = true;
  try {
    const response = await httpClient.post(`${API_ENDPOINTS.ADMIN.BASE}/security/whitelist`, {
      ip: whitelistForm.value.ip,
      comment: whitelistForm.value.comment
    });

    if (response.success) {
      ElMessage.success(response.message || 'IP已添加到白名单');
      addWhitelistDialogVisible.value = false;
      fetchWhitelist();
    } else {
      ElMessage.error(response.error || '添加到白名单失败');
    }
  } catch (error) {
    console.error('添加到白名单失败:', error);
    ElMessage.error('添加到白名单失败: ' + error.message);
  } finally {
    submitting.value = false;
  }
}

// 从黑名单中移除
async function removeFromBlacklist(ip, type) {
  ElMessageBox.confirm(`确定要从${type === 'static' ? '静态' : '动态'}黑名单中移除 ${ip} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await httpClient.delete(`${API_ENDPOINTS.ADMIN.BASE}/security/blacklist/${ip}`, { type });

      if (response.success) {
        ElMessage.success(response.message || 'IP已从黑名单中移除');
        fetchBlacklist();
      } else {
        ElMessage.error(response.error || '从黑名单中移除失败');
      }
    } catch (error) {
      console.error('从黑名单中移除失败:', error);
      ElMessage.error('从黑名单中移除失败: ' + error.message);
    }
  }).catch(() => { });
}

// 从白名单中移除
async function removeFromWhitelist(ip) {
  ElMessageBox.confirm(`确定要从白名单中移除 ${ip} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await httpClient.delete(`${API_ENDPOINTS.ADMIN.BASE}/security/whitelist/${ip}`);

      if (response.success) {
        ElMessage.success(response.message || 'IP已从白名单中移除');
        fetchWhitelist();
      } else {
        ElMessage.error(response.error || '从白名单中移除失败');
      }
    } catch (error) {
      console.error('从白名单中移除失败:', error);
      ElMessage.error('从白名单中移除失败: ' + error.message);
    }
  }).catch(() => { });
}

// 处理安全警报
function handleAlert(alert) {
  currentAlert.value = alert;
  alertForm.value = {
    status: 'reviewed',
    notes: '',
    addToBlacklist: false,
    blacklistType: 'static'
  };
  alertDialogVisible.value = true;
}

// 更新警报状态
async function updateAlertStatus() {
  if (!currentAlert.value) return;

  submitting.value = true;
  try {
    // 更新警报状态
    const response = await httpClient.put(`${API_ENDPOINTS.ADMIN.BASE}/security/alerts/${currentAlert.value.id}`, {
      status: alertForm.value.status,
      notes: alertForm.value.notes
    });

    if (response.success) {
      // 如果选择添加到黑名单
      if (alertForm.value.addToBlacklist) {
        try {
          await httpClient.post(`${API_ENDPOINTS.ADMIN.BASE}/security/blacklist`, {
            ip: currentAlert.value.ip,
            type: alertForm.value.blacklistType,
            duration: alertForm.value.blacklistType === 'dynamic' ? 24 : undefined,
            reason: `从安全警报添加: ${currentAlert.value.type} - ${alertForm.value.notes || '无备注'}`
          });
        } catch (error) {
          console.error('添加到黑名单失败:', error);
          ElMessage.warning('警报状态已更新，但添加到黑名单失败');
        }
      }

      ElMessage.success('安全警报已更新');
      alertDialogVisible.value = false;
      fetchAlerts();
    } else {
      ElMessage.error(response.error || '更新安全警报失败');
    }
  } catch (error) {
    console.error('更新安全警报失败:', error);
    ElMessage.error('更新安全警报失败: ' + error.message);
  } finally {
    submitting.value = false;
  }
}

// 分页处理
function handleAlertsSizeChange(val) {
  alertsPageSize.value = val;
  fetchAlerts();
}

function handleAlertsCurrentChange(val) {
  alertsCurrentPage.value = val;
  fetchAlerts();
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// 获取警报状态类型
function getAlertStatusType(status) {
  switch (status) {
    case 'new': return 'danger';
    case 'reviewed': return 'warning';
    case 'resolved': return 'success';
    case 'false_positive': return 'info';
    default: return '';
  }
}

// 获取警报状态文本
function getAlertStatusText(status) {
  switch (status) {
    case 'new': return '新警报';
    case 'reviewed': return '已审核';
    case 'resolved': return '已解决';
    case 'false_positive': return '误报';
    default: return '未知';
  }
}

// 初始化
onMounted(() => {
  fetchBlacklist();
  fetchWhitelist();
  fetchAlerts();
});
</script>

<style scoped>
.security-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
}

.page-description {
  color: #666;
  margin: 0;
}

.security-tabs {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-container {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.empty-state {
  padding: 30px 0;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.form-hint {
  margin-left: 10px;
  color: #666;
}

.alert-details {
  padding: 10px;
}

.alert-info {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.alert-info p {
  margin: 5px 0;
}
</style>
