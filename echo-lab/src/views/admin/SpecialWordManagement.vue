<!--
  特殊词管理页面
  用于管理TTS服务中的特殊词汇
-->
<template>
  <div class="special-word-management">
    <div class="page-header">
      <h1>特殊词管理</h1>
      <p class="description">
        管理TTS服务中的特殊词汇，确保语音合成时能够正确发音。
      </p>
    </div>

    <el-card class="filter-card">
      <div class="filter-container">
        <el-select v-model="currentService" placeholder="选择TTS服务" @change="handleServiceChange" clearable>
          <el-option v-for="service in availableServices" :key="service.id" :label="service.name" :value="service.id" />
        </el-select>

        <el-select v-model="currentLanguage" placeholder="选择语言" @change="handleLanguageChange" clearable
          :disabled="!currentService">
          <el-option v-for="lang in availableLanguages" :key="lang.code" :label="lang.name" :value="lang.code" />
        </el-select>

        <el-select v-model="filterType" placeholder="词汇类型" clearable>
          <el-option label="全部" value="" />
          <el-option label="系统词汇" value="system" />
          <el-option label="用户词汇" value="user" />
        </el-select>

        <el-input v-model="searchKeyword" placeholder="搜索词汇" clearable>
          <template #prefix>
            <el-icon>
              <i-ep-search />
            </el-icon>
          </template>
        </el-input>
      </div>
    </el-card>

    <el-card class="word-list-card" v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>特殊词汇列表</span>
          <div class="header-actions">
            <el-button type="primary" @click="showAddWordDialog" :disabled="!currentService || !currentLanguage">
              添加特殊词汇
            </el-button>
            <el-button type="success" @click="showImportDialog" :disabled="!currentService || !currentLanguage">
              批量导入
            </el-button>
          </div>
        </div>
      </template>

      <div v-if="loading" class="empty-state">
        <el-empty description="加载中..." />
      </div>

      <div v-else-if="filteredWords.length === 0" class="empty-state">
        <el-empty description="暂无特殊词汇" />
      </div>

      <div v-else class="word-grid">
        <div v-for="word in filteredWords" :key="word.id" class="word-card">
          <div class="word-card-header">
            <div class="word-tags">
              <el-tag :type="word.isSystem ? 'success' : 'info'" size="small">
                {{ word.isSystem ? '系统' : '用户' }}
              </el-tag>
              <!-- 如果未选择特定服务和语言，显示服务和语言标签 -->
              <template v-if="!currentService || !currentLanguage">
                <el-tag type="warning" size="small" class="service-tag">
                  {{ word.serviceDisplay || word.serviceId }}
                </el-tag>
                <el-tag type="primary" size="small" class="language-tag">
                  {{ word.languageDisplay || word.languageCode }}
                </el-tag>
              </template>
            </div>
            <div class="word-actions">
              <el-button v-if="!word.isSystem" type="danger" size="small" icon="Delete" circle
                @click="deleteWord(word)" />
            </div>
          </div>
          <div class="word-content">{{ word.word }}</div>
        </div>
      </div>

      <div class="pagination-container" v-if="filteredWords.length > 0">
        <el-pagination background layout="prev, pager, next" :total="totalWords" :page-size="pageSize"
          :current-page="currentPage" @current-change="handlePageChange" />
      </div>
    </el-card>

    <!-- 添加特殊词汇对话框 -->
    <el-dialog v-model="addWordDialogVisible" title="添加特殊词汇" width="500px">
      <el-form :model="newWordForm" :rules="wordFormRules" ref="wordFormRef" label-width="100px">
        <el-form-item label="TTS服务" prop="serviceId">
          <el-select v-model="newWordForm.serviceId" placeholder="选择TTS服务" disabled>
            <el-option v-for="service in availableServices" :key="service.id" :label="service.name"
              :value="service.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="语言" prop="languageCode">
          <el-select v-model="newWordForm.languageCode" placeholder="选择语言" disabled>
            <el-option v-for="lang in availableLanguages" :key="lang.code" :label="lang.name" :value="lang.code" />
          </el-select>
        </el-form-item>

        <el-form-item label="特殊词汇" prop="word">
          <el-input v-model="newWordForm.word" placeholder="输入特殊词汇" />
        </el-form-item>

        <el-form-item label="系统词汇" prop="isSystem">
          <el-switch v-model="newWordForm.isSystem" :disabled="!isAdmin" />
          <div class="form-tip" v-if="newWordForm.isSystem">
            系统词汇对所有用户可见，且不可删除
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addWordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addWord" :loading="submitting">添加</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog v-model="importDialogVisible" title="批量导入特殊词汇" width="500px">
      <el-form :model="importForm" :rules="importFormRules" ref="importFormRef" label-width="100px">
        <el-form-item label="TTS服务" prop="serviceId">
          <el-select v-model="importForm.serviceId" placeholder="选择TTS服务" disabled>
            <el-option v-for="service in availableServices" :key="service.id" :label="service.name"
              :value="service.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="语言" prop="languageCode">
          <el-select v-model="importForm.languageCode" placeholder="选择语言" disabled>
            <el-option v-for="lang in availableLanguages" :key="lang.code" :label="lang.name" :value="lang.code" />
          </el-select>
        </el-form-item>

        <el-form-item label="特殊词汇" prop="words">
          <el-input v-model="importForm.words" type="textarea" :rows="8" placeholder="每行输入一个特殊词汇" />
        </el-form-item>

        <el-form-item label="系统词汇" prop="isSystem">
          <el-switch v-model="importForm.isSystem" :disabled="!isAdmin" />
          <div class="form-tip" v-if="importForm.isSystem">
            系统词汇对所有用户可见，且不可删除
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="importWords" :loading="submitting">导入</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { ElMessage, ElMessageBox } from 'element-plus';
import { useUserStore } from '@/stores/userStore';
import httpClient from '@/utils/httpClient';
import { API_ENDPOINTS } from '@/config/api';

// 状态
const loading = ref(false);
const submitting = ref(false);
const userStore = useUserStore();
const isAdmin = computed(() => userStore.isAdmin);

// 筛选条件
const currentService = ref('');
const currentLanguage = ref('');
const filterType = ref('');
const searchKeyword = ref('');
const currentPage = ref(1);
const pageSize = ref(20);

// 数据
const availableServices = ref([]);
const availableLanguages = ref([]);
const specialWords = ref([]);

// 对话框
const addWordDialogVisible = ref(false);
const importDialogVisible = ref(false);
const wordFormRef = ref(null);
const importFormRef = ref(null);

// 表单数据
const newWordForm = ref({
  serviceId: '',
  languageCode: '',
  word: '',
  isSystem: false
});

const importForm = ref({
  serviceId: '',
  languageCode: '',
  words: '',
  isSystem: false
});

// 表单验证规则
const wordFormRules = {
  word: [
    { required: true, message: '请输入特殊词汇', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在1到50个字符之间', trigger: 'blur' }
  ]
};

const importFormRules = {
  words: [
    { required: true, message: '请输入特殊词汇', trigger: 'blur' }
  ]
};

// 计算属性
const filteredWords = computed(() => {
  let result = [...specialWords.value];

  // 按类型筛选
  if (filterType.value === 'system') {
    result = result.filter(word => word.isSystem);
  } else if (filterType.value === 'user') {
    result = result.filter(word => !word.isSystem);
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(word => word.word.toLowerCase().includes(keyword));
  }

  // 分页
  const startIndex = (currentPage.value - 1) * pageSize.value;
  return result.slice(startIndex, startIndex + pageSize.value);
});

const totalWords = computed(() => {
  let result = [...specialWords.value];

  // 按类型筛选
  if (filterType.value === 'system') {
    result = result.filter(word => word.isSystem);
  } else if (filterType.value === 'user') {
    result = result.filter(word => !word.isSystem);
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(word => word.word.toLowerCase().includes(keyword));
  }

  return result.length;
});

// 生命周期钩子
onMounted(async () => {
  await loadServices();
  // 页面加载时立即加载所有特殊词汇
  await loadSpecialWords();
});

// 监听筛选条件变化
watch([filterType, searchKeyword], () => {
  currentPage.value = 1;
});

// 方法
async function loadServices() {
  try {
    loading.value = true;
    // 使用正确的API接口
    const response = await httpClient.get(API_ENDPOINTS.AUDIO.TTS_INFO);
    // 从响应中提取服务信息
    const ttsInfo = response.data || {};
    // 从voices中提取唯一的服务ID和名称
    const servicesMap = {};
    (ttsInfo.voices || []).forEach(voice => {
      if (voice.service_id && voice.service_name) {
        servicesMap[voice.service_id] = {
          id: voice.service_id,
          name: voice.service_name
        };
      }
    });
    // 转换为数组
    availableServices.value = Object.values(servicesMap);
  } catch (error) {
    console.error('加载TTS服务失败:', error);
    ElMessage.error('加载TTS服务失败');
  } finally {
    loading.value = false;
  }
}

async function handleServiceChange() {
  currentLanguage.value = '';
  specialWords.value = [];

  if (!currentService.value) return;

  try {
    loading.value = true;
    // 使用正确的API接口
    const response = await httpClient.get(API_ENDPOINTS.AUDIO.TTS_INFO);
    const ttsInfo = response.data || {};

    // 从ttsInfo中提取语言信息
    const languages = ttsInfo.languages || {};
    availableLanguages.value = Object.entries(languages).map(([code, info]) => ({
      code,
      name: info.name
    }));
  } catch (error) {
    console.error('加载语言列表失败:', error);
    ElMessage.error('加载语言列表失败');
  } finally {
    loading.value = false;
  }
}

async function handleLanguageChange() {
  if (!currentService.value || !currentLanguage.value) {
    specialWords.value = [];
    return;
  }

  await loadSpecialWords();
}

async function loadSpecialWords() {
  try {
    loading.value = true;

    // 构建查询参数，如果选择了特定服务或语言，则进行筛选
    const params = {};
    if (currentService.value) {
      params.service_id = currentService.value;
    }
    if (currentLanguage.value) {
      params.language_code = currentLanguage.value;
    }

    const response = await httpClient.get(API_ENDPOINTS.SPECIAL_WORDS.BASE, params);

    // 处理API返回的数据结构
    const data = response.data || {};

    // 如果选择了特定服务和语言，则只显示该服务和语言的特殊词汇
    if (currentService.value && currentLanguage.value) {
      const serviceData = data[currentService.value] || {};
      const languageData = serviceData[currentLanguage.value] || [];
      specialWords.value = languageData;
    } else {
      // 否则，将所有特殊词汇展平为一个数组
      const allWords = [];

      // 遍历所有服务
      Object.entries(data).forEach(([serviceId, serviceData]) => {
        // 遍历服务下的所有语言
        Object.entries(serviceData).forEach(([langCode, words]) => {
          // 为每个词汇添加服务和语言信息
          words.forEach(word => {
            allWords.push({
              ...word,
              serviceId,
              languageCode: langCode,
              // 添加显示名称
              serviceDisplay: availableServices.value.find(s => s.id === serviceId)?.name || serviceId,
              languageDisplay: availableLanguages.value.find(l => l.code === langCode)?.name || langCode
            });
          });
        });
      });

      specialWords.value = allWords;
    }
  } catch (error) {
    console.error('加载特殊词汇失败:', error);
    ElMessage.error('加载特殊词汇失败');
  } finally {
    loading.value = false;
  }
}

function handlePageChange(page) {
  currentPage.value = page;
}

function showAddWordDialog() {
  newWordForm.value = {
    serviceId: currentService.value,
    languageCode: currentLanguage.value,
    word: '',
    isSystem: false
  };
  addWordDialogVisible.value = true;

  // 下一个事件循环中重置表单验证
  setTimeout(() => {
    wordFormRef.value?.resetFields();
  }, 0);
}

function showImportDialog() {
  importForm.value = {
    serviceId: currentService.value,
    languageCode: currentLanguage.value,
    words: '',
    isSystem: false
  };
  importDialogVisible.value = true;

  // 下一个事件循环中重置表单验证
  setTimeout(() => {
    importFormRef.value?.resetFields();
  }, 0);
}

async function addWord() {
  if (!wordFormRef.value) return;

  await wordFormRef.value.validate(async (valid) => {
    if (!valid) return;

    try {
      submitting.value = true;

      // 检查是否已存在
      const exists = specialWords.value.some(w => w.word === newWordForm.value.word);
      if (exists) {
        ElMessage.warning('该特殊词汇已存在');
        return;
      }

      // 调用API添加特殊词汇
      await httpClient.post(API_ENDPOINTS.SPECIAL_WORDS.BASE, {
        word: newWordForm.value.word,
        service_id: newWordForm.value.serviceId,
        language_code: newWordForm.value.languageCode,
        is_system: newWordForm.value.isSystem
      });

      // 添加成功，刷新列表
      ElMessage.success('添加特殊词汇成功');
      addWordDialogVisible.value = false;
      await loadSpecialWords();
    } catch (error) {
      console.error('添加特殊词汇失败:', error);
      ElMessage.error('添加特殊词汇失败: ' + (error.response?.data?.error || error.message));
    } finally {
      submitting.value = false;
    }
  });
}

async function importWords() {
  if (!importFormRef.value) return;

  await importFormRef.value.validate(async (valid) => {
    if (!valid) return;

    try {
      submitting.value = true;

      // 解析输入的词汇
      const words = importForm.value.words
        .split('\n')
        .map(w => w.trim())
        .filter(w => w);

      if (words.length === 0) {
        ElMessage.warning('请输入至少一个特殊词汇');
        return;
      }

      // 调用API批量添加特殊词汇
      const response = await httpClient.post(`${API_ENDPOINTS.SPECIAL_WORDS.BASE}/batch`, {
        words: words.map(word => ({
          word,
          service_id: importForm.value.serviceId,
          language_code: importForm.value.languageCode,
          is_system: importForm.value.isSystem
        }))
      });

      // 添加成功，刷新列表
      const addedCount = response.data?.added || words.length;
      ElMessage.success(`成功导入 ${addedCount} 个特殊词汇`);
      importDialogVisible.value = false;
      await loadSpecialWords();
    } catch (error) {
      console.error('批量导入特殊词汇失败:', error);
      ElMessage.error('批量导入特殊词汇失败: ' + (error.response?.data?.error || error.message));
    } finally {
      submitting.value = false;
    }
  });
}

async function deleteWord(word) {
  try {
    // 构建确认消息，包含服务和语言信息
    let confirmMessage = `确定要删除特殊词汇 "${word.word}" 吗？`;

    // 如果未选择特定服务和语言，显示服务和语言信息
    if (!currentService.value || !currentLanguage.value) {
      const serviceDisplay = word.serviceDisplay || word.serviceId;
      const languageDisplay = word.languageDisplay || word.languageCode;
      confirmMessage = `确定要删除特殊词汇 "${word.word}" (${serviceDisplay}, ${languageDisplay}) 吗？`;
    }

    await ElMessageBox.confirm(
      confirmMessage,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    loading.value = true;

    // 调用API删除特殊词汇
    await httpClient.delete(`${API_ENDPOINTS.SPECIAL_WORDS.BASE}/${word.id}`);

    // 删除成功，刷新列表
    ElMessage.success('删除特殊词汇成功');
    await loadSpecialWords();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除特殊词汇失败:', error);
      ElMessage.error('删除特殊词汇失败: ' + (error.response?.data?.error || error.message));
    }
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped>
.special-word-management {
  padding: 1.25rem;
}

.page-header {
  margin-bottom: 1.25rem;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.description {
  color: #606266;
  margin: 0;
}

.filter-card {
  margin-bottom: 1.25rem;
}

.filter-container {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-container .el-select,
.filter-container .el-input {
  width: 12.5rem;
}

.word-list-card {
  margin-bottom: 1.25rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 0.625rem;
}

.empty-state {
  padding: 2.5rem 0;
}

.word-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(15rem, 1fr));
  gap: 1rem;
  margin-bottom: 1.25rem;
}

.word-card {
  border: 1px solid #e4e7ed;
  border-radius: 0.25rem;
  padding: 0.75rem;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.word-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.word-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  max-width: 80%;
}

.service-tag,
.language-tag {
  margin-top: 0.25rem;
}

.word-content {
  font-size: 1rem;
  word-break: break-all;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 1.25rem;
}

.form-tip {
  font-size: 0.75rem;
  color: #909399;
  margin-top: 0.25rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
