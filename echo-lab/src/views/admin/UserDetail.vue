<!--
  用户详情页面
  用于查看和管理单个用户的详细信息
-->
<template>
  <div class="user-detail">
    <!-- 返回按钮 -->
    <div class="page-actions">
      <el-button @click="goBack" icon="ArrowLeft">返回用户列表</el-button>
    </div>

    <div class="detail-container" v-loading="loading">
      <!-- 用户基本信息 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>用户信息</span>
            <div class="header-actions">
              <el-button type="primary" size="small" @click="refreshUserDetail">
                <el-icon>
                  <i-ep-refresh />
                </el-icon>
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <div class="user-info" v-if="user">
          <div class="user-avatar">
            <el-avatar :size="80" :src="user.avatarUrl">
              {{ userInitials }}
            </el-avatar>
          </div>

          <div class="user-details">
            <div class="detail-row">
              <span class="detail-label">ID:</span>
              <span class="detail-value">{{ user.id }}</span>
            </div>

            <div class="detail-row">
              <span class="detail-label">用户名:</span>
              <span class="detail-value">{{ user.username || '未设置' }}</span>
            </div>

            <div class="detail-row">
              <span class="detail-label">邮箱:</span>
              <span class="detail-value">{{ user.email }}</span>
            </div>

            <div class="detail-row">
              <span class="detail-label">角色:</span>
              <span class="detail-value">
                <el-tag :type="user.role === 'admin' ? 'danger' : 'info'" size="small">
                  {{ user.role === 'admin' ? '管理员' : '用户' }}
                </el-tag>
                <el-button v-if="user.id !== currentUserId" link size="small" @click="showRoleDialog">
                  修改
                </el-button>
              </span>
            </div>

            <div class="detail-row">
              <span class="detail-label">状态:</span>
              <span class="detail-value">
                <el-tag :type="getStatusType(user.status)" size="small">
                  {{ getStatusText(user.status) }}
                </el-tag>
                <el-button v-if="user.id !== currentUserId" link size="small" @click="showStatusDialog">
                  修改
                </el-button>
              </span>
            </div>

            <div class="detail-row">
              <span class="detail-label">注册时间:</span>
              <span class="detail-value">{{ formatDate(user.created_at) }}</span>
            </div>

            <div class="detail-row">
              <span class="detail-label">最后更新:</span>
              <span class="detail-value">{{ formatDate(user.updated_at) }}</span>
            </div>
          </div>
        </div>

        <el-empty v-else description="未找到用户信息" />
      </el-card>

      <!-- 用户统计信息 -->
      <el-card class="stats-card">
        <template #header>
          <div class="card-header">
            <span>用户统计</span>
          </div>
        </template>

        <div class="user-stats" v-if="stats">
          <div class="stat-item">
            <div class="stat-value">{{ stats.contentCount }}</div>
            <div class="stat-label">内容数量</div>
          </div>

          <!-- 可以添加更多统计项 -->
        </div>
      </el-card>
    </div>

    <!-- 角色修改对话框 -->
    <el-dialog v-model="roleDialogVisible" title="修改用户角色" width="30rem" :close-on-click-modal="false">
      <div class="dialog-content">
        <p>当前用户: {{ user?.username || user?.email }}</p>
        <p>当前角色: {{ user?.role === 'admin' ? '管理员' : '用户' }}</p>

        <el-form :model="roleForm" label-width="5rem">
          <el-form-item label="新角色">
            <el-select v-model="roleForm.role" placeholder="请选择角色">
              <el-option label="普通用户" value="user" />
              <el-option label="管理员" value="admin" />
            </el-select>
          </el-form-item>
        </el-form>

        <div class="dialog-warning" v-if="roleForm.role === 'admin'">
          <el-alert title="警告：管理员拥有系统的完全管理权限" type="warning" :closable="false" show-icon />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="roleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateUserRole" :loading="updating">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 状态修改对话框 -->
    <el-dialog v-model="statusDialogVisible" title="修改用户状态" width="30rem" :close-on-click-modal="false">
      <div class="dialog-content">
        <p>当前用户: {{ user?.username || user?.email }}</p>
        <p>当前状态: {{ getStatusText(user?.status) }}</p>

        <el-form :model="statusForm" label-width="5rem">
          <el-form-item label="新状态">
            <el-select v-model="statusForm.status" placeholder="请选择状态">
              <el-option label="活跃" value="active" />
              <el-option label="禁用" value="banned" />
            </el-select>
          </el-form-item>
        </el-form>

        <div class="dialog-warning" v-if="statusForm.status === 'banned'">
          <el-alert title="警告：禁用后该用户将无法登录系统" type="warning" :closable="false" show-icon />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="statusDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateUserStatus" :loading="updating">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { ElMessage } from 'element-plus';
import httpClient from '@/utils/httpClient';
import { API_ENDPOINTS } from '@/config/api';
import { useUserStore } from '@/stores/userStore';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

// 当前用户ID
const currentUserId = computed(() => userStore.user?.id);

// 用户ID
const userId = computed(() => route.params.id);

// 加载状态
const loading = ref(false);
const updating = ref(false);

// 用户数据
const user = ref(null);
const stats = ref(null);

// 对话框状态
const roleDialogVisible = ref(false);
const statusDialogVisible = ref(false);

// 表单数据
const roleForm = ref({
  role: ''
});

const statusForm = ref({
  status: ''
});

// 用户首字母
const userInitials = computed(() => {
  if (!user.value) return '';
  if (user.value.username) return user.value.username.substring(0, 1).toUpperCase();
  return user.value.email.substring(0, 1).toUpperCase();
});

// 获取用户详情
async function fetchUserDetail() {
  loading.value = true;
  try {
    const response = await httpClient.get(`${API_ENDPOINTS.ADMIN.USERS}/${userId.value}`);
    if (response.success) {
      user.value = response.data.user;
      stats.value = response.data.stats;
    } else {
      ElMessage.error(response.error || '获取用户详情失败');
    }
  } catch (error) {
    console.error('获取用户详情失败:', error);
    ElMessage.error('获取用户详情失败: ' + error.message);
  } finally {
    loading.value = false;
  }
}

// 刷新用户详情
function refreshUserDetail() {
  fetchUserDetail();
}

// 返回用户列表
function goBack() {
  router.push('/admin/users');
}

// 显示角色修改对话框
function showRoleDialog() {
  roleForm.value.role = user.value.role;
  roleDialogVisible.value = true;
}

// 显示状态修改对话框
function showStatusDialog() {
  statusForm.value.status = user.value.status;
  statusDialogVisible.value = true;
}

// 更新用户角色
async function updateUserRole() {
  if (roleForm.value.role === user.value.role) {
    roleDialogVisible.value = false;
    return;
  }

  updating.value = true;
  try {
    const response = await httpClient.put(`${API_ENDPOINTS.ADMIN.USERS}/${userId.value}/role`, {
      role: roleForm.value.role
    });

    if (response.success) {
      ElMessage.success(response.message || '用户角色更新成功');
      user.value.role = roleForm.value.role;
      roleDialogVisible.value = false;
    } else {
      ElMessage.error(response.error || '用户角色更新失败');
    }
  } catch (error) {
    console.error('更新用户角色失败:', error);
    ElMessage.error('更新用户角色失败: ' + error.message);
  } finally {
    updating.value = false;
  }
}

// 更新用户状态
async function updateUserStatus() {
  if (statusForm.value.status === user.value.status) {
    statusDialogVisible.value = false;
    return;
  }

  updating.value = true;
  try {
    const response = await httpClient.put(`${API_ENDPOINTS.ADMIN.USERS}/${userId.value}/status`, {
      status: statusForm.value.status
    });

    if (response.success) {
      ElMessage.success(response.message || '用户状态更新成功');
      user.value.status = statusForm.value.status;
      statusDialogVisible.value = false;
    } else {
      ElMessage.error(response.error || '用户状态更新失败');
    }
  } catch (error) {
    console.error('更新用户状态失败:', error);
    ElMessage.error('更新用户状态失败: ' + error.message);
  } finally {
    updating.value = false;
  }
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// 获取用户状态类型
function getStatusType(status) {
  switch (status) {
    case 'active': return 'success';
    case 'inactive': return 'warning';
    case 'banned': return 'danger';
    default: return 'info';
  }
}

// 获取用户状态文本
function getStatusText(status) {
  switch (status) {
    case 'active': return '活跃';
    case 'inactive': return '未激活';
    case 'banned': return '已禁用';
    default: return '未知';
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchUserDetail();
});
</script>

<style scoped>
.user-detail {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.page-actions {
  margin-bottom: 1rem;
}

.detail-container {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.info-card {
  flex: 2;
  min-width: 30rem;
}

.stats-card {
  flex: 1;
  min-width: 15rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.user-info {
  display: flex;
  gap: 2rem;
}

.user-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.user-details {
  flex: 1;
}

.detail-row {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.detail-label {
  font-weight: bold;
  width: 6rem;
  color: #606266;
}

.detail-value {
  flex: 1;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  padding: 1rem 0;
}

.stat-item {
  text-align: center;
  padding: 0.5rem;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #606266;
}

.dialog-content {
  margin-bottom: 1rem;
}

.dialog-warning {
  margin-top: 1rem;
}

/* 移动端样式通过条件类名处理 */
</style>
