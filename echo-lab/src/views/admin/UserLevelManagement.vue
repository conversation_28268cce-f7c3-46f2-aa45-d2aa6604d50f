<template>
  <div class="user-level-management">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <h2>用户等级管理</h2>
          <el-button type="primary" @click="openLevelDialog()">添加等级</el-button>
        </div>
      </template>

      <!-- 等级列表 -->
      <el-table v-loading="loading" :data="levels" border stripe>
        <el-table-column prop="level" label="等级" width="80" />
        <el-table-column prop="name" label="名称" width="150" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="isDefault" label="默认等级" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.isDefault" type="success">是</el-tag>
            <el-tag v-else type="info">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="openLevelDialog(scope.row)">编辑</el-button>
            <el-button size="small" type="primary" @click="openPermissionsDialog(scope.row)">
              权限
            </el-button>
            <el-button size="small" type="warning" @click="openLimitsDialog(scope.row)">
              限制
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 等级编辑对话框 -->
    <el-dialog v-model="levelDialog.visible" :title="levelDialog.isEdit ? '编辑等级' : '添加等级'" width="500px">
      <el-form ref="levelFormRef" :model="levelDialog.form" :rules="levelDialog.rules" label-width="100px">
        <el-form-item label="等级" prop="level">
          <el-input-number v-model="levelDialog.form.level" :min="0" :max="99" :disabled="levelDialog.isEdit" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="levelDialog.form.name" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="levelDialog.form.description" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="默认等级">
          <el-switch v-model="levelDialog.form.isDefault" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="levelDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="saveLevelDialog">保存</el-button>
      </template>
    </el-dialog>

    <!-- 权限管理对话框 -->
    <el-dialog v-model="permissionsDialog.visible" :title="`${permissionsDialog.level?.name || ''} 权限管理`" width="600px">
      <div v-loading="permissionsDialog.loading">
        <el-alert v-if="permissionsDialog.level" type="info" :closable="false" show-icon>
          管理 {{ permissionsDialog.level.name }} (等级 {{ permissionsDialog.level.level }}) 的功能权限
        </el-alert>

        <div class="permissions-list">
          <el-checkbox-group v-model="permissionsDialog.selectedFeatures">
            <el-checkbox v-for="feature in allFeatures" :key="feature.key" :label="feature.key">
              {{ feature.name }}
              <el-tooltip :content="feature.description" placement="top">
                <el-icon class="info-icon">
                  <i-ep-info-filled />
                </el-icon>
              </el-tooltip>
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <template #footer>
        <el-button @click="permissionsDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="savePermissions">保存</el-button>
      </template>
    </el-dialog>

    <!-- 使用限制对话框 -->
    <el-dialog v-model="limitsDialog.visible" :title="`${limitsDialog.level?.name || ''} 使用限制`" width="700px">
      <div v-loading="limitsDialog.loading">
        <el-alert v-if="limitsDialog.level" type="info" :closable="false" show-icon>
          管理 {{ limitsDialog.level.name }} (等级 {{ limitsDialog.level.level }}) 的功能使用限制
        </el-alert>

        <el-table :data="limitsDialog.limits" border>
          <el-table-column prop="featureKey" label="功能" width="150">
            <template #default="scope">
              {{ getFeatureName(scope.row.featureKey) }}
            </template>
          </el-table-column>
          <el-table-column prop="dailyLimit" label="每日限制" width="150">
            <template #default="scope">
              <el-input-number v-model="scope.row.dailyLimit" :min="0" :step="1" :controls="false" placeholder="无限制" />
            </template>
          </el-table-column>
          <el-table-column prop="monthlyLimit" label="每月限制" width="150">
            <template #default="scope">
              <el-input-number v-model="scope.row.monthlyLimit" :min="0" :step="1" :controls="false"
                placeholder="无限制" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button size="small" type="danger" @click="removeLimit(scope.$index)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="add-limit-form">
          <el-form :inline="true" :model="limitsDialog.newLimit">
            <el-form-item label="功能">
              <el-select v-model="limitsDialog.newLimit.featureKey" placeholder="选择功能">
                <el-option v-for="feature in allFeatures" :key="feature.key" :label="feature.name"
                  :value="feature.key" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="addLimit">添加限制</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <el-button @click="limitsDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="saveLimits">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import {
  getAllLevels,
  createLevel,
  updateLevel,
  getLevelPermissions,
  updateLevelPermissions,
  getLevelLimits,
  updateLevelLimits
} from '@/services/adminService';
import { getAllFeatures } from '@/services/featurePermissionService';

// 所有功能列表 - 从服务器获取
const allFeatures = ref([]);

// 数据和状态
const loading = ref(false);
const levels = ref([]);

// 等级编辑对话框
const levelDialog = reactive({
  visible: false,
  isEdit: false,
  form: {
    id: null,
    level: 0,
    name: '',
    description: '',
    isDefault: false
  },
  rules: {
    level: [{ required: true, message: '请输入等级', trigger: 'blur' }],
    name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
  }
});

// 权限管理对话框
const permissionsDialog = reactive({
  visible: false,
  loading: false,
  level: null,
  selectedFeatures: []
});

// 使用限制对话框
const limitsDialog = reactive({
  visible: false,
  loading: false,
  level: null,
  limits: [],
  newLimit: {
    featureKey: '',
    dailyLimit: null,
    monthlyLimit: null
  }
});

// 获取所有等级
async function fetchLevels() {
  loading.value = true;
  try {
    const result = await getAllLevels();
    levels.value = result;
  } catch (error) {
    console.error('获取等级列表失败:', error);
    ElMessage.error('获取等级列表失败');
  } finally {
    loading.value = false;
  }
}

// 打开等级编辑对话框
function openLevelDialog(level = null) {
  levelDialog.isEdit = !!level;

  if (level) {
    levelDialog.form = { ...level };
  } else {
    levelDialog.form = {
      id: null,
      level: Math.max(0, ...levels.value.map(l => l.level)) + 1,
      name: '',
      description: '',
      isDefault: false
    };
  }

  levelDialog.visible = true;
}

// 保存等级
async function saveLevelDialog() {
  try {
    if (levelDialog.isEdit) {
      await updateLevel(levelDialog.form.id, levelDialog.form);
      ElMessage.success('等级更新成功');
    } else {
      await createLevel(levelDialog.form);
      ElMessage.success('等级添加成功');
    }

    levelDialog.visible = false;
    fetchLevels();
  } catch (error) {
    console.error('保存等级失败:', error);
    ElMessage.error('保存等级失败: ' + (error.message || '未知错误'));
  }
}

// 打开权限管理对话框
async function openPermissionsDialog(level) {
  permissionsDialog.level = level;
  permissionsDialog.loading = true;
  permissionsDialog.visible = true;

  try {
    const permissions = await getLevelPermissions(level.level);
    permissionsDialog.selectedFeatures = permissions.map(p => p.featureKey);
  } catch (error) {
    console.error('获取等级权限失败:', error);
    ElMessage.error('获取等级权限失败');
  } finally {
    permissionsDialog.loading = false;
  }
}

// 保存权限
async function savePermissions() {
  if (!permissionsDialog.level) return;

  permissionsDialog.loading = true;
  try {
    await updateLevelPermissions(
      permissionsDialog.level.level,
      permissionsDialog.selectedFeatures
    );
    ElMessage.success('权限更新成功');
    permissionsDialog.visible = false;
  } catch (error) {
    console.error('保存权限失败:', error);
    ElMessage.error('保存权限失败: ' + (error.message || '未知错误'));
  } finally {
    permissionsDialog.loading = false;
  }
}

// 打开使用限制对话框
async function openLimitsDialog(level) {
  limitsDialog.level = level;
  limitsDialog.loading = true;
  limitsDialog.visible = true;
  limitsDialog.newLimit = {
    featureKey: '',
    dailyLimit: null,
    monthlyLimit: null
  };

  try {
    const limits = await getLevelLimits(level.level);
    limitsDialog.limits = limits;
  } catch (error) {
    console.error('获取使用限制失败:', error);
    ElMessage.error('获取使用限制失败');
  } finally {
    limitsDialog.loading = false;
  }
}

// 添加限制
function addLimit() {
  if (!limitsDialog.newLimit.featureKey) {
    ElMessage.warning('请选择功能');
    return;
  }

  // 检查是否已存在
  const exists = limitsDialog.limits.some(
    limit => limit.featureKey === limitsDialog.newLimit.featureKey
  );

  if (exists) {
    ElMessage.warning('该功能已有限制设置');
    return;
  }

  limitsDialog.limits.push({
    featureKey: limitsDialog.newLimit.featureKey,
    dailyLimit: null,
    monthlyLimit: null
  });

  limitsDialog.newLimit.featureKey = '';
}

// 移除限制
function removeLimit(index) {
  limitsDialog.limits.splice(index, 1);
}

// 保存使用限制
async function saveLimits() {
  if (!limitsDialog.level) return;

  limitsDialog.loading = true;
  try {
    await updateLevelLimits(limitsDialog.level.level, limitsDialog.limits);
    ElMessage.success('使用限制更新成功');
    limitsDialog.visible = false;
  } catch (error) {
    console.error('保存使用限制失败:', error);
    ElMessage.error('保存使用限制失败: ' + (error.message || '未知错误'));
  } finally {
    limitsDialog.loading = false;
  }
}

// 获取功能名称
function getFeatureName(featureKey) {
  const feature = allFeatures.find(f => f.key === featureKey);
  return feature ? feature.name : featureKey;
}

// 获取功能列表
async function fetchFeatures() {
  try {
    const features = await getAllFeatures();
    if (features && features.length > 0) {
      allFeatures.value = features;
    }
  } catch (error) {
    console.error('获取功能列表失败:', error);
  }
}

onMounted(async () => {
  await fetchFeatures();
  fetchLevels();
});
</script>

<style scoped>
.user-level-management {
  padding: 1rem;
}

.page-card {
  margin-bottom: 1.5rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.permissions-list {
  margin-top: 1rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.info-icon {
  margin-left: 0.25rem;
  color: var(--el-color-info);
  cursor: help;
}

.add-limit-form {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--el-border-color-lighter);
}
</style>
