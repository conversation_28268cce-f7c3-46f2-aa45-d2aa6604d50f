<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原文内容PDF导出功能测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.8.2/jspdf.plugin.autotable.min.js"></script>
</head>
<body>
    <h1>原文内容PDF导出功能测试</h1>
    <button onclick="testPDFExport()">测试PDF导出</button>
    
    <script>
        // 模拟configJson数据
        const mockConfigJson = {
            nodes: {
                textContent_1: {
                    type: 'textContent',
                    params: {
                        segments: [
                            {
                                id: 'seg_1',
                                content: '今日は良い天気ですね。',
                                speaker: 'A',
                                language: 'ja'
                            },
                            {
                                id: 'seg_2',
                                content: 'はい、とても気持ちがいいです。',
                                speaker: 'B',
                                language: 'ja'
                            },
                            {
                                id: 'seg_3',
                                content: '散歩でもしませんか？',
                                speaker: null,
                                language: 'ja'
                            },
                            {
                                id: 'seg_4',
                                content: 'いいですね。一緒に行きましょう。',
                                speaker: 'default',
                                language: 'ja'
                            }
                        ]
                    }
                }
            }
        };

        // 从configJson中提取原始内容
        function extractOriginalContent(configJson) {
            const originalContent = [];
            
            if (!configJson || !configJson.nodes) {
                console.error('无效的配置JSON:', configJson);
                return originalContent;
            }

            // 遍历所有textContent节点
            Object.values(configJson.nodes)
                .filter(node => node.type === 'textContent')
                .forEach(node => {
                    if (node.params?.segments) {
                        node.params.segments.forEach(segment => {
                            // 检查是否有有效的角色信息
                            const hasValidSpeaker = segment.speaker && 
                                                   segment.speaker !== 'default' && 
                                                   segment.speaker.trim() !== '';
                            
                            originalContent.push({
                                content: segment.content,
                                speaker: hasValidSpeaker ? segment.speaker : null,
                                language: segment.language || 'ja'
                            });
                        });
                    }
                });
            
            return originalContent;
        }

        // 格式化内容为PDF文本
        function formatContentForPDF(originalContent) {
            return originalContent.map(item => {
                if (item.speaker) {
                    return `${item.speaker}：${item.content}`;
                } else {
                    return item.content;
                }
            });
        }

        // 测试函数
        async function testPDFExport() {
            try {
                console.log('开始测试PDF导出...');
                
                // 提取原始内容
                const originalContent = extractOriginalContent(mockConfigJson);
                console.log('提取的原始内容:', originalContent);
                
                // 格式化内容
                const formattedLines = formatContentForPDF(originalContent);
                console.log('格式化后的内容:', formattedLines);
                
                // 创建PDF文档
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF({
                    orientation: 'portrait',
                    unit: 'mm',
                    format: 'a4'
                });

                // 设置字体
                doc.setFont('helvetica');
                
                // 添加标题
                doc.setFontSize(16);
                const title = '测试对话内容';
                doc.text(title, 20, 20);

                // 准备表格数据
                const tableData = formattedLines.map((line, index) => [
                    (index + 1).toString(),
                    line
                ]);

                // 使用autoTable生成表格
                doc.autoTable({
                    startY: 35,
                    head: [['序号', '内容']],
                    body: tableData,
                    styles: {
                        font: 'helvetica',
                        fontSize: 10,
                        cellPadding: 3,
                        overflow: 'linebreak',
                        cellWidth: 'wrap'
                    },
                    headStyles: {
                        fillColor: [240, 240, 240],
                        textColor: [0, 0, 0],
                        fontStyle: 'bold'
                    },
                    columnStyles: {
                        0: { cellWidth: 15, halign: 'center' }, // 序号列
                        1: { cellWidth: 160 } // 内容列
                    },
                    margin: { left: 20, right: 20 },
                    theme: 'grid'
                });

                // 下载PDF
                doc.save('测试对话内容.pdf');
                
                alert('PDF导出成功！请查看下载的文件。');
            } catch (error) {
                console.error('测试失败:', error);
                alert('PDF导出失败: ' + error.message);
            }
        }
    </script>
</body>
</html>
