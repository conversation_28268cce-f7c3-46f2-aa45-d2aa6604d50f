<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>原文内容PDF导出功能测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
  </head>
  <body>
    <h1>原文内容PDF导出功能测试</h1>
    <button onclick="testPDFExport()">测试PDF导出</button>

    <script>
      // 模拟configJson数据
      const mockConfigJson = {
        nodes: {
          textContent_1: {
            type: "textContent",
            params: {
              segments: [
                {
                  id: "seg_1",
                  content: "今日は良い天気ですね。",
                  speaker: "A",
                  language: "ja",
                },
                {
                  id: "seg_2",
                  content: "はい、とても気持ちがいいです。",
                  speaker: "B",
                  language: "ja",
                },
                {
                  id: "seg_3",
                  content: "散歩でもしませんか？",
                  speaker: null,
                  language: "ja",
                },
                {
                  id: "seg_4",
                  content: "いいですね。一緒に行きましょう。",
                  speaker: "default",
                  language: "ja",
                },
              ],
            },
          },
        },
      };

      // 从configJson中提取原始内容
      function extractOriginalContent(configJson) {
        const originalContent = [];

        if (!configJson || !configJson.nodes) {
          console.error("无效的配置JSON:", configJson);
          return originalContent;
        }

        // 遍历所有textContent节点
        Object.values(configJson.nodes)
          .filter((node) => node.type === "textContent")
          .forEach((node) => {
            if (node.params?.segments) {
              node.params.segments.forEach((segment) => {
                // 检查是否有有效的角色信息
                const hasValidSpeaker =
                  segment.speaker &&
                  segment.speaker !== "default" &&
                  segment.speaker.trim() !== "";

                originalContent.push({
                  content: segment.content,
                  speaker: hasValidSpeaker ? segment.speaker : null,
                  language: segment.language || "ja",
                });
              });
            }
          });

        return originalContent;
      }

      // 格式化内容为PDF文本
      function formatContentForPDF(originalContent) {
        return originalContent.map((item) => {
          if (item.speaker) {
            return `${item.speaker}：${item.content}`;
          } else {
            return item.content;
          }
        });
      }

      // 创建HTML内容并转换为PDF
      async function createPDFFromHTML(formattedLines, title) {
        const htmlContent = `
                <!DOCTYPE html>
                <html>
                <head>
                  <meta charset="UTF-8">
                  <style>
                    body {
                      font-family: "Microsoft YaHei", "SimSun", "Arial Unicode MS", sans-serif;
                      font-size: 14px;
                      line-height: 1.6;
                      margin: 20px;
                      color: #333;
                    }
                    .title {
                      font-size: 18px;
                      font-weight: bold;
                      text-align: center;
                      margin-bottom: 30px;
                      border-bottom: 2px solid #333;
                      padding-bottom: 10px;
                    }
                    .content-table {
                      width: 100%;
                      border-collapse: collapse;
                      margin-top: 20px;
                    }
                    .content-table th,
                    .content-table td {
                      border: 1px solid #ddd;
                      padding: 8px;
                      text-align: left;
                    }
                    .content-table th {
                      background-color: #f5f5f5;
                      font-weight: bold;
                      text-align: center;
                    }
                    .content-table .index-col {
                      width: 50px;
                      text-align: center;
                    }
                    .content-table .content-col {
                      word-wrap: break-word;
                      word-break: break-all;
                    }
                  </style>
                </head>
                <body>
                  <div class="title">${title}</div>
                  <table class="content-table">
                    <thead>
                      <tr>
                        <th class="index-col">序号</th>
                        <th class="content-col">内容</th>
                      </tr>
                    </thead>
                    <tbody>
                      ${formattedLines
                        .map(
                          (line, index) => `
                        <tr>
                          <td class="index-col">${index + 1}</td>
                          <td class="content-col">${line}</td>
                        </tr>
                      `
                        )
                        .join("")}
                    </tbody>
                  </table>
                </body>
                </html>
            `;
        return htmlContent;
      }

      // 测试函数
      async function testPDFExport() {
        try {
          console.log("开始测试PDF导出...");

          // 提取原始内容
          const originalContent = extractOriginalContent(mockConfigJson);
          console.log("提取的原始内容:", originalContent);

          // 格式化内容
          const formattedLines = formatContentForPDF(originalContent);
          console.log("格式化后的内容:", formattedLines);

          const title = "测试对话内容";

          // 创建临时DOM元素
          const tempDiv = document.createElement("div");
          tempDiv.style.position = "absolute";
          tempDiv.style.left = "-9999px";
          tempDiv.style.top = "-9999px";
          tempDiv.style.width = "210mm"; // A4宽度
          tempDiv.style.backgroundColor = "white";
          tempDiv.style.fontFamily =
            '"Microsoft YaHei", "SimSun", "Arial Unicode MS", sans-serif';
          tempDiv.style.fontSize = "14px";
          tempDiv.style.lineHeight = "1.6";
          tempDiv.style.padding = "20px";
          tempDiv.style.color = "#333";

          // 创建标题
          const titleDiv = document.createElement("div");
          titleDiv.style.fontSize = "18px";
          titleDiv.style.fontWeight = "bold";
          titleDiv.style.textAlign = "center";
          titleDiv.style.marginBottom = "30px";
          titleDiv.style.borderBottom = "2px solid #333";
          titleDiv.style.paddingBottom = "10px";
          titleDiv.textContent = title;
          tempDiv.appendChild(titleDiv);

          // 创建表格
          const table = document.createElement("table");
          table.style.width = "100%";
          table.style.borderCollapse = "collapse";
          table.style.marginTop = "20px";

          // 创建表头
          const thead = document.createElement("thead");
          const headerRow = document.createElement("tr");

          const indexHeader = document.createElement("th");
          indexHeader.textContent = "序号";
          indexHeader.style.border = "1px solid #ddd";
          indexHeader.style.padding = "8px";
          indexHeader.style.backgroundColor = "#f5f5f5";
          indexHeader.style.fontWeight = "bold";
          indexHeader.style.textAlign = "center";
          indexHeader.style.width = "50px";

          const contentHeader = document.createElement("th");
          contentHeader.textContent = "内容";
          contentHeader.style.border = "1px solid #ddd";
          contentHeader.style.padding = "8px";
          contentHeader.style.backgroundColor = "#f5f5f5";
          contentHeader.style.fontWeight = "bold";
          contentHeader.style.textAlign = "center";

          headerRow.appendChild(indexHeader);
          headerRow.appendChild(contentHeader);
          thead.appendChild(headerRow);
          table.appendChild(thead);

          // 创建表体
          const tbody = document.createElement("tbody");
          formattedLines.forEach((line, index) => {
            const row = document.createElement("tr");

            const indexCell = document.createElement("td");
            indexCell.textContent = (index + 1).toString();
            indexCell.style.border = "1px solid #ddd";
            indexCell.style.padding = "8px";
            indexCell.style.textAlign = "center";
            indexCell.style.width = "50px";

            const contentCell = document.createElement("td");
            contentCell.textContent = line;
            contentCell.style.border = "1px solid #ddd";
            contentCell.style.padding = "8px";
            contentCell.style.wordWrap = "break-word";
            contentCell.style.wordBreak = "break-all";

            row.appendChild(indexCell);
            row.appendChild(contentCell);
            tbody.appendChild(row);
          });

          table.appendChild(tbody);
          tempDiv.appendChild(table);
          document.body.appendChild(tempDiv);

          // 添加调试信息
          console.log("临时元素内容:", tempDiv.innerHTML);
          console.log("临时元素样式:", tempDiv.style.cssText);
          console.log("表格行数:", tempDiv.querySelectorAll("tr").length);

          // 让元素可见以便调试
          tempDiv.style.left = "0px";
          tempDiv.style.top = "0px";
          tempDiv.style.position = "relative";
          tempDiv.style.zIndex = "9999";

          // 等待一下让DOM完全渲染
          await new Promise((resolve) => setTimeout(resolve, 500));

          // 配置html2pdf选项
          const options = {
            margin: 0.5,
            filename: `${title}.pdf`,
            image: { type: "jpeg", quality: 0.98 },
            html2canvas: {
              scale: 1,
              useCORS: true,
              allowTaint: true,
              letterRendering: true,
              logging: true,
              width: tempDiv.offsetWidth,
              height: tempDiv.offsetHeight,
            },
            jsPDF: {
              unit: "in",
              format: "a4",
              orientation: "portrait",
            },
          };

          console.log("开始生成PDF...");

          // 生成并下载PDF
          await html2pdf().set(options).from(tempDiv).save();

          console.log("PDF生成完成");

          // 清理临时元素
          setTimeout(() => {
            if (document.body.contains(tempDiv)) {
              document.body.removeChild(tempDiv);
            }
          }, 1000);

          alert("PDF导出成功！请查看下载的文件。");
        } catch (error) {
          console.error("测试失败:", error);
          alert("PDF导出失败: " + error.message);
        }
      }
    </script>
  </body>
</html>
