<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>原文内容导出功能测试</title>
  </head>
  <body>
    <h1>原文内容导出功能测试</h1>
    <button onclick="testTextExport()">测试文本导出</button>

    <script>
      // 模拟configJson数据
      const mockConfigJson = {
        nodes: {
          textContent_1: {
            type: "textContent",
            params: {
              segments: [
                {
                  id: "seg_1",
                  content: "今日は良い天気ですね。",
                  speaker: "A",
                  language: "ja",
                },
                {
                  id: "seg_2",
                  content: "はい、とても気持ちがいいです。",
                  speaker: "B",
                  language: "ja",
                },
                {
                  id: "seg_3",
                  content: "散歩でもしませんか？",
                  speaker: null,
                  language: "ja",
                },
                {
                  id: "seg_4",
                  content: "いいですね。一緒に行きましょう。",
                  speaker: "default",
                  language: "ja",
                },
              ],
            },
          },
        },
      };

      // 从configJson中提取原始内容
      function extractOriginalContent(configJson) {
        const originalContent = [];

        if (!configJson || !configJson.nodes) {
          console.error("无效的配置JSON:", configJson);
          return originalContent;
        }

        // 遍历所有textContent节点
        Object.values(configJson.nodes)
          .filter((node) => node.type === "textContent")
          .forEach((node) => {
            if (node.params?.segments) {
              node.params.segments.forEach((segment) => {
                // 检查是否有有效的角色信息
                const hasValidSpeaker =
                  segment.speaker &&
                  segment.speaker !== "default" &&
                  segment.speaker.trim() !== "";

                originalContent.push({
                  content: segment.content,
                  speaker: hasValidSpeaker ? segment.speaker : null,
                  language: segment.language || "ja",
                });
              });
            }
          });

        return originalContent;
      }

      // 格式化内容为PDF文本
      function formatContentForPDF(originalContent) {
        return originalContent.map((item) => {
          if (item.speaker) {
            return `${item.speaker}：${item.content}`;
          } else {
            return item.content;
          }
        });
      }

      // 导出原文内容为PDF
      async function exportOriginalContentToPDF(configJson, contentTitle) {
        try {
          // 提取原始内容
          const originalContent = extractOriginalContent(configJson);

          if (originalContent.length === 0) {
            throw new Error("没有找到可导出的原文内容");
          }

          // 格式化内容
          const formattedLines = formatContentForPDF(originalContent);

          console.log("提取的原始内容:", originalContent);
          console.log("格式化后的内容:", formattedLines);

          // 创建PDF文档
          const { jsPDF } = window.jspdf;
          const doc = new jsPDF({
            orientation: "portrait",
            unit: "mm",
            format: "a4",
          });

          // 设置字体
          doc.setFont("helvetica");
          doc.setFontSize(12);

          // 添加标题
          doc.setFontSize(16);
          doc.text(contentTitle || "原文内容", 20, 20);

          // 添加内容
          doc.setFontSize(12);
          let yPosition = 40;
          const lineHeight = 8;
          const pageHeight = 280;
          const leftMargin = 20;
          const rightMargin = 190;

          formattedLines.forEach((line, index) => {
            // 检查是否需要换页
            if (yPosition > pageHeight) {
              doc.addPage();
              yPosition = 20;
            }

            // 处理长文本换行
            const splitText = doc.splitTextToSize(
              line,
              rightMargin - leftMargin
            );

            // 检查分割后的文本是否会超出页面
            if (yPosition + splitText.length * lineHeight > pageHeight) {
              doc.addPage();
              yPosition = 20;
            }

            // 添加文本
            doc.text(splitText, leftMargin, yPosition);
            yPosition += splitText.length * lineHeight + 2;
          });

          // 生成文件名
          const fileName = `${contentTitle || "原文内容"}.pdf`;

          // 下载PDF
          doc.save(fileName);

          return {
            success: true,
            message: "PDF导出成功",
            fileName,
          };
        } catch (error) {
          console.error("PDF导出失败:", error);
          throw new Error(`PDF导出失败: ${error.message}`);
        }
      }

      // 测试函数
      async function testPDFExport() {
        try {
          console.log("开始测试PDF导出...");
          const result = await exportOriginalContentToPDF(
            mockConfigJson,
            "测试对话内容"
          );
          console.log("测试成功:", result);
          alert("PDF导出成功！请查看下载的文件。");
        } catch (error) {
          console.error("测试失败:", error);
          alert("PDF导出失败: " + error.message);
        }
      }
    </script>
  </body>
</html>
