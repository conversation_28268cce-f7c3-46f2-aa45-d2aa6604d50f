<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>原文内容PDF导出功能测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.8.2/jspdf.plugin.autotable.min.js"></script>
  </head>
  <body>
    <h1>原文内容PDF导出功能测试</h1>
    <button onclick="testPDFExport()">测试PDF导出</button>

    <script>
      // 模拟configJson数据
      const mockConfigJson = {
        nodes: {
          textContent_1: {
            type: "textContent",
            params: {
              segments: [
                {
                  id: "seg_1",
                  content: "今日は良い天気ですね。",
                  speaker: "A",
                  language: "ja",
                },
                {
                  id: "seg_2",
                  content: "はい、とても気持ちがいいです。",
                  speaker: "B",
                  language: "ja",
                },
                {
                  id: "seg_3",
                  content: "散歩でもしませんか？",
                  speaker: null,
                  language: "ja",
                },
                {
                  id: "seg_4",
                  content: "いいですね。一緒に行きましょう。",
                  speaker: "default",
                  language: "ja",
                },
              ],
            },
          },
        },
      };

      // 从configJson中提取原始内容
      function extractOriginalContent(configJson) {
        const originalContent = [];

        if (!configJson || !configJson.nodes) {
          console.error("无效的配置JSON:", configJson);
          return originalContent;
        }

        // 遍历所有textContent节点
        Object.values(configJson.nodes)
          .filter((node) => node.type === "textContent")
          .forEach((node) => {
            if (node.params?.segments) {
              node.params.segments.forEach((segment) => {
                // 检查是否有有效的角色信息
                const hasValidSpeaker =
                  segment.speaker &&
                  segment.speaker !== "default" &&
                  segment.speaker.trim() !== "";

                originalContent.push({
                  content: segment.content,
                  speaker: hasValidSpeaker ? segment.speaker : null,
                  language: segment.language || "ja",
                });
              });
            }
          });

        return originalContent;
      }

      // 格式化内容为文本
      function formatContentForPDF(originalContent) {
        return originalContent.map((item) => {
          if (item.speaker) {
            return `${item.speaker}：${item.content}`;
          } else {
            return item.content;
          }
        });
      }

      // 测试函数
      async function testTextExport() {
        try {
          console.log("开始测试文本导出...");

          // 提取原始内容
          const originalContent = extractOriginalContent(mockConfigJson);
          console.log("提取的原始内容:", originalContent);

          // 格式化内容
          const formattedLines = formatContentForPDF(originalContent);
          console.log("格式化后的内容:", formattedLines);

          // 创建文本内容
          const textContent = ["测试对话内容", "", ...formattedLines].join(
            "\n"
          );

          console.log("最终文本内容:", textContent);

          // 创建文本文件下载
          const blob = new Blob([textContent], {
            type: "text/plain;charset=utf-8",
          });
          const url = URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.download = "测试对话内容.txt";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);

          alert("文本导出成功！请查看下载的文件。");
        } catch (error) {
          console.error("测试失败:", error);
          alert("文本导出失败: " + error.message);
        }
      }
    </script>
  </body>
</html>
