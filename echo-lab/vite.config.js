import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";
import { fileURLToPath } from "url";
import Components from "unplugin-vue-components/vite";
import AutoImport from "unplugin-auto-import/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import Icons from "unplugin-icons/vite";
import IconsResolver from "unplugin-icons/resolver";
import { VitePWA } from "vite-plugin-pwa";
import { visualizer } from "rollup-plugin-visualizer";

const isProd = process.env.NODE_ENV === "production";
const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig(() => {
  return {
    plugins: [
      vue({
        // 显式启用模板编译（确保 h 函数可用）
        template: {
          compilerOptions: {
            whitespace: "condense",
          },
        },
      }),
      // 自动导入Element Plus组件
      Components({
        resolvers: [
          ElementPlusResolver({
            importStyle: "sass", // 使用sass样式
          }),
          IconsResolver({
            prefix: "i", // 使用 i 前缀
            enabledCollections: ["ep"], // 启用 Element Plus 图标集
          }),
        ],
        dts: "src/components.d.ts",
      }),
      // 图标插件
      Icons({
        autoInstall: true,
      }),
      // 自动导入Element Plus API
      AutoImport({
        resolvers: [
          ElementPlusResolver(),
          IconsResolver({
            prefix: "i",
            enabledCollections: ["ep"],
          }),
        ],
        imports: ["vue", "vue-router"],
        dts: "src/auto-imports.d.ts",
      }),
      // PWA 配置
      VitePWA({
        registerType: "autoUpdate",
        includeAssets: ["favicon.ico", "icons/*.png"],
        includeManifestIcons: true,
        strategies: "generateSW",
        manifest: {
          id: "/",
          name: "Echo Lab - 日语听力练习",
          short_name: "Echo Lab",
          description: "专业的日语听力练习工具",
          theme_color: "#ff0000",
          background_color: "#ffffff",
          icons: [
            {
              src: "icons/logo-128.png",
              sizes: "128x128",
              type: "image/png",
              purpose: "any",
            },
            {
              src: "icons/logo-192.png",
              sizes: "192x192",
              type: "image/png",
              purpose: "any",
            },
            {
              src: "icons/logo-256.png",
              sizes: "256x256",
              type: "image/png",
              purpose: "any",
            },
            {
              src: "icons/logo-512.png",
              sizes: "512x512",
              type: "image/png",
              purpose: "any",
            },
            {
              src: "icons/logo-192.png",
              sizes: "192x192",
              type: "image/png",
              purpose: "maskable",
            },
            {
              src: "icons/logo-512.png",
              sizes: "512x512",
              type: "image/png",
              purpose: "maskable",
            },
          ],
          display: "standalone",
          start_url: "/",
          scope: "/",
        },
        workbox: {
          // 增加文件大小限制到5MB
          maximumFileSizeToCacheInBytes: 5 * 1024 * 1024,
          skipWaiting: true, // 添加跳过等待
          clientsClaim: true, // 添加客户端控制
          // 静态资源缓存
          globPatterns: isProd
            ? [
                "**/*.{js,css,html,ico,png,jpg,jpeg,gif,svg,webp,woff,woff2,ttf,eot}", // 缓存所有静态资源
                "assets/**", // 缓存构建后的 assets 目录
                "icons/**", // 和图标
              ]
            : [
                "index.html", // 开发环境只缓存主页面
                "icons/*.png", // 和图标
              ],
          // 使用index.html作为导航回退
          navigateFallback: "/index.html",
          navigateFallbackDenylist: [
            /^\/player\/[^/]+$/, // 播放页面不使用回退（后端SSR处理）
            /^\/_/, 
            /\/[^/?]+\.[^/]+$/
          ],
          runtimeCaching: [
            // 播放页面 - 网络优先（后端SSR生成）
            {
              urlPattern: /^\/player\/[^/]+$/,
              handler: "NetworkFirst",
              options: {
                cacheName: "player-pages",
                networkTimeoutSeconds: 3,
                expiration: {
                  maxEntries: 50,
                  maxAgeSeconds: 60 * 60 * 24, // 1天
                }
              }
            },
            // HTML文件 - 网络优先策略
            {
              urlPattern: ({ request }) => 
                request.mode === "navigate" || 
                request.destination === "document" ||
                request.url.endsWith(".html"),
              handler: "NetworkFirst",
              options: {
                cacheName: "html-cache",
                networkTimeoutSeconds: 3, // 3秒网络超时后使用缓存
                expiration: {
                  maxEntries: 5,
                  maxAgeSeconds: 60 * 60, // 1小时后过期
                },
                cacheableResponse: {
                  statuses: [0, 200],
                },
              },
            },
            // JS/CSS文件 - 缓存优先（因为有hash）
            {
              urlPattern: /\.(js|css)$/,
              handler: "CacheFirst",
              options: {
                cacheName: "static-resources",
                expiration: {
                  maxEntries: 100,
                  maxAgeSeconds: 60 * 60 * 24 * 30, // 30天
                },
              },
            },
            {
              // API 请求使用 NetworkFirst
              urlPattern: /^(https?:\/\/[^\/]+)?\/api\/.*/i,
              handler: "NetworkFirst",
              options: {
                cacheName: "api-cache",
                expiration: {
                  maxEntries: 100,
                  maxAgeSeconds: 60 * 60 * 24, // 1天
                },
                cacheableResponse: {
                  statuses: [0, 200],
                },
              },
            },
            {
              // 媒体文件使用 CacheFirst
              urlPattern:
                /^(https?:\/\/[^\/]+)?(\/oss-resources\/|.*\.(mp3|wav|ogg|m4a|jpg|jpeg|png|gif|webp|svg))(\?.*)?$/i,
              handler: "CacheFirst",
              options: {
                cacheName: "resources-cache",
                fetchOptions: {
                  credentials: "omit",
                },
                expiration: {
                  maxEntries: 1000,
                  maxAgeSeconds: 60 * 60 * 24 * 30, // 30天
                },
                cacheableResponse: {
                  statuses: [0, 200],
                },
              },
            },
          ],
        },
        devOptions: {
          enabled: true, // 重新启用开发环境 PWA
          type: "module",
        },
      }),
      // Bundle分析器 - 只在需要时启用
      process.env.ANALYZE &&
        visualizer({
          filename: "dist/stats.html",
          open: true,
          gzipSize: true,
          brotliSize: true,
          template: "treemap", // 'treemap', 'sunburst', 'network'
        }),
    ].filter(Boolean),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
      },
    },
    server: {
      port: 3001,
      host: "0.0.0.0",
      open: true,
      hmr: {
        overlay: false, // 禁用错误覆盖层
      },
      // 修复431错误
      middlewareMode: false,
      fs: {
        strict: false
      },
      proxy: {
        "/api": {
          target: "http://localhost:3000",
          changeOrigin: true,
        },
        // 开发环境播放页面代理到后端
        "^/player/[^/]+$": {
          target: "http://localhost:3000",
          changeOrigin: true,
        },

        // 倍速音频处理代理 - 匹配带speed参数的音频请求
        "^/oss-resources/audio/.+\\.(mp3|wav|m4a)\\?speed=": {
          target: "http://localhost:3000",
          changeOrigin: true,
          rewrite: (path) => {
            // 提取文件路径和速度参数
            const match = path.match(/^\/oss-resources\/(.+)\?speed=(.+)$/);
            if (match) {
              const [, filePath, speed] = match;
              return `/api/audio/speed?path=${filePath}&speed=${speed}`;
            }
            return path;
          },
        },
        "/oss-resources": {
          target: "https://echolab.oss-cn-hongkong.aliyuncs.com/",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/oss-resources/, ""),
        },
      },
    },
    define: {
      // Vue 特性标志
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: !isProd,
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: !isProd,
      // 修复 isFunction 初始化问题
      "process.env.NODE_ENV": JSON.stringify(
        process.env.NODE_ENV || "development"
      ),
    },
    esbuild: {
      target: "es2015",
      jsx: "transform",
      keepNames: true,
    },
    build: {
      minify: isProd ? "terser" : false,
      sourcemap: false,
      terserOptions: isProd
        ? {
            compress: {
              drop_console: true,
              drop_debugger: true,
            },
            format: {
              comments: false,
            },
            mangle: true,
          }
        : undefined,
      rollupOptions: {
        output: {
          entryFileNames: "assets/[name].[hash].js",
          chunkFileNames: "assets/[name].[hash].js",
          assetFileNames: "assets/[name].[hash].[ext]",
          // manualChunks: {
          //   // Vue 核心生态系统
          //   "vue-core": ["vue", "vue-router", "pinia"],

          //   // Element Plus UI 框架（自动包含所有依赖）
          //   "element-plus": ["element-plus"],

          //   // 常用工具库
          //   utils: ["crypto-js", "uuid", "mobile-detect"],

          //   // 网络请求
          //   network: ["axios"],

          //   // 媒体处理（按需加载的大型库）
          //   media: ["wavesurfer.js"],

          //   // 图像处理（按需加载）
          //   image: [
          //     "cropperjs",
          //     "vue-advanced-cropper",
          //     "html2canvas",
          //     "dom-to-image-more",
          //   ],

          //   // 文件处理
          //   file: ["jszip"],

          //   // 其他第三方库
          //   vendor: ["langdetect", "vuedraggable"],
          // },
        },
      },
      target: "es2015",
    },
    // optimizeDeps: {
    //   include: [
    //     // 核心依赖预构建
    //     "vue",
    //     "vue-router",
    //     "pinia",
    //     "element-plus",
    //     "axios",
    //     "crypto-js",
    //     "uuid",
    //     // 将 jszip 移到 include 中，让 Vite 预构建它
    //     "jszip",
    //   ],
    //   exclude: [
    //     // 排除大型库，让它们按需加载
    //     "mp4-muxer",
    //     "html2canvas",
    //     "dom-to-image-more",
    //     "cropperjs",
    //     "vue-advanced-cropper",
    //     "wavesurfer.js",
    //   ],
    // },
  };
});
