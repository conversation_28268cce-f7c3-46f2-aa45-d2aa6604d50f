events {
    worker_connections 1024;
}

http {
    include       /usr/local/etc/nginx/mime.types;
    default_type  application/octet-stream;

    server {
        listen 56675;
        server_name localhost;

        # API 转发到 3000 端口
        location /api {
            proxy_pass http://localhost:3000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 静态文件服务
        location / {
            root /Users/<USER>/JapRepeater/echo-lab/dist;
            try_files $uri $uri/ /index.html;
            index index.html;
        }
    }
}