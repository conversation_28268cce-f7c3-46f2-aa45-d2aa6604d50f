# 原文内容导出功能

本文档详细说明了 Echo Lab 中原文内容导出功能的实现和使用方法。

## 概述

原文内容导出功能允许用户将播放内容的原文导出为文本文件，方便离线阅读和学习。该功能会提取原始的文本内容，按照角色对话的格式进行排版。由于字体支持的复杂性，当前版本导出为文本文件格式，确保中文、日文等多语言内容的正确显示。

## 功能特点

- **原文提取**：从configJson中提取所有textContent节点的原始分句内容
- **角色识别**：自动识别和显示说话人信息
- **格式化输出**：有角色信息的内容显示为"角色：内容"格式，无角色信息的直接显示内容
- **文本文件生成**：生成UTF-8编码的文本文件，完美支持中文、日文等多语言字符
- **多平台支持**：桌面端和移动端都支持文本导出功能

## 使用方法

### 桌面端

1. 在播放页面点击右上角的设置按钮（齿轮图标）
2. 在弹出的菜单中选择"导出原文PDF"
3. 系统会自动生成PDF文件并下载

### 移动端

1. 在播放页面点击右上角的更多功能按钮（齿轮图标）
2. 在弹出的菜单中选择"导出原文PDF"
3. 系统会自动生成PDF文件并下载

## 技术实现

### 数据提取

```javascript
// 从configJson中提取原始内容
function extractOriginalContent(configJson) {
  const originalContent = [];
  
  // 遍历所有textContent节点
  Object.values(configJson.nodes)
    .filter(node => node.type === 'textContent')
    .forEach(node => {
      if (node.params?.segments) {
        node.params.segments.forEach(segment => {
          // 检查是否有有效的角色信息
          const hasValidSpeaker = segment.speaker && 
                                 segment.speaker !== 'default' && 
                                 segment.speaker.trim() !== '';
          
          originalContent.push({
            content: segment.content,
            speaker: hasValidSpeaker ? segment.speaker : null,
            language: segment.language || 'ja'
          });
        });
      }
    });
  
  return originalContent;
}
```

### 内容格式化

```javascript
// 格式化内容为PDF文本
function formatContentForPDF(originalContent) {
  return originalContent.map(item => {
    if (item.speaker) {
      return `${item.speaker}：${item.content}`;
    } else {
      return item.content;
    }
  });
}
```

### PDF生成

使用jsPDF库生成PDF文件：

- **页面设置**：A4纸张，纵向排版
- **字体设置**：Helvetica字体，标题16pt，正文12pt
- **布局设置**：左边距20mm，右边距20mm，行间距8pt
- **自动换页**：内容超出页面时自动添加新页面
- **文本换行**：长文本自动换行处理

## 输出格式示例

```
测试对话内容

A：今日は良い天気ですね。
B：はい、とても気持ちがいいです。
散歩でもしませんか？
いいですね。一緒に行きましょう。
```

## 文件结构

```
echo-lab/
├── src/
│   ├── utils/
│   │   └── pdfExporter.js          # PDF导出工具
│   └── components/
│       └── player/
│           ├── UnifiedControlPanel.vue    # 桌面端控制面板
│           ├── DesktopVideoPlayer.vue     # 桌面端播放器
│           └── MobileVideoPlayer.vue      # 移动端播放器
└── docs/
    └── features/
        └── pdf-export.md           # 本文档
```

## 依赖项

- **jsPDF**: PDF生成库
- **Element Plus**: UI组件库（用于消息提示）

## 注意事项

1. **字体支持**：当前使用Helvetica字体，对中文、日文的支持有限，如需完整支持需要额外配置字体文件
2. **文件大小**：生成的PDF文件大小取决于内容长度，通常较小
3. **浏览器兼容性**：现代浏览器都支持，IE需要polyfill
4. **移动端体验**：在移动设备上下载的PDF文件会保存到默认下载目录

## 未来改进

1. **字体支持**：添加中文、日文字体支持
2. **样式定制**：允许用户自定义PDF样式
3. **批量导出**：支持批量导出多个内容的PDF
4. **云端存储**：支持将PDF保存到云端存储服务
