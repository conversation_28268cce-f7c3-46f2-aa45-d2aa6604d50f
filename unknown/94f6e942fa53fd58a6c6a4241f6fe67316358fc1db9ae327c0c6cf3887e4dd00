/**
 * 图片路由
 * 处理图片上传和获取请求
 */
const express = require("express");
const router = express.Router();
const multer = require("multer");
const imageController = require("../controllers/imageController");

// 配置multer用于处理文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 限制10MB
  },
});

// 图片上传路由
router.post("/upload", upload.single("file"), imageController.uploadImage);

module.exports = router;
