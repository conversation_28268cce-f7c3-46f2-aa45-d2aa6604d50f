/**
 * SEO相关路由
 * 提供sitemap、robots.txt等SEO功能
 */
const express = require("express");
const router = express.Router();
const db = require("../models");
const { adminAuth } = require("../middleware/adminMiddleware");

// 网站基础URL
const BASE_URL = process.env.BASE_URL || "https://echolab.club";

/**
 * 生成sitemap.xml
 * GET /api/seo/sitemap.xml
 */
router.get("/sitemap.xml", async (req, res) => {
  try {
    // 设置响应头
    res.set("Content-Type", "application/xml");

    // 获取所有已发布的公开内容
    const contents = await db.Content.findAll({
      where: {
        status: "published",
        // 注意：这里不使用is_public字段，因为表中可能没有这个字段
      },
      attributes: ["id", "name", "updated_at"],
      order: [["updated_at", "DESC"]],
    });

    // 获取最新内容更新时间作为静态页面的lastmod
    const latestContentUpdate = contents.length > 0 
      ? new Date(contents[0].updated_at).toISOString().split("T")[0]
      : new Date().toISOString().split("T")[0];

    // 静态页面配置
    const staticPages = [
      {
        url: "/",
        changefreq: "daily",
        priority: 1.0,
        lastmod: latestContentUpdate,
      },
      {
        url: "/content-info",
        changefreq: "monthly",
        priority: 0.8,
        lastmod: latestContentUpdate,
      },
      {
        url: "/install-guide",
        changefreq: "monthly",
        priority: 0.7,
        lastmod: latestContentUpdate,
      },
      {
        url: "/login",
        changefreq: "monthly",
        priority: 0.5,
        lastmod: latestContentUpdate,
      },
    ];

    // 生成XML
    let sitemap = '<?xml version="1.0" encoding="UTF-8"?>\n';
    sitemap += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

    // 添加静态页面
    staticPages.forEach((page) => {
      sitemap += "  <url>\n";
      sitemap += `    <loc>${BASE_URL}${page.url}</loc>\n`;
      sitemap += `    <lastmod>${page.lastmod}</lastmod>\n`;
      sitemap += `    <changefreq>${page.changefreq}</changefreq>\n`;
      sitemap += `    <priority>${page.priority}</priority>\n`;
      sitemap += "  </url>\n";
    });

    // 添加内容页面
    contents.forEach((content) => {
      const lastmod = content.updated_at
        ? new Date(content.updated_at).toISOString().split("T")[0]
        : new Date().toISOString().split("T")[0];

      sitemap += "  <url>\n";
      sitemap += `    <loc>${BASE_URL}/player/${content.id}</loc>\n`;
      sitemap += `    <lastmod>${lastmod}</lastmod>\n`;
      sitemap += "    <changefreq>weekly</changefreq>\n";
      sitemap += "    <priority>1.0</priority>\n";
      sitemap += "  </url>\n";
    });

    sitemap += "</urlset>\n";

    res.send(sitemap);
  } catch (error) {
    console.error("生成sitemap失败:", error);
    res
      .status(500)
      .send(
        '<?xml version="1.0" encoding="UTF-8"?><error>Internal Server Error</error>'
      );
  }
});

/**
 * 生成robots.txt
 * GET /api/seo/robots.txt
 */
router.get("/robots.txt", (req, res) => {
  try {
    // 设置响应头
    res.set("Content-Type", "text/plain");

    const isProduction = process.env.NODE_ENV === "production";

    let robotsTxt = "";

    if (isProduction) {
      // 生产环境robots.txt
      robotsTxt += "User-agent: *\n";
      robotsTxt += "Allow: /\n";
      robotsTxt += "Allow: /player/*\n";
      robotsTxt += "Allow: /content-info\n";
      robotsTxt += "Allow: /install-guide\n";
      robotsTxt += "Disallow: /admin/*\n";
      robotsTxt += "Disallow: /api/*\n";
      robotsTxt += "Disallow: /editor/*\n";
      robotsTxt += "Disallow: /content\n";
      robotsTxt += "Disallow: /profile\n";
      robotsTxt += "Disallow: /settings\n";
      robotsTxt += "Disallow: /feedback\n";
      robotsTxt += "Disallow: /favorites\n";
      robotsTxt += "Disallow: /upgrade\n";
      robotsTxt += "Disallow: /unauthorized\n";
      robotsTxt += "Disallow: /mobile-tip\n";
      robotsTxt += "Crawl-delay: 1\n\n";

      // Google Bot
      robotsTxt += "User-agent: Googlebot\n";
      robotsTxt += "Allow: /\n";
      robotsTxt += "Allow: /player/*\n";
      robotsTxt += "Allow: /content-info\n";
      robotsTxt += "Allow: /install-guide\n";
      robotsTxt += "Disallow: /admin/*\n";
      robotsTxt += "Disallow: /api/*\n";
      robotsTxt += "Disallow: /editor/*\n";
      robotsTxt += "Disallow: /content\n";
      robotsTxt += "Disallow: /profile\n";
      robotsTxt += "Disallow: /settings\n";
      robotsTxt += "Disallow: /feedback\n";
      robotsTxt += "Disallow: /favorites\n";
      robotsTxt += "Disallow: /upgrade\n";
      robotsTxt += "Disallow: /unauthorized\n";
      robotsTxt += "Disallow: /mobile-tip\n\n";

      // 禁止恶意爬虫
      const badBots = [
        "AhrefsBot",
        "MJ12bot",
        "DotBot",
        "SemrushBot",
        "BLEXBot",
        "MegaIndex",
        "PetalBot",
      ];

      badBots.forEach((bot) => {
        robotsTxt += `User-agent: ${bot}\n`;
        robotsTxt += "Disallow: /\n\n";
      });

      robotsTxt += `Sitemap: ${BASE_URL}/api/seo/sitemap.xml\n`;
    } else {
      // 开发环境robots.txt - 禁止所有爬虫
      robotsTxt += "User-agent: *\n";
      robotsTxt += "Disallow: /\n";
    }

    res.send(robotsTxt);
  } catch (error) {
    console.error("生成robots.txt失败:", error);
    res.status(500).send("# Error generating robots.txt");
  }
});

/**
 * 获取SEO统计信息（管理员专用）
 * GET /api/seo/stats
 */
router.get("/stats", adminAuth, async (req, res) => {
  try {
    // 获取内容统计
    const totalContents = await db.Content.count({
      where: {
        status: "published",
      },
    });

    const recentContents = await db.Content.count({
      where: {
        status: "published",
        updated_at: {
          [db.Sequelize.Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 最近7天
        },
      },
    });

    // 获取最新更新时间
    const latestContent = await db.Content.findOne({
      where: {
        status: "published",
      },
      order: [["updated_at", "DESC"]],
      attributes: ["updated_at"],
    });

    const stats = {
      sitemap: {
        totalUrls: 4 + totalContents, // 4个静态页面 + 内容页面
        staticPages: 4,
        contentPages: totalContents,
        lastUpdate: latestContent ? latestContent.updated_at : new Date(),
      },
      content: {
        total: totalContents,
        recentlyUpdated: recentContents,
      },
    };

    res.json({
      success: true,
      stats,
    });
  } catch (error) {
    console.error("获取SEO统计失败:", error);
    res.status(500).json({
      success: false,
      error: "获取SEO统计失败",
    });
  }
});

/**
 * 验证sitemap格式（管理员专用）
 * POST /api/seo/validate-sitemap
 */
router.post("/validate-sitemap", adminAuth, async (req, res) => {
  try {
    const { sitemapXML } = req.body;

    if (!sitemapXML) {
      return res.status(400).json({
        success: false,
        error: "sitemap XML内容不能为空",
      });
    }

    // 基本的XML格式验证
    const errors = [];
    const warnings = [];

    // 检查XML格式
    if (!sitemapXML.includes('<?xml version="1.0"')) {
      errors.push("缺少XML声明");
    }

    if (!sitemapXML.includes("<urlset")) {
      errors.push("缺少urlset根元素");
    }

    if (
      !sitemapXML.includes(
        'xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"'
      )
    ) {
      warnings.push("建议添加sitemap命名空间");
    }

    // 统计URL数量
    const urlMatches = sitemapXML.match(/<url>/g);
    const urlCount = urlMatches ? urlMatches.length : 0;

    if (urlCount === 0) {
      warnings.push("sitemap中没有URL条目");
    }

    if (urlCount > 50000) {
      warnings.push("URL数量超过50000，建议分割为多个sitemap文件");
    }

    res.json({
      success: true,
      validation: {
        isValid: errors.length === 0,
        errors,
        warnings,
        urlCount,
      },
    });
  } catch (error) {
    console.error("验证sitemap失败:", error);
    res.status(500).json({
      success: false,
      error: "验证sitemap失败",
    });
  }
});

module.exports = router;
